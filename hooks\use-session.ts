"use client"

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'

interface User {
  id: string
  email: string
  name: string
  type: 'admin' | 'tenant' | 'store' | 'buyer'
  tenantId?: string
  storeId?: string
  buyerId?: string
}

interface SessionState {
  user: User | null
  loading: boolean
  error: string | null
}

export function useSession() {
  const [session, setSession] = useState<SessionState>({
    user: null,
    loading: true,
    error: null
  })
  const router = useRouter()

  useEffect(() => {
    // Only check session if we're not on login page
    if (typeof window !== 'undefined' && !window.location.pathname.includes('/login')) {
      checkSession()
    } else {
      // If on login page, just set loading to false
      setSession(prev => ({ ...prev, loading: false }))
    }
  }, [])

  const checkSession = async () => {
    try {
      console.log('[Session] Checking session...')
      const response = await fetch('/api/auth/login', {
        method: 'GET',
        credentials: 'include'
      })

      const data = await response.json()
      console.log('[Session] Session check response:', data)

      if (data.success) {
        console.log('[Session] Session valid, user:', data.user)
        setSession({
          user: data.user,
          loading: false,
          error: null
        })
      } else {
        console.log('[Session] Session invalid:', data.error)
        setSession({
          user: null,
          loading: false,
          error: data.error
        })
      }
    } catch (error) {
      console.error('[Session] Session check failed:', error)
      setSession({
        user: null,
        loading: false,
        error: 'Failed to check session'
      })
    }
  }

  const logout = async () => {
    try {
      // Clear session cookie
      document.cookie = 'session_token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;'
      
      setSession({
        user: null,
        loading: false,
        error: null
      })

      // Redirect to login
      router.push('/login')
    } catch (error) {
      console.error('Logout failed:', error)
    }
  }

  return {
    ...session,
    logout,
    refreshSession: checkSession
  }
}

"use client"

import React from 'react'

interface ErrorBoundaryState {
  hasError: boolean
  error?: Error
}

interface ErrorBoundaryProps {
  children: React.ReactNode
}

export class SellzioErrorBoundary extends React.Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error }
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Sellzio Error:', error, errorInfo)
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="sellzio-error-page">
          <div className="sellzio-error-container">
            <div className="sellzio-error-card">
              <div className="sellzio-error-icon">⚠️</div>
              <h2 className="sellzio-error-title">
                <PERSON><PERSON><PERSON><PERSON>
              </h2>
              <p className="sellzio-error-message">
                <PERSON><PERSON>, terjadi kesalahan saat memuat halaman. 
                Silakan coba lagi atau kembali ke halaman sebelumnya.
              </p>
              <div className="sellzio-error-actions">
                <button
                  onClick={() => window.location.reload()}
                  className="sellzio-error-btn sellzio-error-btn-primary"
                >
                  Muat Ulang Halaman
                </button>
                <button
                  onClick={() => window.history.back()}
                  className="sellzio-error-btn sellzio-error-btn-secondary"
                >
                  Kembali
                </button>
              </div>
              {process.env.NODE_ENV === 'development' && this.state.error && (
                <details className="sellzio-error-debug">
                  <summary className="sellzio-error-debug-summary">
                    Debug Info (Development)
                  </summary>
                  <pre className="sellzio-error-debug-content">
                    {this.state.error.toString()}
                  </pre>
                </details>
              )}
            </div>
          </div>

          <style jsx>{`
            .sellzio-error-page {
              min-height: 100vh;
              background: #f5f5f5;
              display: flex;
              align-items: center;
              justify-content: center;
              padding: 20px;
              font-family: 'Roboto', Arial, sans-serif;
            }

            .sellzio-error-container {
              max-width: 480px;
              width: 100%;
            }

            .sellzio-error-card {
              background: white;
              padding: 32px;
              border-radius: 12px;
              box-shadow: 0 2px 8px rgba(0,0,0,0.1);
              text-align: center;
            }

            .sellzio-error-icon {
              font-size: 64px;
              margin-bottom: 24px;
            }

            .sellzio-error-title {
              font-size: 24px;
              font-weight: 700;
              color: #333;
              margin: 0 0 16px 0;
            }

            .sellzio-error-message {
              font-size: 16px;
              color: #666;
              line-height: 1.5;
              margin: 0 0 32px 0;
            }

            .sellzio-error-actions {
              display: flex;
              flex-direction: column;
              gap: 12px;
            }

            .sellzio-error-btn {
              padding: 12px 24px;
              border-radius: 8px;
              font-size: 16px;
              font-weight: 600;
              cursor: pointer;
              transition: all 0.2s ease;
              border: none;
            }

            .sellzio-error-btn-primary {
              background: #ee4d2d;
              color: white;
            }

            .sellzio-error-btn-primary:hover {
              background: #d73527;
              transform: translateY(-1px);
              box-shadow: 0 4px 12px rgba(238, 77, 45, 0.3);
            }

            .sellzio-error-btn-secondary {
              background: #f8f9fa;
              color: #333;
              border: 1px solid #e5e5e5;
            }

            .sellzio-error-btn-secondary:hover {
              background: #e9ecef;
            }

            .sellzio-error-debug {
              margin-top: 24px;
              text-align: left;
            }

            .sellzio-error-debug-summary {
              cursor: pointer;
              font-size: 14px;
              color: #666;
              margin-bottom: 8px;
            }

            .sellzio-error-debug-content {
              font-size: 12px;
              color: #e74c3c;
              background: #fff5f5;
              padding: 12px;
              border-radius: 4px;
              overflow: auto;
              white-space: pre-wrap;
              word-break: break-word;
            }

            @media (max-width: 768px) {
              .sellzio-error-card {
                padding: 24px;
              }

              .sellzio-error-icon {
                font-size: 48px;
                margin-bottom: 20px;
              }

              .sellzio-error-title {
                font-size: 20px;
              }

              .sellzio-error-message {
                font-size: 14px;
              }

              .sellzio-error-btn {
                padding: 10px 20px;
                font-size: 14px;
              }
            }
          `}</style>
        </div>
      )
    }

    return this.props.children
  }
}

"use client"

import React from 'react'

interface SellzioPaymentSummaryProps {
  subtotal: number
  shippingCost: number
  voucherDiscount: number
  total: number
}

export const SellzioPaymentSummary: React.FC<SellzioPaymentSummaryProps> = ({
  subtotal,
  shippingCost,
  voucherDiscount,
  total
}) => {
  const formatPrice = (price: number) => {
    return `Rp${price.toLocaleString('id-ID')}`
  }

  return (
    <div className="sellzio-checkout-section">
      <div className="sellzio-section-title">
        <span>Ringkasan Pembayaran</span>
      </div>
      
      <div className="sellzio-cost-breakdown">
        <div className="sellzio-cost-item">
          <span>Subtotal Produk</span>
          <span>{formatPrice(subtotal)}</span>
        </div>
        
        <div className="sellzio-cost-item">
          <span>Ongkos Kirim</span>
          <span>{formatPrice(shippingCost)}</span>
        </div>
        
        {voucherDiscount > 0 && (
          <div className="sellzio-cost-item">
            <span>Voucher</span>
            <span className="sellzio-discount">-{formatPrice(voucherDiscount)}</span>
          </div>
        )}
        
        <div className="sellzio-total-cost">
          <span>Total Pembayaran</span>
          <span className="sellzio-total-price">{formatPrice(total)}</span>
        </div>
      </div>
    </div>
  )
}

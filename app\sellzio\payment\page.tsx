"use client"

import React, { useState, useEffect } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { SellzioPaymentPage } from '@/components/themes/sellzio/payment/sellzio-payment-page'
import { SellzioErrorBoundary } from '@/components/themes/sellzio/common/sellzio-error-boundary'
// Import CSS payment untuk memastikan dimuat dengan benar
import '@/components/themes/sellzio/payment/sellzio-payment-styles.css'

export default function PaymentPage() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const [isLoading, setIsLoading] = useState(true)
  const [orderData, setOrderData] = useState<any>(null)

  useEffect(() => {
    // Apply Sellzio payment styles
    const applyPaymentStyles = () => {
      const paymentPage = document.querySelector('.sellzio-payment-page') as HTMLElement
      if (paymentPage) {
        paymentPage.style.margin = '0'
        paymentPage.style.padding = '0'
        console.log('Payment styles applied')
      }
    }

    // Get order data from URL params or localStorage
    const orderId = searchParams.get('orderId')
    const orderDataFromStorage = localStorage.getItem('sellzio-order-data')
    
    if (orderDataFromStorage) {
      try {
        const parsedOrderData = JSON.parse(orderDataFromStorage)
        setOrderData(parsedOrderData)
      } catch (error) {
        console.error('Error parsing order data:', error)
        router.push('/sellzio/checkout')
        return
      }
    } else if (!orderId) {
      // No order data, redirect to checkout
      router.push('/sellzio/checkout')
      return
    }

    setIsLoading(false)

    // Apply styles immediately and after a short delay
    applyPaymentStyles()
    const timeoutId = setTimeout(applyPaymentStyles, 50)

    return () => clearTimeout(timeoutId)
  }, [router, searchParams])

  const handleBackToCheckout = () => {
    router.push('/sellzio/checkout')
  }

  const handlePaymentSuccess = (paymentData: any) => {
    console.log('Payment successful:', paymentData)
    // Store payment data for status page
    localStorage.setItem('sellzio-payment-data', JSON.stringify(paymentData))
    // Clear order data
    localStorage.removeItem('sellzio-order-data')
    // Redirect to payment status
    router.push(`/sellzio/payment/status?paymentId=${paymentData.payment.id}`)
  }

  const handlePaymentError = (error: string) => {
    console.error('Payment error:', error)
    // Could show error modal or redirect back to checkout
  }

  if (isLoading) {
    return (
      <div style={{
        minHeight: '100vh',
        backgroundColor: '#f5f5f5',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      }}>
        <div style={{
          textAlign: 'center',
          backgroundColor: 'white',
          padding: '2rem',
          borderRadius: '8px',
          boxShadow: '0 1px 3px rgba(0,0,0,0.1)'
        }}>
          <div style={{
            width: '48px',
            height: '48px',
            border: '3px solid #f3f3f3',
            borderTop: '3px solid #ee4d2d',
            borderRadius: '50%',
            animation: 'spin 1s linear infinite',
            margin: '0 auto 16px'
          }}></div>
          <p style={{ color: '#666', fontWeight: '500' }}>Memuat pembayaran...</p>
        </div>
      </div>
    )
  }

  if (!orderData) {
    return (
      <div style={{
        minHeight: '100vh',
        backgroundColor: '#f5f5f5',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      }}>
        <div style={{
          textAlign: 'center',
          backgroundColor: 'white',
          padding: '2rem',
          borderRadius: '8px',
          boxShadow: '0 1px 3px rgba(0,0,0,0.1)'
        }}>
          <p style={{ color: '#666', fontWeight: '500' }}>Data pesanan tidak ditemukan</p>
          <button 
            onClick={handleBackToCheckout}
            style={{
              marginTop: '1rem',
              padding: '0.5rem 1rem',
              backgroundColor: '#ee4d2d',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer'
            }}
          >
            Kembali ke Checkout
          </button>
        </div>
      </div>
    )
  }

  return (
    <SellzioErrorBoundary>
      <SellzioPaymentPage
        orderData={orderData}
        onBack={handleBackToCheckout}
        onPaymentSuccess={handlePaymentSuccess}
        onPaymentError={handlePaymentError}
      />
    </SellzioErrorBoundary>
  )
}

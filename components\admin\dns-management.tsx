"use client"

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { useToast } from '@/hooks/use-toast'
import { 
  Globe, 
  Plus, 
  Trash2, 
  RefreshCw, 
  CheckCircle, 
  AlertCircle,
  Settings,
  Loader2
} from 'lucide-react'

interface DNSRecord {
  id: string
  name: string
  content: string
  type: string
  proxied: boolean
  ttl: number
}

export function DNSManagement() {
  const [records, setRecords] = useState<DNSRecord[]>([])
  const [loading, setLoading] = useState(false)
  const [newSubdomain, setNewSubdomain] = useState('')
  const [targetIP, setTargetIP] = useState('***********')
  const { toast } = useToast()

  useEffect(() => {
    fetchDNSRecords()
  }, [])

  const fetchDNSRecords = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/dns/manage')
      const data = await response.json()
      
      if (response.ok) {
        setRecords(data.records || [])
      } else {
        throw new Error(data.error || 'Failed to fetch DNS records')
      }
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to fetch DNS records",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  const createDNSRecord = async () => {
    if (!newSubdomain) {
      toast({
        title: "Error",
        description: "Subdomain is required",
        variant: "destructive"
      })
      return
    }

    setLoading(true)
    try {
      const response = await fetch('/api/dns/manage', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'create',
          subdomain: newSubdomain,
          targetDomain: 'cname.vercel-dns.com'
        })
      })

      const data = await response.json()
      
      if (data.success) {
        toast({
          title: "Success",
          description: `DNS record created for ${newSubdomain}.sellzio.my.id`,
        })
        setNewSubdomain('')
        await fetchDNSRecords()
      } else {
        throw new Error(data.error || 'Failed to create DNS record')
      }
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to create DNS record",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  const deleteDNSRecord = async (subdomain: string) => {
    setLoading(true)
    try {
      const response = await fetch('/api/dns/manage', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'delete',
          subdomain
        })
      })

      const data = await response.json()
      
      if (data.success) {
        toast({
          title: "Success",
          description: `DNS record deleted for ${subdomain}.sellzio.my.id`,
        })
        await fetchDNSRecords()
      } else {
        throw new Error(data.error || 'Failed to delete DNS record')
      }
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to delete DNS record",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  const verifyDNSRecord = async (subdomain: string) => {
    try {
      const response = await fetch('/api/dns/manage', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'verify',
          subdomain
        })
      })

      const data = await response.json()
      
      toast({
        title: data.success ? "Verified" : "Not Found",
        description: `DNS record for ${subdomain}.sellzio.my.id ${data.success ? 'exists' : 'not found'}`,
        variant: data.success ? "default" : "destructive"
      })
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to verify DNS record",
        variant: "destructive"
      })
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="space-y-2">
        <h2 className="text-2xl font-bold tracking-tight">DNS Management</h2>
        <p className="text-muted-foreground">
          Kelola DNS records untuk subdomain tenant secara otomatis
        </p>
      </div>

      {/* Create New Record */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Plus className="h-5 w-5" />
            Create DNS Record
          </CardTitle>
          <CardDescription>
            Buat DNS record baru untuk subdomain tenant
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <div className="space-y-2">
              <Label htmlFor="subdomain">Subdomain</Label>
              <div className="flex items-center space-x-2">
                <Input
                  id="subdomain"
                  value={newSubdomain}
                  onChange={(e) => setNewSubdomain(e.target.value.toLowerCase())}
                  placeholder="nama-tenant"
                />
                <span className="text-sm text-muted-foreground">.sellzio.my.id</span>
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="targetIP">Target IP</Label>
              <Input
                id="targetIP"
                value={targetIP}
                onChange={(e) => setTargetIP(e.target.value)}
                placeholder="***********"
              />
            </div>
          </div>
          <Button 
            onClick={createDNSRecord} 
            disabled={loading || !newSubdomain}
            className="w-full md:w-auto"
          >
            {loading ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Creating...
              </>
            ) : (
              <>
                <Plus className="h-4 w-4 mr-2" />
                Create DNS Record
              </>
            )}
          </Button>
        </CardContent>
      </Card>

      {/* DNS Records List */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Globe className="h-5 w-5" />
                DNS Records
              </CardTitle>
              <CardDescription>
                Daftar semua DNS records untuk subdomain tenant
              </CardDescription>
            </div>
            <Button variant="outline" onClick={fetchDNSRecords} disabled={loading}>
              <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-6 w-6 animate-spin mr-2" />
              Loading DNS records...
            </div>
          ) : records.length === 0 ? (
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                No DNS records found. Create your first subdomain record above.
              </AlertDescription>
            </Alert>
          ) : (
            <div className="space-y-4">
              {records.map((record) => (
                <div key={record.id} className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="space-y-1">
                    <div className="flex items-center gap-2">
                      <span className="font-medium">{record.name}</span>
                      <Badge variant={record.proxied ? "default" : "outline"}>
                        {record.type}
                      </Badge>
                      {record.proxied && (
                        <Badge variant="secondary">
                          <CheckCircle className="h-3 w-3 mr-1" />
                          Proxied
                        </Badge>
                      )}
                    </div>
                    <p className="text-sm text-muted-foreground">
                      Points to: {record.content}
                    </p>
                  </div>
                  <div className="flex items-center gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => verifyDNSRecord(record.name.replace('.sellzio.my.id', ''))}
                    >
                      <Settings className="h-3 w-3 mr-1" />
                      Verify
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => deleteDNSRecord(record.name.replace('.sellzio.my.id', ''))}
                      className="text-red-600 hover:text-red-700"
                    >
                      <Trash2 className="h-3 w-3 mr-1" />
                      Delete
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Configuration Info */}
      <Alert>
        <Settings className="h-4 w-4" />
        <AlertDescription>
          <strong>Auto DNS:</strong> Sistem akan otomatis membuat DNS record saat tenant mengubah subdomain. 
          Pastikan Cloudflare API Token dan Zone ID sudah dikonfigurasi di environment variables.
        </AlertDescription>
      </Alert>
    </div>
  )
}

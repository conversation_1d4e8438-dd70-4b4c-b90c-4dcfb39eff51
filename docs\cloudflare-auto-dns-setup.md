# Setup Auto DNS dengan Cloudflare API

## Overview
Sistem ini memungkinkan tenant untuk membuat subdomain yang langsung aktif tanpa perlu konfigurasi DNS manual. Saat tenant mengubah subdomain, sistem otomatis membuat DNS record di Cloudflare.

## Langkah Setup Cloudflare

### 1. Dapatkan Cloudflare API Token

1. **Login ke Cloudflare Dashboard**
   - Akses: https://dash.cloudflare.com
   - Login dengan akun Anda

2. **Buat API Token**
   - Klik profil (kanan atas) → "My Profile"
   - Tab "API Tokens" → "Create Token"
   - Pilih "Custom token"

3. **Konfigurasi Token**
   ```
   Token name: Sellzio DNS Management
   Permissions:
   - Zone:Zone:Read
   - Zone:DNS:Edit
   Zone Resources:
   - Include: Specific zone → sellzio.my.id
   ```

4. **Copy API Token**
   - Simpan token yang dihasilkan (hanya ditampilkan sekali)

### 2. Dapatkan Zone ID

1. **Pilih Domain**
   - Di dashboard Cloudflare, pilih domain `sellzio.my.id`

2. **Copy Zone ID**
   - Di sidebar kanan, bagian "API" → copy "Zone ID"

### 3. Konfigurasi Environment Variables

#### A. Di Vercel Dashboard:
1. Buka project Sellzio di Vercel
2. Settings → Environment Variables
3. Tambahkan:
   ```
   CLOUDFLARE_API_TOKEN = your_api_token_here
   CLOUDFLARE_ZONE_ID = your_zone_id_here
   VERCEL_IP = ***********
   ```

#### B. Di Local Development (.env.local):
```env
CLOUDFLARE_API_TOKEN=your_api_token_here
CLOUDFLARE_ZONE_ID=your_zone_id_here
VERCEL_IP=***********
```

### 4. Verifikasi Setup

1. **Test API Connection**
   ```bash
   curl -X GET "https://api.cloudflare.com/client/v4/zones/YOUR_ZONE_ID" \
        -H "Authorization: Bearer YOUR_API_TOKEN" \
        -H "Content-Type: application/json"
   ```

2. **Test via Admin Dashboard**
   - Login ke admin: `https://sellzio.my.id/admin/login`
   - Akses DNS Management
   - Coba buat DNS record test

## Cara Kerja Sistem

### 1. Flow Otomatis
```
Tenant Update Subdomain → API Update Database → Auto Create DNS → Subdomain Aktif
```

### 2. Proses Detail
1. **Tenant mengubah subdomain** di dashboard
2. **API `/api/tenants/update-subdomain`** dipanggil
3. **Database diupdate** dengan subdomain baru
4. **Cloudflare API dipanggil** untuk membuat DNS record
5. **DNS record dibuat** dengan konfigurasi:
   - Type: A
   - Name: subdomain-baru
   - Content: *********** (Vercel IP)
   - Proxied: true (untuk SSL otomatis)
6. **Subdomain langsung aktif** dalam 1-2 menit

### 3. Cleanup Otomatis
- Saat subdomain diubah, DNS record lama otomatis dihapus
- Mencegah DNS record yang tidak terpakai

## DNS Record Configuration

### Format Record yang Dibuat:
```
Type: CNAME
Name: nama-tenant
Content: cname.vercel-dns.com
TTL: Auto
Proxied: No (DNS Only - Gray Cloud)
```

### Contoh:
- Tenant pilih subdomain: `toko-bella`
- DNS record dibuat: `toko-bella.sellzio.my.id → CNAME → cname.vercel-dns.com`
- URL aktif: `https://toko-bella.sellzio.my.id` (langsung tampil storefront)

## Troubleshooting

### 1. DNS Record Tidak Terbuat
**Penyebab:**
- API Token tidak valid
- Zone ID salah
- Permissions tidak cukup

**Solusi:**
- Verifikasi API Token dan Zone ID
- Cek permissions di Cloudflare
- Lihat logs di Vercel Functions

### 2. Subdomain Tidak Bisa Diakses
**Penyebab:**
- DNS belum propagasi
- IP target salah
- SSL belum aktif

**Solusi:**
- Tunggu 1-5 menit untuk propagasi
- Verifikasi IP Vercel
- Pastikan Proxied enabled

### 3. Error "Zone not found"
**Penyebab:**
- Zone ID salah
- Domain tidak ada di akun Cloudflare

**Solusi:**
- Verifikasi Zone ID di dashboard
- Pastikan domain sudah ditambahkan ke Cloudflare

## API Endpoints

### 1. Auto DNS (Internal)
```
POST /api/tenants/update-subdomain
{
  "subdomain": "nama-baru",
  "tenantId": "uuid"
}
```

### 2. Manual DNS Management
```
POST /api/dns/manage
{
  "action": "create|update|delete|verify",
  "subdomain": "nama-subdomain",
  "targetIP": "***********"
}
```

### 3. List DNS Records
```
GET /api/dns/manage
GET /api/dns/manage?subdomain=nama-subdomain
```

## Security Best Practices

### 1. API Token Security
- Gunakan token dengan permissions minimal
- Jangan commit token ke repository
- Rotate token secara berkala

### 2. Rate Limiting
- Cloudflare API memiliki rate limit
- Sistem sudah handle error gracefully
- Tidak akan gagal jika DNS creation error

### 3. Validation
- Subdomain format divalidasi
- Reserved names dicegah
- Duplicate checking

## Monitoring & Logs

### 1. Vercel Logs
- Function logs tersedia di Vercel dashboard
- Monitor DNS creation success/failure

### 2. Cloudflare Logs
- DNS changes tercatat di Cloudflare audit log
- Monitor API usage di dashboard

### 3. Application Logs
- Success/error logs di console
- Database changes tracked

## Backup & Recovery

### 1. DNS Backup
- Export DNS records secara berkala
- Simpan konfigurasi penting

### 2. Rollback Plan
- Manual DNS creation jika API gagal
- Fallback ke konfigurasi manual

---

**Catatan:** Setelah setup selesai, tenant dapat langsung mengubah subdomain dan akan aktif dalam 1-2 menit tanpa intervensi manual.

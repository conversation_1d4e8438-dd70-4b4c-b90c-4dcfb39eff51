<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Voucher Shopee</title>
    <style>
        /* Reset CSS */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: '<PERSON><PERSON>', <PERSON>l, sans-serif;
        }

        body {
            background-color: #f5f5f5;
            color: #333;
            line-height: 1.6;
        }

        /* Header */
        .header {
            position: sticky;
            top: 0;
            width: 100%;
            background-color: #fff;
            padding: 15px 0;
            text-align: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            z-index: 100;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        @media (min-width: 800px) {
            .header-container {
                max-width: 800px;
                margin: 0 auto;
                position: relative;
                width: 100%;
            }
        }

        .header h1 {
            font-size: 1.2rem;
            font-weight: bold;
        }

       .back-btn {
    position: absolute;
    left: 15px;
    top: 30%;
    transform: translateY(-50%);
    border: none;
    background: none;
    font-size: 4rem;
    cursor: pointer;
    color: #ee4d2d;
    line-height: 1;
}

        .container {
            max-width: 800px;
            margin: 3px auto 20px;
            padding: 15px;
        }

        .section {
            background-color: #fff;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 8px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        /* Search */
        .search-bar {
            padding: 10px 16px;
            background-color: #fff;
            position: relative;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            margin-bottom: 8px;
        }

        .search-input {
            display: flex;
            align-items: center;
            background-color: #f5f5f5;
            border-radius: 10px;
            padding: 8px 12px;
            margin-top: 2px;
        }

        .search-input input {
            flex: 1;
            border: none;
            background: none;
            outline: none;
            font-size: 14px;
        }

        .search-icon {
            margin-left: 8px;
            font-size: 18px;
            color: #757575;
            cursor: pointer;
        }
        
        .search-icon.active {
            color: #ee4d2d;
        }

        .clear-icon {
            margin-right: 8px;
            font-size: 18px;
            color: #999;
            cursor: pointer;
            display: none;
        }

        /* Search suggestions */
        .search-suggestions {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background-color: #fff;
            border-radius: 0 0 8px 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            z-index: 100;
            max-height: 300px;
            overflow-y: auto;
            display: none;
        }

        .suggestion-item {
            padding: 12px 16px;
            border-bottom: 1px solid #f0f0f0;
            font-size: 14px;
            cursor: pointer;
        }

        .suggestion-item:hover {
            background-color: #f9f9f9;
        }

        .suggestion-title {
            font-weight: bold;
            margin-bottom: 4px;
        }

        .suggestion-desc {
            font-size: 12px;
            color: #757575;
        }

        .suggestion-keyword {
            color: #ee4d2d;
            padding: 4px 0;
        }

        /* Tabs as buttons */
        .tabs {
            display: flex;
            background-color: #fff;
            padding: 16px 16px 16px 16px;
            margin-bottom: 8px;
            overflow-x: auto;
            white-space: nowrap;
            scrollbar-width: none;
            -ms-overflow-style: none;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .tabs::-webkit-scrollbar {
            display: none;
        }

        .tab {
            display: inline-block;
            padding: 8px 16px;
            margin-right: 8px;
            border-radius: 16px;
            font-size: 14px;
            font-weight: bold;
            color: #757575;
            background-color: #f5f5f5;
            cursor: pointer;
            border: 1px solid #e0e0e0;
        }

        .tab.active {
            color: #fff;
            background-color: #ee4d2d;
            border-color: #ee4d2d;
        }

        /* Main container */
        .content-area {
            background-color: #f5f5f5;
            border-radius: 8px;
            margin-bottom: 8px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            padding: 15px;
        }

        /* Voucher List */
        .voucher-list {
            padding: 0;
        }

        .voucher-group {
            margin-bottom: 20px;
        }

        .group-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
            padding: 0 4px;
        }

        .group-title {
            font-size: 16px;
            font-weight: bold;
            color: #333;
        }

        .show-more {
            font-size: 13px;
            color: #ee4d2d;
            cursor: pointer;
            display: flex;
            align-items: center;
        }

        .show-more-icon {
            margin-left: 4px;
            font-size: 14px;
        }

        .voucher-item {
            display: flex;
            margin-bottom: 12px;
            border-radius: 4px;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            position: relative;
            width: 100%;
            background-color: #fff;
        }

        .voucher-left {
            width: 80px;
            min-width: 80px;
            background-color: #ee4d2d;
            color: #fff;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: 12px 0;
            position: relative;
        }

        .voucher-left::after {
            content: '';
            position: absolute;
            right: 0;
            top: 0;
            bottom: 0;
            width: 8px;
            background-image: radial-gradient(circle at 0 50%, transparent 8px, #fff 8px);
            background-size: 8px 16px;
            background-repeat: repeat-y;
        }

        .voucher-amount {
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 4px;
            line-height: 1;
        }

        .voucher-min {
            font-size: 10px;
            opacity: 0.8;
            text-align: center;
            padding: 5px 10px;
            margin: 0px 0;
        }

        .voucher-right {
            flex: 1;
            background-color: #fff;
            padding: 12px;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }

        .voucher-title {
            font-size: 14px;
            font-weight: bold;
            margin-bottom: 4px;
            color: #212121;
        }

        .voucher-desc {
            font-size: 12px;
            color: #757575;
            margin-bottom: 8px;
        }

        .voucher-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .voucher-date {
            font-size: 11px;
            color: #999;
        }

        /* Circular checkbox for vouchers */
        .circular-checkbox {
            width: 24px;
            height: 24px;
            position: relative;
        }

        .circular-checkbox input {
            opacity: 0;
            width: 0;
            height: 0;
            position: absolute;
        }

        .checkmark {
            position: absolute;
            top: 0;
            left: 0;
            height: 24px;
            width: 24px;
            background-color: #fff;
            border: 2px solid #ddd;
            border-radius: 50%;
        }

        .circular-checkbox input:checked ~ .checkmark {
            background-color: #ee4d2d;
            border-color: #ee4d2d;
        }

        .checkmark:after {
            content: "";
            position: absolute;
            display: none;
        }

        .circular-checkbox input:checked ~ .checkmark:after {
            display: block;
            left: 8px;
            top: 4px;
            width: 5px;
            height: 10px;
            border: solid white;
            border-width: 0 2px 2px 0;
            transform: rotate(45deg);
        }

        /* Selected vouchers summary */
        .selected-vouchers-summary {
            background-color: #fff9f5;
            padding: 8px 8px 8px 8px;
            margin-bottom: 8px;
            border-top: 1px solid #ffe5d9;
            font-size: 13px;
            border-radius: 8px;
            display: none !important;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .selected-voucher-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 4px;
          background-color: #fff9f5;
    border-radius: 10px;
    padding: 10px 15px;
    margin-bottom: 8px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .selected-voucher-name {
            font-weight: bold;
            color: #333;
        }

        .selected-voucher-value {
            color: #ee4d2d;
            font-weight: bold;
        }
  

        /* Footer */
        .footer {
            position: sticky;
            bottom: 0;
            width: 100%;
            background-color: #fff;
            padding: 15px;
            box-shadow: 0 -2px 4px rgba(0,0,0,0.1);
            display: flex;
            flex-direction: column;
            margin-top: 15px;
            border-radius: 8px;
            z-index: 50;
        }

        .footer-summary {
            margin-bottom: 15px;
            font-size: 13px;
        }

        .apply-btn {
            background-color: #ee4d2d;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 4px;
            font-weight: bold;
            cursor: pointer;
            transition: background-color 0.3s;
            width: 100%;
        }

        .apply-btn:hover {
            background-color: #d63c1e;
        }

        .apply-btn:disabled {
            background-color: #aaa;
            cursor: not-allowed;
        }

        /* Recommended badge */
        .recommended-badge {
            position: absolute;
            top: 0;
            right: 0;
            background-color: #ff9800;
            color: white;
            font-size: 10px;
            font-weight: bold;
            padding: 3px 8px;
            border-radius: 0 4px 0 4px;
            z-index: 1;
        }

        /* Variant styling */
        .shipping .voucher-left {
            background-color: #00bfa5;
        }

        .cashback .voucher-left {
            background-color: #9c27b0;
        }

        .payment .voucher-left {
            background-color: #3f51b5;
        }

        .bank .voucher-left {
            background-color: #4CAF50;
        }

        /* Divider */
        .divider {
            height: 8px;
            background-color: #f5f5f5;
            margin: 16px 0;
        }
        
        /* Default show only 2 vouchers per group */
        .voucher-item:nth-child(n+10) {
            display: none;
        }
        
        .voucher-item.visible {
            display: flex !important;
        }

        /* Tab content areas */
        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }
        
        /* Untuk grup voucher gratis ongkir, hanya tampilkan 1 voucher default */
        .voucher-group[data-group="shipping"] .voucher-item:nth-child(n+2) {
            display: none;
        }

        /* Toast Notification */
        .toast {
            position: fixed;
            top: 70px;
            left: 50%;
            transform: translateX(-50%);
            background-color: rgba(0,0,0,0.7);
            color: white;
            padding: 10px 20px;
            border-radius: 4px;
            z-index: 1000;
            display: none;
        }

        /* Scrollbar styling */
        .voucher-list::-webkit-scrollbar {
            width: 6px;
        }

        .voucher-list::-webkit-scrollbar-track {
            background: #f1f1f1;
        }

        .voucher-list::-webkit-scrollbar-thumb {
            background: #ddd;
            border-radius: 4px;
        }

        .voucher-list::-webkit-scrollbar-thumb:hover {
            background: #ccc;
        }

        /* Responsive */
        @media (max-width: 480px) {
            .container {
                padding: 10px;
            }

            .section {
                padding: 12px;
            }

            .footer {
                padding: 10px;
            }

            .apply-btn {
                padding: 10px 20px;
            }
        }

        /* Voucher yang tidak aktif */
        .voucher-item.disabled .voucher-left {
            background-color: #aaaaaa !important;
            opacity: 0.7;
        }

        .voucher-item.disabled .voucher-right {
            opacity: 0.7;
        }

        .voucher-item.disabled .recommended-badge {
            background-color: #888888;
        }

        /* Checkbox tidak aktif */
        .voucher-item.disabled .circular-checkbox {
            cursor: not-allowed;
        }

        .voucher-item.disabled .circular-checkbox input {
            pointer-events: none;
        }

        .voucher-item.disabled .checkmark {
            background-color: #e0e0e0;
            border-color: #cccccc;
        }
        
        /* Card peringatan tidak ada hasil */
        .no-results-card {
            background-color: #fff8e1;
            border-radius: 8px;
            padding: 15px;
            margin-top: 10px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            display: none;
            text-align: center;
            color: #ff6d00;
            font-size: 14px;
            border-left: 4px solid #ff9800;
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="header-container" style="max-width: 800px; width: 100%; padding: 0 15px; margin: 0 auto;">
            <button class="back-btn">←</button>
            <h1>Voucher Saya</h1>
        </div>
    </header>

    <div class="container">
        <!-- Search Bar -->
        <div class="search-bar">
            <div class="search-input">
                <input type="text" placeholder="Cari voucher..." id="search-input">
                <svg class="clear-icon" xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <circle cx="12" cy="12" r="10"></circle>
                    <line x1="15" y1="9" x2="9" y2="15"></line>
                    <line x1="9" y1="9" x2="15" y2="15"></line>
                </svg>
                <svg class="search-icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <circle cx="11" cy="11" r="8"></circle>
                    <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
                </svg>
            </div>
            <div class="search-suggestions" id="search-suggestions">
                <!-- Search suggestions will be dynamically populated here -->
            </div>
            <div class="no-results-card" id="no-results-card">
                Maaf, voucher yang Anda cari tidak ditemukan.
            </div>
        </div>
        
        <!-- Tabs -->
        <div class="tabs">
            <div class="tab active" data-tab="all">Semua</div>
            <div class="tab" data-tab="shipping">Gratis Ongkir</div>
            <div class="tab" data-tab="discount">Diskon</div>
            <div class="tab" data-tab="cashback">Cashback</div>
        </div>
        
        <!-- Main Content -->
        <div class="content-area">
            <!-- All Vouchers Tab -->
            <div class="tab-content active" id="tab-all">
                <div class="voucher-list">
                    <!-- Voucher Diskon Group -->
                    <div class="voucher-group" data-group="discount">
                        <div class="group-header">
                            <div class="group-title">Voucher Diskon</div>
                        </div>
                        
                        <div class="voucher-item visible">
                            <div class="recommended-badge">Recommended</div>
                            <div class="voucher-left">
                                <div class="voucher-amount">Rp50rb</div>
                                <div class="voucher-min">Min. belanja Rp200rb</div>
                            </div>
                            <div class="voucher-right">
                                <div>
                                    <div class="voucher-title">Diskon Belanja</div>
                                    <div class="voucher-desc">Diskon Rp50.000 untuk semua kategori produk</div>
                                </div>
                                <div class="voucher-info">
                                    <div class="voucher-date" data-expiry-date="2025-03-30">Berlaku hingga: 30 Mar 2025</div>
                                    <label class="circular-checkbox">
                                        <input type="checkbox" class="voucher-checkbox" data-value="50000" data-min="200000" data-group="discount" data-title="Diskon Belanja">
                                        <span class="checkmark"></span>
                                    </label>
                                </div>
                            </div>
                        </div>
                        
                        <div class="voucher-item visible">
                            <div class="voucher-left">
                                <div class="voucher-amount">Rp20rb</div>
                                <div class="voucher-min">Min. belanja Rp100rb</div>
                            </div>
                            <div class="voucher-right">
                                <div>
                                    <div class="voucher-title">Diskon Fashion</div>
                                    <div class="voucher-desc">Diskon Rp20.000 khusus kategori Fashion</div>
                                </div>
                                <div class="voucher-info">
                                    <div class="voucher-date" data-expiry-date="2025-03-25">Berlaku hingga: 25 Mar 2025</div>
                                    <label class="circular-checkbox">
                                        <input type="checkbox" class="voucher-checkbox" data-value="20000" data-min="100000" data-group="discount" data-title="Diskon Fashion">
                                        <span class="checkmark"></span>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="divider"></div>
                    
                    <!-- Gratis Ongkir Group -->
                    <div class="voucher-group" data-group="shipping">
                        <div class="group-header">
                            <div class="group-title">Gratis Ongkir</div>
                            <div class="show-more" id="show-more-shipping">
                                Lihat Lainnya
                                <svg class="show-more-icon" xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <polyline points="6 9 12 15 18 9"></polyline>
                                </svg>
                            </div>
                        </div>
                        
                        <div class="voucher-item shipping visible">
                            <div class="recommended-badge">Recommended</div>
                            <div class="voucher-left">
                                <div class="voucher-amount">Gratis</div>
                                <div class="voucher-min">Ongkir s/d 20rb</div>
                            </div>
                            <div class="voucher-right">
                                <div>
                                    <div class="voucher-title">Gratis Ongkir</div>
                                    <div class="voucher-desc">Gratis ongkir hingga Rp20.000 untuk semua toko</div>
                                </div>
                                <div class="voucher-info">
                                    <div class="voucher-date" data-expiry-date="2025-04-05">Berlaku hingga: 5 Apr 2025</div>
                                    <label class="circular-checkbox">
                                        <input type="checkbox" class="voucher-checkbox" data-value="20000" data-type="shipping" data-group="shipping" data-title="Gratis Ongkir Rp20rb">
                                        <span class="checkmark"></span>
                                    </label>
                                </div>
                            </div>
                        </div>
                        
                        <div class="voucher-item shipping">
                            <div class="voucher-left">
                                <div class="voucher-amount">Gratis</div>
                                <div class="voucher-min">Ongkir s/d 15rb</div>
                            </div>
                            <div class="voucher-right">
                                <div>
                                    <div class="voucher-title">Gratis Ongkir Reguler</div>
                                    <div class="voucher-desc">Gratis ongkir hingga Rp15.000 dengan pengiriman reguler</div>
                                </div>
                                <div class="voucher-info">
                                    <div class="voucher-date" data-expiry-date="2025-04-10">Berlaku hingga: 10 Apr 2025</div>
                                    <label class="circular-checkbox">
                                        <input type="checkbox" class="voucher-checkbox" data-value="15000" data-type="shipping" data-group="shipping" data-title="Gratis Ongkir Reguler">
                                        <span class="checkmark"></span>
                                    </label>
                                </div>
                            </div>
                        </div>
                        
                        <div class="voucher-item shipping">
                            <div class="voucher-left">
                                <div class="voucher-amount">Gratis</div>
                                <div class="voucher-min">Ongkir s/d 30rb</div>
                            </div>
                            <div class="voucher-right">
                                <div>
                                    <div class="voucher-title">Gratis Ongkir Premium</div>
                                    <div class="voucher-desc">Gratis ongkir hingga Rp30.000 dengan min. belanja Rp150.000</div>
                                </div>
                                <div class="voucher-info">
                                    <div class="voucher-date" data-expiry-date="2025-04-15">Berlaku hingga: 15 Apr 2025</div>
                                    <label class="circular-checkbox">
                                        <input type="checkbox" class="voucher-checkbox" data-value="30000" data-type="shipping" data-min="150000" data-group="shipping" data-title="Gratis Ongkir Premium">
                                        <span class="checkmark"></span>
                                    </label>
                                </div>
                            </div>
                        </div>
                        
                        <div class="voucher-item shipping">
                            <div class="voucher-left">
                                <div class="voucher-amount">Gratis</div>
                                <div class="voucher-min">Ongkir s/d 25rb</div>
                            </div>
                            <div class="voucher-right">
                                <div>
                                    <div class="voucher-title">Gratis Ongkir COD</div>
                                    <div class="voucher-desc">Gratis ongkir hingga Rp25.000 untuk metode pembayaran COD</div>
                                </div>
                                <div class="voucher-info">
                                    <div class="voucher-date" data-expiry-date="2025-04-18">Berlaku hingga: 18 Apr 2025</div>
                                    <label class="circular-checkbox">
                                        <input type="checkbox" class="voucher-checkbox" data-value="25000" data-type="shipping" data-group="shipping" data-title="Gratis Ongkir COD">
                                        <span class="checkmark"></span>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="divider"></div>
                    
                    <!-- Cashback Group -->
                    <div class="voucher-group" data-group="cashback">
                        <div class="group-header">
                            <div class="group-title">Voucher Cashback</div>
                        </div>
                        
                        <div class="voucher-item cashback visible">
                            <div class="voucher-left">
                                <div class="voucher-amount">10%</div>
                                <div class="voucher-min">Maks. Rp35rb</div>
                            </div>
                            <div class="voucher-right">
                                <div>
                                    <div class="voucher-title">Diskon Elektronik</div>
                                    <div class="voucher-desc">Diskon 10% untuk kategori Elektronik</div>
                                </div>
                                <div class="voucher-info">
                                    <div class="voucher-date" data-expiry-date="2025-03-10">Berlaku hingga: 10 Mar 2025</div>
                                    <label class="circular-checkbox">
                                        <input type="checkbox" class="voucher-checkbox" data-value="35000" data-type="percentage" data-percentage="10" data-group="cashback" data-title="Diskon Elektronik 10%">
                                        <span class="checkmark"></span>
                                    </label>
                                </div>
                            </div>
                        </div>
                        
                        <div class="voucher-item cashback visible">
                            <div class="recommended-badge">Recommended</div>
                            <div class="voucher-left">
                                <div class="voucher-amount">15%</div>
                                <div class="voucher-min">Maks. Rp30rb</div>
                            </div>
                            <div class="voucher-right">
                                <div>
                                    <div class="voucher-title">Cashback Belanja</div>
                                    <div class="voucher-desc">Cashback 15% dalam bentuk koin Shopee</div>
                                </div>
                                <div class="voucher-info">
                                    <div class="voucher-date" data-expiry-date="2025-04-12">Berlaku hingga: 12 Apr 2025</div>
                                    <label class="circular-checkbox">
                                        <input type="checkbox" class="voucher-checkbox" data-value="30000" data-type="cashback" data-percentage="15" data-group="cashback" data-title="Cashback 15%">
                                        <span class="checkmark"></span>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="divider"></div>
                    
                    <!-- Payment Method Group -->
                    <div class="voucher-group" data-group="payment">
                        <div class="group-header">
                            <div class="group-title">Voucher Metode Pembayaran</div>
                        </div>
                        
                        <div class="voucher-item payment visible">
                            <div class="voucher-left">
                                <div class="voucher-amount">Rp10rb</div>
                                <div class="voucher-min">Bayar dengan COD</div>
                            </div>
                            <div class="voucher-right">
                                <div>
                                    <div class="voucher-title">Diskon COD</div>
                                    <div class="voucher-desc">Diskon Rp10.000 untuk pembayaran COD min. Rp100.000</div>
                                </div>
                                <div class="voucher-info">
                                    <div class="voucher-date" data-expiry-date="2025-04-08">Berlaku hingga: 8 Apr 2025</div>
                                    <label class="circular-checkbox">
                                        <input type="checkbox" class="voucher-checkbox" data-value="10000" data-type="payment" data-method="cod" data-min="100000" data-group="payment" data-title="Diskon COD">
                                        <span class="checkmark"></span>
                                    </label>
                                </div>
                            </div>
                        </div>
                        
                        <div class="voucher-item bank visible">
                            <div class="voucher-left">
                                <div class="voucher-amount">5%</div>
                                <div class="voucher-min">Maks. Rp25rb</div>
                            </div>
                            <div class="voucher-right">
                                <div>
                                    <div class="voucher-title">Diskon Transfer Bank</div>
                                    <div class="voucher-desc">Diskon 5% untuk pembayaran via Transfer Bank</div>
                                </div>
                                <div class="voucher-info">
                                    <div class="voucher-date" data-expiry-date="2025-04-20">Berlaku hingga: 20 Apr 2025</div>
                                    <label class="circular-checkbox">
                                        <input type="checkbox" class="voucher-checkbox" data-value="25000" data-type="payment" data-method="bank" data-percentage="5" data-group="payment" data-title="Diskon Transfer 5%">
                                        <span class="checkmark"></span>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Shipping Vouchers Tab -->
            <div class="tab-content" id="tab-shipping">
                <div class="voucher-list">
                    <div class="voucher-group" data-group="shipping">
                        <div class="group-header">
                            <div class="group-title">Gratis Ongkir</div>
                            <div class="show-more" id="show-more-shipping-tab">
                                Lihat Lainnya
                                <svg class="show-more-icon" xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <polyline points="6 9 12 15 18 9"></polyline>
                                </svg>
                            </div>
                        </div>
                        
                        <div class="voucher-item shipping visible">
                            <div class="recommended-badge">Recommended</div>
                            <div class="voucher-left">
                                <div class="voucher-amount">Gratis</div>
                                <div class="voucher-min">Ongkir s/d 20rb</div>
                            </div>
                            <div class="voucher-right">
                                <div>
                                    <div class="voucher-title">Gratis Ongkir</div>
                                    <div class="voucher-desc">Gratis ongkir hingga Rp20.000 untuk semua toko</div>
                                </div>
                                <div class="voucher-info">
                                    <div class="voucher-date" data-expiry-date="2025-04-05">Berlaku hingga: 5 Apr 2025</div>
                                    <label class="circular-checkbox">
                                        <input type="checkbox" class="voucher-checkbox" data-value="20000" data-type="shipping" data-group="shipping" data-title="Gratis Ongkir Rp20rb">
                                        <span class="checkmark"></span>
                                    </label>
                                </div>
                            </div>
                        </div>
                        
                        <div class="voucher-item shipping">
                            <div class="voucher-left">
                                <div class="voucher-amount">Gratis</div>
                                <div class="voucher-min">Ongkir s/d 15rb</div>
                            </div>
                            <div class="voucher-right">
                                <div>
                                    <div class="voucher-title">Gratis Ongkir Reguler</div>
                                    <div class="voucher-desc">Gratis ongkir hingga Rp15.000 dengan pengiriman reguler</div>
                                </div>
                                <div class="voucher-info">
                                    <div class="voucher-date" data-expiry-date="2025-04-10">Berlaku hingga: 10 Apr 2025</div>
                                    <label class="circular-checkbox">
                                        <input type="checkbox" class="voucher-checkbox" data-value="15000" data-type="shipping" data-group="shipping" data-title="Gratis Ongkir Reguler">
                                        <span class="checkmark"></span>
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="voucher-item shipping">
                            <div class="voucher-left">
                                <div class="voucher-amount">Gratis</div>
                                <div class="voucher-min">Ongkir s/d 30rb</div>
                            </div>
                            <div class="voucher-right">
                                <div>
                                    <div class="voucher-title">Gratis Ongkir Premium</div>
                                    <div class="voucher-desc">Gratis ongkir hingga Rp30.000 dengan min. belanja Rp150.000</div>
                                </div>
                                <div class="voucher-info">
                                    <div class="voucher-date" data-expiry-date="2025-04-15">Berlaku hingga: 15 Apr 2025</div>
                                    <label class="circular-checkbox">
                                        <input type="checkbox" class="voucher-checkbox" data-value="30000" data-type="shipping" data-min="150000" data-group="shipping" data-title="Gratis Ongkir Premium">
                                        <span class="checkmark"></span>
                                    </label>
                                </div>
                            </div>
                        </div>
                        
                        <div class="voucher-item shipping">
                            <div class="voucher-left">
                                <div class="voucher-amount">Gratis</div>
                                <div class="voucher-min">Ongkir s/d 25rb</div>
                            </div>
                            <div class="voucher-right">
                                <div>
                                    <div class="voucher-title">Gratis Ongkir COD</div>
                                    <div class="voucher-desc">Gratis ongkir hingga Rp25.000 untuk metode pembayaran COD</div>
                                </div>
                                <div class="voucher-info">
                                    <div class="voucher-date" data-expiry-date="2025-04-18">Berlaku hingga: 18 Apr 2025</div>
                                    <label class="circular-checkbox">
                                        <input type="checkbox" class="voucher-checkbox" data-value="25000" data-type="shipping" data-group="shipping" data-title="Gratis Ongkir COD">
                                        <span class="checkmark"></span>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Discount Vouchers Tab -->
            <div class="tab-content" id="tab-discount">
                <div class="voucher-list">
                    <div class="voucher-group" data-group="discount">
                        <div class="group-header">
                            <div class="group-title">Voucher Diskon</div>
                        </div>
                        
                        <div class="voucher-item visible">
                            <div class="recommended-badge">Recommended</div>
                            <div class="voucher-left">
                                <div class="voucher-amount">Rp50rb</div>
                                <div class="voucher-min">Min. belanja Rp200rb</div>
                            </div>
                            <div class="voucher-right">
                                <div>
                                    <div class="voucher-title">Diskon Belanja</div>
                                    <div class="voucher-desc">Diskon Rp50.000 untuk semua kategori produk</div>
                                </div>
                                <div class="voucher-info">
                                    <div class="voucher-date" data-expiry-date="2025-03-30">Berlaku hingga: 30 Mar 2025</div>
                                    <label class="circular-checkbox">
                                        <input type="checkbox" class="voucher-checkbox" data-value="50000" data-min="200000" data-group="discount" data-title="Diskon Belanja">
                                        <span class="checkmark"></span>
                                    </label>
                                </div>
                            </div>
                        </div>
                        
                        <div class="voucher-item visible">
                            <div class="voucher-left">
                                <div class="voucher-amount">Rp20rb</div>
                                <div class="voucher-min">Min. belanja Rp100rb</div>
                            </div>
                            <div class="voucher-right">
                                <div>
                                    <div class="voucher-title">Diskon Fashion</div>
                                    <div class="voucher-desc">Diskon Rp20.000 khusus kategori Fashion</div>
                                </div>
                                <div class="voucher-info">
                                    <div class="voucher-date" data-expiry-date="2025-03-25">Berlaku hingga: 25 Mar 2025</div>
                                    <label class="circular-checkbox">
                                        <input type="checkbox" class="voucher-checkbox" data-value="20000" data-min="100000" data-group="discount" data-title="Diskon Fashion">
                                        <span class="checkmark"></span>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Cashback Vouchers Tab -->
            <div class="tab-content" id="tab-cashback">
                <div class="voucher-list">
                    <div class="voucher-group" data-group="cashback">
                        <div class="group-header">
                            <div class="group-title">Voucher Cashback</div>
                        </div>
                        
                        <div class="voucher-item cashback visible">
                            <div class="voucher-left">
                                <div class="voucher-amount">10%</div>
                                <div class="voucher-min">Maks. Rp35rb</div>
                            </div>
                            <div class="voucher-right">
                                <div>
                                    <div class="voucher-title">Diskon Elektronik</div>
                                    <div class="voucher-desc">Diskon 10% untuk kategori Elektronik</div>
                                </div>
                                <div class="voucher-info">
                                    <div class="voucher-date" data-expiry-date="2025-03-10">Berlaku hingga: 10 Mar 2025</div>
                                    <label class="circular-checkbox">
                                        <input type="checkbox" class="voucher-checkbox" data-value="35000" data-type="percentage" data-percentage="10" data-group="cashback" data-title="Diskon Elektronik 10%">
                                        <span class="checkmark"></span>
                                    </label>
                                </div>
                            </div>
                        </div>
                        
                        <div class="voucher-item cashback visible">
                            <div class="recommended-badge">Recommended</div>
                            <div class="voucher-left">
                                <div class="voucher-amount">15%</div>
                                <div class="voucher-min">Maks. Rp30rb</div>
                            </div>
                            <div class="voucher-right">
                                <div>
                                    <div class="voucher-title">Cashback Belanja</div>
                                    <div class="voucher-desc">Cashback 15% dalam bentuk koin Shopee</div>
                                </div>
                                <div class="voucher-info">
                                    <div class="voucher-date" data-expiry-date="2025-04-12">Berlaku hingga: 12 Apr 2025</div>
                                    <label class="circular-checkbox">
                                        <input type="checkbox" class="voucher-checkbox" data-value="30000" data-type="cashback" data-percentage="15" data-group="cashback" data-title="Cashback 15%">
                                        <span class="checkmark"></span>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
                
        <!-- Selected vouchers summary -->
        <div class="selected-vouchers-summary" id="selected-vouchers-summary" style="display: none;">
            <div id="selected-vouchers-list"></div>
        </div>
        
        <div class="toast" id="toast">Notification message</div>
    </div>
    
    <footer class="footer">
        <div class="footer-summary" id="footer-summary" style="display: none;"></div>
        <button class="apply-btn" id="apply-btn" disabled>Terapkan Voucher</button>
    </footer>
  <script>
        // Store all voucher data for search functionality
        const allVouchers = [
            {
                id: 'discount-1',
                title: 'Diskon Belanja',
                desc: 'Diskon Rp50.000 untuk semua kategori produk',
                value: '50000',
                min: '200000',
                group: 'discount',
                recommended: true
            },
            {
                id: 'discount-2',
                title: 'Diskon Fashion',
                desc: 'Diskon Rp20.000 khusus kategori Fashion',
                value: '20000',
                min: '100000',
                group: 'discount'
            },
            {
                id: 'shipping-1',
                title: 'Gratis Ongkir',
                desc: 'Gratis ongkir hingga Rp20.000 untuk semua toko',
                value: '20000',
                type: 'shipping',
                group: 'shipping',
                recommended: true
            },
            {
                id: 'shipping-2',
                title: 'Gratis Ongkir Reguler',
                desc: 'Gratis ongkir hingga Rp15.000 dengan pengiriman reguler',
                value: '15000',
                type: 'shipping',
                group: 'shipping'
            },
            {
                id: 'shipping-3',
                title: 'Gratis Ongkir Premium',
                desc: 'Gratis ongkir hingga Rp30.000 dengan min. belanja Rp150.000',
                value: '30000',
                type: 'shipping',
                min: '150000',
                group: 'shipping'
            },
            {
                id: 'shipping-4',
                title: 'Gratis Ongkir COD',
                desc: 'Gratis ongkir hingga Rp25.000 untuk metode pembayaran COD',
                value: '25000',
                type: 'shipping',
                group: 'shipping'
            },
            {
                id: 'cashback-1',
                title: 'Diskon Elektronik',
                desc: 'Diskon 10% untuk kategori Elektronik',
                value: '35000',
                type: 'percentage',
                percentage: '10',
                group: 'cashback'
            },
            {
                id: 'cashback-2',
                title: 'Cashback Belanja',
                desc: 'Cashback 15% dalam bentuk koin Shopee',
                value: '30000',
                type: 'cashback',
                percentage: '15',
                group: 'cashback',
                recommended: true
            },
            {
                id: 'payment-1',
                title: 'Diskon COD',
                desc: 'Diskon Rp10.000 untuk pembayaran COD min. Rp100.000',
                value: '10000',
                type: 'payment',
                method: 'cod',
                min: '100000',
                group: 'payment'
            },
            {
                id: 'payment-2',
                title: 'Diskon Transfer Bank',
                desc: 'Diskon 5% untuk pembayaran via Transfer Bank',
                value: '25000',
                type: 'payment',
                method: 'bank',
                percentage: '5',
                group: 'payment'
            }
        ];
        
        // Keyword suggestions for search
        const searchKeywords = [
            { keyword: "diskon", matches: ["discount", "diskon", "potongan", "potong harga"] },
            { keyword: "ongkir", matches: ["ongkir", "kirim", "shipping", "gratis ongkir", "pengiriman"] },
            { keyword: "cashback", matches: ["cashback", "koin", "coin", "kembalian"] },
            { keyword: "cod", matches: ["cod", "bayar ditempat", "cash on delivery", "bayar tunai"] },
            { keyword: "bank", matches: ["bank", "transfer", "e-wallet", "pembayaran"] },
            { keyword: "minimum", matches: ["min", "minimum", "minimal", "min belanja"] },
            { keyword: "fashion", matches: ["fashion", "pakaian", "baju", "celana"] },
            { keyword: "elektronik", matches: ["elektronik", "gadget", "device", "perangkat"] },
            { keyword: "rekomendasi", matches: ["recommended", "recommend", "rekomendasi", "terbaik"] }
        ];

        document.addEventListener('DOMContentLoaded', function() {
            // Tab navigation
            const tabs = document.querySelectorAll('.tab');
            const tabContents = document.querySelectorAll('.tab-content');
            
            tabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    const tabId = this.getAttribute('data-tab');
                    
                    // Remove active class from all tabs and contents
                    tabs.forEach(t => t.classList.remove('active'));
                    tabContents.forEach(c => c.classList.remove('active'));
                    
                    // Add active class to clicked tab and corresponding content
                    this.classList.add('active');
                    document.getElementById('tab-' + tabId).classList.add('active');
                });
            });
            
            // Back button
            const backButton = document.querySelector('.back-btn');
            
            backButton.addEventListener('click', function() {
                // In a real app, this would navigate back
                showToast('Kembali ke halaman sebelumnya');
            });
            
            // Voucher checkboxes - limit to 1 per category and 2 total
            const voucherCheckboxes = document.querySelectorAll('.voucher-checkbox');
            const applyButton = document.getElementById('apply-btn');
            const summaryContainer = document.getElementById('selected-vouchers-summary');
            const summaryList = document.getElementById('selected-vouchers-list');
            const footerSummary = document.getElementById('footer-summary');
            
            // Show more functionality for shipping vouchers
            const showMoreShipping = document.getElementById('show-more-shipping');
            const showMoreShippingTab = document.getElementById('show-more-shipping-tab');
            
            function toggleShippingVouchers(event, groupSelector) {
                const shippingVouchers = document.querySelectorAll(groupSelector + ' .voucher-item.shipping');
                const isExpanded = event.currentTarget.getAttribute('data-expanded') === 'true';
                
                if (!isExpanded) {
                    // Show all vouchers
                    shippingVouchers.forEach(item => {
                        item.classList.add('visible');
                    });
                    event.currentTarget.innerHTML = 'Sembunyikan <svg class="show-more-icon" xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="18 15 12 9 6 15"></polyline></svg>';
                    event.currentTarget.setAttribute('data-expanded', 'true');
                } else {
                    // Hide all except first voucher
                    shippingVouchers.forEach((item, index) => {
                        if (index > 0) {
                            item.classList.remove('visible');
                        }
                    });
                    event.currentTarget.innerHTML = 'Lihat Lainnya <svg class="show-more-icon" xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="6 9 12 15 18 9"></polyline></svg>';
                    event.currentTarget.setAttribute('data-expanded', 'false');
                }
            }
            
            if (showMoreShipping) {
                showMoreShipping.addEventListener('click', function(event) {
                    toggleShippingVouchers(event, '#tab-all .voucher-group[data-group="shipping"]');
                });
            }
            
            if (showMoreShippingTab) {
                showMoreShippingTab.addEventListener('click', function(event) {
                    toggleShippingVouchers(event, '#tab-shipping .voucher-group[data-group="shipping"]');
                });
            }
            
            // Fungsi untuk memeriksa dan memperbarui status voucher
            function updateVoucherStatus() {
                const voucherItems = document.querySelectorAll('.voucher-item');
                const today = new Date();
                
                // Simulasi total belanja (dalam kondisi nyata ini akan diambil dari keranjang)
                const currentSpend = 20000; // Contoh total belanja saat ini: Rp 200.000
                
                voucherItems.forEach(item => {
                    let isDisabled = false;
                    const checkbox = item.querySelector('.voucher-checkbox');
                    const dateDiv = item.querySelector('.voucher-date');
                    
                    // Cek tanggal kedaluwarsa
                    if (dateDiv) {
                        if (dateDiv.hasAttribute('data-expiry-date')) {
                            // Cara manual
                            const dateStr = dateDiv.getAttribute('data-expiry-date');
                            const expiryDate = new Date(dateStr);
                            
                            if (today > expiryDate) {
                                isDisabled = true;
                            }
                        }
                    }
                    
                    // Cek minimum belanja
                    if (checkbox && checkbox.hasAttribute('data-min')) {
                        const minSpend = parseInt(checkbox.getAttribute('data-min'));
                        if (minSpend > currentSpend) {
                            isDisabled = true;
                        }
                    }
                    
                    // Terapkan status disabled
                    if (isDisabled) {
                        item.classList.add('disabled');
                        if (checkbox) {
                            checkbox.disabled = true;
                        }
                    } else {
                        item.classList.remove('disabled');
                        if (checkbox) {
                            checkbox.disabled = false;
                        }
                    }
                });
            }
            
            // Set dynamic expiry dates
            function updateExpiryDates() {
                const dateDivs = document.querySelectorAll('.voucher-date');
                
                dateDivs.forEach(div => {
                    // Cek apakah menggunakan data-expiry-date (manual)
                    if (div.hasAttribute('data-expiry-date')) {
                        // Cara manual
                        const dateStr = div.getAttribute('data-expiry-date'); // Format: YYYY-MM-DD
                        const expiryDate = new Date(dateStr);
                        
                        const day = expiryDate.getDate();
                        const month = expiryDate.toLocaleString('id-ID', { month: 'short' });
                        const year = expiryDate.getFullYear();
                        
                        div.textContent = `Berlaku hingga: ${day} ${month} ${year}`;
                    }
                });
            }
            
            // Function to update apply button state and summary
            function updateApplyButtonState() {
                let checkedVouchers = [];
                
                voucherCheckboxes.forEach(checkbox => {
                    if (checkbox.checked) {
                        checkedVouchers.push({
                            title: checkbox.getAttribute('data-title'),
                            value: checkbox.getAttribute('data-value'),
                            group: checkbox.getAttribute('data-group')
                        });
                    }
                });
                
                // Enable/disable apply button
                applyButton.disabled = checkedVouchers.length === 0;
                
                // Update summary containers
                if (checkedVouchers.length > 0) {
                    // Update the main summary container
                    summaryContainer.style.display = 'block';
                    summaryList.innerHTML = '';
                    
                    // Update the footer summary
                    footerSummary.style.display = 'block';
                    footerSummary.innerHTML = '';
                    
                    checkedVouchers.forEach(voucher => {
                        // For main summary
                        const item = document.createElement('div');
                        item.className = 'selected-voucher-item';
                        
                        const nameSpan = document.createElement('span');
                        nameSpan.className = 'selected-voucher-name';
                        nameSpan.textContent = voucher.title;
                        
                        const valueSpan = document.createElement('span');
                        valueSpan.className = 'selected-voucher-value';
                        valueSpan.textContent = formatCurrency(voucher.value);
                        
                        item.appendChild(nameSpan);
                        item.appendChild(valueSpan);
                        summaryList.appendChild(item);
                        
                        // For footer summary
                        const footerItem = document.createElement('div');
                        footerItem.className = 'selected-voucher-item';
                        
                        const footerNameSpan = document.createElement('span');
                        footerNameSpan.className = 'selected-voucher-name';
                        footerNameSpan.textContent = voucher.title;
                        
                        const footerValueSpan = document.createElement('span');
                        footerValueSpan.className = 'selected-voucher-value';
                        footerValueSpan.textContent = formatCurrency(voucher.value);
                        
                        footerItem.appendChild(footerNameSpan);
                        footerItem.appendChild(footerValueSpan);
                        footerSummary.appendChild(footerItem);
                    });
                } else {
                    summaryContainer.style.display = 'none';
                    footerSummary.style.display = 'none';
                }
            }
            
            // Format currency
            function formatCurrency(value) {
                return 'Rp' + parseInt(value).toLocaleString('id-ID');
            }
            
            // Toast notification
            const toast = document.getElementById('toast');
            
            function showToast(message) {toast.textContent = message;
                toast.style.display = 'block';
                
                setTimeout(() => {
                    toast.style.display = 'none';
                }, 3000);
            }
            
            // Manage checkbox selection
            function manageCheckboxSelection(checkbox) {
                if (checkbox.checked) {
                    const currentGroup = checkbox.getAttribute('data-group');
                    const allChecked = Array.from(voucherCheckboxes).filter(cb => cb.checked);
                    
                    // If more than 2 vouchers selected, uncheck the previously checked one
                    if (allChecked.length > 2) {
                        for (let i = 0; i < allChecked.length; i++) {
                            if (allChecked[i] !== checkbox) {
                                allChecked[i].checked = false;
                                break;
                            }
                        }
                    }
                    
                    const sameGroupChecked = Array.from(voucherCheckboxes).filter(
                        cb => cb.checked && cb !== checkbox && cb.getAttribute('data-group') === currentGroup
                    );
                    
                    if (sameGroupChecked.length > 0) {
                        sameGroupChecked[0].checked = false;
                    }
                }
                
                updateApplyButtonState();
                updateVoucherStatus();
            }
            
            // Add event listeners to checkboxes
            voucherCheckboxes.forEach(checkbox => {
                checkbox.addEventListener('change', function() {
                    manageCheckboxSelection(this);
                });
            });
            
            // Apply button click handler
            applyButton.addEventListener('click', function() {
                // Collect selected vouchers
                const selectedVouchers = [];
                
                voucherCheckboxes.forEach(checkbox => {
                    if (checkbox.checked) {
                        const voucherValue = checkbox.getAttribute('data-value');
                        const voucherType = checkbox.getAttribute('data-type') || 'discount';
                        const voucherMin = checkbox.getAttribute('data-min') || '0';
                        const voucherPercentage = checkbox.getAttribute('data-percentage') || null;
                        const voucherMethod = checkbox.getAttribute('data-method') || null;
                        const voucherTitle = checkbox.getAttribute('data-title') || '';
                        
                        selectedVouchers.push({
                            title: voucherTitle,
                            value: voucherValue,
                            type: voucherType,
                            min: voucherMin,
                            percentage: voucherPercentage,
                            method: voucherMethod
                        });
                    }
                });
                
                if (selectedVouchers.length > 0) {
                    // In a real app, you would pass these vouchers back to the cart
                    showToast('Voucher berhasil diterapkan!');
                    
                    // Simulate order processing
                    setTimeout(() => {
                        showToast('Mengalihkan ke halaman checkout...');
                    }, 2000);
                }
            });
            
            // Search functionality with suggestions
            const searchInput = document.getElementById('search-input');
            const searchSuggestions = document.getElementById('search-suggestions');
            const clearIcon = document.querySelector('.clear-icon');
            const searchIcon = document.querySelector('.search-icon');
            const noResultsCard = document.getElementById('no-results-card');
            
            // Function to toggle search icon and clear icon based on input content
            function toggleSearchIcons() {
                const hasText = searchInput.value.length > 0;
                
                // Toggle clear icon visibility
                clearIcon.style.display = hasText ? 'block' : 'none';
                
                // Toggle search icon color
                if (hasText) {
                    searchIcon.classList.add('active');
                } else {
                    searchIcon.classList.remove('active');
                }
            }
            
            // Function to clear search input
            function clearSearchInput() {
                searchInput.value = '';
                toggleSearchIcons();
                searchSuggestions.style.display = 'none';
                noResultsCard.style.display = 'none';
            }
            
            // Add click event for clear icon
            clearIcon.addEventListener('click', clearSearchInput);
            
            // Add click event for search icon
            searchIcon.addEventListener('click', function() {
                const searchTerm = searchInput.value.toLowerCase().trim();
                
                if (searchTerm.length > 0) {
                    // Perform search
                    const matchedVouchers = allVouchers.filter(voucher => 
                        voucher.title.toLowerCase().includes(searchTerm) || 
                        voucher.desc.toLowerCase().includes(searchTerm)
                    );
                    
                    // Check if there are any matches
                    if (matchedVouchers.length === 0) {
                        // Show no results card
                        noResultsCard.style.display = 'block';
                        searchSuggestions.style.display = 'none';
                    } else {
                        // Hide no results card and close suggestions
                        noResultsCard.style.display = 'none';
                        searchSuggestions.style.display = 'none';
                        
                        // Highlight the first match
                        const firstVoucher = matchedVouchers[0];
                        const checkbox = findCheckboxById(firstVoucher.id);
                        if (checkbox) {
                            checkbox.checked = true;
                            manageCheckboxSelection(checkbox);
                        }
                    }
                }
            });
            
            // Function to display initial keyword suggestions on focus
            function showInitialSuggestions() {
                searchSuggestions.innerHTML = '';
                searchSuggestions.style.display = 'block';
                noResultsCard.style.display = 'none';
                
                // Add popular keywords suggestions
                const popularKeywords = ['diskon', 'ongkir', 'cashback', 'rekomendasi'];
                
                // Add a heading for suggestions
                const heading = document.createElement('div');
                heading.style.padding = '8px 16px';
                heading.style.fontSize = '12px';
                heading.style.color = '#757575';
                heading.style.borderBottom = '1px solid #f0f0f0';
                heading.textContent = 'Kata Kunci Populer';
                searchSuggestions.appendChild(heading);
                
                popularKeywords.forEach(keyword => {
                    const keywordData = searchKeywords.find(k => k.keyword === keyword);
                    if (keywordData) {
                        const suggestionItem = document.createElement('div');
                        suggestionItem.className = 'suggestion-item';
                        
                        const keywordSpan = document.createElement('div');
                        keywordSpan.className = 'suggestion-keyword';
                        keywordSpan.textContent = `"${keywordData.keyword}"`;
                        
                        suggestionItem.appendChild(keywordSpan);
                        searchSuggestions.appendChild(suggestionItem);
                        
                        suggestionItem.addEventListener('click', function() {
                            searchInput.value = keywordData.keyword;
                            toggleSearchIcons();
                            searchSuggestions.style.display = 'none';
                            
                            // Find vouchers matching this keyword and highlight the best match
                            const relevantVouchers = findRelevantVouchers(keywordData.keyword);
                            if (relevantVouchers.length > 0) {
                                const firstVoucher = relevantVouchers[0];
                                const checkbox = findCheckboxById(firstVoucher.id);
                                if (checkbox) {
                                    checkbox.checked = true;
                                    manageCheckboxSelection(checkbox);
                                }
                            }
                        });
                    }
                });
                
                // Add some recommended vouchers
                const recommendedHeading = document.createElement('div');
                recommendedHeading.style.padding = '8px 16px';
                recommendedHeading.style.fontSize = '12px';
                recommendedHeading.style.color = '#757575';
                recommendedHeading.style.borderBottom = '1px solid #f0f0f0';
                recommendedHeading.style.borderTop = '1px solid #f0f0f0';
                recommendedHeading.style.marginTop = '8px';
                recommendedHeading.textContent = 'Voucher Rekomendasi';
                searchSuggestions.appendChild(recommendedHeading);
                
                // Get recommended vouchers
                const recommendedVouchers = allVouchers.filter(v => v.recommended).slice(0, 2);
                
                recommendedVouchers.forEach(voucher => {
                    const suggestionItem = document.createElement('div');
                    suggestionItem.className = 'suggestion-item';
                    suggestionItem.setAttribute('data-id', voucher.id);
                    
                    const titleDiv = document.createElement('div');
                    titleDiv.className = 'suggestion-title';
                    titleDiv.textContent = voucher.title;
                    
                    const descDiv = document.createElement('div');
                    descDiv.className = 'suggestion-desc';
                    descDiv.textContent = voucher.desc;
                    
                    suggestionItem.appendChild(titleDiv);
                    suggestionItem.appendChild(descDiv);
                    searchSuggestions.appendChild(suggestionItem);
                    
                    // Add click event
                    suggestionItem.addEventListener('click', function() {
                        // Find related checkbox and check it
                        const relevantCheckbox = findCheckboxById(this.getAttribute('data-id'));
                        if (relevantCheckbox) {
                            relevantCheckbox.checked = true;
                            manageCheckboxSelection(relevantCheckbox);
                        }
                        
                        // Update search input with voucher title
                        searchInput.value = voucher.title;
                        toggleSearchIcons();
                        
                        // Hide suggestions
                        searchSuggestions.style.display = 'none';
                    });
                });
            }
            
            // Show initial suggestions when search input is focused
            if (searchInput) {
                searchInput.addEventListener('focus', showInitialSuggestions);
                
                // Update search icons when input changes
                searchInput.addEventListener('input', function() {
                    toggleSearchIcons();
                    
                    const searchTerm = this.value.toLowerCase().trim();
                    
                    if (searchTerm.length > 1) {
                        // Reset suggestions
                        searchSuggestions.innerHTML = '';
                        let hasMatches = false;
                        
                        // Hide no results card - only show it when search icon is clicked
                        noResultsCard.style.display = 'none';
                        
                        // Find matching keywords first
                        const matchingKeywords = searchKeywords.filter(item => 
                            item.matches.some(match => match.includes(searchTerm))
                        );
                        
                        // Add keyword suggestions
                        if (matchingKeywords.length > 0) {
                            hasMatches = true;
                            matchingKeywords.forEach(item => {
                                const suggestionItem = document.createElement('div');
                                suggestionItem.className = 'suggestion-item';
                                
                                const keywordSpan = document.createElement('div');
                                keywordSpan.className = 'suggestion-keyword';
                                keywordSpan.textContent = `"${item.keyword}"`;
                                
                                suggestionItem.appendChild(keywordSpan);
                                searchSuggestions.appendChild(suggestionItem);
                                
                                suggestionItem.addEventListener('click', function() {
                                    searchInput.value = item.keyword;
                                    toggleSearchIcons();
                                    searchSuggestions.style.display = 'none';
                                    
                                    // Find vouchers matching this keyword and highlight the best match
                                    const relevantVouchers = findRelevantVouchers(item.keyword);
                                    if (relevantVouchers.length > 0) {
                                        const firstVoucher = relevantVouchers[0];
                                        const checkbox = findCheckboxById(firstVoucher.id);
                                        if (checkbox) {
                                            checkbox.checked = true;
                                            manageCheckboxSelection(checkbox);
                                        }
                                    }
                                });
                            });
                        }
                        
                        // Find matching vouchers
                        const matchingVouchers = allVouchers.filter(voucher => 
                            voucher.title.toLowerCase().includes(searchTerm) || 
                            voucher.desc.toLowerCase().includes(searchTerm)
                        );
                        
                        // Add voucher suggestions
                        if (matchingVouchers.length > 0) {
                            hasMatches = true;
                            matchingVouchers.forEach(voucher => {
                                const suggestionItem = document.createElement('div');
                                suggestionItem.className = 'suggestion-item';
                                suggestionItem.setAttribute('data-id', voucher.id);
                                
                                const titleDiv = document.createElement('div');
                                titleDiv.className = 'suggestion-title';
                                titleDiv.textContent = voucher.title;
                                
                                const descDiv = document.createElement('div');
                                descDiv.className = 'suggestion-desc';
                                descDiv.textContent = voucher.desc;
                                
                                suggestionItem.appendChild(titleDiv);
                                suggestionItem.appendChild(descDiv);
                                searchSuggestions.appendChild(suggestionItem);
                                
                                // Add click event
                                suggestionItem.addEventListener('click', function() {
                                    // Find related checkbox and check it
                                    const relevantCheckbox = findCheckboxById(this.getAttribute('data-id'));
                                    if (relevantCheckbox) {
                                        relevantCheckbox.checked = true;
                                        manageCheckboxSelection(relevantCheckbox);
                                    }
                                    
                                    // Update search input with voucher title
                                    searchInput.value = voucher.title;
                                    toggleSearchIcons();
                                    
                                    // Hide suggestions
                                    searchSuggestions.style.display = 'none';
                                });
                            });
                        }
                        
                        // Display or hide suggestions
                        if (hasMatches) {
                            searchSuggestions.style.display = 'block';
                        } else {
                            searchSuggestions.style.display = 'none';
                        }
                    } else if (searchTerm.length === 0) {
                        showInitialSuggestions();
                        noResultsCard.style.display = 'none';
                    } else {
                        searchSuggestions.style.display = 'none';
                        noResultsCard.style.display = 'none';
                    }
                });
                
                // Handle enter key in search input
                searchInput.addEventListener('keyup', function(event) {
                    if (event.key === 'Enter') {
                        searchIcon.click();
                    }
                });
            }
            
            // Find vouchers that are relevant to a keyword
            function findRelevantVouchers(keyword) {
                keyword = keyword.toLowerCase();
                
                // Map keywords to voucher types
                const keywordMapping = {
                    'diskon': allVouchers.filter(v => v.group === 'discount'),
                    'ongkir': allVouchers.filter(v => v.group === 'shipping'),
                    'cashback': allVouchers.filter(v => v.group === 'cashback'),
                    'cod': allVouchers.filter(v => v.type === 'payment' && v.method === 'cod'),
                    'bank': allVouchers.filter(v => v.type === 'payment' && v.method === 'bank'),
                    'minimum': allVouchers.sort((a, b) => parseInt(a.min || 0) - parseInt(b.min || 0)),
                    'fashion': allVouchers.filter(v => v.title.toLowerCase().includes('fashion') || v.desc.toLowerCase().includes('fashion')),
                    'elektronik': allVouchers.filter(v => v.title.toLowerCase().includes('elektronik') || v.desc.toLowerCase().includes('elektronik')),
                    'rekomendasi': allVouchers.filter(v => v.recommended)
                };
                
                // Return relevant vouchers
                return keywordMapping[keyword] || [];
            }
            
            // Helper to find checkbox by voucher ID
            function findCheckboxById(voucherId) {
                // Map voucher IDs to DOM elements
                const voucherIdMap = {
                    'discount-1': document.querySelector('.voucher-checkbox[data-title="Diskon Belanja"]'),
                    'discount-2': document.querySelector('.voucher-checkbox[data-title="Diskon Fashion"]'),
                    'shipping-1': document.querySelector('.voucher-checkbox[data-title="Gratis Ongkir Rp20rb"]'),
                    'shipping-2': document.querySelector('.voucher-checkbox[data-title="Gratis Ongkir Reguler"]'),
                    'shipping-3': document.querySelector('.voucher-checkbox[data-title="Gratis Ongkir Premium"]'),
                    'shipping-4': document.querySelector('.voucher-checkbox[data-title="Gratis Ongkir COD"]'),
                    'cashback-1': document.querySelector('.voucher-checkbox[data-title="Diskon Elektronik 10%"]'),
                    'cashback-2': document.querySelector('.voucher-checkbox[data-title="Cashback 15%"]'),
                    'payment-1': document.querySelector('.voucher-checkbox[data-title="Diskon COD"]'),
                    'payment-2': document.querySelector('.voucher-checkbox[data-title="Diskon Transfer 5%"]')
                };
                
                return voucherIdMap[voucherId];
            }
            
            // Close suggestions when clicking outside
            document.addEventListener('click', function(event) {
                if (!event.target.closest('#search-input') && 
                    !event.target.closest('#search-suggestions') && 
                    !event.target.closest('.clear-icon') && 
                    !event.target.closest('.search-icon')) {
                    searchSuggestions.style.display = 'none';
                }
            });
            
            // Initialize 
            updateExpiryDates();
            updateVoucherStatus();
            updateApplyButtonState();
            toggleSearchIcons();
        });
    </script>
</body>
</html>
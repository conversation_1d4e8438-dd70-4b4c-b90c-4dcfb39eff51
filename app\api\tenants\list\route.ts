import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

function getServiceClient() {
  return createClient(supabaseUrl, supabaseServiceKey)
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const limit = searchParams.get('limit')
    const orderBy = searchParams.get('orderBy') || 'created_at'
    const orderDirection = searchParams.get('orderDirection') || 'desc'

    const supabase = getServiceClient()

    let query = supabase
      .from('tenants')
      .select('id, name, domain, subdomain, status, plan, store_count, user_count, revenue, created_at')
      .order(orderBy, { ascending: orderDirection === 'asc' })

    if (limit) {
      query = query.limit(parseInt(limit))
    }

    const { data, error } = await query

    if (error) {
      console.error('Error fetching tenants:', error)
      return NextResponse.json(
        { success: false, error: error.message },
        { status: 500 }
      )
    }

    // Transform data to match expected format
    const transformedData = (data || []).map(tenant => ({
      id: tenant.id,
      name: tenant.name,
      slug: tenant.subdomain, // Use subdomain as slug
      domain: tenant.domain,
      status: tenant.status || 'active',
      plan: tenant.plan || 'free',
      storeCount: tenant.store_count || 0,
      userCount: tenant.user_count || 0,
      revenue: tenant.revenue ? `$${parseFloat(tenant.revenue).toLocaleString()}` : '$0',
      subscriptionPlan: tenant.plan || 'free', // For backward compatibility
      subscriptionStatus: tenant.status || 'active', // For backward compatibility
      createdAt: tenant.created_at
    }))

    return NextResponse.json({
      success: true,
      data: transformedData,
      count: transformedData.length
    })

  } catch (error) {
    console.error('Error in list tenants API:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}

"use client"

import React from 'react'
import { ArrowLeft } from 'lucide-react'

interface SellzioCheckoutHeaderProps {
  onBack: () => void
}

export const SellzioCheckoutHeader: React.FC<SellzioCheckoutHeaderProps> = ({
  onBack
}) => {
  return (
    <header className="sellzio-checkout-header">
      <div className="sellzio-checkout-header-inner">
        <button
          className="sellzio-checkout-back-btn"
          onClick={onBack}
        >
          <ArrowLeft size={20} />
        </button>
        <h1 className="sellzio-checkout-title">Checkout</h1>
      </div>
    </header>
  )
}

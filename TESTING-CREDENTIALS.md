# Testing Credentials - SaaS Multi-Tenant System dengan Multi-Role

## 🔐 Login Credentials untuk Testing

### **Admin (Super Admin)**
- **URL**: `http://localhost:3000/admin/login`
- **Email**: `<EMAIL>`
- **Password**: `admin123`
- **Akses**: Semua data di platform

---

## **TENANT 1** (Test Store 1)
**Tenant ID**: `96111d56-b82d-43e7-9926-8f0530dc6063`

### 1. **Tenant Owner**
- **URL**: `http://localhost:3000/app/login`
- **Email**: `<EMAIL>`
- **Password**: `user123`
- **Akses**: Semua store dan buyer dalam Tenant 1

### 2. **Store Owners (Store Only)**

#### Store 1 Owner
- **URL**: `http://localhost:3000/app/login`
- **Email**: `<EMAIL>`
- **Password**: `user123`
- **Store**: nama5 (ID: 179700a1-d6c1-40eb-9fd1-317754ba3c57)
- **Role**: Store Owner Only
- **Akses**: Data store sendiri + buyer dalam Tenant 1

#### Store 2 Owner
- **URL**: `http://localhost:3000/app/login`
- **Email**: `<EMAIL>`
- **Password**: `user123`
- **Store**: tes (ID: a70cae23-39af-4ff5-b1d8-b227a28585f8)
- **Role**: Store Owner Only
- **Akses**: Data store sendiri + buyer dalam Tenant 1

### 3. **Buyers (Buyer Only)**

#### Buyer 1 (Bronze Level)
- **URL**: `http://localhost:3000/app/login`
- **Email**: `<EMAIL>`
- **Password**: `user123`
- **Role**: Buyer Only
- **Level**: Bronze
- **Akses**: Data pribadi + produk dari semua store dalam Tenant 1

#### Buyer 2 (Bronze Level)
- **URL**: `http://localhost:3000/app/login`
- **Email**: `<EMAIL>`
- **Password**: `user123`
- **Role**: Buyer Only
- **Level**: Bronze
- **Akses**: Data pribadi + produk dari semua store dalam Tenant 1

### 4. **Multi-Role Users dalam Tenant 1**

#### Buyer + Store Owner (Silver Level)
- **URL**: `http://localhost:3000/app/login`
- **Email**: `<EMAIL>`
- **Password**: `user123`
- **Role**: Buyer + Store Owner
- **Store**: nama5 (shared ownership)
- **Buyer Level**: Silver (5 orders, Rp 1.5M spent)
- **Akses**: Bisa berbelanja + manage store + lihat buyers dalam tenant

#### Buyer + Affiliate (Gold Level)
- **URL**: `http://localhost:3000/app/login`
- **Email**: `<EMAIL>`
- **Password**: `user123`
- **Role**: Buyer + Affiliate
- **Buyer Level**: Gold (10 orders, Rp 3M spent)
- **Affiliate**: Code AFF1T1, 7.5% commission, Rp 150K earned
- **Akses**: Bisa berbelanja + earn commission dari referral

#### Buyer + Store + Affiliate (Platinum Level)
- **URL**: `http://localhost:3000/app/login`
- **Email**: `<EMAIL>`
- **Password**: `user123`
- **Role**: Buyer + Store Owner + Affiliate
- **Store**: tes (shared ownership)
- **Buyer Level**: Platinum (20 orders, Rp 5M spent)
- **Affiliate**: Code AFFALL1, 10% commission, Rp 500K earned
- **Akses**: Full access - berbelanja + manage store + earn commission

---

## **TENANT 2** (Test Store 2)
**Tenant ID**: `9bb06506-e2a9-4f8d-876b-a1a10ef5f240`

### 1. **Tenant Owner**
- **URL**: `http://localhost:3000/app/login`
- **Email**: `<EMAIL>`
- **Password**: `user123`
- **Akses**: Semua store dan buyer dalam Tenant 2

### 2. **Store Owners (Store Only)**

#### Store 3 Owner
- **URL**: `http://localhost:3000/app/login`
- **Email**: `<EMAIL>`
- **Password**: `user123`
- **Store**: nama3 (ID: 62a64062-f017-4cb7-a608-575cab102560)
- **Role**: Store Owner Only
- **Akses**: Data store sendiri + buyer dalam Tenant 2

#### Store 4 Owner
- **URL**: `http://localhost:3000/app/login`
- **Email**: `<EMAIL>`
- **Password**: `user123`
- **Store**: nama6 (ID: 07f4637f-d360-4f29-9b83-d326e60f6941)
- **Role**: Store Owner Only
- **Akses**: Data store sendiri + buyer dalam Tenant 2

### 3. **Buyers (Buyer Only)**

#### Buyer 3 (Bronze Level)
- **URL**: `http://localhost:3000/app/login`
- **Email**: `<EMAIL>`
- **Password**: `user123`
- **Role**: Buyer Only
- **Level**: Bronze
- **Akses**: Data pribadi + produk dari semua store dalam Tenant 2

#### Buyer 4 (Bronze Level)
- **URL**: `http://localhost:3000/app/login`
- **Email**: `<EMAIL>`
- **Password**: `user123`
- **Role**: Buyer Only
- **Level**: Bronze
- **Akses**: Data pribadi + produk dari semua store dalam Tenant 2

### 4. **Multi-Role Users dalam Tenant 2**

#### Buyer + Store Owner (Silver Level)
- **URL**: `http://localhost:3000/app/login`
- **Email**: `<EMAIL>`
- **Password**: `user123`
- **Role**: Buyer + Store Owner
- **Store**: nama3 (shared ownership)
- **Buyer Level**: Silver (3 orders, Rp 800K spent)
- **Akses**: Bisa berbelanja + manage store + lihat buyers dalam tenant

#### Buyer + Affiliate (Gold Level)
- **URL**: `http://localhost:3000/app/login`
- **Email**: `<EMAIL>`
- **Password**: `user123`
- **Role**: Buyer + Affiliate
- **Buyer Level**: Gold (8 orders, Rp 2.5M spent)
- **Affiliate**: Code AFF1T2, 7.5% commission, Rp 120K earned
- **Akses**: Bisa berbelanja + earn commission dari referral

#### Buyer + Store + Affiliate (Platinum Level)
- **URL**: `http://localhost:3000/app/login`
- **Email**: `<EMAIL>`
- **Password**: `user123`
- **Role**: Buyer + Store Owner + Affiliate
- **Store**: nama6 (shared ownership)
- **Buyer Level**: Platinum (15 orders, Rp 4M spent)
- **Affiliate**: Code AFFALL2, 10% commission, Rp 400K earned
- **Akses**: Full access - berbelanja + manage store + earn commission

---

## 🧪 **Skenario Testing Data Isolation & Multi-Role**

### **Test 1: Tenant Isolation**
1. **Login sebagai Tenant 1** (`<EMAIL>`)
   - Buka `/tenant/dashboard/stores`
   - **Harus melihat**: 4 stores (nama5, tes + 2 shared stores)
   - **Tidak boleh melihat**: Store dari Tenant 2
   - Buka `/tenant/dashboard/buyers`
   - **Harus melihat**: 6 buyers dengan berbagai kombinasi role
   - **Tidak boleh melihat**: Buyers dari Tenant 2

2. **Login sebagai Tenant 2** (`<EMAIL>`)
   - Buka `/tenant/dashboard/stores`
   - **Harus melihat**: 4 stores (nama3, nama6 + 2 shared stores)
   - **Tidak boleh melihat**: Store dari Tenant 1
   - Buka `/tenant/dashboard/buyers`
   - **Harus melihat**: 6 buyers dengan berbagai kombinasi role
   - **Tidak boleh melihat**: Buyers dari Tenant 1

### **Test 2: Store Owner Isolation**
1. **Login sebagai Store 1 Owner** (`<EMAIL>`)
   - Buka `/store/dashboard/buyers`
   - **Harus melihat**: Semua buyers dalam Tenant 1 (6 users)
   - **Tidak boleh melihat**: Buyers dari Tenant 2

2. **Login sebagai Store 3 Owner** (`<EMAIL>`)
   - Buka `/store/dashboard/buyers`
   - **Harus melihat**: Semua buyers dalam Tenant 2 (6 users)
   - **Tidak boleh melihat**: Buyers dari Tenant 1

### **Test 3: Multi-Role Access**
1. **Login sebagai Buyer+Store** (`<EMAIL>`)
   - **Dashboard**: Harus bisa akses buyer dan store dashboard
   - **Store Management**: Bisa manage store "nama5"
   - **Shopping**: Bisa berbelanja dari store lain dalam tenant
   - **Buyers View**: Bisa lihat potential customers dalam tenant

2. **Login sebagai Buyer+Affiliate** (`<EMAIL>`)
   - **Dashboard**: Harus bisa akses buyer dan affiliate dashboard
   - **Shopping**: Bisa berbelanja dengan buyer level Gold
   - **Affiliate**: Bisa lihat commission dan referral data
   - **Referral**: Bisa generate referral links

3. **Login sebagai Buyer+Store+Affiliate** (`<EMAIL>`)
   - **Full Access**: Bisa akses semua dashboard (buyer, store, affiliate)
   - **Store Management**: Bisa manage store "tes"
   - **Shopping**: Bisa berbelanja dengan buyer level Platinum
   - **Affiliate**: Bisa earn commission dari referral
   - **Analytics**: Bisa lihat gabungan analytics dari semua role

### **Test 4: Role-Based Data Filtering**
1. **API Testing**:
   - `/api/tenant/buyers?tenantId=96111d56...&role=buyer-only` → 2 users
   - `/api/tenant/buyers?tenantId=96111d56...&role=buyer-store` → 1 user
   - `/api/tenant/buyers?tenantId=96111d56...&role=buyer-affiliate` → 1 user
   - `/api/tenant/buyers?tenantId=96111d56...&role=buyer-store-affiliate` → 1 user

2. **Dashboard Filtering**:
   - Tenant dashboard harus bisa filter buyers berdasarkan kombinasi role
   - Store dashboard harus show buyer capabilities (store owner, affiliate, etc)
   - Analytics harus aggregate data berdasarkan multi-role

---

## 📊 **Expected Results**

### **Tenant Dashboard**
- **Tenant 1**: Melihat 2 stores, 2 buyers
- **Tenant 2**: Melihat 2 stores, 2 buyers
- **Cross-tenant data**: TIDAK TERLIHAT

### **Store Dashboard**
- **Store dalam Tenant 1**: Melihat 2 buyers dari Tenant 1
- **Store dalam Tenant 2**: Melihat 2 buyers dari Tenant 2
- **Cross-tenant buyers**: TIDAK TERLIHAT
- **Data store lain**: TIDAK TERLIHAT

### **API Endpoints**
- `/api/tenant/stores?tenantId=96111d56...` → 2 stores (Tenant 1)
- `/api/tenant/stores?tenantId=9bb06506...` → 2 stores (Tenant 2)
- `/api/tenant/users?tenantId=96111d56...&role=USER` → 2 buyers (Tenant 1)
- `/api/tenant/users?tenantId=9bb06506...&role=USER` → 2 buyers (Tenant 2)

---

## ✅ **Validation Checklist**

- [ ] Tenant 1 hanya melihat data dalam tenant mereka
- [ ] Tenant 2 hanya melihat data dalam tenant mereka
- [ ] Store owners hanya melihat buyers dalam tenant yang sama
- [ ] Store owners tidak melihat data store lain
- [ ] Buyers hanya melihat produk dalam tenant mereka
- [ ] API endpoints mengembalikan data yang benar berdasarkan tenant
- [ ] Tidak ada data cross-tenant yang bocor
- [ ] Dashboard menampilkan jumlah data yang benar

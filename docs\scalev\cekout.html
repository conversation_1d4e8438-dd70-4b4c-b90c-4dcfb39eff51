<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Checkout - E-Commerce</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: '<PERSON><PERSON>', <PERSON>l, sans-serif;
        }

        body {
            background-color: #f5f5f5;
            color: #333;
            line-height: 1.6;
        }

        .header {
            position: sticky;
            top: 0;
            width: 100%;
            background-color: #fff;
            padding: 15px 0;
            text-align: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            z-index: 100;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        @media (min-width: 800px) {
            .header-container {
                max-width: 800px;
                margin: 0 auto;
                position: relative;
                width: 100%;
            }
        }

        .header h1 {
            font-size: 1.2rem;
            font-weight: bold;
        }

        .back-btn {
            position: absolute;
            left: 15px;
            border: none;
            background: none;
            font-size: 1.2rem;
            cursor: pointer;
        }

        .container {
            max-width: 800px;
            margin: 3px auto 20px;
            padding: 15px;
        }

        .section {
            background-color: #fff;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 8px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .section-title {
            font-size: 1rem;
            font-weight: bold;
            margin-bottom: 10px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .edit-btn {
            color: #ee4d2d;
            font-size: 0.9rem;
            font-weight: normal;
            background: none;
            border: none;
            cursor: pointer;
        }

        /* Alamat Pengiriman */
        .address-info {
            margin-bottom: 6px;
            font-size: 14px;
        }

        .address-info .name {
            font-weight: bold;
        }

        .address-info .phone {
            margin-left: 10px;
            color: #666;
        }

        .address-info .address {
            color: #666;
            margin-top: 4px;
        }

        /* Produk */
        .product-container {
            margin-bottom: 10px;
        }

        .product-item {
            display: flex;
            padding: 10px 0;
            border-bottom: 1px solid #f0f0f0;
            flex-direction: column;
        }

        .product-item:last-child {
            border-bottom: none;
        }

        .product-image {
            width: 80px;
            height: 80px;
            border-radius: 4px;
            object-fit: cover;
            margin-right: 15px;
        }

        .product-details {
            flex: 1;
        }

        .product-content {
            display: flex;
            margin-top: 5px;
        }

        .product-name {
            font-weight: bold;
            font-size: 16px;
            margin-bottom: 5px;
        }

        .product-variant {
            font-size: 0.8rem;
            color: #666;
            margin-bottom: 5px;
        }

        .product-actions {
            display: flex;
            justify-content: flex-start;
            align-items: center;
            margin-top: 10px;
        }

        .quantity-display {
            font-size: 0.9rem;
            color: #666;
            margin-left: auto;
        }

        .quantity {
            font-weight: bold;
            color: #333;
        }

        .product-price {
            color: #ee4d2d;
            font-weight: bold;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .price-original {
            color: #999;
            text-decoration: line-through;
            font-weight: normal;
            font-size: 0.85em;
        }

        .shipping-tag {
            display: inline-block;
            background-color: #ffeee6;
            color: #ee4d2d;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 0.7rem;
            margin-top: 5px;
        }

        /* Pengiriman */
        .shipping-option {
            display: flex;
            flex-direction: column;
            cursor: pointer;
        }

        .shipping-name {
            font-weight: bold;
        }

        .shipping-estimate {
            font-size: 0.8rem;
            color: #666;
        }

        /* Pembayaran - Clean styles */

        /* Voucher */
        .voucher-option {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            cursor: pointer;
        }

        .voucher-label {
            display: flex;
            align-items: center;
        }

        .voucher-icon {
            color: #ee4d2d;
            margin-right: 10px;
            font-size: 1.2rem;
        }

        .voucher-value {
            color: #ee4d2d;
        }

        /* Total Pembayaran */
        .cost-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
        }

        .total-cost {
            display: flex;
            justify-content: space-between;
            font-weight: bold;
            font-size: 1.1rem;
            margin-top: 10px;
            padding-top: 10px;
            border-top: 1px solid #f0f0f0;
        }

        .total-price {
            color: #ee4d2d;
        }

        /* Footer */
        .footer {
            position: sticky;
            bottom: 0;
            width: 100%;
            background-color: #fff;
            padding: 15px;
            box-shadow: 0 -2px 4px rgba(0,0,0,0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 15px;
            border-radius: 8px;
            z-index: 50;
        }

        .order-total {
            font-size: 1.1rem;
            font-weight: bold;
            color: #ee4d2d;
        }

        .order-btn {
            background-color: #ee4d2d;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 4px;
            font-weight: bold;
            cursor: pointer;
            transition: background-color 0.3s;
        }

        .order-btn:hover {
            background-color: #d63c1e;
        }

        .order-btn:disabled {
            background-color: #aaa;
            cursor: not-allowed;
        }

        /* Toast Notification */
        .toast {
            position: fixed;
            top: 70px;
            left: 50%;
            transform: translateX(-50%);
            background-color: rgba(0,0,0,0.7);
            color: white;
            padding: 10px 20px;
            border-radius: 4px;
            z-index: 1000;
            display: none;
        }

        /* Scrollbar styling */
        .product-container::-webkit-scrollbar {
            width: 6px;
        }

        .product-container::-webkit-scrollbar-track {
            background: #f1f1f1;
        }

        .product-container::-webkit-scrollbar-thumb {
            background: #ddd;
            border-radius: 4px;
        }

        .product-container::-webkit-scrollbar-thumb:hover {
            background: #ccc;
        }

        /* Responsive */
        @media (max-width: 480px) {
            .container {
                padding: 10px;
            }

            .section {
                padding: 12px;
            }

            .product-image {
                width: 60px;
                height: 60px;
            }

            .footer {
                padding: 10px;
            }

            .order-btn {
                padding: 10px 20px;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="header-container" style="max-width: 800px; width: 100%; padding: 0 15px; margin: 0 auto;">
            <button class="back-btn">←</button>
            <h1>Checkout</h1>
        </div>
    </header>

    <div class="container">
        <!-- Alamat Pengiriman -->
        <div class="section">
            <div class="section-title">
                <span>Alamat Pengiriman</span>
                <button class="edit-btn">Ubah</button>
            </div>
            <div class="address-info">
                <span class="name">Budi Santoso</span>
                <span class="phone">0812-3456-7890</span>
                <div class="address">
                    Jl. Merdeka No. 123, RT 05/RW 10, Kelurahan Sejahtera, Kecamatan Bahagia, Kota Jakarta Selatan, DKI Jakarta, 12345
                </div>
            </div>
        </div>

        <!-- Detail Pesanan -->
        <div class="section">
            <div class="section-title">
                <span>Detail Pesanan</span>
            </div>
            <div class="product-container">
                <div class="product-item">
                    <div class="product-shop">
                        <span class="shop-icon">
                            <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M13.5 3H2.5L3.5 7H12.5L13.5 3Z" stroke="#ee4d2d" stroke-width="1.5" stroke-linejoin="round"/>
                                <path d="M3 7V13H13V7" stroke="#ee4d2d" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                <path d="M6 10H10" stroke="#ee4d2d" stroke-width="1.5" stroke-linecap="round"/>
                                <path d="M8 7V13" stroke="#ee4d2d" stroke-width="1.5" stroke-linecap="round"/>
                            </svg>
                        </span>
                        <span>Sepatu Official Store</span>
                    </div>
                    <div class="product-content">
                        <img src="/api/placeholder/80/80" alt="Produk" class="product-image">
                        <div class="product-details">
                            <div class="product-name">Sepatu Running Premium Ultra Boost</div>
                            <div class="product-variant">Warna: Hitam, Ukuran: 42</div>
                            <div class="product-actions">
                                <div class="product-price">Rp599.000 <span class="price-original">Rp799.000</span></div>
                                <div class="quantity-display">
                                    x<span class="quantity">1</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="product-item">
                    <div class="product-shop">
                        <span class="shop-icon">
                            <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M13.5 3H2.5L3.5 7H12.5L13.5 3Z" stroke="#ee4d2d" stroke-width="1.5" stroke-linejoin="round"/>
                                <path d="M3 7V13H13V7" stroke="#ee4d2d" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                <path d="M6 10H10" stroke="#ee4d2d" stroke-width="1.5" stroke-linecap="round"/>
                                <path d="M8 7V13" stroke="#ee4d2d" stroke-width="1.5" stroke-linecap="round"/>
                            </svg>
                        </span>
                        <span>Fashion Store</span>
                    </div>
                    <div class="product-content">
                        <img src="/api/placeholder/80/80" alt="Produk" class="product-image">
                        <div class="product-details">
                            <div class="product-name">T-Shirt Katun Premium</div>
                            <div class="product-variant">Warna: Putih, Ukuran: L</div>
                            <div class="product-actions">
                                <div class="product-price">Rp199.000</div>
                                <div class="quantity-display">
                                    x<span class="quantity">2</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="product-item">
                    <div class="product-shop">
                        <span class="shop-icon">
                            <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M13.5 3H2.5L3.5 7H12.5L13.5 3Z" stroke="#ee4d2d" stroke-width="1.5" stroke-linejoin="round"/>
                                <path d="M3 7V13H13V7" stroke="#ee4d2d" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                <path d="M6 10H10" stroke="#ee4d2d" stroke-width="1.5" stroke-linecap="round"/>
                                <path d="M8 7V13" stroke="#ee4d2d" stroke-width="1.5" stroke-linecap="round"/>
                            </svg>
                        </span>
                        <span>Outdoor Gear Mall</span>
                    </div>
                    <div class="product-content">
                        <img src="/api/placeholder/80/80" alt="Produk" class="product-image">
                        <div class="product-details">
                            <div class="product-name">Tas Ransel Waterproof</div>
                            <div class="product-variant">Warna: Navy Blue</div>
                            <div class="product-actions">
                                <div class="product-price">Rp349.000 <span class="price-original">Rp450.000</span></div>
                                <div class="quantity-display">
                                    x<span class="quantity">1</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Pengiriman -->
        <div class="section">
            <div class="section-title">
                <span>Opsi Pengiriman</span>
                <button class="edit-btn">Lihat Semua <span style="font-size: 14px;">›</span></button>
            </div>
            <div class="shipping-option" style="position: relative; border: 1px solid #00BFA5; border-radius: 8px; padding: 15px; margin-top: 10px; cursor: pointer;">
                <div style="position: absolute; top: -10px; left: 15px; background-color: #00BFA5; color: white; padding: 2px 10px; border-radius: 12px; font-size: 12px; font-weight: bold;">Gratis Ongkir</div>
                <div style="display: flex; justify-content: space-between; margin-bottom: -4px;">
                    <div style="font-weight: bold; font-size: 14px;">Reguler</div>
                    <div style="display: flex; align-items: center;">
                        <span style="color: #999; text-decoration: line-through; margin-right: 8px;">Rp17.000</span>
                        <span style="font-weight: bold; font-size: 16px;">Rp0</span>
                        <span style="color: #00BFA5; margin-left: 5px;"><svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <circle cx="12" cy="12" r="10" stroke="#00BFA5" stroke-width="2" fill="none"/>
                            <path d="M8 12L10.5 14.5L16 9" stroke="#00BFA5" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg></span>
                    </div>
                </div>
                <div style="display: flex; align-items: center; margin-bottom: -4px; color: #00BFA5;">
                    <span style="margin-right: 8px;">
                        <svg width="24" height="24" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <g transform="translate(0, 5) scale(0.9)">
                                <path d="M14 30C15.6569 30 17 28.6569 17 27C17 25.3431 15.6569 24 14 24C12.3431 24 11 25.3431 11 27C11 28.6569 12.3431 30 14 30Z" fill="#00BFA5"/>
                                <path d="M28 30C29.6569 30 31 28.6569 31 27C31 25.3431 29.6569 24 28 24C26.3431 24 25 25.3431 25 27C25 28.6569 26.3431 30 28 30Z" fill="#00BFA5"/>
                                <path d="M33 13H27L25 8H10L7 13H3V27H7M33 27H36V17L33 13" stroke="#00BFA5" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                <path d="M14 27H28" stroke="#00BFA5" stroke-width="2" stroke-linecap="round"/>
                                <path d="M1 17.5H3.5" stroke="#00BFA5" stroke-width="2.5" stroke-linecap="round"/>
                                <path d="M4.5 17.5H7.5" stroke="#00BFA5" stroke-width="2" stroke-linecap="round"/>
                                <path d="M8.5 17.5H11" stroke="#00BFA5" stroke-width="1.5" stroke-linecap="round"/>
                                <path d="M1 20.5H3.5" stroke="#00BFA5" stroke-width="2.5" stroke-linecap="round"/>
                                <path d="M4.5 20.5H7.5" stroke="#00BFA5" stroke-width="2" stroke-linecap="round"/>
                                <path d="M8.5 20.5H11" stroke="#00BFA5" stroke-width="1.5" stroke-linecap="round"/>
                            </g>
                        </svg>
                    </span>
                    <span style="font-weight: 500; color: #00BFA5; font-size: 12px;">Garansi tiba: 20 - 23 Mar</span>
                </div>
                <div style="color: #666; font-size: 12px; margin-bottom: -10px;">Voucher s/d Rp10.000 jika pesanan belum tiba 23 Mar 2025.</div>
            </div>
        </div>

        <!-- Pembayaran -->
        <div class="section" style="cursor: pointer;">
            <div style="display: flex; align-items: center; justify-content: space-between;">
                <div style="display: flex; align-items: center;">
                    <div style="width: 40px; height: 40px; background-color: none; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: -2px; margin-left: -10px;">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <circle cx="12" cy="12" r="10" fill="#FFEEE5" stroke="#FF5722" stroke-width="1.5"/>
                            <path d="M12 6v6l4 2" stroke="#FF5722" stroke-width="1.5" stroke-linecap="round"/>
                        </svg>
                    </div>
                    <span style="font-weight: font-size: 10px; white-space: nowrap;">Metode Pembayaran</span>
                </div>
                <div style="display: flex; align-items: center;">
                    <span style="margin-right: 10px; color: #666; margin-left: 16px; font-size: 14px;">Transfer Bank - Bank BCA</span>
                    <span style="color: #999; font-size: 30px;">›</span>
                </div>
            </div>
        </div>

        <!-- Voucher -->
        <div class="section">
            <div class="voucher-option">
                <div class="voucher-label">
                    <span class="voucher-icon">🎟️</span>
                    <span>Voucher</span>
                </div>
                <span class="edit-btn">Pilih Voucher</span>
            </div>
        </div>

        <!-- Ringkasan Pembayaran -->
        <div class="section">
            <div class="section-title">
                <span>Ringkasan Pembayaran</span>
            </div>
            <div class="cost-item">
                <span>Subtotal Produk</span>
                <span>Rp1.346.000</span>
            </div>
            <div class="cost-item">
                <span>Ongkos Kirim</span>
                <span>Rp18.000</span>
            </div>
            <div class="cost-item">
                <span>Voucher</span>
                <span>-Rp50.000</span>
            </div>
            <div class="total-cost">
                <span>Total Pembayaran</span>
                <span class="total-price">Rp1.314.000</span>
            </div>
        </div>
    </div>

    <div class="toast" id="toast">Notification message</div>
    
   <footer class="footer" style="display: flex; justify-content: space-between; align-items: center; padding: 10px;">
    <div class="order-total">Rp1.314.000</div>
    <button class="order-btn">Buat Pesanan</button>
</footer>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Quantity controls
            const plusBtns = document.querySelectorAll('.plus');
            const minusBtns = document.querySelectorAll('.minus');
            const orderBtn = document.querySelector('.order-btn');
            const toast = document.getElementById('toast');

            // Plus button
            plusBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    const quantityEl = this.parentElement.querySelector('.quantity');
                    let quantity = parseInt(quantityEl.textContent);
                    quantity++;
                    quantityEl.textContent = quantity;
                    updateTotalPrice();
                });
            });

            // Minus button
            minusBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    const quantityEl = this.parentElement.querySelector('.quantity');
                    let quantity = parseInt(quantityEl.textContent);
                    if (quantity > 1) {
                        quantity--;
                        quantityEl.textContent = quantity;
                        updateTotalPrice();
                    } else {
                        showToast('Jumlah minimum adalah 1');
                    }
                });
            });

            // Shipping options
            const shippingOptions = document.querySelectorAll('.shipping-option');
            
            shippingOptions.forEach(option => {
                option.addEventListener('click', function() {
                    // Remove selected class from all options
                    shippingOptions.forEach(opt => {
                        opt.classList.remove('selected');
                    });
                    
                    // Add selected class to clicked option
                    this.classList.add('selected');
                    
                    // Update shipping price in summary
                    const shippingPrice = this.querySelector('.shipping-price').textContent;
                    document.querySelector('.cost-item:nth-child(2) span:last-child').textContent = shippingPrice;
                    
                    updateTotalPrice();
                });
            });

            // Payment options
            const paymentOptions = document.querySelectorAll('.payment-option');
            
            paymentOptions.forEach(option => {
                option.addEventListener('click', function() {
                    // Remove selected class from all options
                    paymentOptions.forEach(opt => {
                        opt.classList.remove('selected');
                    });
                    
                    // Add selected class to clicked option
                    this.classList.add('selected');
                });
            });

            // Edit address button
            document.querySelector('.edit-btn').addEventListener('click', function() {
                showToast('Fitur ubah alamat belum tersedia');
            });

            // Voucher button
            document.querySelector('.voucher-option .edit-btn').addEventListener('click', function() {
                showToast('Fitur pilih voucher belum tersedia');
            });

            // Order button
            orderBtn.addEventListener('click', function() {
                showToast('Pesanan berhasil dibuat!');
                
                // Simulate order processing
                setTimeout(() => {
                    showToast('Redirecting to payment...');
                }, 2000);
            });

            // Update total price
            function updateTotalPrice() {
                // Get product prices
                let subtotal = 0;
                const products = document.querySelectorAll('.product-item');
                
                products.forEach(product => {
                    const priceText = product.querySelector('.product-price').textContent;
                    const price = parseInt(priceText.replace(/\D/g, ''));
                    const quantity = parseInt(product.querySelector('.quantity').textContent);
                    subtotal += price * quantity;
                });
                
                // Update subtotal
                document.querySelector('.cost-item:first-child span:last-child').textContent = formatPrice(subtotal);
                
                // Get shipping price
                const shippingPriceText = document.querySelector('.shipping-option.selected .shipping-price').textContent;
                const shippingPrice = parseInt(shippingPriceText.replace(/\D/g, ''));
                
                // Get voucher discount
                const voucherText = document.querySelector('.cost-item:nth-child(3) span:last-child').textContent;
                const voucherDiscount = parseInt(voucherText.replace(/\D/g, ''));
                
                // Calculate total
                const total = subtotal + shippingPrice - voucherDiscount;
                
                // Update total in summary and footer
                document.querySelector('.total-price').textContent = formatPrice(total);
                document.querySelector('.order-total').textContent = formatPrice(total);
            }

            // Format price
            function formatPrice(price) {
                return 'Rp' + price.toString().replace(/\B(?=(\d{3})+(?!\d))/g, '.');
            }

            // Show toast notification
            function showToast(message) {
                toast.textContent = message;
                toast.style.display = 'block';
                
                setTimeout(() => {
                    toast.style.display = 'none';
                }, 3000);
            }

            // Back button
            document.querySelector('.back-btn').addEventListener('click', function() {
                showToast('Kembali ke halaman keranjang');
            });
        });
    </script>
</body>
</html>
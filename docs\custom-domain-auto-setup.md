# Custom Domain Auto Setup

## Overview

Sistem otomatis untuk konfigurasi custom domain tenant yang men<PERSON>ola **domain ownership verification**, **Cloudflare DNS**, dan **Vercel domain** secara terpusat.

## ✅ Keuntungan Sistem Centralized

### **1. Tenant Benefits**
- ✅ **Cloudflare Pro Features:** DDoS protection, CDN, SSL, security
- ✅ **Zero Technical Setup:** Tinggal input domain, sisanya otomatis
- ✅ **Unified Support:** Single point of contact untuk troubleshooting
- ✅ **Consistent Performance:** Benefit dari infrastruktur platform

### **2. Platform Benefits**
- ✅ **Unified Management:** Semua domain dalam satu dashboard
- ✅ **Consistent Configuration:** Standardized setup untuk semua tenant
- ✅ **Easier Monitoring:** Centralized logging dan analytics
- ✅ **Cost Efficiency:** Shared Cloudflare Pro subscription

## Cara Kerja

### **1. Flow Otomatis**
```
Tenant Input Domain → Domain Verification → DNS Auto-Config → Vercel Auto-Add → Domain Aktif
```

### **2. Domain Verification Process**
1. **Tenant input domain:** `example.com`
2. **System generate verification:** `sellzio-verification=tenant-uuid`
3. **Tenant add TXT record:** Di DNS provider mereka
4. **System verify ownership:** Check TXT record exists
5. **Auto-configuration:** DNS + Vercel setup

### **3. DNS Configuration**
- **Type:** CNAME
- **Name:** `example.com`
- **Content:** `cname.vercel-dns.com` atau `tenant-subdomain.sellzio.my.id`
- **Proxied:** ✅ Enabled (untuk Cloudflare Pro benefits)

## API Endpoints

### **1. Set Custom Domain**
```javascript
POST /api/tenants/custom-domain
{
  "domain": "example.com",
  "tenantId": "tenant-uuid"
}
```

**Response Success:**
```javascript
{
  "success": true,
  "tenant": { /* updated tenant data */ },
  "dnsConfigured": true,
  "vercelConfigured": true,
  "message": "Custom domain configured successfully"
}
```

**Response Verification Required:**
```javascript
{
  "success": false,
  "verificationRequired": true,
  "verificationRecord": "sellzio-verification=tenant-uuid",
  "instructions": [
    "Add a TXT record to your domain DNS:",
    "Name: sellzio-verification",
    "Value: tenant-uuid",
    "Wait for DNS propagation (up to 24 hours)",
    "Try again after verification record is active"
  ]
}
```

### **2. Remove Custom Domain**
```javascript
DELETE /api/tenants/custom-domain
{
  "tenantId": "tenant-uuid"
}
```

## Domain Verification

### **1. Verification Record**
**Format:** `sellzio-verification=<tenant-id>`

**Example:**
```
Type: TXT
Name: sellzio-verification
Value: 96111d56-b82d-43e7-9926-8f0530dc6063
```

### **2. Tenant Instructions**
1. **Login ke DNS Provider** (Cloudflare, Namecheap, GoDaddy, dll)
2. **Add TXT Record:**
   - Name: `sellzio-verification`
   - Value: `<tenant-id>` (provided by system)
3. **Wait for Propagation** (5 minutes - 24 hours)
4. **Retry Setup** di tenant dashboard

### **3. Verification Tools**
- **DNS Checker:** https://dnschecker.org
- **Google DNS:** `nslookup -type=TXT sellzio-verification.example.com *******`

## Cloudflare Pro Benefits untuk Tenant

### **1. Performance**
- ✅ **Global CDN:** 200+ data centers worldwide
- ✅ **Smart Routing:** Optimal path selection
- ✅ **HTTP/3 & QUIC:** Latest protocol support
- ✅ **Image Optimization:** Auto WebP conversion

### **2. Security**
- ✅ **DDoS Protection:** Unlimited mitigation
- ✅ **WAF (Web Application Firewall):** Advanced threat protection
- ✅ **Bot Management:** Intelligent bot detection
- ✅ **SSL/TLS:** Full encryption with HSTS

### **3. Reliability**
- ✅ **99.99% Uptime SLA:** Enterprise-grade availability
- ✅ **Load Balancing:** Automatic failover
- ✅ **Health Checks:** Proactive monitoring

## Implementation Details

### **1. DNS Configuration**
```javascript
// Cloudflare DNS Record
{
  type: 'CNAME',
  name: 'example.com',
  content: 'cname.vercel-dns.com',
  ttl: 1, // Auto TTL
  proxied: true // Enable Cloudflare proxy
}
```

### **2. Vercel Domain**
```javascript
// Vercel API Call
POST /v9/projects/{project-id}/domains
{
  "name": "example.com"
}
```

### **3. Error Handling**
- **Non-fatal errors:** DNS/Vercel failures don't block database update
- **Verification failures:** Clear instructions provided to tenant
- **Retry mechanism:** Tenant can retry after fixing verification

## Troubleshooting

### **1. Verification Failed**
**Symptoms:** `verificationRequired: true`
**Causes:**
- TXT record not added
- DNS not propagated
- Wrong record value

**Solutions:**
- Check TXT record exists: `nslookup -type=TXT sellzio-verification.example.com`
- Wait for propagation (up to 24 hours)
- Verify record value matches tenant ID

### **2. DNS Not Configured**
**Symptoms:** `dnsConfigured: false`
**Causes:**
- Missing Cloudflare credentials
- Zone ID incorrect
- API permissions insufficient

**Solutions:**
- Check environment variables
- Verify Cloudflare API token permissions
- Test with debug tools

### **3. Domain Not Accessible**
**Symptoms:** 404 or connection errors
**Causes:**
- DNS propagation delay
- Vercel domain not verified
- SSL certificate pending

**Solutions:**
- Wait 5-10 minutes for propagation
- Check Vercel dashboard for domain status
- Verify SSL certificate is active

## Best Practices

### **1. Tenant Communication**
- **Clear Instructions:** Step-by-step verification guide
- **Expected Timeline:** Set realistic expectations (5 min - 24 hours)
- **Support Contact:** Provide help when needed

### **2. Monitoring**
- **Success Rate Tracking:** Monitor verification success rate
- **Performance Metrics:** DNS resolution time, SSL setup time
- **Error Alerting:** Notify admin of configuration failures

### **3. Security**
- **Domain Validation:** Strict format checking
- **Ownership Verification:** Always verify before configuration
- **Access Control:** Tenant can only manage their own domains

## Cost Analysis

### **Centralized vs Decentralized**

**Centralized (Recommended):**
- ✅ **Platform Cost:** 1x Cloudflare Pro ($20/month)
- ✅ **Tenant Cost:** $0 (included in platform fee)
- ✅ **Total for 100 tenants:** $20/month

**Decentralized:**
- ❌ **Platform Cost:** $0
- ❌ **Tenant Cost:** $20/month each (if they want Pro features)
- ❌ **Total for 100 tenants:** $2,000/month

**Savings:** $1,980/month untuk 100 tenant! 💰

## Future Enhancements

### **1. Advanced Verification**
- **DNS-01 Challenge:** Automatic Let's Encrypt SSL
- **HTTP Verification:** Alternative to TXT records
- **Email Verification:** Domain admin email confirmation

### **2. Multi-Domain Support**
- **Subdomain Wildcards:** `*.example.com`
- **Multiple Domains:** Multiple custom domains per tenant
- **Domain Aliases:** Redirect between domains

### **3. Analytics & Monitoring**
- **Domain Performance:** Speed, uptime, traffic metrics
- **SSL Monitoring:** Certificate expiry alerts
- **Usage Analytics:** Domain access patterns

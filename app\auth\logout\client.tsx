"use client"

import { useEffect } from "react"
import { useRouter } from "next/navigation"
import { useAuth } from "@/contexts/auth-context"

export default function LogoutClient() {
  const { logout } = useAuth()
  const router = useRouter()

  useEffect(() => {
    const timer = setTimeout(() => {
      logout()
      // AuthContext sudah handle redirect yang benar
    }, 100)

    return () => clearTimeout(timer)
  }, [logout, router])

  return (
    <div className="flex min-h-screen flex-col items-center justify-center p-4">
      <p>Logging out...</p>
    </div>
  )
}

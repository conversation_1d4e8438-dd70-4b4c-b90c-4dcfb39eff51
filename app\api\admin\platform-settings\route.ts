import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

function getClient() {
  return createClient(supabaseUrl, supabaseServiceKey)
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const category = searchParams.get('category')

    console.log('🔥 API: Fetching platform settings, category:', category)

    const supabase = getClient()

    let query = supabase
      .from('PlatformSettings')
      .select('*')
      .order('category', { ascending: true })

    if (category) {
      query = query.eq('category', category)
    }

    const { data, error } = await query

    if (error) {
      console.error('🔥 API: Error fetching platform settings:', error)
      return NextResponse.json(
        { error: 'Failed to fetch platform settings' },
        { status: 500 }
      )
    }

    console.log('🔥 API: Platform settings fetched:', data?.length, 'items')

    return NextResponse.json({
      success: true,
      settings: data
    })
  } catch (error) {
    console.error('🔥 API: Error fetching platform settings:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const { key, value, description, category } = await request.json()

    if (!key || value === undefined) {
      return NextResponse.json(
        { error: 'Key and value are required' },
        { status: 400 }
      )
    }

    console.log('🔥 API: Updating platform setting:', { key, value, category })

    const supabase = getClient()

    // Upsert setting
    const { data, error } = await supabase
      .from('PlatformSettings')
      .upsert({
        key,
        value,
        description,
        category: category || 'general',
        updatedAt: new Date().toISOString(),
        updatedBy: 'admin' // TODO: Get from auth
      })
      .select()
      .single()

    if (error) {
      console.error('🔥 API: Error updating platform setting:', error)
      return NextResponse.json(
        { error: 'Failed to update platform setting' },
        { status: 500 }
      )
    }

    console.log('🔥 API: Platform setting updated successfully:', data)

    return NextResponse.json({
      success: true,
      setting: data
    })
  } catch (error) {
    console.error('🔥 API: Error updating platform setting:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    const { settings } = await request.json()

    if (!Array.isArray(settings)) {
      return NextResponse.json(
        { error: 'Settings must be an array' },
        { status: 400 }
      )
    }

    console.log('🔥 API: Bulk updating platform settings:', settings.length, 'items')

    const supabase = getClient()

    // Update multiple settings
    const updates = settings.map(setting => ({
      ...setting,
      updatedAt: new Date().toISOString(),
      updatedBy: 'admin'
    }))

    const { data, error } = await supabase
      .from('PlatformSettings')
      .upsert(updates)
      .select()

    if (error) {
      console.error('🔥 API: Error bulk updating platform settings:', error)
      return NextResponse.json(
        { error: 'Failed to update platform settings' },
        { status: 500 }
      )
    }

    console.log('🔥 API: Platform settings bulk updated successfully:', data?.length, 'items')

    return NextResponse.json({
      success: true,
      settings: data
    })
  } catch (error) {
    console.error('🔥 API: Error bulk updating platform settings:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

import type React from "react"
import { TenantThemeProvider } from "@/components/tenant/tenant-theme-provider"
import { Metadata } from "next"

interface SellzioTenantLayoutProps {
  params: { slug: string }
  children: React.ReactNode
}

// Metadata untuk SEO
export async function generateMetadata({ params }: { params: { slug: string } }): Promise<Metadata> {
  return {
    title: `${params.slug} - Sellzio Marketplace`,
    description: `Marketplace ${params.slug} powered by Sellzio - Jual beli online dengan mudah dan aman`,
    keywords: ["marketplace", "jual beli", "online shop", params.slug, "sellzio"],
    openGraph: {
      title: `${params.slug} - Sellzio Marketplace`,
      description: `Marketplace ${params.slug} powered by Sellzio`,
      type: "website",
      siteName: `${params.slug} Marketplace`,
    },
    twitter: {
      card: "summary_large_image",
      title: `${params.slug} - Sell<PERSON> Marketplace`,
      description: `Marketplace ${params.slug} powered by Sellzio`,
    },
  }
}

export default function SellzioTenantLayout({ params, children }: SellzioTenantLayoutProps) {
  return (
    <TenantThemeProvider tenantId={params.slug}>
      <div className="sellzio-tenant-layout">
        {/* Inject Sellzio specific styles */}
        <style jsx global>{`
          /* Sellzio tenant specific overrides */
          :root {
            --sellzio-primary: #ee4d2d;
            --sellzio-secondary: #ff6b35;
            --sellzio-accent: #f5a623;
          }
          
          .sellzio-tenant-layout {
            min-height: 100vh;
            background: #f5f5f5;
          }
          
          /* Ensure Sellzio styles take precedence */
          .sellzio-tenant-layout * {
            box-sizing: border-box;
          }
          
          /* Mobile optimizations for tenant */
          @media (max-width: 768px) {
            .sellzio-tenant-layout {
              padding: 0;
            }
          }
        `}</style>
        {children}
      </div>
    </TenantThemeProvider>
  )
}

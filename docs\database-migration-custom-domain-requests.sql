-- Custom Domain Requests Table
-- Run this in Supabase SQL Editor

CREATE TABLE IF NOT EXISTS custom_domain_requests (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
  domain VARCHAR(255) NOT NULL,
  reason TEXT,
  status VARCHAR(50) DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected', 'configuration_failed')),
  admin_notes TEXT,
  requested_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  processed_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_custom_domain_requests_tenant_id ON custom_domain_requests(tenant_id);
CREATE INDEX IF NOT EXISTS idx_custom_domain_requests_status ON custom_domain_requests(status);
CREATE INDEX IF NOT EXISTS idx_custom_domain_requests_domain ON custom_domain_requests(domain);
CREATE INDEX IF NOT EXISTS idx_custom_domain_requests_requested_at ON custom_domain_requests(requested_at DESC);

-- Unique constraint to prevent duplicate pending requests for same domain
CREATE UNIQUE INDEX IF NOT EXISTS idx_custom_domain_requests_unique_pending 
ON custom_domain_requests(domain, tenant_id) 
WHERE status = 'pending';

-- RLS (Row Level Security) policies
ALTER TABLE custom_domain_requests ENABLE ROW LEVEL SECURITY;

-- Policy: Tenants can only see their own requests
CREATE POLICY "Tenants can view own domain requests" ON custom_domain_requests
  FOR SELECT USING (tenant_id = auth.uid());

-- Policy: Tenants can create their own requests
CREATE POLICY "Tenants can create domain requests" ON custom_domain_requests
  FOR INSERT WITH CHECK (tenant_id = auth.uid());

-- Policy: Only admins can update requests (approve/reject)
-- Note: This assumes admin role checking - adjust based on your auth system
CREATE POLICY "Admins can update domain requests" ON custom_domain_requests
  FOR UPDATE USING (true); -- Adjust this based on your admin role logic

-- Policy: Only admins can view all requests
CREATE POLICY "Admins can view all domain requests" ON custom_domain_requests
  FOR SELECT USING (true); -- Adjust this based on your admin role logic

-- Function to automatically update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Trigger to automatically update updated_at
CREATE TRIGGER update_custom_domain_requests_updated_at 
  BEFORE UPDATE ON custom_domain_requests 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Sample data for testing (optional)
-- INSERT INTO custom_domain_requests (tenant_id, domain, reason, status) VALUES
-- ('96111d56-b82d-43e7-9926-8f0530dc6063', 'example.com', 'Business domain for professional use', 'pending'),
-- ('96111d56-b82d-43e7-9926-8f0530dc6063', 'mybusiness.com', 'Custom domain for branding', 'approved');

-- Verify table creation
SELECT 
  table_name, 
  column_name, 
  data_type, 
  is_nullable,
  column_default
FROM information_schema.columns 
WHERE table_name = 'custom_domain_requests' 
ORDER BY ordinal_position;

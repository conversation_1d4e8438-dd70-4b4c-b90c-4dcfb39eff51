import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const storeId = searchParams.get('storeId')
    const tenantId = searchParams.get('tenantId')
    
    if (!storeId || !tenantId) {
      return NextResponse.json({ error: 'Store ID and Tenant ID are required' }, { status: 400 })
    }

    // Get buyers (USER role) who have orders or interactions with this store
    // For now, we'll get all buyers in the same tenant
    // In a real system, you'd filter by actual store interactions
    const { data: buyers, error } = await supabase
      .from('User')
      .select(`
        id,
        email,
        name,
        role,
        tenantId,
        storeId,
        createdAt,
        updatedAt
      `)
      .eq('tenantId', tenantId)
      .eq('role', 'USER')
      .order('createdAt', { ascending: false })

    if (error) {
      console.error('Error fetching store buyers:', error)
      return NextResponse.json({ error: 'Failed to fetch buyers' }, { status: 500 })
    }

    // In a real system, you would also join with orders table to get buyers who actually bought from this store
    // For now, we return all buyers in the tenant
    return NextResponse.json({
      buyers,
      total: buyers?.length || 0,
      storeId,
      tenantId
    })

  } catch (error) {
    console.error('Error in store buyers API:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

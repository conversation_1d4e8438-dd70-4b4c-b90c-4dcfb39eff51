import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const subdomain = searchParams.get('subdomain')

    if (!subdomain) {
      return NextResponse.json(
        { error: 'Subdomain is required' },
        { status: 400 }
      )
    }

    console.log('🔥 API: Verifying subdomain status for:', subdomain)

    // Get platform configuration
    const platformResponse = await fetch(`${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/api/platform/config`)
    const platformData = await platformResponse.json()
    const platformDomain = platformData.success ? platformData.config.domain : 'sellzio.com'
    const protocol = platformData.success ? platformData.config.protocol : 'https'

    // For subdomains on our platform, they're always verified and active
    const subdomainUrl = `${subdomain}.${platformDomain}`

    try {
      // Check if subdomain responds
      const controller = new AbortController()
      const timeoutId = setTimeout(() => controller.abort(), 5000)

      const response = await fetch(`${protocol}://${subdomainUrl}`, {
        method: 'HEAD',
        signal: controller.signal
      })

      clearTimeout(timeoutId)
      const isActive = response.ok

      console.log('🔥 SUBDOMAIN: Status check result:', {
        subdomain: subdomainUrl,
        verified: true,
        active: isActive
      })

      return NextResponse.json({
        subdomain: subdomainUrl,
        verified: true, // Always true for our subdomains
        active: isActive,
        lastChecked: new Date().toISOString()
      })
    } catch (error) {
      console.log('🔥 SUBDOMAIN: Check failed, assuming active:', error)

      // If check fails, assume it's active (network issues, etc.)
      return NextResponse.json({
        subdomain: subdomainUrl,
        verified: true,
        active: true,
        lastChecked: new Date().toISOString()
      })
    }
  } catch (error) {
    console.error('🔥 API: Error verifying subdomain:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

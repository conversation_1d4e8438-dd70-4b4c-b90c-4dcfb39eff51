import Link from 'next/link'

export default function Home() {
  return (
    <main className="min-h-screen bg-gray-50 flex items-center justify-center">
      <div className="max-w-md mx-auto text-center p-8 bg-white rounded-lg shadow-lg">
        <h1 className="text-3xl font-bold text-gray-900 mb-4">
          Sellzio SaaS Platform
        </h1>
        <p className="text-gray-600 mb-8">
          Multi-tenant e-commerce platform dengan dukungan custom domain
        </p>

        <div className="space-y-4">
          <Link
            href="/admin/login"
            className="block w-full bg-blue-600 text-white py-3 px-6 rounded-lg hover:bg-blue-700 transition-colors"
          >
            Admin Login
          </Link>

          <Link
            href="/app/login"
            className="block w-full bg-green-600 text-white py-3 px-6 rounded-lg hover:bg-green-700 transition-colors"
          >
            User Login (Tenant/Store/Buyer)
          </Link>

          <Link
            href="/sellzio"
            className="block w-full bg-orange-600 text-white py-3 px-6 rounded-lg hover:bg-orange-700 transition-colors"
          >
            Demo Store (Sellzio Theme)
          </Link>
        </div>

        <div className="mt-8 text-sm text-gray-500">
          <p>Environment: {process.env.NODE_ENV}</p>
          <p>Domain: {typeof window !== 'undefined' ? window.location.hostname : 'Server'}</p>
        </div>
      </div>
    </main>
  )
}

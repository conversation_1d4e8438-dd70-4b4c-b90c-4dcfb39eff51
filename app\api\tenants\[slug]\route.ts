import { NextResponse } from "next/server"

// Konfigurasi untuk static export
export const dynamic = "force-dynamic"
export const revalidate = 0

// Simulasi database tenant
// Dalam implementasi sebenarnya, ini akan disimpan di Supabase
const mockTenants = [
  {
    id: "1",
    slug: "demo",
    name: "Demo Store",
    domain: "demo.sellzio.com",
    theme: "sellzio",
    status: "active",
    plan: "professional",
    settings: {
      allowRegistration: true,
      enableChat: true,
      enableReviews: true,
      currency: "IDR",
      language: "id",
      timezone: "Asia/Jakarta"
    },
    branding: {
      logo: "/sellzio-logo.png",
      favicon: "/sellzio-favicon.ico",
      primaryColor: "#ee4d2d",
      secondaryColor: "#ff6b35"
    },
    contact: {
      email: "<EMAIL>",
      phone: "+62-21-1234-5678",
      address: "Jakarta, Indonesia"
    },
    createdAt: "2024-01-01T00:00:00.000Z",
    updatedAt: "2024-01-01T00:00:00.000Z"
  },
  {
    id: "2",
    slug: "fashionhub",
    name: "Fashion Hub",
    domain: "fashionhub.sellzio.com",
    theme: "sellzio",
    status: "active",
    plan: "enterprise",
    settings: {
      allowRegistration: true,
      enableChat: true,
      enableReviews: true,
      currency: "IDR",
      language: "id",
      timezone: "Asia/Jakarta"
    },
    branding: {
      logo: "/fashionhub-logo.png",
      favicon: "/fashionhub-favicon.ico",
      primaryColor: "#ec4899",
      secondaryColor: "#f472b6"
    },
    contact: {
      email: "<EMAIL>",
      phone: "+62-21-8765-4321",
      address: "Bandung, Indonesia"
    },
    createdAt: "2024-01-15T00:00:00.000Z",
    updatedAt: "2024-01-15T00:00:00.000Z"
  },
  {
    id: "3",
    slug: "techstore",
    name: "Tech Store",
    domain: "techstore.sellzio.com",
    theme: "sellzio",
    status: "active",
    plan: "professional",
    settings: {
      allowRegistration: true,
      enableChat: true,
      enableReviews: true,
      currency: "IDR",
      language: "id",
      timezone: "Asia/Jakarta"
    },
    branding: {
      logo: "/techstore-logo.png",
      favicon: "/techstore-favicon.ico",
      primaryColor: "#059669",
      secondaryColor: "#10b981"
    },
    contact: {
      email: "<EMAIL>",
      phone: "+62-21-5555-6666",
      address: "Surabaya, Indonesia"
    },
    createdAt: "2024-02-01T00:00:00.000Z",
    updatedAt: "2024-02-01T00:00:00.000Z"
  }
]

export async function GET(request: Request, { params }: { params: { slug: string } }) {
  try {
    const slug = params.slug

    // Cari tenant berdasarkan slug
    const tenant = mockTenants.find((t) => t.slug === slug)

    if (!tenant) {
      return new NextResponse(JSON.stringify({ 
        error: "Tenant not found",
        message: `Tenant with slug '${slug}' does not exist`
      }), {
        status: 404,
        headers: { "Content-Type": "application/json" },
      })
    }

    // Check tenant status
    if (tenant.status !== "active") {
      return new NextResponse(JSON.stringify({ 
        error: "Tenant not available",
        message: `Tenant '${slug}' is currently ${tenant.status}`
      }), {
        status: 403,
        headers: { "Content-Type": "application/json" },
      })
    }

    return NextResponse.json({
      success: true,
      data: tenant
    })
  } catch (error) {
    console.error("Error processing tenant request:", error)
    return new NextResponse(JSON.stringify({ 
      error: "Internal server error",
      message: "Failed to fetch tenant information"
    }), {
      status: 500,
      headers: { "Content-Type": "application/json" },
    })
  }
}

// POST - Update tenant settings (for tenant admin)
export async function POST(request: Request, { params }: { params: { slug: string } }) {
  try {
    const slug = params.slug
    const body = await request.json()

    // Cari tenant berdasarkan slug
    const tenant = mockTenants.find((t) => t.slug === slug)

    if (!tenant) {
      return new NextResponse(JSON.stringify({ 
        error: "Tenant not found",
        message: `Tenant with slug '${slug}' does not exist`
      }), {
        status: 404,
        headers: { "Content-Type": "application/json" },
      })
    }

    // Update tenant data (in real implementation, this would update the database)
    const updatedTenant = {
      ...tenant,
      ...body,
      updatedAt: new Date().toISOString()
    }

    return NextResponse.json({
      success: true,
      data: updatedTenant,
      message: "Tenant updated successfully"
    })
  } catch (error) {
    console.error("Error updating tenant:", error)
    return new NextResponse(JSON.stringify({ 
      error: "Internal server error",
      message: "Failed to update tenant"
    }), {
      status: 500,
      headers: { "Content-Type": "application/json" },
    })
  }
}

// PATCH - Update specific tenant fields
export async function PATCH(request: Request, { params }: { params: { slug: string } }) {
  try {
    const slug = params.slug
    const body = await request.json()

    // Cari tenant berdasarkan slug
    const tenant = mockTenants.find((t) => t.slug === slug)

    if (!tenant) {
      return new NextResponse(JSON.stringify({ 
        error: "Tenant not found",
        message: `Tenant with slug '${slug}' does not exist`
      }), {
        status: 404,
        headers: { "Content-Type": "application/json" },
      })
    }

    // Merge updates with existing data
    const updatedTenant = {
      ...tenant,
      settings: { ...tenant.settings, ...body.settings },
      branding: { ...tenant.branding, ...body.branding },
      contact: { ...tenant.contact, ...body.contact },
      updatedAt: new Date().toISOString()
    }

    return NextResponse.json({
      success: true,
      data: updatedTenant,
      message: "Tenant updated successfully"
    })
  } catch (error) {
    console.error("Error updating tenant:", error)
    return new NextResponse(JSON.stringify({ 
      error: "Internal server error",
      message: "Failed to update tenant"
    }), {
      status: 500,
      headers: { "Content-Type": "application/json" },
    })
  }
}

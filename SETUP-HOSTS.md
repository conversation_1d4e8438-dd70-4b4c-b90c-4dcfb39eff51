# Setup Subdomain untuk Development

Untuk menggunakan `app.localhost:3000/login`, Anda perlu menambahkan entry ke file hosts Windows.

## Cara Setup:

### 1. Buka Command Prompt sebagai Administrator
- <PERSON><PERSON> kanan pada Command Prompt
- Pilih "Run as administrator"

### 2. Jalankan perintah berikut:
```cmd
echo 127.0.0.1 app.localhost >> C:\Windows\System32\drivers\etc\hosts
echo 127.0.0.1 admin.localhost >> C:\Windows\System32\drivers\etc\hosts
```

### 3. Atau edit manual:
- Buka file `C:\Windows\System32\drivers\etc\hosts` dengan Notepad sebagai Administrator
- Tambahkan baris berikut di akhir file:
```
127.0.0.1 app.localhost
127.0.0.1 admin.localhost
```

### 4. Restart browser dan test:
- `http://app.localhost:3000/login` → User Login
- `http://admin.localhost:3000/login` → Admin Login

## URL yang Didukung Tanpa Setup Hosts:

1. **Parameter-based**: `http://localhost:3000/login?app=true`
2. **Path-based**: `http://localhost:3000/app/login`
3. **Admin**: `http://localhost:3000/admin/login`

## URL yang Didukung Setelah Setup Hosts:

1. **Subdomain User**: `http://app.localhost:3000/login`
2. **Subdomain Admin**: `http://admin.localhost:3000/login`
3. **Semua URL di atas** tetap berfungsi

## Database Credentials:

**User Login:**
- Tenant: `<EMAIL>` / `user123`
- Store: `<EMAIL>` / `user123`
- Buyer: `<EMAIL>` / `user123`
- Khadijah: `<EMAIL>` / `user123`

**Admin Login:**
- Admin: `<EMAIL>` / `admin123`

"use client"

import { useEffect, useState } from "react"
import { AdminLoginForm } from "@/components/auth/admin-login-form"
import { ErrorBoundary } from "@/components/error-boundary"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { AlertCircle } from "lucide-react"

export function AdminLoginClient() {
  const [domainError, setDomainError] = useState<string | null>(null)

  useEffect(() => {
    // Validasi domain untuk admin login
    if (typeof window !== 'undefined') {
      const hostname = window.location.hostname
      const pathname = window.location.pathname

      // Admin hanya boleh login dari localhost:3000/admin/login atau domain-utama/admin/login
      const isValidDomain = hostname === 'localhost' ||
                           hostname === '127.0.0.1' ||
                           (!hostname.includes('app.') && !hostname.includes('admin.'))

      if (!isValidDomain) {
        setDomainError(`Admin login tidak diizinkan dari domain ini. Silakan gunakan localhost:3000/admin/login`)
        return
      }

      // Cek jika ada subdomain app
      if (hostname.includes('app.')) {
        setDomainError(`Admin login tidak diizinkan dari subdomain app. Silakan gunakan localhost:3000/admin/login`)
        return
      }
    }
  }, [])

  if (domainError) {
    return (
      <ErrorBoundary>
        <div className="flex min-h-screen flex-col items-center justify-center p-4">
          <div className="w-full max-w-md">
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                {domainError}
              </AlertDescription>
            </Alert>
          </div>
        </div>
      </ErrorBoundary>
    )
  }

  return (
    <ErrorBoundary>
      <div className="flex min-h-screen flex-col items-center justify-center p-4">
        <div className="w-full max-w-md">
          <h1 className="text-2xl font-bold mb-6 text-center">Admin Login</h1>
          <p className="text-sm text-muted-foreground mb-6 text-center">
            Email: <EMAIL><br />
            Password: admin123
          </p>
          <AdminLoginForm />
        </div>
      </div>
    </ErrorBoundary>
  )
}

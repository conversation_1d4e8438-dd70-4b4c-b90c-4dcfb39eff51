import { NextRequest, NextResponse } from 'next/server'
import { getClient } from '@/lib/supabase'

// Function to check if domain points to our servers
async function checkDNSRecord(domain: string, expectedTarget: string): Promise<boolean> {
  try {
    // In production, you would use a DNS lookup service
    // For now, we'll simulate the check
    console.log('🔥 DNS: Checking DNS record for:', domain, 'expected:', expectedTarget)
    
    // Simulate DNS lookup delay
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // For development/demo, we'll consider localhost domains as verified
    if (domain.includes('localhost') || domain.includes('127.0.0.1')) {
      return true
    }
    
    // For real domains, you would implement actual DNS checking here
    // Example using a DNS service:
    /*
    const response = await fetch(`https://dns.google/resolve?name=${domain}&type=CNAME`)
    const data = await response.json()
    
    if (data.Answer) {
      const cnameRecord = data.Answer.find(record => record.type === 5) // CNAME type
      return cnameRecord && cnameRecord.data === expectedTarget
    }
    */
    
    // For demo purposes, return true for specific test domains
    const testDomains = [
      'sellzio.com',
      'demo.sellzio.com',
      'mystore.com',
      'teststore.com'
    ]
    
    return testDomains.includes(domain)
  } catch (error) {
    console.error('🔥 DNS: Error checking DNS record:', error)
    return false
  }
}

// Function to check SSL certificate status
async function checkSSLStatus(domain: string): Promise<boolean> {
  try {
    console.log('🔥 SSL: Checking SSL status for:', domain)
    
    // For localhost, always return true
    if (domain.includes('localhost') || domain.includes('127.0.0.1')) {
      return true
    }
    
    // In production, you would check SSL certificate
    // For now, simulate SSL check
    const response = await fetch(`https://${domain}`, {
      method: 'HEAD',
      signal: AbortSignal.timeout(5000) // 5 second timeout
    })
    
    return response.ok
  } catch (error) {
    console.log('🔥 SSL: SSL check failed for:', domain, error.message)
    // For demo, assume SSL is available for test domains
    const testDomains = [
      'sellzio.com',
      'demo.sellzio.com',
      'mystore.com',
      'teststore.com'
    ]
    
    return testDomains.includes(domain)
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const domain = searchParams.get('domain')

    if (!domain) {
      return NextResponse.json(
        { error: 'Domain parameter is required' },
        { status: 400 }
      )
    }

    console.log('🔥 API: Verifying domain status for:', domain)

    const supabase = getClient()

    // Get tenant info for this domain
    const { data: tenant, error: tenantError } = await supabase
      .from('Tenant')
      .select('id, slug')
      .eq('domain', domain)
      .single()

    if (tenantError || !tenant) {
      return NextResponse.json({
        verified: false,
        ssl: false,
        message: 'Domain not found in our records'
      })
    }

    const expectedTarget = `${tenant.slug}.sellzio.com`
    
    // Check DNS record
    const dnsVerified = await checkDNSRecord(domain, expectedTarget)
    
    // Check SSL status
    const sslActive = await checkSSLStatus(domain)

    console.log('🔥 API: Domain verification result:', {
      domain,
      dnsVerified,
      sslActive,
      expectedTarget
    })

    return NextResponse.json({
      verified: dnsVerified,
      ssl: sslActive,
      expectedTarget,
      message: dnsVerified 
        ? 'Domain verified successfully' 
        : `Please point your domain to ${expectedTarget}`
    })
  } catch (error) {
    console.error('🔥 API: Error verifying domain:', error)
    return NextResponse.json(
      { 
        verified: false,
        ssl: false,
        error: 'Internal server error' 
      },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const { domain, tenantId } = await request.json()

    if (!domain || !tenantId) {
      return NextResponse.json(
        { error: 'Domain and tenantId are required' },
        { status: 400 }
      )
    }

    console.log('🔥 API: Manual domain verification requested:', { domain, tenantId })

    const supabase = getClient()

    // Get tenant info
    const { data: tenant, error: tenantError } = await supabase
      .from('Tenant')
      .select('id, slug')
      .eq('id', tenantId)
      .single()

    if (tenantError || !tenant) {
      return NextResponse.json({
        verified: false,
        message: 'Tenant not found'
      }, { status: 404 })
    }

    const expectedTarget = `${tenant.slug}.sellzio.com`
    
    // Check DNS record
    const dnsVerified = await checkDNSRecord(domain, expectedTarget)
    
    // Check SSL status
    const sslActive = await checkSSLStatus(domain)

    // If verified, update the tenant's domain in database
    if (dnsVerified) {
      const { error: updateError } = await supabase
        .from('Tenant')
        .update({ domain })
        .eq('id', tenantId)

      if (updateError) {
        console.error('🔥 API: Error updating tenant domain:', updateError)
      }
    }

    console.log('🔥 API: Manual verification result:', {
      domain,
      tenantId,
      dnsVerified,
      sslActive,
      expectedTarget
    })

    return NextResponse.json({
      verified: dnsVerified,
      ssl: sslActive,
      expectedTarget,
      message: dnsVerified 
        ? 'Domain verified and updated successfully' 
        : `DNS verification failed. Please ensure your domain points to ${expectedTarget}`
    })
  } catch (error) {
    console.error('🔥 API: Error in manual domain verification:', error)
    return NextResponse.json(
      { 
        verified: false,
        error: 'Internal server error' 
      },
      { status: 500 }
    )
  }
}

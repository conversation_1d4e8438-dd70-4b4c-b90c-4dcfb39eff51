import { NextResponse } from "next/server"
import { createSellzioTenantTheme } from "@/lib/models/sellzio-theme"
import { velozioTheme } from "@/lib/models/velozio-theme"
import type { TenantTheme } from "@/lib/models/tenant-theme"

// Konfigurasi untuk static export
export const dynamic = "force-dynamic"
export const revalidate = 0

// Mock data untuk tenant themes berdasarkan slug
const getTenantThemeBySlug = (slug: string): TenantTheme => {
  // Default theme mapping berdasarkan tenant slug
  const themeMapping: Record<string, () => TenantTheme> = {
    demo: () => createSellzioTenantTheme("demo"),
    fashionhub: () => ({
      ...createSellzioTenantTheme("fashionhub"),
      name: "Sellzio Fashion",
      colors: {
        ...createSellzioTenantTheme("fashionhub").colors,
        primary: "#ec4899", // Pink for fashion
        secondary: "#f472b6",
        accent: "#8b5cf6",
      },
      customCSS: `
        ${createSellzioTenantTheme("fashionhub").customCSS}
        
        /* Fashion-specific overrides */
        .sellzio-header {
          background: linear-gradient(135deg, #ec4899 0%, #f472b6 100%);
        }
        
        .sellzio-badge-mall {
          background: linear-gradient(135deg, #ec4899, #f472b6);
        }
        
        .sellzio-price {
          color: #ec4899;
        }
        
        .sellzio-cart-button {
          background: linear-gradient(135deg, #ec4899, #f472b6);
        }
        
        .sellzio-checkout-button {
          background: linear-gradient(135deg, #ec4899, #f472b6);
        }
      `
    }),
    techstore: () => ({
      ...createSellzioTenantTheme("techstore"),
      name: "Sellzio Tech",
      colors: {
        ...createSellzioTenantTheme("techstore").colors,
        primary: "#059669", // Green for tech
        secondary: "#10b981",
        accent: "#f59e0b",
      },
      customCSS: `
        ${createSellzioTenantTheme("techstore").customCSS}
        
        /* Tech-specific overrides */
        .sellzio-header {
          background: linear-gradient(135deg, #059669 0%, #10b981 100%);
        }
        
        .sellzio-badge-mall {
          background: linear-gradient(135deg, #059669, #10b981);
        }
        
        .sellzio-price {
          color: #059669;
        }
        
        .sellzio-cart-button {
          background: linear-gradient(135deg, #059669, #10b981);
        }
        
        .sellzio-checkout-button {
          background: linear-gradient(135deg, #059669, #10b981);
        }
      `
    }),
  }

  // Return specific theme or default Sellzio theme
  return themeMapping[slug] ? themeMapping[slug]() : createSellzioTenantTheme(slug)
}

export async function GET(request: Request, { params }: { params: { slug: string } }) {
  try {
    const slug = params.slug

    // Get theme for tenant
    const theme = getTenantThemeBySlug(slug)

    if (!theme) {
      return new NextResponse(JSON.stringify({ 
        error: "Theme not found",
        message: `No theme found for tenant '${slug}'`
      }), {
        status: 404,
        headers: { "Content-Type": "application/json" },
      })
    }

    return NextResponse.json({
      success: true,
      data: theme
    })
  } catch (error) {
    console.error("Error fetching tenant theme:", error)
    return new NextResponse(JSON.stringify({ 
      error: "Internal server error",
      message: "Failed to fetch tenant theme"
    }), {
      status: 500,
      headers: { "Content-Type": "application/json" },
    })
  }
}

// POST - Update tenant theme
export async function POST(request: Request, { params }: { params: { slug: string } }) {
  try {
    const slug = params.slug
    const body = await request.json()

    // Get current theme
    const currentTheme = getTenantThemeBySlug(slug)

    // Update theme with new data
    const updatedTheme: TenantTheme = {
      ...currentTheme,
      ...body,
      tenantId: slug,
      updatedAt: new Date().toISOString()
    }

    // In real implementation, this would save to database
    console.log(`Updated theme for tenant ${slug}:`, updatedTheme)

    return NextResponse.json({
      success: true,
      data: updatedTheme,
      message: "Theme updated successfully"
    })
  } catch (error) {
    console.error("Error updating tenant theme:", error)
    return new NextResponse(JSON.stringify({ 
      error: "Internal server error",
      message: "Failed to update tenant theme"
    }), {
      status: 500,
      headers: { "Content-Type": "application/json" },
    })
  }
}

// PATCH - Update specific theme properties
export async function PATCH(request: Request, { params }: { params: { slug: string } }) {
  try {
    const slug = params.slug
    const body = await request.json()

    // Get current theme
    const currentTheme = getTenantThemeBySlug(slug)

    // Merge updates with existing theme
    const updatedTheme: TenantTheme = {
      ...currentTheme,
      colors: { ...currentTheme.colors, ...body.colors },
      fonts: { ...currentTheme.fonts, ...body.fonts },
      logo: { ...currentTheme.logo, ...body.logo },
      layout: { ...currentTheme.layout, ...body.layout },
      customCSS: body.customCSS !== undefined ? body.customCSS : currentTheme.customCSS,
      updatedAt: new Date().toISOString()
    }

    // In real implementation, this would save to database
    console.log(`Patched theme for tenant ${slug}:`, updatedTheme)

    return NextResponse.json({
      success: true,
      data: updatedTheme,
      message: "Theme updated successfully"
    })
  } catch (error) {
    console.error("Error patching tenant theme:", error)
    return new NextResponse(JSON.stringify({ 
      error: "Internal server error",
      message: "Failed to update tenant theme"
    }), {
      status: 500,
      headers: { "Content-Type": "application/json" },
    })
  }
}

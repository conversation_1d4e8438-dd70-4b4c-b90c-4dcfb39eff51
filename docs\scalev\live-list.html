<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Live Shopping</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        @font-face {
            font-family: 'Material Icons';
            font-style: normal;
            font-weight: 400;
            src: url(https://cdnjs.cloudflare.com/ajax/libs/material-design-icons/4.0.0/font/MaterialIcons-Regular.ttf) format('truetype');
        }
        
        .material-icons {
            font-family: 'Material Icons';
            font-weight: normal;
            font-style: normal;
            font-size: 24px;
            line-height: 1;
            letter-spacing: normal;
            text-transform: none;
            display: inline-block;
            white-space: nowrap;
            word-wrap: normal;
            direction: ltr;
        }
        
        /* Icon panah gradient */
        .icon-arrow-gradient {
            width: 20px;
            height: 20px;
            background: linear-gradient(45deg, white, rgba(255,255,255,0.7));
            mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M5 13h11.17l-4.88 4.88c-.39.39-.39 1.03 0 1.42.39.39 1.02.39 1.41 0l6.59-6.59c.39-.39.39-1.02 0-1.41l-6.58-6.6c-.39-.39-1.02-.39-1.41 0-.39.39-.39 1.02 0 1.41L16.17 11H5c-.55 0-1 .45-1 1s.45 1 1 1z'/%3E%3C/svg%3E") no-repeat center center;
            mask-size: contain;
        }

        html, body {
            width: 100%;
            height: 100%;
            overflow: hidden;
            background-color: #000;
        }

        .video-container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1;
            overflow: hidden;
        }

        /* Overlay untuk menutupi elemen YouTube */
        .video-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 2;
        }
        
        /* Frame tambahan untuk menghindari tampilan elemen YouTube */
        .iframe-wrapper {
            position: relative;
            width: 100%;
            height: 100%;
            overflow: hidden;
        }

        iframe {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            border: 0;
        }

        @media (orientation: landscape) {
            iframe {
                object-fit: contain;
            }
        }
        
        /* Style untuk shop info */
        .shop-info {
            position: absolute;
            top: 15px;
            left: 15px;
            display: flex;
            align-items: center;
            z-index: 95;
        }
        
        .shop-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: #EE4D2D;
            display: flex;
            justify-content: center;
            align-items: center;
            color: white;
            font-weight: bold;
            border: 2px solid white;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
            overflow: hidden;
        }
        
        .shop-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 50%;
        }
        
        .shop-details {
            margin-left: 10px;
        }
        
        .shop-name {
            font-size: 14px;
            font-weight: bold;
            color: white;
            text-shadow: 0 1px 3px rgba(0, 0, 0, 0.7);
            background-color: rgba(0, 0, 0, 0.5);
            padding: 3px 8px;
            border-radius: 10px;
        }
        
        .shop-viewers {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.8);
            margin-top: 5px;
            display: flex;
            align-items: center;
            background-color: rgba(0, 0, 0, 0.5);
            padding: 2px 8px;
            border-radius: 10px;
        }
        
        .shop-viewers .material-icons {
            font-size: 14px;
            margin-right: 4px;
        }
        
        /* Style untuk bottom control bar (WhatsApp style) */
        .bottom-control-bar {
            position: absolute;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(30, 30, 30, 0.85);
            backdrop-filter: blur(10px);
            padding: 10px 15px;
            display: flex;
            align-items: center;
            gap: 10px;
            z-index: 100;
        }
        
        .cart-button {
            width: 45px;
            height: 45px;
            background-color: #ff9800;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            color: white;
            cursor: pointer;
            flex-shrink: 0;
        }
        
        .cart-badge {
            position: absolute;
            top: -5px;
            right: -5px;
            background-color: #4CAF50;
            color: white;
            font-size: 12px;
            padding: 2px 6px;
            border-radius: 10px;
            font-weight: bold;
        }
        
        .comment-input-container {
            flex: 1;
            display: flex;
            background-color: #2A3942;
            border-radius: 20px;
            padding: 5px 12px;
            align-items: center;
            position: relative;
            max-width: 70%;
            height: 40px;
        }
        
        .comment-input-container input {
            flex: 1;
            border: none;
            background: transparent;
            color: white;
            padding: 5px;
            outline: none;
            font-size: 13px;
        }
        
        .send-button {
            background-color: #EE4D2D;
            border: none;
            width: 32px;
            height: 32px;
            border-radius: 50%;
            color: white;
            display: flex;
            justify-content: center;
            align-items: center;
            cursor: pointer;
            font-size: 14px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
            transition: all 0.2s ease;
            opacity: 0;
            position: absolute;
            right: 4px;
            transform: scale(0.8);
            pointer-events: none;
        }
        
        .send-button.active {
            opacity: 1;
            transform: scale(1);
            pointer-events: auto;
        }
        
        .send-button:hover {
            transform: scale(1.05);
            box-shadow: 0 3px 8px rgba(0, 0, 0, 0.3);
        }
        
        .send-button:active {
            transform: scale(0.95);
        }
        
        /* Style untuk Love Icon dengan Gradient Glow (Style No. 2) */
        .love-icon-gradient {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: linear-gradient(45deg, #ff416c, #ff4b2b);
            display: flex;
            justify-content: center;
            align-items: center;
            box-shadow: 0 0 10px rgba(255, 75, 43, 0.7);
            cursor: pointer;
            transition: all 0.3s ease;
            flex-shrink: 0;
            margin-left: 10px;
            position: relative; /* Untuk posisi absolut love counter */
        }
        
        .love-icon-gradient:hover {
            transform: scale(1.1);
            box-shadow: 0 0 15px rgba(255, 75, 43, 0.9);
        }
        
        .love-icon-gradient.active {
            animation: pulse 0.5s ease;
        }
        
        /* Counter untuk total likes */
        .love-total-count {
            position: absolute;
            top: -8px;
            right: -8px;
            background-color: #EE4D2D;
            color: white;
            font-size: 11px;
            font-weight: bold;
            min-width: 18px;
            height: 18px;
            text-align: center;
            border-radius: 10px;
            padding: 1px 5px;
            border: 2px solid rgba(30, 30, 30, 0.85);
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
            display: flex;
            justify-content: center;
            align-items: center;
            transition: all 0.3s ease;
        }
        
        /* Animasi untuk counter ketika bertambah */
        .love-total-count.pulse {
            animation: counterPulse 0.5s ease;
        }
        
        @keyframes counterPulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.35); }
            100% { transform: scale(1); }
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.2); }
            100% { transform: scale(1); }
        }
        
        .cart-icon {
            width: 50px;
            height: 50px;
            background-color: rgba(255, 77, 77, 0.9);
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            color: white;
            cursor: pointer;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
            transition: all 0.3s ease;
        }
        
        .cart-icon:hover, .cart-icon:active {
            transform: scale(1.1);
        }
        
        .cart-text {
            color: white;
            font-size: 12px;
            margin-top: 5px;
            background-color: rgba(0, 0, 0, 0.5);
            padding: 3px 8px;
            border-radius: 10px;
        }
        
        .cart-badge {
            position: absolute;
            top: -5px;
            right: -5px;
            background-color: #ffeb3b;
            color: #333;
            font-size: 12px;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            font-weight: bold;
        }
        
        /* Style 6: Floating with Animation */
        .cart-floating {
            animation: float 2s ease-in-out infinite;
            background-color: #ff9800;
        }
        
        .cart-badge-pulse {
            width: auto;
            height: auto;
            padding: 2px 5px;
            border-radius: 10px;
            background-color: #4CAF50;
            color: white;
            animation: pulse 1.5s infinite;
        }
        
        @keyframes float {
            0% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
            100% { transform: translateY(0px); }
        }

        /* Styles untuk komponen komentar (Style 3 - Minimalis dengan Reaksi) */
        .comments-container {
            position: absolute;
            left: 0;
            right: 0;
            bottom: 65px; /* Di atas bottom bar */
            max-height: 280px;
            overflow-y: auto;
            background-color: transparent;
            padding: 15px;
            z-index: 90;
        }
        
        .comment {
            background-color: #202C33;
            border-radius: 12px;
            padding: 10px 12px;
            margin-bottom: 10px;
            position: relative;
            transition: all 0.2s;
            animation: pop 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            max-width: 80%;
        }
        
        .comment-right {
            margin-left: auto;
            background-color: #005C4B; /* Warna pesan terkirim WhatsApp */
            margin-right: 10px;
        }
        
        .comment-left {
            margin-right: auto;
            margin-left: 10px;
        }

        .comments-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .comments-list {
            max-height: 280px;
            overflow-y: auto;
            margin-bottom: 10px;
        }
        
        .comments-header h3 {
            margin: 0;
            font-size: 16px;
            font-weight: 500;
        }
        
        .comments-count {
            background-color: #EE4D2D; /* Warna Shopee */
            color: white;
            border-radius: 12px;
            padding: 2px 8px;
            font-size: 12px;
            font-weight: bold;
        }

        .comment {
            background-color: #202C33;
            border-radius: 12px;
            padding: 10px 12px;
            margin-bottom: 10px;
            position: relative;
            transition: all 0.2s;
            animation: pop 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        }
        
        @keyframes pop {
            0% { transform: scale(0.8); opacity: 0; }
            100% { transform: scale(1); opacity: 1; }
        }

        .comment:hover {
            background-color: #263238;
        }

        .comment-user {
            display: flex;
            align-items: center;
            margin-bottom: 5px;
        }

        .user-name {
            font-weight: bold;
            font-size: 14px;
            color: #E9EDF0;
        }

        .user-badge {
            background-color: #EE4D2D; /* Warna Shopee */
            color: white;
            font-size: 9px;
            padding: 2px 5px;
            border-radius: 8px;
            margin-left: 8px;
            text-transform: uppercase;
        }

        .comment-text {
            font-size: 14px;
            color: #D1D7DB;
            line-height: 1.4;
        }

        .comment-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 8px;
            font-size: 11px;
            color: #8696A0;
        }
        
        .reaction {
            display: flex;
            align-items: center;
        }
        
        .reaction span {
            background-color: rgba(255, 255, 255, 0.1);
            padding: 3px 8px;
            border-radius: 10px;
            margin-right: 5px;
            cursor: pointer;
        }
        
        .reaction span:hover {
            background-color: rgba(255, 255, 255, 0.2);
        }

        .comment-input {
            display: flex;
            margin-top: 15px;
            background-color: #2A3942;
            border-radius: 20px;
            padding: 8px 15px;
            align-items: center;
        }

        .comment-input input {
            flex: 1;
            border: none;
            background: transparent;
            color: white;
            padding: 8px 5px;
            outline: none;
            font-size: 14px;
        }

        .comment-input button {
            background-color: #EE4D2D; /* Warna Shopee */
            border: none;
            width: 32px;
            height: 32px;
            border-radius: 50%;
            color: white;
            display: flex;
            justify-content: center;
            align-items: center;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.2s;
        }
        
        .comment-input button:hover {
            background-color: #d04224; /* Warna Shopee yang lebih gelap */
            transform: scale(1.05);
        }
        
        /* Animasi notifikasi untuk komentar baru */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .new-comment {
            animation: fadeIn 0.3s ease-out;
        }
        
        /* Animasi love floating */
        @keyframes floatUp {
            0% { transform: translateY(0) scale(0.8); opacity: 0.7; }
            70% { opacity: 0.7; }
            100% { transform: translateY(-150px) scale(1.8); opacity: 0; }
        }
        
        /* Style untuk tombol play */
        .play-button {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 80px;
            height: 80px;
            background-color: rgba(238, 77, 45, 0.8);
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            color: white;
            font-size: 36px;
            cursor: pointer;
            z-index: 100;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.5);
            transition: all 0.3s ease;
        }
        
        .play-button:hover {
            background-color: rgba(238, 77, 45, 1);
            transform: translate(-50%, -50%) scale(1.1);
        }
        
        .play-button.hidden {
            opacity: 0;
            pointer-events: none;
            transform: translate(-50%, -50%) scale(0.5);
        }
        
        /* Menghilangkan icon play YouTube dari tengah video */
        .ytp-large-play-button {
            display: none !important;
            opacity: 0 !important;
            visibility: hidden !important;
        }
        
        /* Style untuk popup produk */
        .product-popup {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background-color: #fff;
            border-top-left-radius: 15px;
            border-top-right-radius: 15px;
            z-index: 200;
            box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.2);
            transform: translateY(100%);
            transition: transform 0.3s ease-out;
            height: 80vh;
            max-height: 80vh;
            display: flex;
            flex-direction: column;
        }
        
        .product-popup.active {
            transform: translateY(0);
        }
        
        .popup-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 199;
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.3s ease;
        }
        
        .popup-overlay.active {
            opacity: 1;
            visibility: visible;
        }
    /* Styles untuk daftar produk - dari kode 2 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
        }
        
        .container-s2 {
            max-width: 500px;
            margin: 5px 5px 0px 5px;
            background-color: #fff;
            height: 530px; /* Fixed height to enable scrolling */
            overflow-y: auto; /* Enable vertical scrolling */
            border: 1px solid #ddd;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            position: relative; /* Untuk menampung search-again-btn */
            padding-top: 40px; /* Beri ruang untuk tombol Cari lagi */
            flex: 1;
        }
        
        .product-list {
            width: 100%;
        }
        
        .product-item {
            padding: 16px;
            position: relative;
            display: flex;
            border-bottom: 1px solid #f5f5f5;
            width: 100%;
            cursor: pointer; /* Menambahkan cursor pointer untuk menunjukkan area bisa diklik */
            transition: background-color 0.2s; /* Efek transisi ketika hover */
        }
        
        .product-item:hover {
            background-color: #f9f9f9; /* Warna latar belakang saat hover */
        }
        
        .product-item:last-child {
            border-bottom: none;
        }
        
        /* Label Live dengan Gelombang Suara */
        .live-discount-label {
          display: flex;
          height: 20px;
          width: fit-content;
          font-family: Arial, sans-serif;
          margin-bottom: 5px;
          border: 1px solid #FF4D4D;
          overflow: hidden;
          margin-left: -5px; /* Geser ke kiri */
        }
        
        .live-label {
          background-color: #FF4D4D;
          color: white;
          font-weight: bold;
          font-size: 10px;
          padding: 0 6px 0 20px;
          display: flex;
          align-items: center;
          justify-content: center;
          position: relative;
        }
        
        .discount-label {
          background-color: white;
          color: #FF4D4D;
          font-weight: bold;
          font-size: 10px;
          padding: 0 6px;
          display: flex;
          align-items: center;
          justify-content: center;
        }
        
        .sound-wave {
          position: absolute;
          left: 6px;
          display: flex;
          gap: 1.5px;
          height: 10px;
          align-items: center;
        }
        
        .wave-bar {
          width: 1px;
          background-color: white;
          border-radius: 0.5px;
        }
        
        .wave-bar:nth-child(1) {
          height: 6px;
          animation: sound-wave 1s infinite alternate;
        }
        
        .wave-bar:nth-child(2) {
          height: 9px;
          animation: sound-wave 1s infinite alternate 0.2s;
        }
        
        .wave-bar:nth-child(3) {
          height: 5px;
          animation: sound-wave 1s infinite alternate 0.4s;
        }
        
        @keyframes sound-wave {
          0% {
            height: 2px;
            transform: translateY(3.5px);
          }
          100% {
            height: 100%;
            transform: translateY(0);
          }
        }
        
        /* Product Number Badge */
        .number-badge {
            position: absolute;
            left: 16px;
            top: 16px;
            width: 28px;
            height: 28px;
            background-color: rgba(0, 0, 0, 0.6);
            color: white;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 14px;
            z-index: 2;
        }
        
        /* Product Image & Discount - Changed position to top right */
        .product-img-container-s2 {
            width: 100px;
            min-width: 100px;
            height: 100px;
            position: relative;
            margin-right: 16px;
            margin-left: 10; /* Added to move image left */
        }
        
        .product-discount {
            position: absolute;
            top: 0;
            right: 0; /* Changed from left to right */
            background-color: #EE4D2D;
            color: white;
            padding: 2px 2px;
            font-weight: bold;
            font-size: 12px;
            border-radius: 2px;
            z-index: 1;
        }
        
        .product-img {
            width: 100%;
            height: 100%;
            object-fit: contain;
            border-radius: 4px;
            border: 1px solid #f0f0f0;
        }
  
        /* Product Details */
        .product-details {
            flex: 1;
            display: flex;
            flex-direction: column;
            min-width: 0; /* Fix for text overflow */
        }
        
        .product-title {
            font-size: 16px;
            font-weight: 500;
            margin-bottom: 10px;
            color: #333;
            /* Prevent long titles from breaking layout - MODIFIED for mobile */
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 1;
            -webkit-box-orient: vertical;
            line-height: 1.3;
            margin-left: -5px; /* Geser ke kiri */
        }
        
        /* Pricing - Moved and aligned with Live label */
        .product-pricing {
            position: absolute;
            left: 0;
            bottom: 26px; /* Raised more from 20px to 26px */
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            margin-left: 126px; /* Same left margin as product details content */
        }
        
        /* Pricing container for better alignment */
        .product-pricing {
            position: absolute;
            right: 120px;
            bottom: 14px;
            display: flex;
            flex-direction: column;
            align-items: flex-start;
        }
        
        /* New pricing position next to cart button */
        .current-price {
            color: #FF6600; /* Orange color */
            font-size: 16px;
            font-weight: bold;
            white-space: nowrap;
            padding: 0;
            text-align: left;
        }
        
        .price-details {
            padding: 0;
            display: flex;
            align-items: center;
            overflow: hidden;
            max-width: 140px; /* Control max width to ensure truncation */
            margin-bottom: 4px;
        }
        
        .original-price {
            text-decoration: line-through;
            color: #999;
            font-size: 14px;
            white-space: nowrap;
            overflow: hidden;
            max-width: 100%;
            margin: 0 0 2px 0;
        }
        
        /* Ensure Live label and prices align properly */
        .live-discount-label {
            margin-left: -5px; /* Changed from 0 to -5px to geser ke kiri */
        }
        
        /* Action Buttons */
        .buy-section {
            position: absolute;
            right: 16px; /* Adjusted for better alignment */
            bottom: 16px;
            display: flex;
            align-items: center;
            gap: 4px;
        }
        
        .cart-button-s2 {
            width: 24px; /* Reduced from 32px */
            height: 24px; /* Reduced from 32px */
            border-radius: 4px;
            background-color: #FBE9E7;
            border: 1px solid #FFCCBC;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            position: relative;
            z-index: 3;
        }
        
        .cart-icon-s2 {
            color: #EE4D2D;
            font-size: 16px; /* Reduced from 18px */
        }
        
        .buy-button {
            background-color: #EE4D2D;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 4px 8px; /* Reduced from 6px 15px */
            font-weight: bold;
            font-size: 12px; /* Reduced from 14px */
            white-space: nowrap;
            cursor: pointer;
            position: relative;
            z-index: 3;
        }
        
        /* Page title and description */
        .page-header {
            text-align: center;
            padding: 20px;
            background-color: #fff;
            margin-bottom: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            max-width: 500px;
            margin: 5px 5px 5px 5px; /* Menambahkan auto untuk responsive di desktop */
            position: relative;
            height: 50px; /* Tetapkan tinggi yang konsisten */
            width: 100%; /* Memastikan responsif pada tampilan desktop */
        }
   
        /* Close button and search button */
        .header-buttons {
            position: absolute;
            top: 10px;
            right: 10px;
            display: flex;
            gap: 8px;
        }
        
        .close-button, .search-button {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background-color: #f5f5f5;
            border: none;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .close-icon, .search-icon {
            color: #666;
            font-size: 25px;
        }
        
        /* Search box - Dimodifikasi untuk tampilan sederhana */
        .search-container-s2 {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            width: 100%;
            height: 100vh;
            background-color: rgba(255, 255, 255, 0.95);
            z-index: 100;
            flex-direction: column;
            align-items: center;
            padding-top: 30px;
        }
        
        .search-input-group {
            display: flex;
            width: 80%;
            max-width: 350px;
            position: relative;
            margin-bottom: 20px;
        }
        
        .search-input {
            flex-grow: 1;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 8px;
            padding-left: 10px;
            font-size: 14px;
            width: 100%;
        }
        
        .search-btn {
            display: none;
            background-color: #EE4D2D;
            color: white;
            border: none;
            border-radius: 4px;
            margin-left: 5px;
            padding: 0 12px;
            font-size: 14px;
            cursor: pointer;
        }
        
        /* Tombol kembali di kolom pencarian */
        .back-button {
            position: absolute;
            left: -30px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 101;
        }
        
        .back-icon {
            color: #EE4D2D;
            font-size: 24px;
        }
        
        /* Tombol Cari Lagi di atas kiri daftar produk */
        .search-again-btn {
            position: absolute;
            top: 10px;
            left: 10px;
            background-color: #fff;
            border: 1px solid #EE4D2D;
            border-radius: 4px;
            padding: 4px 8px;
            font-size: 12px;
            color: #EE4D2D;
            display: none;
            align-items: center;
            gap: 5px;
            cursor: pointer;
            z-index: 10;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .search-again-icon {
            color: #EE4D2D;
            font-size: 16px;
        }
        
        .search-again-btn.active {
            display: flex;
        }
        
        .search-container-s2.active {
            display: flex;
        }
        
        /* Tambah sugesti keyword */
        .search-suggestions {
            width: 80%;
            max-width: 350px;
            display: flex;
            flex-direction: column;
            gap: 10px;
        }
        
        .suggestion-item {
            padding: 8px 12px;
            background-color: #f5f5f5;
            border-radius: 4px;
            font-size: 14px;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        
        .suggestion-item:hover {
            background-color: #e0e0e0;
        }
        
        /* No results message - DIPERBARUI AGAR TIDAK TERLALU BAWAH & TIDAK DI LUAR HALAMAN */
        .no-results-container-s2 {
            display: none;
            position: absolute;
            top: 150px; /* Ubah dari 50% menjadi nilai tetap */
            left: 50%;
            transform: translateX(-50%); /* Ubah dari translate(-50%, -50%) */
            width: 80%;
            max-width: 400px;
            text-align: center;
            z-index: 5;
            background-color: rgba(255,255,255,0.9);
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .no-results {
            color: #EE4D2D;
            font-size: 16px;
            font-weight: bold;
        }
        
        .no-results-icon {
            font-size: 36px;
            color: #EE4D2D;
            margin-bottom: 10px;
        }
        
        .no-results-container-s2.active {
            display: block;
        }
        
        /* Product counter */
        .product-counter {
            position: absolute;
            top: 10px;
            left: 10px;
            background-color: #EE4D2D;
            color: white;
            font-size: 12px;
            font-weight: bold;
            padding: 2px 8px;
            border-radius: 10px;
        }
        
        /* Scrollbar styling for better appearance */
        .container-s2::-webkit-scrollbar {
            width: 3px;
        }
        
        .container-s2::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 10px;
        }
        
        .container-s2::-webkit-scrollbar-thumb {
            background: #EE4D2D;
            border-radius: 10px;
        }
        
        .container-s2::-webkit-scrollbar-thumb:hover {
            background: #d6331d;
        }
        
        /* Perubahan tambahan: saat mode pencarian, tampilan normal (padding) */
        .container-s2.search-mode {
            padding-top: 40px;
        }
        
        /* Saat tidak ada mode pencarian, kembalikan padding normal */
        .container-s2:not(.search-mode) {
            padding-top: 0;
        }
        
        /* Judul produk tanpa label live - maksimal 2 baris */
        .product-title.no-live {
            -webkit-line-clamp: 2; /* Maksimal 2 baris untuk produk tanpa label live */
        }

        /* Media query untuk membuat header responsif di desktop */
        @media (min-width: 501px) {
            .page-header {
                width: 500px;
                margin: 10px auto;
            }
            
            .container-s2 {
                margin: 0 auto;
            }
            
            /* Pastikan pesan 'tidak ditemukan' tetap berada pada posisi yang tepat di tampilan desktop */
            .no-results-container-s2 {
                max-width: 400px;
                top: 150px; /* Posisi tetap dari atas */
            }
        }
        
        /* Styling khusus untuk popup */
        .popup-handle {
            width: 40px;
            height: 5px;
            background-color: #ddd;
            border-radius: 3px;
            margin: 10px auto;
        }
    </style>
</head>
<body>
    <div class="video-container" id="videoContainer">
        <div class="iframe-wrapper">
            <iframe 
                id="youtube-video"
                src="https://www.youtube.com/embed/CjlOl_ZI9ns?autoplay=0&mute=1&loop=1&playlist=CjlOl_ZI9ns&controls=0&showinfo=0&rel=0&modestbranding=1&playsinline=1&disablekb=1&iv_load_policy=3&fs=0"
                title="YouTube video player"
                frameborder="0"
                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
                allowfullscreen>
            </iframe>
            <div class="video-overlay" id="videoOverlay"></div>
        </div>
        
        <!-- Tombol Play ditambahkan di sini -->
        <div class="play-button" id="playButton">
            <span class="material-icons">play_arrow</span>
        </div>
        
        <!-- Shop Info - Avatar, nama toko, dan jumlah penonton -->
        <div class="shop-info">
            <div class="shop-avatar">
                <img src="https://cdn.scalev.id/Image/2ebcb98892ab4a5ea33a4cb49f42c73c.webp" alt="Shop Avatar">
            </div>
            <div class="shop-details">
                <div class="shop-name">Toko Fashion Kita</div>
                <div class="shop-viewers">
                    <span class="material-icons">visibility</span>
                    <span class="viewers-count">1.2K</span>&nbsp;&nbsp;sedang menonton
                </div>
            </div>
        </div>
        
        <!-- Container untuk WhatsApp style chat bar -->
        <div class="bottom-control-bar">
            <div class="cart-button" id="cartButton">
                <span class="material-icons">shopping_bag</span>
            </div>
            
            <div class="comment-input-container">
                <input type="text" placeholder="Tulis komentar...">
                <div class="send-button">
                    <span class="material-icons">send</span>
                </div>
            </div>
            
            <!-- Love Icon dengan Gradient Glow dan Counter -->
            <div class="love-icon-gradient">
                <span class="material-icons" style="color: white;">favorite</span>
                <div class="love-total-count">200</div>
            </div>
        </div>
        
        <!-- Container untuk komentar -->
        <div class="comments-container">
            <div class="comments-list">
                <!-- Comments will be added here dynamically -->
            </div>
        </div>
    </div>
    
    <!-- Popup overlay -->
    <div class="popup-overlay" id="popupOverlay"></div>
    
    <!-- Popup daftar produk -->
    <div class="product-popup" id="productPopup">
        <div class="popup-handle"></div>
        <div class="page-header">
            <div class="product-counter">10 Produk</div>
            <div class="header-buttons">
                <button class="search-button">
                    <span class="material-icons search-icon">search</span>
                </button>
                <button class="close-button" id="closePopupButton">
                    <span class="material-icons close-icon">close</span>
                </button>
            </div>
            <div class="search-container-s2">
                <div class="search-input-group">
                    <button class="back-button">
                        <span class="material-icons back-icon">arrow_back</span>
                    </button>
                    <input type="text" class="search-input" placeholder="Cari produk...">
                    <button class="search-btn">Cari</button>
                </div>
                <div class="search-suggestions">
                    <div class="suggestion-item">Keju</div>
                    <div class="suggestion-item">Gula</div>
                    <div class="suggestion-item">Garam</div>
                    <div class="suggestion-item">Kopi</div>
                    <div class="suggestion-item">Minyak Goreng</div>
                    <div class="suggestion-item">Teh Celup</div>
                    <div class="suggestion-item">Snack</div>
                </div>
            </div>
        </div>
        
        <div class="no-results-container-s2">
            <div class="no-results-icon">
                <span class="material-icons">search_off</span>
            </div>
            <div class="no-results">Produk tidak ditemukan!</div>
        </div>
        
        <div class="container-s2">
            <!-- Tombol Cari Lagi di atas kiri daftar produk -->
            <button class="search-again-btn">
                <span class="material-icons search-again-icon">arrow_back</span>
                <span>Cari lagi</span>
            </button>
            
            <div class="product-list">
                <!-- Product 1 -->
                <div class="product-item" data-link="https://facebook.com">
                    <div class="number-badge">1</div>
                    <div class="product-img-container-s2">
                        <div class="product-discount">-57%</div>
                        <img class="product-img" src="https://cdn.scalev.id/Image/2ebcb98892ab4a5ea33a4cb49f42c73c.webp" alt="WINCheez Keju">
                    </div>
                    <div class="product-details">
                        <div class="product-title">WINCheez Keju GOLD 170 Gram</div>
                        <!-- LABEL LIVE+DISKON DENGAN UKURAN LEBIH KECIL -->
                        <div class="live-discount-label">
                          <div class="live-label">
                            <div class="sound-wave">
                              <span class="wave-bar"></span>
                              <span class="wave-bar"></span>
                              <span class="wave-bar"></span>
                            </div>
                            <span>Live</span>
                          </div>
                          <div class="discount-label">Diskon 12%</div>
                        </div>
                        
                        <div class="product-pricing">
                            <div class="current-price">Rp10.800.000</div>
                            <div class="price-details">
                                <div class="original-price">Rp15.077.000</div>
                                
                            </div>
                        </div>
                    </div>
                    
                    
                    <div class="buy-section">
                        <button class="cart-button-s2">
                            <span class="material-icons cart-icon-s2">shopping_cart</span>
                        </button>
                        <button class="buy-button">Beli Sekarang</button>
                    </div>
                </div>
                
                <!-- Product 2 -->
                <div class="product-item" data-link="https://google.com">
                    <div class="number-badge">2</div>
                    <div class="product-img-container-s2">
                        <div class="product-discount">-15%</div>
                        <img class="product-img" src="https://cdn.scalev.id/Image/2ebcb98892ab4a5ea33a4cb49f42c73c.webp" alt="GULA ROSE">
                    </div>
                    <div class="product-details">
                        <div class="product-title">GULA ROSE BRAND 1 KG</div>
                        <!-- LABEL LIVE+DISKON DENGAN UKURAN LEBIH KECIL -->
                        <div class="live-discount-label">
                          <div class="live-label">
                            <div class="sound-wave">
                              <span class="wave-bar"></span>
                              <span class="wave-bar"></span>
                              <span class="wave-bar"></span>
                            </div>
                            <span>Live</span>
                          </div>
                          <div class="discount-label">Diskon 12%</div>
                        </div>
                        
                        <div class="product-pricing">
                            <div class="current-price">Rp21.250</div>
                            <div class="price-details">
                                <div class="original-price">Rp25.000</div>
                                
                            </div>
                        </div>
                    </div>
                    
                   
                    <div class="buy-section">
                        <button class="cart-button-s2">
                            <span class="material-icons cart-icon-s2">shopping_cart</span>
                        </button>
                        <button class="buy-button">Beli Sekarang</button>
                    </div>
                </div>
                
                <!-- Product 3 -->
                <div class="product-item" data-link="https://instagram.com">
                    <div class="number-badge">3</div>
                    <div class="product-img-container-s2">
                        <div class="product-discount">-52%</div>
                        <img class="product-img" src="https://cdn.scalev.id/Image/2ebcb98892ab4a5ea33a4cb49f42c73c.webp" alt="Garam Masak">
                    </div>
                    <div class="product-details">
                        <div class="product-title no-live">10 Pcs Garam Masak/Garam Dapur/ Garam beryodium 90-100gr</div>
                        <div class="product-pricing">
                            <div class="current-price">Rp4.800</div>
                            <div class="price-details">
                                <div class="original-price">Rp10.000</div>
                                
                            </div>
                        </div>
                    </div>
                    
                    <div class="buy-section">
                        <button class="cart-button-s2">
                            <span class="material-icons cart-icon-s2">shopping_cart</span>
                        </button>
                        <button class="buy-button">Beli Sekarang</button>
                    </div>
                </div>
                
                <!-- Product 4 -->
                <div class="product-item" data-link="https://twitter.com">
                    <div class="number-badge">4</div>
                    <div class="product-img-container-s2">
                        <div class="product-discount">-25%</div>
                        <img class="product-img" src="https://cdn.scalev.id/Image/2ebcb98892ab4a5ea33a4cb49f42c73c.webp" alt="Kopi Sachet">
                    </div>
                    <div class="product-details">
                        <div class="product-title no-live">Kopi Sachet Premium Mix 10 Pcs</div>
                        <div class="product-pricing">
                            <div class="current-price">Rp15.000</div>
                            <div class="price-details">
                                <div class="original-price">Rp20.000</div>
                                
                            </div>
                        </div>
                    </div>
                   
                    <div class="buy-section">
                        <button class="cart-button-s2">
                            <span class="material-icons cart-icon-s2">shopping_cart</span>
                        </button>
                        <button class="buy-button">Beli Sekarang</button>
                    </div>
                </div>
                
                <!-- Product 5 -->
                <div class="product-item" data-link="https://linkedin.com">
                    <div class="number-badge">5</div>
                    <div class="product-img-container-s2">
                        <div class="product-discount">-30%</div>
                        <img class="product-img" src="https://cdn.scalev.id/Image/2ebcb98892ab4a5ea33a4cb49f42c73c.webp" alt="Mie Instan">
                    </div>
                    <div class="product-details">
                        <div class="product-title no-live">Mie Instan Special Rasa Ayam Pedas Paket 5 Pcs</div>
                        <div class="product-pricing">
                            <div class="current-price">Rp12.500</div>
                            <div class="price-details">
                                <div class="original-price">Rp17.500</div>
                                
                            </div>
                        </div>
                    </div>
                    
                    <div class="buy-section">
                        <button class="cart-button-s2">
                            <span class="material-icons cart-icon-s2">shopping_cart</span>
                        </button>
                        <button class="buy-button">Beli Sekarang</button>
                    </div>
                </div>
                
                <!-- Product 6 -->
                <div class="product-item" data-link="https://pinterest.com">
                    <div class="number-badge">6</div>
                    <div class="product-img-container-s2">
                        <div class="product-discount">-40%</div>
                        <img class="product-img" src="https://cdn.scalev.id/Image/2ebcb98892ab4a5ea33a4cb49f42c73c.webp" alt="Teh Celup">
                    </div>
                    <div class="product-details">
                       <div class="product-title no-live">Teh Celup Premium Box Isi 25 Sachet</div>
                        <div class="product-pricing">
                            <div class="current-price">Rp9.000</div>
                            <div class="price-details">
                                <div class="original-price">Rp15.000</div>
                             
                            </div>
                        </div>
                    </div>
                    
                    <div class="buy-section">
                        <button class="cart-button-s2">
                            <span class="material-icons cart-icon-s2">shopping_cart</span>
                        </button>
                        <button class="buy-button">Beli Sekarang</button>
                    </div>
                </div>
                
                <!-- Product 7 -->
                <div class="product-item" data-link="https://youtube.com">
                    <div class="number-badge">7</div>
                    <div class="product-img-container-s2">
                        <div class="product-discount">-35%</div>
                        <img class="product-img" src="https://cdn.scalev.id/Image/2ebcb98892ab4a5ea33a4cb49f42c73c.webp" alt="Snack Keripik">
                    </div>
                    <div class="product-details">
                        <div class="product-title no-live">Snack Keripik Kentang Rasa BBQ Jumbo Pack</div>
                        <div class="product-pricing">
                            <div class="current-price">Rp18.500</div>
                            <div class="price-details">
                                <div class="original-price">Rp28.500</div>
                                
                            </div>
                        </div>
                    </div>
                    
                    <div class="buy-section">
                        <button class="cart-button-s2">
                            <span class="material-icons cart-icon-s2">shopping_cart</span>
                        </button>
                        <button class="buy-button">Beli Sekarang</button>
                    </div>
                </div>
                
                <!-- Product 8 -->
                <div class="product-item" data-link="https://github.com">
                    <div class="number-badge">8</div>
                    <div class="product-img-container-s2">
                        <div class="product-discount">-60%</div>
                        <img class="product-img" src="https://cdn.scalev.id/Image/2ebcb98892ab4a5ea33a4cb49f42c73c.webp" alt="Coklat Batang">
                    </div>
                    <div class="product-details">
                        <div class="product-title no-live">Coklat Batang Premium 100 gram Dark Chocolate</div>
                        <div class="product-pricing">
                            <div class="current-price">Rp16.000</div>
                            <div class="price-details">
                                <div class="original-price">Rp40.000</div>
                                
                            </div>
                        </div>
                    </div>
                    
                    <div class="buy-section">
                        <button class="cart-button-s2">
                            <span class="material-icons cart-icon-s2">shopping_cart</span>
                        </button>
                        <button class="buy-button">Beli Sekarang</button>
                    </div>
                </div>
                
                <!-- Product 9 -->
                <div class="product-item" data-link="https://medium.com">
                    <div class="number-badge">9</div>
                    <div class="product-img-container-s2">
                        <div class="product-discount">-45%</div>
                        <img class="product-img" src="https://cdn.scalev.id/Image/2ebcb98892ab4a5ea33a4cb49f42c73c.webp" alt="Susu UHT">
                    </div>
                    <div class="product-details">
                        <div class="product-title no-live">Susu UHT Full Cream 1 Liter Pack isi 6</div>
                        <div class="product-pricing">
                            <div class="current-price">Rp88.000</div>
                            <div class="price-details">
                                <div class="original-price">Rp160.000</div>
                                
                            </div>
                        </div>
                    </div>
                    
                    <div class="buy-section">
                        <button class="cart-button-s2">
                            <span class="material-icons cart-icon-s2">shopping_cart</span>
                        </button>
                        <button class="buy-button">Beli Sekarang</button>
                    </div>
                </div>
                
                <!-- Product 10 -->
                <div class="product-item" data-link="https://reddit.com">
                    <div class="number-badge">10</div>
                    <div class="product-img-container-s2">
                        <div class="product-discount">-70%</div>
                        <img class="product-img" src="https://cdn.scalev.id/Image/2ebcb98892ab4a5ea33a4cb49f42c73c.webp" alt="Minyak Goreng">
                    </div>
                    <div class="product-details">
                        <div class="product-title no-live">Minyak Goreng Premium Kemasan 2 Liter Paket Hemat</div>
                        <div class="product-pricing">
                            <div class="current-price">Rp29.000</div>
                            <div class="price-details">
                                <div class="original-price">Rp99.000</div>
                                
                            </div>
                        </div>
                    </div>
                    
                    <div class="buy-section">
                        <button class="cart-button-s2">
                            <span class="material-icons cart-icon-s2">shopping_cart</span>
                        </button>
                        <button class="buy-button">Beli Sekarang</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // Mengambil elemen DOM
        const iframe = document.getElementById('youtube-video');
        const videoOverlay = document.getElementById('videoOverlay');
        const videoContainer = document.getElementById('videoContainer');
        const playButton = document.getElementById('playButton');
        
        // YouTube Player API
        let player;
        
        // Auto fullscreen pada mobile
        window.addEventListener('load', () => {
            // Cek apakah browser mendukung screen orientation API
            if (screen.orientation) {
                // Coba masuk mode fullscreen saat page load
                try {
                    if (document.documentElement.requestFullscreen) {
                        document.documentElement.requestFullscreen();
                    }
                } catch (e) {
                    console.log('Fullscreen API tidak didukung atau membutuhkan interaksi pengguna');
                }
            }
            
            // Tambahan: Coba sembunyikan elemen YouTube yang masih terlihat
            setTimeout(() => {
                const iframe = document.getElementById('youtube-video');
                if (iframe && iframe.contentWindow) {
                    try {
                        // Mencoba menyuntikkan CSS ke dalam iframe YouTube
                        const css = `
                            .ytp-chrome-top, .ytp-chrome-bottom, .ytp-watermark, 
                            .ytp-youtube-button, .ytp-embed-title, .ytp-large-play-button, 
                            .ytp-paid-content-overlay, .ytp-pause-overlay, .ytp-suggested-action,
                            .ytp-embed, .ytp-title, .ytp-share-button, .ytp-watch-later-button, 
                            .ytp-more-videos-content {
                                display: none !important;
                                opacity: 0 !important;
                                visibility: hidden !important;
                            }
                        `;
                        
                        const style = document.createElement('style');
                        style.type = 'text/css';
                        style.appendChild(document.createTextNode(css));
                        
                        if (iframe.contentDocument) {
                            iframe.contentDocument.head.appendChild(style);
                        }
                    } catch (e) {
                        console.log('Tidak bisa mengakses iframe content:', e);
                    }
                }
            }, 1000);
        });
        
        // Load YouTube API
        function loadYouTubeAPI() {
            const tag = document.createElement('script');
            tag.src = "https://www.youtube.com/iframe_api";
            const firstScriptTag = document.getElementsByTagName('script')[0];
            firstScriptTag.parentNode.insertBefore(tag, firstScriptTag);
        }
        
        // Fungsi ini akan dipanggil secara otomatis oleh YouTube API
        function onYouTubeIframeAPIReady() {
            player = new YT.Player('youtube-video', {
                events: {
                    'onReady': onPlayerReady,
                    'onStateChange': onPlayerStateChange
                }
            });
        }
        
        // Fungsi untuk player sudah siap
        function onPlayerReady(event) {
            // Player sudah siap, dapat menerima perintah
            console.log('YouTube Player ready');
        }
        
        // Tangani perubahan status player
        function onPlayerStateChange(event) {
            // Cek jika video sudah berjalan
            if (event.data == YT.PlayerState.PLAYING) {
                // Sembunyikan tombol play
                playButton.classList.add('hidden');
            } else if (event.data == YT.PlayerState.PAUSED || event.data == YT.PlayerState.ENDED) {
                // Tampilkan tombol play lagi
                playButton.classList.remove('hidden');
            }
        }
        
        // Fungsi untuk memutar video dan suara
        function playVideo() {
            if (player && typeof player.playVideo === 'function') {
                // Putar video melalui YouTube API
                player.unMute(); // Aktifkan suara
                player.playVideo(); // Putar video
                playButton.classList.add('hidden'); // Sembunyikan tombol play
            } else {
                // Fallback jika API belum siap
                const iframeSrc = iframe.src;
                iframe.src = iframeSrc.replace('mute=1', 'mute=0').replace('autoplay=0', 'autoplay=1');
                playButton.classList.add('hidden');
            }
        }
        
        // Load YouTube API ketika halaman dimuat
        loadYouTubeAPI();
        
        // Event listener untuk tombol play
        playButton.addEventListener('click', () => {
            playVideo();
        });
        
        // Referensi elemen-elemen UI
        const cartButton = document.querySelector('.cart-button');
        const commentInput = document.querySelector('.comment-input-container input');
        const sendButton = document.querySelector('.send-button');
        const commentsList = document.querySelector('.comments-list');
        const loveButton = document.querySelector('.love-icon-gradient');
        const loveTotalCount = document.querySelector('.love-total-count');
        
        // Daftar variasi efek reakci
        const reactionVariations = [
            { icon: 'favorite', color: '#ff416c' },        // Love (merah)
            { icon: 'thumb_up', color: '#1e88e5' },        // Like (biru)
            { icon: 'sentiment_very_satisfied', color: '#fdd835' }, // Smiley (kuning)
            { icon: 'mood', color: '#ffb74d' },           // Happy (orange)
            { icon: 'celebration', color: '#ba68c8' },     // Celebration (ungu)
            { icon: 'local_fire_department', color: '#ff7043' }, // Fire (orange-merah)
            { icon: 'star', color: '#ffd54f' },           // Star (kuning emas)
            { icon: 'emoji_emotions', color: '#4caf50' }   // Emoji (hijau)
        ];
        
        // Variabel untuk menghitung jumlah love
        let loveCounter = 200; // Diubah dari 0 menjadi 200 sesuai permintaan
        
        // Event listener untuk input komentar (menampilkan/menyembunyikan tombol kirim)
        commentInput.addEventListener('input', () => {
            if (commentInput.value.trim().length > 0) {
                sendButton.classList.add('active');
            } else {
                sendButton.classList.remove('active');
            }
        });
        
        // Event listener untuk tombol kirim
        sendButton.addEventListener('click', () => {
            addNewComment();
        });
        
        // Event listener untuk tombol enter pada input
        commentInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && commentInput.value.trim().length > 0) {
                addNewComment();
            }
        });
        
        // Event listener untuk tombol love
        loveButton.addEventListener('click', (e) => {
            e.stopPropagation();
            incrementLoveCounter();
            
            // Tambahkan animasi love floating di atas tombol love
            const rect = loveButton.getBoundingClientRect();
            createRandomReaction(rect.left + rect.width/2, rect.top);
        });
        
        // PERBAIKAN: Fitur tap pada layar untuk menambahkan love tanpa fullscreen
        videoOverlay.addEventListener('click', (e) => {
            e.stopPropagation();
            e.preventDefault();
            
            // Hindari jika klik pada komponen UI lainnya
            if (e.target.closest('.bottom-control-bar') || e.target.closest('.comments-container') || e.target.closest('.shop-info') || e.target.closest('.play-button')) {
                return;
            }
            
            // Ambil posisi tombol love untuk efek
            const loveRect = loveButton.getBoundingClientRect();
            
            // Tambahkan efek love di atas tombol love (bukan di posisi klik)
            createRandomReaction(loveRect.left + loveRect.width/2, loveRect.top);
            incrementLoveCounter();
        });
        
        // Fungsi untuk menambah love counter
        function incrementLoveCounter() {
            // Increment counter
            loveCounter++;
            
            // Update tampilan counter
            loveTotalCount.textContent = loveCounter;
            
            // Tambahkan animasi pulse pada counter
            loveTotalCount.classList.add('pulse');
            
            // Tambahkan animasi pada button
            loveButton.classList.add('active');
            
            // Hilangkan kelas pulse setelah animasi selesai
            setTimeout(() => {
                loveTotalCount.classList.remove('pulse');
                loveButton.classList.remove('active');
            }, 500);
        }
        
        // Fungsi untuk membuat efek variasi reaksi floating
        function createRandomReaction(x, y) {
            // Pilih variasi reaksi secara acak
            const randomVariation = reactionVariations[Math.floor(Math.random() * reactionVariations.length)];
            
            // Membuat elemen floating
            const reactionFloat = document.createElement('div');
            reactionFloat.style.position = 'absolute';
            reactionFloat.style.left = x + 'px';
            reactionFloat.style.top = y + 'px';
            reactionFloat.style.color = randomVariation.color;
            reactionFloat.style.fontSize = Math.floor(Math.random() * 15) + 30 + 'px'; // Ukuran acak diperbesar
            reactionFloat.style.opacity = '0.9';
            reactionFloat.style.zIndex = '200';
            reactionFloat.style.pointerEvents = 'none';
            reactionFloat.innerHTML = `<span class="material-icons">${randomVariation.icon}</span>`;
            
            // Menambahkan animasi
            reactionFloat.style.animation = 'floatUp 2s ease-out forwards';
            
            // Randomize sedikit posisi horizontal
            const randomOffset = (Math.random() - 0.5) * 60; // Diperlebar
            reactionFloat.style.transform = `translateX(${randomOffset}px)`;
            
            // Menambahkan elemen ke body
            document.body.appendChild(reactionFloat);
            
            // Menghapus elemen setelah animasi selesai
            setTimeout(() => {
                reactionFloat.remove();
            }, 2000);
        }
        
        // Fungsi untuk menambahkan komentar baru
        function addNewComment() {
            const commentText = commentInput.value.trim();
            if (commentText) {
                const newComment = document.createElement('div');
                newComment.className = 'comment comment-right new-comment'; // Komentar dari user di sebelah kanan
                
                const now = new Date();
                const time = now.getHours() + ':' + (now.getMinutes() < 10 ? '0' : '') + now.getMinutes();
                newComment.innerHTML = `
                    <div class="comment-text">${commentText}</div>
                    <div class="comment-footer">
                        <div class="comment-time">${time}</div>
                    </div>
                `;
                
                // Tambahkan komentar baru ke daftar komentar
                commentsList.appendChild(newComment);
                
                // Kosongkan input
                commentInput.value = '';
                
                // Scroll ke komentar baru
                newComment.scrollIntoView({ behavior: 'smooth' });
                
                // Sembunyikan tombol kirim karena input kosong
                sendButton.classList.remove('active');
                
                // Simulasikan respon admin setelah 2 detik
                if (Math.random() > 0.5) { // 50% kemungkinan untuk mendapatkan respon
                    setTimeout(() => {
                        const adminResponses = [
                            "Terima kasih atas komentarnya! 😊",
                            "Baik, akan segera kami proses ya!",
                            "Stok masih tersedia. Silakan tambahkan ke keranjang!",
                            "Pengiriman 1-3 hari kerja ke seluruh Indonesia",
                            "Diskon 20% masih berlaku sampai akhir bulan!"
                        ];
                        
                        const adminComment = document.createElement('div');
                        adminComment.className = 'comment comment-left new-comment'; // Komentar dari admin di sebelah kiri
                        
                        const adminTime = new Date();
                        const adminTimeStr = adminTime.getHours() + ':' + (adminTime.getMinutes() < 10 ? '0' : '') + adminTime.getMinutes();
                        
                        // Pilih respon acak
                        const randomResponse = adminResponses[Math.floor(Math.random() * adminResponses.length)];
                        
                        adminComment.innerHTML = `
                            <div class="comment-user">
                                <div class="user-name">Admin</div>
                            </div>
                            <div class="comment-text">${randomResponse}</div>
                            <div class="comment-footer">
                                <div class="comment-time">${adminTimeStr}</div>
                            </div>
                        `;
                        
                        // Tambahkan komentar admin
                        commentsList.appendChild(adminComment);
                        
                        // Scroll ke komentar admin
                        adminComment.scrollIntoView({ behavior: 'smooth' });
                    }, 2000);
                }
            }
        }
        
        // Kode untuk Popup Produk
        const productPopup = document.getElementById('productPopup');
        const popupOverlay = document.getElementById('popupOverlay');
        const closePopupButton = document.getElementById('closePopupButton');
        
        // Tampilkan popup saat tombol cart diklik
        cartButton.addEventListener('click', (e) => {
            e.stopPropagation();
            productPopup.classList.add('active');
            popupOverlay.classList.add('active');
            document.body.style.overflow = 'hidden'; // Mencegah scroll pada body
        });
        
        // Tutup popup
        closePopupButton.addEventListener('click', () => {
            productPopup.classList.remove('active');
            popupOverlay.classList.remove('active');
            document.body.style.overflow = ''; // Izinkan scroll kembali
        });
        
        // Tutup popup saat overlay diklik
        popupOverlay.addEventListener('click', () => {
            productPopup.classList.remove('active');
            popupOverlay.classList.remove('active');
            document.body.style.overflow = ''; // Izinkan scroll kembali
        });
        
        // Kode untuk Search dari Kode 2
        // Action buttons
        const cartButtons = document.querySelectorAll('.cart-button-s2');
        cartButtons.forEach(btn => {
            btn.addEventListener('click', function(event) {
                event.stopPropagation(); // Mencegah event klik merambat ke item produk
                const productItem = this.closest('.product-item');
                const productTitle = productItem.querySelector('.product-title').textContent;
                alert(`${productTitle} ditambahkan ke keranjang!`);
            });
        });
        
        const buyButtons = document.querySelectorAll('.buy-button');
        buyButtons.forEach(btn => {
            btn.addEventListener('click', function(event) {
                event.stopPropagation(); // Mencegah event klik merambat ke item produk
                const productItem = this.closest('.product-item');
                const link = productItem.getAttribute('data-link');
                if (link) {
                    window.open(link, '_blank');
                }
            });
        });
        
        // Search button
        const searchButton = document.querySelector('.search-button');
        const searchContainer = document.querySelector('.search-container-s2');
        const searchInput = document.querySelector('.search-input');
        const searchBtn = document.querySelector('.search-btn');
        const noResultsContainer = document.querySelector('.no-results-container-s2');
        const productList = document.querySelector('.product-list');
        const productItems = document.querySelectorAll('.product-item');
        const container = document.querySelector('.container-s2');
        const backButton = document.querySelector('.back-button');
        const suggestionItems = document.querySelectorAll('.suggestion-item');
        const productCounter = document.querySelector('.product-counter');
        const searchAgainBtn = document.querySelector('.search-again-btn');
        
        // Tambahkan event click ke setiap item produk
        productItems.forEach(item => {
            item.addEventListener('click', function() {
                const link = this.getAttribute('data-link');
                if (link) {
                    window.open(link, '_blank');
                }
            });
        });
        
        // Variabel untuk melacak apakah saat ini dalam mode pencarian
        let isSearchMode = false;
        // Variabel untuk menyimpan kata kunci pencarian saat ini
        let currentSearchTerm = '';
        
        // Fungsi untuk menambahkan atau menghapus class search-mode pada container
        function toggleSearchMode(isActive) {
            if (isActive) {
                container.classList.add('search-mode');
            } else {
                container.classList.remove('search-mode');
            }
        }
        
        // Hitung jumlah produk yang ditampilkan dan perbarui counter
        function updateProductCounter() {
            const visibleItems = Array.from(productItems).filter(item => 
                item.style.display !== 'none'
            ).length;
            productCounter.textContent = `${visibleItems} Produk`;
        }
        
        // Panggil saat halaman dimuat
        updateProductCounter();
        
        searchButton.addEventListener('click', function() {
            searchContainer.classList.add('active');
            if (searchContainer.classList.contains('active')) {
                searchInput.focus();
                noResultsContainer.classList.remove('active');
            }
        });
        
        // Tombol panah kembali
        backButton.addEventListener('click', function() {
            searchContainer.classList.remove('active');
            searchInput.value = '';
        });
        
        // Tombol Cari Lagi
        searchAgainBtn.addEventListener('click', function() {
            // Reset pencarian
            resetSearch();
            // Sembunyikan tombol Cari Lagi
            searchAgainBtn.classList.remove('active');
            // Kembali ke tampilan utama (bukan menampilkan kolom pencarian)
            toggleSearchMode(false);
            isSearchMode = false;
        });
        
        // Sugesti keyword
        suggestionItems.forEach(item => {
            item.addEventListener('click', function() {
                const keyword = this.textContent;
                searchInput.value = keyword;
                performSearch();
                searchContainer.classList.remove('active'); // Tutup halaman pencarian
            });
        });
        
        // Search functionality
        function performSearch() {
            const searchTerm = searchInput.value.toLowerCase().trim();
            currentSearchTerm = searchTerm;
            
            if (searchTerm === '') {
                resetSearch();
                searchAgainBtn.classList.remove('active');
                toggleSearchMode(false);
                isSearchMode = false;
                return;
            }
            
            isSearchMode = true;
            toggleSearchMode(true);
            let foundMatch = false;
            
            productItems.forEach(item => {
                const title = item.querySelector('.product-title').textContent.toLowerCase();
                if (title.includes(searchTerm)) {
                    item.style.display = 'flex';
                    foundMatch = true;
                } else {
                    item.style.display = 'none';
                }
            });
            
            // Show no results message if no matches found
            if (!foundMatch) {
                noResultsContainer.classList.add('active');
                productList.style.visibility = 'hidden'; // Hide all products when no results
            } else {
                noResultsContainer.classList.remove('active');
                productList.style.visibility = 'visible';
            }
            
            // Tampilkan tombol Cari Lagi
            searchAgainBtn.classList.add('active');
            
            // Update product counter after search
            updateProductCounter();
        }
        
        // Attach search to both input and button
        searchInput.addEventListener('keyup', function(event) {
            if (event.key === 'Enter') {
                performSearch();
                searchContainer.classList.remove('active'); // Tutup halaman pencarian saat Enter
            }
            
            // Tampilkan tombol Cari saat pengguna mulai mengetik
            if (searchInput.value.trim() !== '') {
                searchBtn.style.display = 'block';
            } else {
                searchBtn.style.display = 'none';
            }
        });
        
        searchBtn.addEventListener('click', function() {
            performSearch();
            searchContainer.classList.remove('active'); // Tutup halaman pencarian saat klik Cari
        });
        
        // Reset search function
        function resetSearch() {
            productItems.forEach(item => {
                item.style.display = 'flex';
            });
            noResultsContainer.classList.remove('active');
            productList.style.visibility = 'visible';
            currentSearchTerm = '';
            isSearchMode = false;
            
            // Update product counter after reset
            updateProductCounter();
        }
        
        // Tutup pencarian saat mengklik di luar
        document.addEventListener('click', function(event) {
            const isClickInside = searchContainer.contains(event.target) || 
                                  searchButton.contains(event.target) ||
                                  searchAgainBtn.contains(event.target);
            
            if (!isClickInside && searchContainer.classList.contains('active')) {
                searchContainer.classList.remove('active');
            }
        });
    </script>
</body>
</html>
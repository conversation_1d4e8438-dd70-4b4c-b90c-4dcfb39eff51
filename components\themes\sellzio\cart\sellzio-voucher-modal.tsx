"use client"

import React, { useState } from 'react'
import { ArrowLeft, Search, X } from 'lucide-react'
import { Voucher } from './types'

interface SellzioVoucherModalProps {
  isOpen: boolean
  onClose: () => void
  onApplyVouchers: (vouchers: any[]) => void
  appliedVouchers: any[]
}

export const SellzioVoucherModal: React.FC<SellzioVoucherModalProps> = ({
  isOpen,
  onClose,
  onApplyVouchers,
  appliedVouchers
}) => {
  const [activeTab, setActiveTab] = useState('semua')
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedVouchers, setSelectedVouchers] = useState<string[]>([])
  const [showSearchSuggestions, setShowSearchSuggestions] = useState(false)

  // Sample voucher data
  const sampleVouchers: Voucher[] = [
    {
      id: 'v1',
      type: 'discount',
      title: 'Diskon 50%',
      description: 'Berlaku untuk semua produk fashion',
      amount: '50%',
      minPurchase: 'Min. Rp100.000',
      validUntil: 'Berlaku hingga 31 Des',
      isRecommended: true
    },
    {
      id: 'v2',
      type: 'shipping',
      title: 'Gratis Ongkir',
      description: 'Gratis ongkos kirim ke seluruh Indonesia',
      amount: 'GRATIS',
      minPurchase: 'Min. Rp50.000',
      validUntil: 'Berlaku hingga 25 Des'
    },
    {
      id: 'v3',
      type: 'discount',
      title: 'Diskon Rp25.000',
      description: 'Potongan langsung untuk pembelian pertama',
      amount: 'Rp25K',
      minPurchase: 'Min. Rp150.000',
      validUntil: 'Berlaku hingga 30 Des'
    },
    {
      id: 'v4',
      type: 'cashback',
      title: 'Cashback 20%',
      description: 'Cashback maksimal Rp50.000',
      amount: '20%',
      minPurchase: 'Min. Rp200.000',
      validUntil: 'Berlaku hingga 28 Des'
    }
  ]

  const tabs = [
    { id: 'semua', label: 'Semua' },
    { id: 'discount', label: 'Diskon' },
    { id: 'shipping', label: 'Gratis Ongkir' },
    { id: 'cashback', label: 'Cashback' },
    { id: 'payment', label: 'Pembayaran' }
  ]

  const searchSuggestions = [
    { keyword: 'diskon 50%', title: 'Diskon 50% Fashion', desc: 'Berlaku untuk semua produk fashion' },
    { keyword: 'gratis ongkir', title: 'Gratis Ongkir Indonesia', desc: 'Pengiriman gratis ke seluruh Indonesia' },
    { keyword: 'cashback', title: 'Cashback 20%', desc: 'Cashback maksimal Rp50.000' }
  ]

  const filteredVouchers = sampleVouchers.filter(voucher => {
    const matchesTab = activeTab === 'semua' || voucher.type === activeTab
    const matchesSearch = voucher.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         voucher.description.toLowerCase().includes(searchQuery.toLowerCase())
    return matchesTab && matchesSearch
  })

  const handleVoucherSelect = (voucherId: string) => {
    setSelectedVouchers(prev => {
      if (prev.includes(voucherId)) {
        return prev.filter(id => id !== voucherId)
      } else {
        return [...prev, voucherId]
      }
    })
  }

  const handleApplyVouchers = () => {
    const selectedVoucherData = sampleVouchers
      .filter(v => selectedVouchers.includes(v.id))
      .map(v => ({
        id: v.id,
        name: v.title,
        type: v.type === 'discount' ? 'voucher-badge-discount' : 'voucher-badge-shipping',
        amount: v.amount
      }))
    
    onApplyVouchers(selectedVoucherData)
    onClose()
  }

  const handleSearchFocus = () => {
    setShowSearchSuggestions(true)
  }

  const handleSearchBlur = () => {
    // Delay to allow clicking on suggestions
    setTimeout(() => setShowSearchSuggestions(false), 200)
  }

  const handleSuggestionClick = (keyword: string) => {
    setSearchQuery(keyword)
    setShowSearchSuggestions(false)
  }

  const getVoucherTypeClass = (type: string) => {
    switch (type) {
      case 'shipping': return 'sellzio-voucher-shipping'
      case 'cashback': return 'sellzio-voucher-cashback'
      case 'payment': return 'sellzio-voucher-payment'
      case 'bank': return 'sellzio-voucher-bank'
      default: return 'sellzio-voucher-discount'
    }
  }

  if (!isOpen) return null

  return (
    <div className="sellzio-voucher-page">
      <div className="sellzio-voucher-page-wrapper">
        {/* Header */}
        <div className="sellzio-voucher-page-header">
          <div className="sellzio-voucher-header-container">
            <button className="sellzio-voucher-back-btn" onClick={onClose}>
              <ArrowLeft size={20} />
            </button>
            <h1>Pilih Voucher</h1>
          </div>
        </div>

        {/* Content */}
        <div className="sellzio-voucher-page-content">
          <div className="sellzio-voucher-container">
            {/* Search Bar */}
            <div className="sellzio-search-bar">
              <div className="sellzio-search-input">
                <input
                  type="text"
                  placeholder="Cari voucher..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  onFocus={handleSearchFocus}
                  onBlur={handleSearchBlur}
                />
                {searchQuery && (
                  <X 
                    className="sellzio-clear-icon" 
                    size={18}
                    onClick={() => setSearchQuery('')}
                  />
                )}
                <Search className="sellzio-search-icon" size={18} />
              </div>

              {/* Search Suggestions */}
              {showSearchSuggestions && searchQuery && (
                <div className="sellzio-search-suggestions">
                  {searchSuggestions
                    .filter(s => s.keyword.toLowerCase().includes(searchQuery.toLowerCase()))
                    .map((suggestion, index) => (
                      <div 
                        key={index}
                        className="sellzio-suggestion-item"
                        onClick={() => handleSuggestionClick(suggestion.keyword)}
                      >
                        <div className="sellzio-suggestion-title">{suggestion.title}</div>
                        <div className="sellzio-suggestion-desc">{suggestion.desc}</div>
                      </div>
                    ))}
                </div>
              )}
            </div>

            {/* Tabs */}
            <div className="sellzio-tabs">
              {tabs.map(tab => (
                <button
                  key={tab.id}
                  className={`sellzio-tab ${activeTab === tab.id ? 'active' : ''}`}
                  onClick={() => setActiveTab(tab.id)}
                >
                  {tab.label}
                </button>
              ))}
            </div>

            {/* Voucher List */}
            <div className="sellzio-content-area">
              <div className="sellzio-voucher-list">
                <div className="sellzio-voucher-group">
                  <div className="sellzio-group-header">
                    <div className="sellzio-group-title">Voucher Tersedia</div>
                  </div>

                  {filteredVouchers.map(voucher => (
                    <div 
                      key={voucher.id}
                      className={`sellzio-voucher-item ${getVoucherTypeClass(voucher.type)} ${voucher.isDisabled ? 'disabled' : ''}`}
                    >
                      {voucher.isRecommended && (
                        <div className="sellzio-recommended-badge">Direkomendasikan</div>
                      )}

                      <div className="sellzio-voucher-left">
                        <div className="sellzio-voucher-amount">{voucher.amount}</div>
                        <div className="sellzio-voucher-min">{voucher.minPurchase}</div>
                      </div>

                      <div className="sellzio-voucher-right">
                        <div className="sellzio-voucher-title">{voucher.title}</div>
                        <div className="sellzio-voucher-desc">{voucher.description}</div>
                        <div className="sellzio-voucher-info">
                          <div className="sellzio-voucher-date">{voucher.validUntil}</div>
                          <div className="sellzio-circular-checkbox">
                            <input
                              type="checkbox"
                              checked={selectedVouchers.includes(voucher.id)}
                              onChange={() => handleVoucherSelect(voucher.id)}
                              disabled={voucher.isDisabled}
                            />
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}

                  {filteredVouchers.length === 0 && (
                    <div className="sellzio-no-results-card">
                      Tidak ada voucher yang sesuai dengan pencarian Anda
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="sellzio-voucher-footer">
          <div className="sellzio-voucher-footer-content">
            <button 
              className="sellzio-apply-btn"
              onClick={handleApplyVouchers}
              disabled={selectedVouchers.length === 0}
            >
              Terapkan ({selectedVouchers.length})
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

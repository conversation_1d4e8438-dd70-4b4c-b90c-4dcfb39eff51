"use client"

import React, { useState, useEffect } from 'react'
import { ArrowLeft } from 'lucide-react'
import { SellzioPaymentHeader } from './sellzio-payment-header'
import { SellzioOrderSummary } from './sellzio-order-summary'
import { SellzioPaymentMethods } from './sellzio-payment-methods'
import { SellzioPaymentFooter } from './sellzio-payment-footer'
import { SellzioToast } from '../checkout/sellzio-toast'
import { paymentAPI } from '@/lib/api/payment'
import type { PaymentGateway, PaymentMethod } from '@/lib/models/payment'

interface SellzioPaymentPageProps {
  orderData: any
  onBack: () => void
  onPaymentSuccess: (paymentData: any) => void
  onPaymentError: (error: string) => void
}

export const SellzioPaymentPage: React.FC<SellzioPaymentPageProps> = ({
  orderData,
  onBack,
  onPaymentSuccess,
  onPaymentError
}) => {
  const [paymentGateways, setPaymentGateways] = useState<PaymentGateway[]>([])
  const [selectedMethod, setSelectedMethod] = useState<PaymentMethod | null>(null)
  const [selectedGateway, setSelectedGateway] = useState<PaymentGateway | null>(null)
  const [isProcessing, setIsProcessing] = useState(false)
  const [toast, setToast] = useState<{ message: string; type: 'success' | 'error' | 'info' } | null>(null)

  useEffect(() => {
    const loadPaymentGateways = async () => {
      try {
        const gateways = await paymentAPI.getPaymentGateways()
        setPaymentGateways(gateways)
      } catch (error) {
        console.error('Error loading payment gateways:', error)
        showToast('Gagal memuat metode pembayaran', 'error')
      }
    }

    loadPaymentGateways()
  }, [])

  const showToast = (message: string, type: 'success' | 'error' | 'info') => {
    setToast({ message, type })
    setTimeout(() => setToast(null), 3000)
  }

  const handleSelectPaymentMethod = (method: PaymentMethod, gateway: PaymentGateway) => {
    setSelectedMethod(method)
    setSelectedGateway(gateway)
    showToast(`${method.name} dipilih`, 'success')
  }

  const handleProcessPayment = async () => {
    if (!selectedMethod || !selectedGateway) {
      showToast('Silakan pilih metode pembayaran', 'error')
      return
    }

    if (isProcessing) return

    setIsProcessing(true)

    try {
      const paymentRequest = {
        orderId: orderData.orderId,
        amount: orderData.total,
        paymentMethodId: selectedMethod.id,
        paymentGatewayId: selectedGateway.id,
        customerName: orderData.address.name,
        customerEmail: orderData.address.email || '<EMAIL>',
        customerPhone: orderData.address.phone,
        callbackUrl: `${window.location.origin}/api/payment/callback`,
        returnUrl: `${window.location.origin}/sellzio/payment/status`,
      }

      const response = await paymentAPI.createPayment(paymentRequest)
      
      // Handle different payment types
      if (response.redirectUrl) {
        // For e-wallet or credit card, redirect to payment gateway
        showToast('Mengalihkan ke halaman pembayaran...', 'info')
        setTimeout(() => {
          // In real implementation, redirect to payment gateway
          // window.location.href = response.redirectUrl
          
          // For demo, simulate success and redirect to status
          onPaymentSuccess(response)
        }, 2000)
      } else {
        // For virtual account, bank transfer, etc., show payment instructions
        onPaymentSuccess(response)
      }
    } catch (error) {
      console.error('Payment error:', error)
      onPaymentError('Gagal memproses pembayaran. Silakan coba lagi.')
      showToast('Gagal memproses pembayaran', 'error')
    } finally {
      setIsProcessing(false)
    }
  }

  return (
    <div
      className="sellzio-payment-page"
      style={{
        minHeight: '100vh',
        backgroundColor: '#f5f5f5',
        color: '#333',
        lineHeight: '1.6',
        fontFamily: "'Roboto', Arial, sans-serif",
        width: '100%',
        overflowX: 'hidden',
        position: 'relative',
        margin: 0,
        padding: 0
      }}
    >
      <SellzioPaymentHeader onBack={onBack} />

      <div className="sellzio-payment-container">
        <SellzioOrderSummary orderData={orderData} />
        
        <SellzioPaymentMethods
          paymentGateways={paymentGateways}
          selectedMethod={selectedMethod}
          selectedGateway={selectedGateway}
          onSelectMethod={handleSelectPaymentMethod}
        />
      </div>

      <SellzioPaymentFooter
        total={orderData.total}
        selectedMethod={selectedMethod}
        onProcessPayment={handleProcessPayment}
        isProcessing={isProcessing}
      />

      {toast && (
        <SellzioToast
          message={toast.message}
          type={toast.type}
          onClose={() => setToast(null)}
        />
      )}

      <style jsx global>{`
        .sellzio-payment-container {
          padding: 0 16px 80px 16px;
          max-width: 100%;
          margin: 0 auto;
        }

        .sellzio-payment-section {
          background: white;
          margin-bottom: 8px;
          padding: 16px;
          border-radius: 8px;
          box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .sellzio-section-header {
          margin-bottom: 16px;
          padding-bottom: 12px;
          border-bottom: 1px solid #f0f0f0;
        }

        .sellzio-section-title {
          font-size: 16px;
          font-weight: 600;
          color: #333;
          margin: 0;
        }

        @media (max-width: 768px) {
          .sellzio-payment-container {
            padding: 0 12px 80px 12px;
          }

          .sellzio-payment-section {
            margin-bottom: 6px;
            padding: 12px;
          }
        }

        /* Add bottom padding to prevent footer overlap */
        body {
          padding-bottom: 80px;
        }
      `}</style>
    </div>
  )
}

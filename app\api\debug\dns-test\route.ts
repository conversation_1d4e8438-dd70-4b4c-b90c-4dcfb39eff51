import { NextRequest, NextResponse } from 'next/server'
import { createCloudflareService } from '@/lib/services/cloudflare-dns'

export async function POST(request: NextRequest) {
  try {
    const { subdomain } = await request.json()

    if (!subdomain) {
      return NextResponse.json({
        success: false,
        error: 'Subdomain is required'
      }, { status: 400 })
    }

    // Check environment variables
    const envCheck = {
      hasApiToken: !!process.env.CLOUDFLARE_API_TOKEN,
      hasZoneId: !!process.env.CLOUDFLARE_ZONE_ID,
      vercelIp: process.env.VERCEL_IP || '***********'
    }

    console.log('🔥 DNS TEST: Environment check:', envCheck)

    if (!envCheck.hasApiToken || !envCheck.hasZoneId) {
      return NextResponse.json({
        success: false,
        error: 'Cloudflare credentials not configured',
        envCheck
      }, { status: 500 })
    }

    // Test DNS creation
    console.log('🔥 DNS TEST: Creating DNS record for:', subdomain)
    
    const cloudflareService = createCloudflareService()
    const result = await cloudflareService.createSubdomainRecord(subdomain, envCheck.vercelIp)

    console.log('🔥 DNS TEST: Result:', result)

    return NextResponse.json({
      success: result,
      subdomain,
      targetIP: envCheck.vercelIp,
      envCheck,
      message: result ? 'DNS record created successfully' : 'Failed to create DNS record'
    })

  } catch (error: any) {
    console.error('🔥 DNS TEST: Error:', error)
    return NextResponse.json({
      success: false,
      error: error.message,
      stack: error.stack
    }, { status: 500 })
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const subdomain = searchParams.get('subdomain')

    if (!subdomain) {
      return NextResponse.json({
        success: false,
        error: 'Subdomain parameter is required'
      }, { status: 400 })
    }

    // Check if DNS record exists
    console.log('🔥 DNS TEST: Checking DNS record for:', subdomain)
    
    const cloudflareService = createCloudflareService()
    const record = await cloudflareService.getRecord(subdomain)

    console.log('🔥 DNS TEST: Record found:', record)

    return NextResponse.json({
      success: true,
      subdomain,
      record,
      exists: !!record
    })

  } catch (error: any) {
    console.error('🔥 DNS TEST: Error:', error)
    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 })
  }
}

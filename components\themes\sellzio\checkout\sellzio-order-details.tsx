"use client"

import React from 'react'
import { CartItem } from '../cart/types'

interface SellzioOrderDetailsProps {
  items: CartItem[]
}

export const SellzioOrderDetails: React.FC<SellzioOrderDetailsProps> = ({
  items
}) => {
  // Group items by store
  const itemsByStore = items.reduce((acc, item) => {
    const storeId = item.storeId || item.store
    if (!acc[storeId]) {
      acc[storeId] = {
        storeName: item.store,
        items: []
      }
    }
    acc[storeId].items.push(item)
    return acc
  }, {} as Record<string, { storeName: string; items: CartItem[] }>)

  const formatPrice = (price: number) => {
    return `Rp${price.toLocaleString('id-ID')}`
  }

  return (
    <div className="sellzio-checkout-section">
      <div className="sellzio-section-title">
        <span>Detail Pesanan</span>
      </div>
      <div className="sellzio-product-container">
        {Object.entries(itemsByStore).map(([storeId, storeData]) =>
          storeData.items.map((item) => (
            <div key={item.id} className="sellzio-product-item">
              <div className="sellzio-product-shop">
                <span className="sellzio-shop-icon">
                  <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M13.5 3H2.5L3.5 7H12.5L13.5 3Z" stroke="#ee4d2d" strokeWidth="1.5" strokeLinejoin="round"/>
                    <path d="M3 7V13H13V7" stroke="#ee4d2d" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                    <path d="M6 10H10" stroke="#ee4d2d" strokeWidth="1.5" strokeLinecap="round"/>
                    <path d="M8 7V13" stroke="#ee4d2d" strokeWidth="1.5" strokeLinecap="round"/>
                  </svg>
                </span>
                <span>{storeData.storeName}</span>
              </div>
              <div className="sellzio-product-content">
                <img
                  src={item.image}
                  alt={item.name}
                  className="sellzio-product-image"
                />
                <div className="sellzio-product-details">
                  <div className="sellzio-product-name">{item.name}</div>
                  {item.variant && (
                    <div className="sellzio-product-variant">{item.variant}</div>
                  )}
                  <div className="sellzio-product-actions">
                    <div className="sellzio-product-price">
                      {formatPrice(item.price)}
                      {item.originalPrice && item.originalPrice > item.price && (
                        <span className="sellzio-price-original">
                          {formatPrice(item.originalPrice)}
                        </span>
                      )}
                    </div>
                    <div className="sellzio-quantity-display">
                      x<span className="sellzio-quantity">{item.quantity}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  )
}

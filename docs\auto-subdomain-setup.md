# Sistem Auto Subdomain Setup

## Overview

Sistem otomatis yang mengkonfigurasi **Cloudflare DNS** dan **Vercel Domain** secara bersamaan ketika tenant membuat atau mengubah subdomain.

## ✅ Status Implementasi

### **Cloudflare DNS (Otomatis)**
- ✅ Auto-create CNAME record
- ✅ Point ke `cname.vercel-dns.com`
- ✅ DNS-only (tidak proxied)
- ✅ Auto-cleanup old records

### **Vercel Domain (Otomatis)**
- ✅ Auto-add domain ke Vercel project
- ✅ Auto-remove old domain ketika subdomain berubah
- ✅ Handle domain already exists
- ✅ Non-fatal error handling
- ✅ Support wildcard domains

## Cara Kerja

### 1. Flow Otomatis
```
Tenant Update Subdomain → Database Update → Cloudflare DNS (Update/Replace) → Vercel Domain (Remove Old + Add New) → Subdomain Aktif
```

### 2. API Endpoint
**File:** `app/api/tenants/update-subdomain/route.ts`

**Proses:**
1. Validasi subdomain format
2. Check availability
3. Update database
4. **Auto-update Cloudflare DNS** (replace old record)
5. **Auto-manage Vercel domain** (remove old + add new)
6. Return success status

### 3. Environment Variables Required
```bash
# Cloudflare
CLOUDFLARE_API_TOKEN=your_token
CLOUDFLARE_ZONE_ID=your_zone_id

# Vercel
VERCEL_TOKEN=your_vercel_token
VERCEL_PROJECT_ID=prj_IMIuXcUvgE7Z9BRfwQFi1fxgkwmq
```

## Testing

### 1. Manual Test
- Go to: http://localhost:3001/admin/dashboard/debug/test-auto-setup
- Input test subdomain
- Click "Test Auto Setup"
- Verify both DNS and Vercel configured

### 2. Real Tenant Test
- Login sebagai tenant
- Go to domain settings
- Change subdomain
- Check logs untuk konfirmasi auto-setup

### 3. Verification Steps
1. **Database:** Subdomain updated
2. **Cloudflare:** CNAME record created
3. **Vercel:** Domain added to project
4. **Access:** https://subdomain.sellzio.my.id works

## Error Handling

### 1. Non-Fatal Errors
- DNS creation failure → Continue with Vercel
- Vercel addition failure → Continue with response
- Both failures → Still update database

### 2. Fatal Errors
- Invalid subdomain format
- Subdomain already taken
- Database update failure

### 3. Logging
```javascript
// Success logs
🔥 API: DNS record created successfully
🔥 API: Domain added to Vercel successfully

// Error logs (non-fatal)
🔥 API: DNS creation error (non-fatal)
🔥 API: Vercel domain addition error (non-fatal)
```

## Response Format

```javascript
{
  "success": true,
  "tenant": { /* updated tenant data */ },
  "dnsConfigured": true,      // Cloudflare status
  "vercelConfigured": true    // Vercel status
}
```

## Troubleshooting

### 1. DNS Not Created
**Symptoms:** `dnsConfigured: false`
**Causes:**
- Missing Cloudflare credentials
- Invalid API token
- Zone ID incorrect

**Solutions:**
- Check environment variables
- Verify Cloudflare API permissions
- Test with debug page

### 2. Vercel Not Configured
**Symptoms:** `vercelConfigured: false`
**Causes:**
- Missing Vercel token
- Invalid project ID
- Token permissions insufficient

**Solutions:**
- Generate new Vercel token
- Verify project ID
- Check token scopes

### 3. Subdomain Still 404
**Symptoms:** DEPLOYMENT_NOT_FOUND after setup
**Causes:**
- DNS propagation delay
- Vercel domain not verified
- Cache issues

**Solutions:**
- Wait 2-5 minutes
- Clear browser cache
- Check Vercel dashboard

## Manual Fallback

Jika auto-setup gagal, gunakan manual setup:

### 1. Cloudflare Manual
```bash
# Via debug page
POST /api/dns/create-subdomain
{
  "subdomain": "nama-subdomain"
}
```

### 2. Vercel Manual
```bash
# Via debug page
POST /api/vercel/add-domain
{
  "domain": "subdomain.sellzio.my.id"
}
```

## Best Practices

### 1. Environment Setup
- Set semua environment variables
- Test dengan debug tools
- Monitor logs untuk errors

### 2. Monitoring
- Check auto-setup success rate
- Monitor DNS propagation time
- Track Vercel domain verification

### 3. Backup Strategy
- Keep manual tools available
- Document fallback procedures
- Regular system health checks

## API Integration

### Frontend Usage
```javascript
// Tenant subdomain update
const response = await fetch('/api/tenants/update-subdomain', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    subdomain: 'new-subdomain',
    tenantId: 'tenant-uuid'
  })
})

const result = await response.json()

if (result.success) {
  console.log('Auto-setup completed:', {
    dns: result.dnsConfigured,
    vercel: result.vercelConfigured
  })
}
```

### Error Handling
```javascript
if (!result.dnsConfigured) {
  // Show DNS setup instructions
}

if (!result.vercelConfigured) {
  // Show Vercel setup instructions
}
```

## Future Enhancements

### 1. Wildcard Support
- Auto-add `*.sellzio.my.id` to Vercel
- Reduce individual domain additions

### 2. SSL Monitoring
- Check SSL certificate status
- Auto-retry failed verifications

### 3. Health Checks
- Periodic subdomain accessibility tests
- Auto-fix broken configurations

### 4. Analytics
- Track setup success rates
- Monitor performance metrics
- Alert on failures

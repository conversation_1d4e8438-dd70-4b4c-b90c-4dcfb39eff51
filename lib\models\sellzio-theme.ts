import type { TenantTheme } from "./tenant-theme"

// Tema default Sellzio
export const sellzioTheme: Partial<TenantTheme> = {
  name: "<PERSON><PERSON><PERSON>",
  colors: {
    primary: "#ee4d2d", // Shopee-like orange-red
    secondary: "#ff6b35", // Bright orange
    accent: "#f5a623", // Golden yellow
    background: "#ffffff",
    foreground: "#333333",
    muted: "#f5f5f5",
    mutedForeground: "#666666",
    border: "#e5e5e5",
    input: "#f0f0f0",
    card: "#ffffff",
    cardForeground: "#333333",
    destructive: "#dc2626",
    destructiveForeground: "#ffffff",
  },
  fonts: {
    heading: "Roboto, -apple-system, BlinkMacSystemFont, sans-serif",
    body: "Roboto, -apple-system, BlinkMacSystemFont, sans-serif",
  },
  layout: {
    header: "default",
    footer: "default",
    sidebar: "default",
  },
  customCSS: `
    /* Sellzio Custom CSS */
    .sellzio-header {
      background: linear-gradient(135deg, #ee4d2d 0%, #ff6b35 100%);
      box-shadow: 0 2px 8px rgba(238, 77, 45, 0.15);
    }
    
    .sellzio-search-container .search-input {
      border: 2px solid var(--primary);
      border-radius: 4px;
      transition: all 0.2s ease;
    }
    
    .sellzio-search-container .search-input:focus {
      border-color: var(--secondary);
      box-shadow: 0 0 0 3px rgba(238, 77, 45, 0.1);
    }
    
    .sellzio-search-container .search-icon {
      color: var(--primary);
    }
    
    .sellzio-product-card {
      border: 1px solid #e5e5e5;
      border-radius: 8px;
      transition: all 0.3s ease;
      background: white;
    }
    
    .sellzio-product-card:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(0,0,0,0.1);
      border-color: var(--primary);
    }
    
    .sellzio-badge-mall {
      background: linear-gradient(135deg, #ee4d2d, #ff6b35);
      color: white;
      font-weight: 600;
    }
    
    .sellzio-badge-star {
      background: linear-gradient(135deg, #f5a623, #ff6b35);
      color: white;
      font-weight: 600;
    }
    
    .sellzio-price {
      color: var(--primary);
      font-weight: 600;
    }
    
    .sellzio-rating-stars {
      color: #fbbf24;
    }
    
    .sellzio-shipping-badge {
      background: #10b981;
      color: white;
      border-radius: 4px;
      padding: 2px 6px;
      font-size: 11px;
    }
    
    .sellzio-facet-panel {
      background: white;
      border: 1px solid #e5e5e5;
      border-radius: 8px;
    }
    
    .sellzio-facet-header {
      background: linear-gradient(135deg, #ee4d2d, #ff6b35);
      color: white;
      padding: 16px;
      border-radius: 8px 8px 0 0;
    }
    
    .sellzio-category-item:hover {
      background: rgba(238, 77, 45, 0.05);
      color: var(--primary);
    }
    
    .sellzio-category-item.active {
      background: var(--primary);
      color: white;
    }
    
    .sellzio-flash-sale-badge {
      background: linear-gradient(135deg, #dc2626, #ee4d2d);
      color: white;
      animation: pulse 2s infinite;
    }
    
    @keyframes pulse {
      0%, 100% { opacity: 1; }
      50% { opacity: 0.8; }
    }
    
    .sellzio-cart-button {
      background: linear-gradient(135deg, #ee4d2d, #ff6b35);
      color: white;
      border: none;
      border-radius: 6px;
      padding: 12px 24px;
      font-weight: 600;
      transition: all 0.2s ease;
    }
    
    .sellzio-cart-button:hover {
      background: linear-gradient(135deg, #dc2626, #ee4d2d);
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(238, 77, 45, 0.3);
    }
    
    .sellzio-checkout-button {
      background: linear-gradient(135deg, #ee4d2d, #ff6b35);
      color: white;
      border: none;
      border-radius: 6px;
      padding: 16px 32px;
      font-weight: 600;
      font-size: 16px;
      width: 100%;
      transition: all 0.2s ease;
    }
    
    .sellzio-checkout-button:hover {
      background: linear-gradient(135deg, #dc2626, #ee4d2d);
      box-shadow: 0 4px 12px rgba(238, 77, 45, 0.3);
    }
    
    .sellzio-modal {
      border-radius: 12px;
      overflow: hidden;
      box-shadow: 0 20px 60px rgba(0,0,0,0.15);
    }
    
    .sellzio-modal-header {
      background: linear-gradient(135deg, #ee4d2d, #ff6b35);
      color: white;
      padding: 16px 20px;
    }
    
    /* Mobile optimizations */
    @media (max-width: 768px) {
      .sellzio-product-card {
        border-radius: 6px;
      }
      
      .sellzio-header {
        padding: 8px 12px;
      }
      
      .sellzio-search-container .search-input {
        border-radius: 6px;
      }
    }
    
    /* Dark mode support */
    .dark .sellzio-product-card {
      background: #1f2937;
      border-color: #374151;
      color: #f9fafb;
    }
    
    .dark .sellzio-facet-panel {
      background: #1f2937;
      border-color: #374151;
    }
    
    .dark .sellzio-modal {
      background: #1f2937;
      color: #f9fafb;
    }
  `,
}

// Sellzio theme configuration untuk tenant
export const createSellzioTenantTheme = (tenantId: string): TenantTheme => ({
  id: `sellzio-theme-${tenantId}`,
  tenantId,
  name: "Sellzio Theme",
  isActive: true,
  colors: sellzioTheme.colors!,
  fonts: sellzioTheme.fonts!,
  logo: {
    light: "/sellzio-logo-light.png",
    dark: "/sellzio-logo-dark.png",
    favicon: "/sellzio-favicon.ico",
  },
  layout: sellzioTheme.layout!,
  customCSS: sellzioTheme.customCSS || "",
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
})

// Sellzio theme variants untuk berbagai use case
export const sellzioThemeVariants = {
  default: sellzioTheme,
  
  // Variant untuk marketplace yang lebih formal
  professional: {
    ...sellzioTheme,
    name: "Sellzio Professional",
    colors: {
      ...sellzioTheme.colors!,
      primary: "#1e40af", // Blue
      secondary: "#3b82f6",
      accent: "#f59e0b",
    },
  },
  
  // Variant untuk fashion/lifestyle
  fashion: {
    ...sellzioTheme,
    name: "Sellzio Fashion",
    colors: {
      ...sellzioTheme.colors!,
      primary: "#ec4899", // Pink
      secondary: "#f472b6",
      accent: "#8b5cf6",
    },
  },
  
  // Variant untuk electronics/tech
  tech: {
    ...sellzioTheme,
    name: "Sellzio Tech",
    colors: {
      ...sellzioTheme.colors!,
      primary: "#059669", // Green
      secondary: "#10b981",
      accent: "#f59e0b",
    },
  },
}

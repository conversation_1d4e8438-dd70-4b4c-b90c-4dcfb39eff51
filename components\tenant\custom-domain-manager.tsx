"use client"

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { Separator } from '@/components/ui/separator'
import { Copy, ExternalLink, CheckCircle, AlertCircle, Globe, Settings } from 'lucide-react'
import { useToast } from '@/hooks/use-toast'

interface CustomDomainManagerProps {
  tenantId: string
  currentDomain?: string
}

interface DomainStatus {
  domain: string
  verified: boolean
  ssl: boolean
  lastChecked: string
}

export function CustomDomainManager({ tenantId, currentDomain }: CustomDomainManagerProps) {
  const [customDomain, setCustomDomain] = useState(currentDomain || '')
  const [newDomain, setNewDomain] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [isVerifying, setIsVerifying] = useState(false)
  const [domainStatus, setDomainStatus] = useState<DomainStatus | null>(null)
  const { toast } = useToast()

  // Default subdomain
  const defaultSubdomain = `${tenantId}.sellzio.com`

  useEffect(() => {
    if (customDomain) {
      checkDomainStatus(customDomain)
    }
  }, [customDomain])

  const checkDomainStatus = async (domain: string) => {
    try {
      console.log('🔥 DOMAIN: Checking domain status for:', domain)

      // Real domain verification check
      const response = await fetch(`/api/tenants/verify-domain?domain=${encodeURIComponent(domain)}`)

      if (response.ok) {
        const data = await response.json()
        setDomainStatus({
          domain,
          verified: data.verified || false,
          ssl: data.ssl || false,
          lastChecked: new Date().toISOString()
        })
        console.log('🔥 DOMAIN: Status check result:', data)
      } else {
        // Fallback status if API fails
        setDomainStatus({
          domain,
          verified: false,
          ssl: false,
          lastChecked: new Date().toISOString()
        })
      }
    } catch (error) {
      console.error('🔥 DOMAIN: Error checking domain status:', error)
      // Fallback status on error
      setDomainStatus({
        domain,
        verified: false,
        ssl: false,
        lastChecked: new Date().toISOString()
      })
    }
  }

  const handleSetCustomDomain = async () => {
    if (!newDomain.trim()) {
      toast({
        title: "Error",
        description: "Please enter a domain name",
        variant: "destructive"
      })
      return
    }

    setIsLoading(true)
    try {
      const response = await fetch('/api/tenants/lookup-domain', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          domain: newDomain.trim(),
          tenantId
        })
      })

      if (!response.ok) {
        throw new Error('Failed to set custom domain')
      }

      const data = await response.json()
      
      if (data.success) {
        setCustomDomain(newDomain.trim())
        setNewDomain('')
        toast({
          title: "Success",
          description: "Custom domain has been set successfully",
        })
      }
    } catch (error) {
      console.error('Error setting custom domain:', error)
      toast({
        title: "Error",
        description: "Failed to set custom domain. Please try again.",
        variant: "destructive"
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleVerifyDomain = async () => {
    if (!customDomain) return

    setIsVerifying(true)
    try {
      console.log('🔥 DOMAIN: Starting domain verification for:', customDomain)

      // Real domain verification
      const response = await fetch('/api/tenants/verify-domain', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          domain: customDomain,
          tenantId
        })
      })

      const data = await response.json()

      if (response.ok && data.verified) {
        await checkDomainStatus(customDomain)

        toast({
          title: "Domain Verified",
          description: "Your custom domain has been verified successfully",
        })
      } else {
        toast({
          title: "Verification Failed",
          description: data.message || "Failed to verify domain. Please check your DNS settings.",
          variant: "destructive"
        })
      }
    } catch (error) {
      console.error('🔥 DOMAIN: Error verifying domain:', error)
      toast({
        title: "Verification Failed",
        description: "Failed to verify domain. Please check your DNS settings.",
        variant: "destructive"
      })
    } finally {
      setIsVerifying(false)
    }
  }

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
    toast({
      title: "Copied",
      description: "Copied to clipboard",
    })
  }

  return (
    <div className="space-y-6">
      {/* Default Subdomain */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Globe className="h-5 w-5" />
            Default Subdomain
          </CardTitle>
          <CardDescription>
            Your store is automatically available on our platform subdomain
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center gap-2">
            <Input 
              value={defaultSubdomain} 
              readOnly 
              className="flex-1"
            />
            <Button 
              variant="outline" 
              size="sm"
              onClick={() => copyToClipboard(defaultSubdomain)}
            >
              <Copy className="h-4 w-4" />
            </Button>
            <Button 
              variant="outline" 
              size="sm"
              onClick={() => window.open(`https://${defaultSubdomain}`, '_blank')}
            >
              <ExternalLink className="h-4 w-4" />
            </Button>
          </div>
          <Badge variant="secondary" className="bg-green-50 text-green-700 border-green-200">
            <CheckCircle className="h-3 w-3 mr-1" />
            Active
          </Badge>
        </CardContent>
      </Card>

      {/* Custom Domain */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Custom Domain
          </CardTitle>
          <CardDescription>
            Use your own domain for better branding and SEO
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Current Custom Domain */}
          {customDomain && (
            <div className="space-y-4">
              <div>
                <Label>Current Custom Domain</Label>
                <div className="flex items-center gap-2 mt-1">
                  <Input 
                    value={customDomain} 
                    readOnly 
                    className="flex-1"
                  />
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => copyToClipboard(customDomain)}
                  >
                    <Copy className="h-4 w-4" />
                  </Button>
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => window.open(`https://${customDomain}`, '_blank')}
                  >
                    <ExternalLink className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              {/* Domain Status */}
              {domainStatus && (
                <div className="flex items-center gap-4">
                  <Badge 
                    variant={domainStatus.verified ? "default" : "destructive"}
                    className={domainStatus.verified ? "bg-green-50 text-green-700 border-green-200" : ""}
                  >
                    {domainStatus.verified ? (
                      <>
                        <CheckCircle className="h-3 w-3 mr-1" />
                        Verified
                      </>
                    ) : (
                      <>
                        <AlertCircle className="h-3 w-3 mr-1" />
                        Not Verified
                      </>
                    )}
                  </Badge>
                  
                  <Badge 
                    variant={domainStatus.ssl ? "default" : "secondary"}
                    className={domainStatus.ssl ? "bg-blue-50 text-blue-700 border-blue-200" : ""}
                  >
                    {domainStatus.ssl ? 'SSL Active' : 'SSL Pending'}
                  </Badge>

                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={handleVerifyDomain}
                    disabled={isVerifying}
                  >
                    {isVerifying ? 'Verifying...' : 'Re-verify'}
                  </Button>
                </div>
              )}

              <Separator />
            </div>
          )}

          {/* Add New Domain */}
          <div className="space-y-4">
            <div>
              <Label htmlFor="new-domain">
                {customDomain ? 'Change Custom Domain' : 'Add Custom Domain'}
              </Label>
              <div className="flex gap-2 mt-1">
                <Input
                  id="new-domain"
                  placeholder="example.com"
                  value={newDomain}
                  onChange={(e) => setNewDomain(e.target.value)}
                  className="flex-1"
                />
                <Button 
                  onClick={handleSetCustomDomain}
                  disabled={isLoading || !newDomain.trim()}
                >
                  {isLoading ? 'Setting...' : 'Set Domain'}
                </Button>
              </div>
            </div>

            {/* DNS Instructions */}
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>DNS Configuration Required</AlertTitle>
              <AlertDescription className="mt-2">
                <p className="mb-3">To use your custom domain, configure DNS records in your domain provider (e.g., Cloudflare):</p>

                {/* Main Domain CNAME */}
                <div className="space-y-3">
                  <div>
                    <p className="text-sm font-medium mb-1">For main domain (example.com):</p>
                    <div className="bg-gray-50 p-3 rounded text-sm font-mono border">
                      <div className="grid grid-cols-3 gap-2">
                        <div><strong>Type:</strong> CNAME</div>
                        <div><strong>Name:</strong> @</div>
                        <div><strong>Target:</strong> {tenantId}.sellzio.com</div>
                      </div>
                    </div>
                  </div>

                  {/* WWW Subdomain */}
                  <div>
                    <p className="text-sm font-medium mb-1">For www subdomain (www.example.com):</p>
                    <div className="bg-gray-50 p-3 rounded text-sm font-mono border">
                      <div className="grid grid-cols-3 gap-2">
                        <div><strong>Type:</strong> CNAME</div>
                        <div><strong>Name:</strong> www</div>
                        <div><strong>Target:</strong> {tenantId}.sellzio.com</div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Cloudflare Specific Instructions */}
                <div className="mt-4 p-3 bg-blue-50 rounded border border-blue-200">
                  <p className="text-sm font-medium text-blue-800 mb-2">📋 Cloudflare Setup Instructions:</p>
                  <ol className="text-xs text-blue-700 space-y-1 list-decimal list-inside">
                    <li>Login to your Cloudflare dashboard</li>
                    <li>Select your domain</li>
                    <li>Go to DNS → Records</li>
                    <li>Add the CNAME records above</li>
                    <li>Set Proxy status to "Proxied" (orange cloud) for SSL</li>
                    <li>Click "Save" and wait for propagation</li>
                  </ol>
                </div>

                {/* Additional Notes */}
                <div className="mt-3 space-y-1">
                  <p className="text-xs text-muted-foreground">
                    ⏱️ DNS changes may take up to 24 hours to propagate globally
                  </p>
                  <p className="text-xs text-muted-foreground">
                    🔒 SSL certificates will be automatically provisioned once DNS is verified
                  </p>
                  <p className="text-xs text-muted-foreground">
                    ✅ Use the "Re-verify" button above to check your configuration
                  </p>
                </div>
              </AlertDescription>
            </Alert>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

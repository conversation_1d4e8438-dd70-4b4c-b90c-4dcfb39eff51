"use client"

import { useState, useEffect, useRef } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Calendar } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { cn } from "@/lib/utils"
import { format, subDays, formatDistanceToNow } from "date-fns"
import { id } from "date-fns/locale"
import { DateRange } from "react-day-picker"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import {
  ArrowLeft,
  Plus,
  Search,
  Filter,
  MoreHorizontal,
  Store,
  Users,
  Package,
  TrendingUp,
  Eye,
  Edit,
  Trash2,
  Settings,
  X,
  Check,
  Loader2,
  Columns as ColumnsIcon,
  Calendar as CalendarIcon
} from "lucide-react"
import Link from "next/link"
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import { DateRangePickerKustom } from "@/components/date-range-picker-custom"
import { useAuth } from "@/contexts/auth-context"

// Import fungsi-fungsi Supabase
import {
  getStores,
  createStore,
  updateStore,
  deleteStore as deleteStoreService,
  updateStoreStatus as updateStoreStatusService,
  updateStoreSettings,
  Store as StoreType
} from "@/lib/services/stores"

// Schema untuk validasi form
const storeFormSchema = z.object({
  name: z.string().min(3, "Nama toko harus minimal 3 karakter"),
  owner: z.string().min(3, "Nama pemilik harus minimal 3 karakter"),
  email: z.string().email("Email tidak valid"),
  category: z.string().min(1, "Kategori harus dipilih"),
  location: z.string().min(3, "Lokasi harus minimal 3 karakter"),
  plan: z.string().min(1, "Paket harus dipilih"),
  status: z.string().min(1, "Status harus dipilih"),
});

// Data dummy untuk stores (akan diganti dengan data dari Supabase)
const initialStoresData = [
  {
    id: "store-1",
    name: "Toko Fashion Kita",
    owner: "Ahmad Rizki",
    email: "<EMAIL>",
    status: "active",
    plan: "Premium",
    products: 245,
    orders: 1250,
    revenue: 45000000,
    rating: 4.8,
    joinDate: "2023-01-15",
    lastActive: "2 menit lalu",
    category: "Fashion",
    location: "Jakarta"
  },
  {
    id: "store-2",
    name: "Elektronik Murah",
    owner: "Siti Nurhaliza",
    email: "<EMAIL>",
    status: "active",
    plan: "Business",
    products: 180,
    orders: 890,
    revenue: 32000000,
    rating: 4.6,
    joinDate: "2023-02-20",
    lastActive: "15 menit lalu",
    category: "Elektronik",
    location: "Bandung"
  },
  {
    id: "store-3",
    name: "Makanan Sehat",
    owner: "Budi Santoso",
    email: "<EMAIL>",
    status: "pending",
    plan: "Starter",
    products: 65,
    orders: 320,
    revenue: 8500000,
    rating: 4.3,
    joinDate: "2023-11-10",
    lastActive: "1 jam lalu",
    category: "Makanan",
    location: "Surabaya"
  },
  {
    id: "store-4",
    name: "Buku & Alat Tulis",
    owner: "Maya Sari",
    email: "<EMAIL>",
    status: "suspended",
    plan: "Business",
    products: 420,
    orders: 650,
    revenue: 15000000,
    rating: 4.1,
    joinDate: "2023-05-08",
    lastActive: "3 hari lalu",
    category: "Buku",
    location: "Yogyakarta"
  },
  {
    id: "store-5",
    name: "Olahraga & Fitness",
    owner: "Andi Pratama",
    email: "<EMAIL>",
    status: "active",
    plan: "Premium",
    products: 310,
    orders: 1100,
    revenue: 38000000,
    rating: 4.7,
    joinDate: "2023-03-12",
    lastActive: "30 menit lalu",
    category: "Olahraga",
    location: "Medan"
  }
]

// Kategori toko
const storeCategories = [
  "Fashion",
  "Elektronik",
  "Makanan",
  "Buku",
  "Olahraga",
  "Kesehatan",
  "Kecantikan",
  "Rumah Tangga",
  "Otomotif",
  "Lainnya"
]

// Paket langganan
const storePlans = [
  "Starter",
  "Business",
  "Premium"
]

// Status toko
const storeStatuses = [
  "active",
  "pending",
  "suspended"
]

const getStatusBadge = (status: string) => {
  switch (status) {
    case "active":
      return <Badge variant="default" className="bg-green-100 text-green-800">Aktif</Badge>
    case "pending":
      return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">Pending</Badge>
    case "suspended":
      return <Badge variant="destructive" className="bg-red-100 text-red-800">Suspended</Badge>
    default:
      return <Badge variant="outline">Unknown</Badge>
  }
}

const getPlanBadge = (plan: string) => {
  switch (plan) {
    case "Premium":
      return <Badge variant="default" className="bg-purple-100 text-purple-800">Premium</Badge>
    case "Business":
      return <Badge variant="secondary" className="bg-blue-100 text-blue-800">Business</Badge>
    case "Starter":
      return <Badge variant="outline">Starter</Badge>
    default:
      return <Badge variant="outline">Unknown</Badge>
  }
}

const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('id-ID', {
    style: 'currency',
    currency: 'IDR',
    minimumFractionDigits: 0,
  }).format(amount)
}

export default function StoresPage() {
  // State untuk menyimpan data stores dari Supabase
  const [storesData, setStoresData] = useState<StoreType[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [tenantId, setTenantId] = useState<string | null>(null);

  // State untuk pencarian dan filter
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")
  const [dateRange, setDateRange] = useState<DateRange | undefined>({
    from: subDays(new Date(), 30),
    to: new Date(),
  })

  // State untuk dialog
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [isDetailDialogOpen, setIsDetailDialogOpen] = useState(false)
  const [isSettingsDialogOpen, setIsSettingsDialogOpen] = useState(false)
  const [selectedStore, setSelectedStore] = useState<StoreType | null>(null)

  // State untuk bulk actions
  const [selectedStores, setSelectedStores] = useState<Set<string>>(new Set())
  const [isBulkActionLoading, setIsBulkActionLoading] = useState(false)
  const [bulkAction, setBulkAction] = useState<'activate' | 'suspend' | 'delete' | null>(null)

  // State untuk kolom yang ditampilkan
  const [visibleColumns, setVisibleColumns] = useState({
    logo: true,
    name: true,
    owner: true,
    email: true,
    status: true,
    plan: true,
    products: true,
    orders: true,
    revenue: true,
    rating: true,
    lastActive: true,
    joinDate: true,
    actions: true
  });

  // Fungsi untuk mendapatkan lebar kolom dari localStorage atau nilai default
  const getInitialColumnWidths = () => {
    // Cek apakah ada data yang tersimpan di localStorage
    if (typeof window !== 'undefined') {
      const savedWidths = localStorage.getItem('tableColumnWidths');
      if (savedWidths) {
        try {
          return JSON.parse(savedWidths);
        } catch (e) {
          console.error('Gagal memuat pengaturan lebar kolom', e);
        }
      }
    }

    // Nilai default jika tidak ada data yang tersimpan
    return {
      logo: 50,
      name: 200,
      owner: 150,
      email: 200,
      status: 120,
      plan: 120,
      products: 100,
      orders: 100,
      revenue: 120,
      rating: 100,
      lastActive: 150,
      joinDate: 150,
      actions: 100
    };
  };

  // Definisikan tipe untuk lebar kolom
  type ColumnWidths = {
    select: number;
    logo: number;
    name: number;
    owner: number;
    email: number;
    status: number;
    plan: number;
    products: number;
    orders: number;
    revenue: number;
    rating: number;
    lastActive: number;
    joinDate: number;
    actions: number;
    [key: string]: number; // Untuk kompatibilitas dengan akses dinamis
  };

  // Inisialisasi lebar kolom
  const [columnWidths, setColumnWidths] = useState<ColumnWidths>(getInitialColumnWidths());

  // Simpan perubahan lebar kolom ke localStorage
  useEffect(() => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('tableColumnWidths', JSON.stringify(columnWidths));
    }
  }, [columnWidths]);

  // State untuk drag and drop
  const [isResizing, setIsResizing] = useState(false);
  const [resizeColumn, setResizeColumn] = useState("");
  const [startX, setStartX] = useState(0);
  const [startWidth, setStartWidth] = useState(0);
  const [hoveredColumn, setHoveredColumn] = useState("");

  // Style untuk indikator resize
  const resizeIndicatorStyle = (column: string) => ({
    position: 'absolute' as const,
    top: 0,
    right: 0,
    width: '1px',
    height: '100%',
    cursor: 'col-resize',
    backgroundColor: hoveredColumn === column || isResizing ? '#3b82f6' : 'rgba(0, 0, 0, 0.1)',
    transition: 'background-color 0.2s ease, width 0.1s ease',
    zIndex: 10,
    '&:hover': {
      width: '3px', // Sedikit melebar saat dihover untuk feedback visual
    },
  });

  // Style untuk sel tabel
  const tableCellStyle = {
    borderRight: '1px solid hsl(var(--border))',
    borderBottom: '1px solid hsl(var(--border))',
    padding: '0.5rem 1rem',
  };

  // Style untuk header sel
  const tableHeaderStyle = {
    ...tableCellStyle,
    backgroundColor: 'hsl(var(--muted))',
    color: 'hsl(var(--muted-foreground))',
    fontWeight: 600,
    fontSize: '0.875rem',
    lineHeight: '1.25rem',
    borderBottom: '1px solid hsl(var(--border))',
    padding: '0.75rem 1rem',
    verticalAlign: 'middle',
    '&:hover': {
      backgroundColor: 'hsl(var(--muted))',
    },
  };

  // Fungsi untuk toggle visibilitas kolom
  const toggleColumnVisibility = (column: string) => {
    setVisibleColumns(prev => ({
      ...prev,
      [column]: !prev[column as keyof typeof prev]
    }));
  };

  // Fungsi untuk memulai resize kolom
  const startResize = (e: React.MouseEvent, column: string) => {
    e.preventDefault();
    e.stopPropagation();
    setIsResizing(true);
    setResizeColumn(column);
    setStartX(e.pageX);
    setStartWidth(columnWidths[column] || 100);
    document.body.style.cursor = 'col-resize';
    document.body.style.userSelect = 'none';
  };

  // Fungsi untuk melakukan resize kolom
  const resizeColumnWidth = (e: MouseEvent) => {
    if (!isResizing) return;

    const width = startWidth + (e.pageX - startX);
    const minWidth = 50; // Lebar minimum kolom
    const maxWidth = 800; // Lebar maksimum kolom

    // Pastikan lebar kolom berada dalam batas yang ditentukan
    const newWidth = Math.min(Math.max(width, minWidth), maxWidth);

    setColumnWidths((prev: ColumnWidths) => ({
      ...prev,
      [resizeColumn]: newWidth
    }));
  };

  // Fungsi untuk menghentikan resize
  const stopResize = () => {
    setIsResizing(false);
    document.body.style.cursor = '';
    document.body.style.userSelect = '';
  };

  // Efek untuk menangani event mouse move dan mouse up
  useEffect(() => {
    if (isResizing) {
      window.addEventListener('mousemove', resizeColumnWidth);
      window.addEventListener('mouseup', stopResize);
    }

    return () => {
      window.removeEventListener('mousemove', resizeColumnWidth);
      window.removeEventListener('mouseup', stopResize);
    };
  }, [isResizing, resizeColumn, startWidth]);

  // State untuk pengaturan store
  const [emailNotificationsEnabled, setEmailNotificationsEnabled] = useState(false)
  const [productVerificationEnabled, setProductVerificationEnabled] = useState(false)
  const [isUpdatingSettings, setIsUpdatingSettings] = useState(false)

  // Fungsi untuk mengirim reset password email
  const sendResetPasswordEmail = async (email: string) => {
    try {
      // Menampilkan loading state
      const loadingMsg = `Mengirim email reset password ke ${email}...`;
      console.log(loadingMsg);

      // Simulasi API call dengan timeout
      // Dalam implementasi nyata, ini akan memanggil API endpoint Supabase Auth
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Dalam aplikasi produksi, gunakan kode seperti ini:
      // const { error } = await supabase.auth.resetPasswordForEmail(email, {
      //   redirectTo: `${window.location.origin}/reset-password`,
      // });
      //
      // if (error) throw error;

      // Menampilkan sukses message
      alert(`Email reset password telah dikirim ke ${email}`);
    } catch (error) {
      console.error('Error sending reset password email:', error);
      alert('Gagal mengirim email reset password. Silakan coba lagi.');
    }
  }

  // Fungsi untuk mengekspor data store sebagai CSV
  const exportStoreData = (store: StoreType) => {
    try {
      // Membuat header CSV
      const headers = [
        'ID', 'Nama Store', 'Pemilik', 'Email', 'Kategori', 'Lokasi',
        'Status', 'Paket', 'Produk', 'Order', 'Revenue', 'Rating',
        'Tanggal Bergabung', 'Terakhir Aktif'
      ];

      // Membuat baris data
      const data = [
        store.id,
        store.name,
        store.owner_name || store.owner,
        store.email,
        store.category,
        store.location,
        store.status,
        store.plan,
        store.products,
        store.orders,
        store.revenue,
        store.rating,
        store.join_date,
        store.last_active
      ];

      // Menggabungkan header dan data
      const csvContent = [
        headers.join(','),
        data.join(',')
      ].join('\n');

      // Membuat Blob dan link untuk download
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const url = URL.createObjectURL(blob);

      // Membuat elemen anchor untuk download
      const link = document.createElement('a');
      link.setAttribute('href', url);
      link.setAttribute('download', `store_${store.name.replace(/\s+/g, '_')}_data.csv`);
      link.style.visibility = 'hidden';

      // Menambahkan ke DOM, memicu download, dan membersihkan
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      alert(`Data store ${store.name} telah diekspor sebagai CSV`);
    } catch (error) {
      console.error('Error exporting store data:', error);
      alert('Gagal mengekspor data store. Silakan coba lagi.');
    }
  }

  // Fungsi untuk mengaktifkan/menonaktifkan tampilan marketplace
  const toggleMarketplaceVisibility = async (store: StoreType) => {
    const newStatus = store.status === "active" ? "pending" : "active";
    await changeStoreStatus(store.id, newStatus as any);
  }

  // Fungsi untuk toggle email notifications
  const toggleEmailNotifications = async (storeId: string, enabled: boolean) => {
    setIsUpdatingSettings(true);
    try {
      const { success, error } = await updateStoreSettings(storeId, {
        email_notifications: enabled
      });

      if (error) throw error;

      setEmailNotificationsEnabled(enabled);
      alert(enabled ? "Notifikasi email telah diaktifkan" : "Notifikasi email telah dinonaktifkan");
    } catch (error) {
      console.error("Error updating email notifications setting:", error);
      alert("Gagal mengubah pengaturan notifikasi email. Silakan coba lagi.");
      // Kembalikan state ke nilai sebelumnya jika gagal
      setEmailNotificationsEnabled(!enabled);
    } finally {
      setIsUpdatingSettings(false);
    }
  }

  // Fungsi untuk toggle product verification
  const toggleProductVerification = async (storeId: string, enabled: boolean) => {
    setIsUpdatingSettings(true);
    try {
      const { success, error } = await updateStoreSettings(storeId, {
        product_verification: enabled
      });

      if (error) throw error;

      setProductVerificationEnabled(enabled);
      alert(enabled ? "Verifikasi produk telah diaktifkan" : "Verifikasi produk telah dinonaktifkan");
    } catch (error) {
      console.error("Error updating product verification setting:", error);
      alert("Gagal mengubah pengaturan verifikasi produk. Silakan coba lagi.");
      // Kembalikan state ke nilai sebelumnya jika gagal
      setProductVerificationEnabled(!enabled);
    } finally {
      setIsUpdatingSettings(false);
    }
  }

  // Setup form dengan react-hook-form dan zod validation
  const form = useForm<z.infer<typeof storeFormSchema>>({
    resolver: zodResolver(storeFormSchema),
    defaultValues: {
      name: "",
      owner: "",
      email: "",
      category: "",
      location: "",
      plan: "Starter",
      status: "pending",
    },
  });

  // Reset form ketika dialog tambah/edit dibuka/ditutup
  useEffect(() => {
    if (!isAddDialogOpen && !isEditDialogOpen) {
      form.reset({
        name: "",
        owner: "",
        email: "",
        category: "",
        location: "",
        plan: "Starter",
        status: "pending",
      });
    }
  }, [isAddDialogOpen, isEditDialogOpen, form]);

  // Get tenant ID from user context
  const { user } = useAuth();

  useEffect(() => {
    if (user?.tenantId) {
      setTenantId(user.tenantId);
    }
  }, [user]);

  // Fetch data stores dari Supabase berdasarkan tenant
  useEffect(() => {
    async function fetchTenantStores() {
      if (!tenantId) return;

      setIsLoading(true);
      try {
        const response = await fetch(`/api/tenant/stores?tenantId=${tenantId}`);
        const result = await response.json();

        if (!response.ok) {
          throw new Error(result.error || 'Failed to fetch stores');
        }

        setStoresData(result.stores || []);
      } catch (err) {
        console.error("Error fetching tenant stores:", err);
        setError("Gagal memuat data stores. Silakan coba lagi nanti.");
      } finally {
        setIsLoading(false);
      }
    }

    fetchTenantStores();
  }, [tenantId]);

  // Filter stores berdasarkan pencarian, status, dan rentang tanggal
  const filteredStores = storesData.filter(store => {
    const matchesSearch = store.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         store.owner?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         store.category?.toLowerCase().includes(searchTerm.toLowerCase())

    const matchesStatus = statusFilter === "all" || store.status === statusFilter

    // Filter berdasarkan rentang tanggal jika dipilih
    let matchesDateRange = true
    if (dateRange?.from && dateRange?.to) {
      try {
        const storeDate = store.join_date ? new Date(store.join_date) : null

        if (storeDate) {
          // Normalisasi waktu ke awal hari untuk tanggal mulai dan akhir
          const startDate = new Date(dateRange.from)
          startDate.setHours(0, 0, 0, 0)

          const endDate = new Date(dateRange.to)
          endDate.setHours(23, 59, 59, 999)

          // Debug logging
          console.log('Filtering store:', {
            store: store.name,
            join_date: store.join_date,
            storeDate: storeDate.toISOString(),
            startDate: startDate.toISOString(),
            endDate: endDate.toISOString(),
            isAfterStart: storeDate >= startDate,
            isBeforeEnd: storeDate <= endDate
          })

          matchesDateRange = storeDate >= startDate && storeDate <= endDate
        } else {
          console.warn('Store has invalid join_date:', store.id, store.name, store.join_date)
        }
      } catch (error) {
        console.error('Error filtering store by date:', error, store)
        matchesDateRange = false
      }
    }

    const shouldInclude = matchesSearch && matchesStatus && matchesDateRange
    console.log('Store filter result:', {
      store: store.name,
      matchesSearch,
      matchesStatus,
      matchesDateRange,
      shouldInclude
    })

    return shouldInclude
  })

  // Menghitung statistik berdasarkan stores yang sudah difilter
  const totalStores = filteredStores.length
  const activeStores = filteredStores.filter(s => s.status === "active").length
  const pendingStores = filteredStores.filter(s => s.status === "pending").length
  const totalRevenue = filteredStores.reduce((sum, store) => sum + store.revenue, 0)
  const totalProducts = filteredStores.reduce((sum, store) => sum + store.products, 0)

  // Fungsi untuk membuka dialog tambah store
  const openAddDialog = () => {
    form.reset({
      name: "",
      owner: "",
      email: "",
      category: "",
      location: "",
      plan: "Starter",
      status: "pending",
    });
    setIsAddDialogOpen(true);
  };

  // Fungsi untuk membuka dialog edit store
  const openEditDialog = (store: StoreType) => {
    setSelectedStore(store);
    form.reset({
      name: store.name,
      owner: store.owner_name || store.owner, // Gunakan owner_name jika tersedia, jika tidak gunakan owner
      email: store.email,
      category: store.category,
      location: store.location,
      plan: store.plan,
      status: store.status,
    });
    setIsEditDialogOpen(true);
  };

  // Fungsi untuk membuka dialog detail store
  const openDetailDialog = (store: StoreType) => {
    setSelectedStore(store);
    setIsDetailDialogOpen(true);
  };

  // Fungsi untuk membuka dialog pengaturan store
  const openSettingsDialog = (store: StoreType) => {
    setSelectedStore(store);
    setIsSettingsDialogOpen(true);
  };

  // Fungsi untuk membuka dialog konfirmasi hapus
  const openDeleteDialog = (store: StoreType) => {
    setSelectedStore(store);
    setIsDeleteDialogOpen(true);
  };

  // Fungsi untuk menambah store baru dengan Supabase
  const addStore = async (data: z.infer<typeof storeFormSchema>) => {
    try {
      const newStore = {
        name: data.name,
        owner: data.owner,
        email: data.email,
        status: data.status as any,
        plan: data.plan,
        products: 0,
        orders: 0,
        revenue: 0,
        rating: 0,
        join_date: new Date().toISOString(), // Menggunakan join_date bukan joinDate
        last_active: new Date().toISOString(), // Menggunakan last_active bukan lastActive
        category: data.category,
        location: data.location
      };

      console.log('Mengirim data ke createStore:', newStore);

      const { data: createdStore, error } = await createStore(newStore);

      if (error) {
        console.error('Error dari createStore:', error);
        throw error;
      }

      if (createdStore) {
        // Update state dengan store baru
        setStoresData(prevStores => [...prevStores, createdStore as StoreType]);
        setIsAddDialogOpen(false);
        alert("Store berhasil ditambahkan");
      }
    } catch (err) {
      console.error("Error creating store:", err);
      alert("Gagal menambahkan store. Silakan coba lagi.");
    }
  };

  // Fungsi untuk mengupdate store dengan Supabase
  const updateStoreData = async (data: z.infer<typeof storeFormSchema>) => {
    if (!selectedStore) return;

    try {
      const updates = {
        name: data.name,
        owner_name: data.owner, // Simpan nama pemilik di field owner_name
        email: data.email,
        status: data.status as any,
        plan: data.plan,
        category: data.category,
        location: data.location
      };

      const { data: updatedStore, error } = await updateStore(selectedStore.id, updates);

      if (error) {
        throw error;
      }

      if (updatedStore) {
        // Update state dengan store yang diperbarui
        const updatedStores = storesData.map(store =>
          store.id === selectedStore.id ? (updatedStore as unknown as StoreType) : store
        );

        setStoresData(updatedStores as StoreType[]);
        setIsEditDialogOpen(false);
        alert("Store berhasil diperbarui");
      }
    } catch (err) {
      console.error("Error updating store:", err);
      alert("Gagal memperbarui store. Silakan coba lagi.");
    }
  };

  // Fungsi untuk menghapus store dengan Supabase
  const deleteStoreData = async () => {
    if (!selectedStore) return;

    try {
      const { success, error } = await deleteStoreService(selectedStore.id);

      if (error) {
        throw error;
      }

      if (success) {
        // Update state dengan menghapus store
        const updatedStores = storesData.filter(store => store.id !== selectedStore.id);
        setStoresData(updatedStores);
        setIsDeleteDialogOpen(false);
        alert("Store berhasil dihapus");
      }
    } catch (err) {
      console.error("Error deleting store:", err);
      alert("Gagal menghapus store. Silakan coba lagi.");
    }
  };

  // Fungsi untuk mengubah status store dengan Supabase
  const changeStoreStatus = async (storeId: string, newStatus: 'active' | 'pending' | 'suspended') => {
    try {
      const result = await updateStoreStatusService(storeId, newStatus);

      if (result.error) {
        throw result.error;
      }

      if (result.success) {
        // Update state dengan status store yang diperbarui
        const updatedStores = storesData.map(store =>
          store.id === storeId ? { ...store, status: newStatus } : store
        );

        setStoresData(updatedStores);
        alert(`Status store berhasil diubah menjadi ${newStatus}`);
      }
    } catch (err) {
      console.error("Error updating store status:", err);
      alert("Gagal mengubah status store. Silakan coba lagi.");
    }
  };

  // Fungsi untuk menghapus store
  const handleDeleteStore = async () => {
    if (!selectedStore) return

    try {
      await deleteStoreService(selectedStore.id)
      setStoresData(storesData.filter(store => store.id !== selectedStore.id))
      setIsDeleteDialogOpen(false)
      setSelectedStore(null)
      // Tampilkan notifikasi sukses
    } catch (error) {
      console.error('Error deleting store:', error)
      // Tampilkan notifikasi error
    }
  }

  // Fungsi untuk menangani pemilihan store
  const handleSelectStore = (storeId: string, isSelected: boolean) => {
    const newSelection = new Set(selectedStores)
    if (isSelected) {
      newSelection.add(storeId)
    } else {
      newSelection.delete(storeId)
    }
    setSelectedStores(newSelection)
  }

  // Fungsi untuk memilih semua store
  const handleSelectAllStores = (isSelected: boolean) => {
    if (isSelected) {
      const allStoreIds = new Set(filteredStores.map(store => store.id))
      setSelectedStores(allStoreIds)
    } else {
      setSelectedStores(new Set())
    }
  }

  // Fungsi untuk mengeksekusi bulk action
  const executeBulkAction = async () => {
    if (selectedStores.size === 0 || !bulkAction) return

    setIsBulkActionLoading(true)

    try {
      if (bulkAction === 'delete') {
        // Tampilkan konfirmasi sebelum menghapus
        if (!confirm(`Apakah Anda yakin ingin menghapus ${selectedStores.size} store yang dipilih?`)) {
          return
        }

        // Hapus store yang dipilih
        for (const storeId of selectedStores) {
          await deleteStoreService(storeId)
        }

        // Perbarui daftar store
        setStoresData(storesData.filter(store => !selectedStores.has(store.id)))
      } else {
        // Update status store yang dipilih
        const newStatus = bulkAction === 'activate' ? 'active' : 'suspended'

        for (const storeId of selectedStores) {
          await updateStoreStatusService(storeId, newStatus)
        }

        // Perbarui data store dengan status baru
        setStoresData(storesData.map(store =>
          selectedStores.has(store.id) ? { ...store, status: newStatus } : store
        ))
      }

      // Reset seleksi dan action
      setSelectedStores(new Set())
      setBulkAction(null)

      // Tampilkan notifikasi sukses
      alert(`Berhasil ${bulkAction} ${selectedStores.size} store`)

    } catch (error) {
      console.error(`Error performing bulk ${bulkAction}:`, error)
      alert(`Gagal melakukan aksi pada ${selectedStores.size} store. Silakan coba lagi.`)
    } finally {
      setIsBulkActionLoading(false)
    }
  }

  // Efek untuk mengeksekusi bulk action ketika dipilih
  useEffect(() => {
    if (bulkAction) {
      executeBulkAction()
    }
  }, [bulkAction])

  const headerCheckboxRef = useRef<HTMLInputElement>(null);

  // Efek untuk mengatur properti indeterminate pada checkbox header
  useEffect(() => {
    if (headerCheckboxRef.current) {
      headerCheckboxRef.current.indeterminate = selectedStores.size > 0 && selectedStores.size < filteredStores.length;
    }
  }, [selectedStores, filteredStores]);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Kelola Stores</h2>
          <p className="text-muted-foreground">
            Manage semua stores dalam marketplace Anda
          </p>
        </div>

        <div className="flex items-center gap-2">
          {/* Date Range Picker - Ganti dengan komponen baru */}
          <DateRangePickerKustom
            value={dateRange}
            onChange={setDateRange}
          />

          <Button onClick={() => setIsAddDialogOpen(true)}>
            <Plus className="mr-2 h-4 w-4" />
            Tambah Store Baru
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Stores</CardTitle>
            <Store className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalStores}</div>
            <p className="text-xs text-muted-foreground">
              {dateRange?.from && dateRange?.to ? (
                <>Data dari {format(dateRange.from, 'dd MMM yyyy', { locale: id })} - {format(dateRange.to, 'dd MMM yyyy', { locale: id })}</>
              ) : "+2 dari bulan lalu"}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Stores Aktif</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{activeStores}</div>
            <p className="text-xs text-muted-foreground">
              {dateRange?.from && dateRange?.to ? (
                <>{pendingStores} pending dalam rentang tanggal</>
              ) : `${pendingStores} pending approval`}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Produk</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalProducts}</div>
            <p className="text-xs text-muted-foreground">
              {dateRange?.from && dateRange?.to ? (
                <>Produk dari {format(dateRange.from, 'dd MMM', { locale: id })} - {format(dateRange.to, 'dd MMM', { locale: id })}</>
              ) : "Across all stores"}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(totalRevenue)}</div>
            <p className="text-xs text-muted-foreground">
              {dateRange?.from && dateRange?.to ? (
                <>Pendapatan dari {format(dateRange.from, 'dd MMM', { locale: id })} - {format(dateRange.to, 'dd MMM', { locale: id })}</>
              ) : "+12% dari bulan lalu"}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Card>
        <CardHeader>
          <div className="flex flex-col gap-4">
            <div className="flex items-center justify-between">
              <div>
                <CardTitle>Daftar Stores</CardTitle>
                <div className="text-sm text-muted-foreground">
                  <span>Manage dan monitor semua stores dalam marketplace</span>
                </div>
              </div>

              {/* Search and Filter */}
              <div className="flex items-center gap-2">
                <div className="relative">
                  <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Cari stores..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-8 w-[200px] h-9"
                  />
                </div>



                {/* Tombol untuk mengatur kolom */}
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="outline" size="sm" className="h-9">
                      <ColumnsIcon className="h-4 w-4 mr-2" />
                      Kolom
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent className="w-[300px] p-4">
                    <div className="mb-2 text-sm font-medium">Tampilkan Kolom</div>
                    <div className="grid grid-cols-2 gap-2">
                      {Object.entries({
                        logo: 'Logo',
                        name: 'Nama Store',
                        owner: 'Pemilik',
                        email: 'Email',
                        status: 'Status',
                        plan: 'Paket',
                        products: 'Produk',
                        orders: 'Pesanan',
                        revenue: 'Pendapatan',
                        rating: 'Rating',
                        lastActive: 'Terakhir Aktif',
                        joinDate: 'Tanggal Bergabung',
                        actions: 'Aksi'
                      }).map(([key, label]) => (
                        <div key={key} className="flex items-center space-x-2">
                          <input
                            type="checkbox"
                            id={`column-${key}`}
                            className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                            checked={visibleColumns[key as keyof typeof visibleColumns]}
                            onChange={() => toggleColumnVisibility(key)}
                          />
                          <label
                            htmlFor={`column-${key}`}
                            className="text-sm font-normal cursor-pointer select-none"
                          >
                            {label}
                          </label>
                        </div>
                      ))}
                    </div>
                    <div className="flex justify-between mt-4 pt-3 border-t">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => {
                          // Set semua kolom ke true
                          setVisibleColumns(prev => {
                            const newState = { ...prev };
                            Object.keys(newState).forEach(key => {
                              newState[key as keyof typeof newState] = true;
                            });
                            return newState;
                          });
                        }}
                      >
                        Pilih Semua
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => {
                          // Set semua kolom ke false kecuali logo dan aksi
                          setVisibleColumns(prev => {
                            const newState = { ...prev };
                            Object.keys(newState).forEach(key => {
                              newState[key as keyof typeof newState] = ['logo', 'actions'].includes(key);
                            });
                            return newState;
                          });
                        }}
                      >
                        Hapus Semua
                      </Button>
                    </div>
                  </DropdownMenuContent>
                </DropdownMenu>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="outline" size="sm" className="h-9">
                      <Filter className="h-4 w-4 mr-2" />
                      Filter Status
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent>
                    <DropdownMenuItem onClick={() => setStatusFilter("all")}>
                      Semua Status
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => setStatusFilter("active")}>
                      Aktif
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => setStatusFilter("pending")}>
                      Pending
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => setStatusFilter("suspended")}>
                      Suspended
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </div>

            {/* Bulk Action Bar */}
            {selectedStores.size > 0 && (
              <div className="flex items-center justify-between bg-accent/50 p-2 rounded-md border" data-component-name="StoresPage">
                <div className="flex items-center gap-2">
                  <span className="text-sm">
                    {selectedStores.size} dipilih
                  </span>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setSelectedStores(new Set())}
                    className="h-7 px-2 text-xs"
                  >
                    Batal
                  </Button>
                </div>
                <div className="flex items-center gap-2">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button
                        variant="outline"
                        size="sm"
                        className="h-8"
                        disabled={isBulkActionLoading}
                      >
                        {isBulkActionLoading ? (
                          <>
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                            Memproses...
                          </>
                        ) : 'Aksi Massal'}
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => setBulkAction('activate')}>
                        <Check className="mr-2 h-4 w-4 text-green-600" />
                        Aktifkan Store
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => setBulkAction('suspend')}>
                        <X className="h-4 w-4 mr-2 text-yellow-600" />
                        Suspensi Store
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onClick={() => setBulkAction('delete')}
                        className="text-red-600"
                      >
                        <Trash2 className="h-4 w-4 mr-2" />
                        Hapus Store
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </div>
            )}
          </div>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="flex justify-center items-center py-8">
              <Loader2 className="h-8 w-8 text-primary animate-spin" />
              <span className="ml-2">Memuat data...</span>
            </div>
          ) : error ? (
            <div className="text-center py-8 text-red-500">
              <div className="mb-2">{error}</div>
              <Button variant="outline" onClick={() => window.location.reload()}>
                Coba Lagi
              </Button>
            </div>
          ) : (
            <>
              <div className="rounded-md border border-border overflow-hidden">
                <Table className="table-fixed w-full border-collapse">
                  <colgroup>
                    <col style={{ width: `${columnWidths['select']}px` }} />
                    <col style={{ width: `${columnWidths['logo']}px` }} />
                    {visibleColumns.name && <col style={{ width: `${columnWidths['name']}px` }} />}
                    {visibleColumns.owner && <col style={{ width: `${columnWidths['owner']}px` }} />}
                    {visibleColumns.email && <col style={{ width: `${columnWidths['email']}px` }} />}
                    {visibleColumns.status && <col style={{ width: `${columnWidths['status']}px` }} />}
                    {visibleColumns.plan && <col style={{ width: `${columnWidths['plan']}px` }} />}
                    {visibleColumns.products && <col style={{ width: `${columnWidths['products']}px` }} />}
                    {visibleColumns.orders && <col style={{ width: `${columnWidths['orders']}px` }} />}
                    {visibleColumns.revenue && <col style={{ width: `${columnWidths['revenue']}px` }} />}
                    {visibleColumns.rating && <col style={{ width: `${columnWidths['rating']}px` }} />}
                    {visibleColumns.lastActive && <col style={{ width: `${columnWidths['lastActive']}px` }} />}
                    {visibleColumns.joinDate && <col style={{ width: `${columnWidths['joinDate']}px` }} />}
                    <col style={{ width: `${columnWidths['actions']}px` }} />
                  </colgroup>
                  <TableHeader>
                    <TableRow>
                      <TableHead
                        className="relative select-none cursor-pointer"
                        style={{
                          ...tableHeaderStyle,
                          width: `${columnWidths['select']}px`,
                          minWidth: `${columnWidths['select']}px`,
                          maxWidth: `${columnWidths['select']}px`,
                          textAlign: 'center',
                          padding: '0.5rem 0.5rem 0.5rem 1rem', // Menambah padding kiri
                          position: 'relative',
                          zIndex: 5,
                          borderRight: '1px solid hsl(var(--border))', // Border sama dengan kolom lainnya
                          backgroundColor: 'hsl(var(--muted))'
                        }}
                        onClick={(e) => {
                          e.preventDefault();
                          handleSelectAllStores(!selectedStores.size || selectedStores.size < filteredStores.length);
                        }}
                      >
                        <div className="flex items-center justify-center pl-1.5">
                          <input
                            type="checkbox"
                            className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary cursor-pointer"
                            checked={selectedStores.size > 0 && selectedStores.size === filteredStores.length}
                            onChange={(e) => handleSelectAllStores(e.target.checked)}
                            onClick={(e) => e.stopPropagation()}
                            ref={headerCheckboxRef}
                          />
                        </div>
                        <div
                          style={resizeIndicatorStyle('select')}
                          onMouseDown={(e) => startResize(e, 'select')}
                          onMouseEnter={() => setHoveredColumn('select')}
                          onMouseLeave={() => setHoveredColumn('')}
                        />
                      </TableHead>
                      <TableHead
                        className="relative"
                        style={{
                          ...tableHeaderStyle,
                          width: `${columnWidths['logo']}px`,
                          minWidth: `${columnWidths['logo']}px`,
                          maxWidth: `${columnWidths['logo']}px`,
                          textAlign: 'center',
                          padding: '0.5rem 0.25rem',
                          borderRight: '1px solid hsl(var(--border))',
                          backgroundColor: 'hsl(var(--muted))'
                        }}
                      >
                        Logo
                        <div
                          style={resizeIndicatorStyle('logo')}
                          onMouseDown={(e) => startResize(e, 'logo')}
                          onMouseEnter={() => setHoveredColumn('logo')}
                          onMouseLeave={() => setHoveredColumn('')}
                        />
                      </TableHead>
                      {visibleColumns.name && (
                        <TableHead
                          className="relative"
                          style={{
                            ...tableHeaderStyle,
                            width: `${columnWidths['name']}px`,
                            borderRight: '1px solid hsl(var(--border))',
                            position: 'relative',
                            zIndex: 1
                          }}
                        >
                          Nama Store
                          <div
                            style={resizeIndicatorStyle('name')}
                            onMouseDown={(e) => startResize(e, 'name')}
                            onMouseEnter={() => setHoveredColumn('name')}
                            onMouseLeave={() => setHoveredColumn('')}
                          />
                        </TableHead>
                      )}
                      {visibleColumns.owner && (
                        <TableHead
                          className="relative"
                          style={{
                            ...tableHeaderStyle,
                            width: `${columnWidths['owner']}px`,
                            borderRight: '1px solid hsl(var(--border))'
                          }}
                        >
                          Owner
                          <div
                            style={resizeIndicatorStyle('owner')}
                            onMouseDown={(e) => startResize(e, 'owner')}
                            onMouseEnter={() => setHoveredColumn('owner')}
                            onMouseLeave={() => setHoveredColumn('')}
                          />
                        </TableHead>
                      )}
                      {visibleColumns.email && (
                        <TableHead
                          className="relative"
                          style={{
                            ...tableHeaderStyle,
                            width: `${columnWidths['email']}px`,
                            borderRight: '1px solid hsl(var(--border))'
                          }}
                        >
                          Email
                          <div
                            style={resizeIndicatorStyle('email')}
                            onMouseDown={(e) => startResize(e, 'email')}
                            onMouseEnter={() => setHoveredColumn('email')}
                            onMouseLeave={() => setHoveredColumn('')}
                          />
                        </TableHead>
                      )}
                      {visibleColumns.status && (
                        <TableHead
                          className="relative"
                          style={{
                            ...tableHeaderStyle,
                            width: `${columnWidths['status']}px`,
                            borderRight: '1px solid hsl(var(--border))'
                          }}
                        >
                          Status
                          <div
                            style={resizeIndicatorStyle('status')}
                            onMouseDown={(e) => startResize(e, 'status')}
                            onMouseEnter={() => setHoveredColumn('status')}
                            onMouseLeave={() => setHoveredColumn('')}
                          />
                        </TableHead>
                      )}
                      {visibleColumns.plan && (
                        <TableHead
                          className="relative"
                          style={{
                            ...tableHeaderStyle,
                            width: `${columnWidths['plan']}px`,
                            borderRight: '1px solid hsl(var(--border))'
                          }}
                        >
                          Plan
                          <div
                            style={resizeIndicatorStyle('plan')}
                            onMouseDown={(e) => startResize(e, 'plan')}
                            onMouseEnter={() => setHoveredColumn('plan')}
                            onMouseLeave={() => setHoveredColumn('')}
                          />
                        </TableHead>
                      )}
                      {visibleColumns.products && (
                        <TableHead
                          className="relative"
                          style={{
                            ...tableHeaderStyle,
                            width: `${columnWidths['products']}px`,
                            borderRight: '1px solid hsl(var(--border))'
                          }}
                        >
                          Produk
                          <div
                            style={resizeIndicatorStyle('products')}
                            onMouseDown={(e) => startResize(e, 'products')}
                            onMouseEnter={() => setHoveredColumn('products')}
                            onMouseLeave={() => setHoveredColumn('')}
                          />
                        </TableHead>
                      )}
                      {visibleColumns.orders && (
                        <TableHead
                          className="relative"
                          style={{
                            ...tableHeaderStyle,
                            width: `${columnWidths['orders']}px`,
                            borderRight: '1px solid hsl(var(--border))'
                          }}
                        >
                          Orders
                          <div
                            style={resizeIndicatorStyle('orders')}
                            onMouseDown={(e) => startResize(e, 'orders')}
                            onMouseEnter={() => setHoveredColumn('orders')}
                            onMouseLeave={() => setHoveredColumn('')}
                          />
                        </TableHead>
                      )}
                      {visibleColumns.revenue && (
                        <TableHead
                          className="relative"
                          style={{
                            ...tableHeaderStyle,
                            width: `${columnWidths['revenue']}px`,
                            borderRight: '1px solid hsl(var(--border))'
                          }}
                        >
                          Revenue
                          <div
                            style={resizeIndicatorStyle('revenue')}
                            onMouseDown={(e) => startResize(e, 'revenue')}
                            onMouseEnter={() => setHoveredColumn('revenue')}
                            onMouseLeave={() => setHoveredColumn('')}
                          />
                        </TableHead>
                      )}
                      {visibleColumns.rating && (
                        <TableHead
                          className="relative"
                          style={{
                            ...tableHeaderStyle,
                            width: `${columnWidths['rating']}px`,
                            borderRight: '1px solid hsl(var(--border))'
                          }}
                        >
                          Rating
                          <div
                            style={resizeIndicatorStyle('rating')}
                            onMouseDown={(e) => startResize(e, 'rating')}
                            onMouseEnter={() => setHoveredColumn('rating')}
                            onMouseLeave={() => setHoveredColumn('')}
                          />
                        </TableHead>
                      )}
                      {visibleColumns.lastActive && (
                        <TableHead
                          className="relative"
                          style={{
                            ...tableHeaderStyle,
                            width: `${columnWidths['lastActive']}px`,
                            borderRight: '1px solid hsl(var(--border))'
                          }}
                        >
                          Last Active
                          <div
                            style={resizeIndicatorStyle('lastActive')}
                            onMouseDown={(e) => startResize(e, 'lastActive')}
                            onMouseEnter={() => setHoveredColumn('lastActive')}
                            onMouseLeave={() => setHoveredColumn('')}
                          />
                        </TableHead>
                      )}
                      {visibleColumns.joinDate && (
                        <TableHead
                          className="relative"
                          style={{
                            ...tableHeaderStyle,
                            width: `${columnWidths['joinDate']}px`,
                            borderRight: '1px solid hsl(var(--border))'
                          }}
                        >
                          Join Date
                          <div
                            style={resizeIndicatorStyle('joinDate')}
                            onMouseDown={(e) => startResize(e, 'joinDate')}
                            onMouseEnter={() => setHoveredColumn('joinDate')}
                            onMouseLeave={() => setHoveredColumn('')}
                          />
                        </TableHead>
                      )}
                      <TableHead
                        className="relative text-right"
                        style={{
                          ...tableHeaderStyle,
                          width: `${columnWidths['actions']}px`,
                          borderRight: '1px solid hsl(var(--border))'
                        }}
                      >
                        Aksi
                        <div
                          style={resizeIndicatorStyle('actions')}
                          onMouseDown={(e) => startResize(e, 'actions')}
                          onMouseEnter={() => setHoveredColumn('actions')}
                          onMouseLeave={() => setHoveredColumn('')}
                        />
                      </TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredStores.length > 0 ? (
                      filteredStores.map((store) => (
                        <TableRow key={store.id} className="hover:bg-muted/50">
                          <TableCell
                            className="p-0"
                            style={{
                              ...tableCellStyle,
                              width: `${columnWidths['select']}px`,
                              minWidth: `${columnWidths['select']}px`,
                              maxWidth: `${columnWidths['select']}px`,
                              borderRight: '1px solid hsl(var(--border))', // Border sama dengan kolom lainnya
                              padding: '0.5rem 0.5rem 0.5rem 1rem' // Menambah padding kiri
                            }}
                          >
                            <div className="flex items-center justify-center pl-1.5">
                              <input
                                type="checkbox"
                                className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary cursor-pointer"
                                checked={selectedStores.has(store.id)}
                                onChange={(e) => handleSelectStore(store.id, e.target.checked)}
                                onClick={(e) => e.stopPropagation()}
                              />
                            </div>
                          </TableCell>
                          <TableCell
                            className="p-0"
                            style={{
                              ...tableCellStyle,
                              width: `${columnWidths['logo']}px`,
                              minWidth: `${columnWidths['logo']}px`,
                              maxWidth: `${columnWidths['logo']}px`,
                              borderRight: '1px solid hsl(var(--border))'
                            }}
                          >
                            <div className="h-10 w-10 flex items-center justify-center rounded-md bg-primary/10 mx-auto">
                              <Store className="h-5 w-5 text-primary" />
                            </div>
                          </TableCell>
                          {visibleColumns.name && (
                            <TableCell
                              className="overflow-hidden"
                              style={{
                                width: `${columnWidths['name']}px`,
                                maxWidth: `${columnWidths['name']}px`,
                                borderRight: '1px solid hsl(var(--border))'
                              }}
                            >
                              <div className="font-medium truncate" title={store.name}>{store.name}</div>
                              <div className="text-sm text-muted-foreground truncate" title={store.category}>
                                {store.category}
                              </div>
                            </TableCell>
                          )}
                          {visibleColumns.owner && (
                            <TableCell
                              className="overflow-hidden"
                              style={{
                                width: `${columnWidths['owner']}px`,
                                maxWidth: `${columnWidths['owner']}px`,
                                borderRight: '1px solid hsl(var(--border))'
                              }}
                            >
                              <div className="font-medium truncate" title={store.owner_name || store.owner}>
                                {store.owner_name || store.owner}
                              </div>
                            </TableCell>
                          )}
                          {visibleColumns.email && (
                            <TableCell
                              className="text-sm text-muted-foreground overflow-hidden"
                              style={{
                                width: `${columnWidths['email']}px`,
                                maxWidth: `${columnWidths['email']}px`,
                                borderRight: '1px solid hsl(var(--border))'
                              }}
                            >
                              <div className="truncate" title={store.email}>
                                {store.email}
                              </div>
                            </TableCell>
                          )}
                          {visibleColumns.status && (
                            <TableCell
                              className="overflow-hidden"
                              style={{
                                width: `${columnWidths['status']}px`,
                                maxWidth: `${columnWidths['status']}px`,
                                borderRight: '1px solid hsl(var(--border))'
                              }}
                            >
                              {getStatusBadge(store.status)}
                            </TableCell>
                          )}
                          {visibleColumns.plan && (
                            <TableCell
                              className="overflow-hidden"
                              style={{
                                width: `${columnWidths['plan']}px`,
                                maxWidth: `${columnWidths['plan']}px`,
                                borderRight: '1px solid hsl(var(--border))'
                              }}
                            >
                              {getPlanBadge(store.plan)}
                            </TableCell>
                          )}
                          {visibleColumns.products && (
                            <TableCell
                              className="overflow-hidden"
                              style={{
                                width: `${columnWidths['products']}px`,
                                maxWidth: `${columnWidths['products']}px`,
                                borderRight: '1px solid hsl(var(--border))'
                              }}
                            >
                              <div className="truncate">{store.products}</div>
                            </TableCell>
                          )}
                          {visibleColumns.orders && (
                            <TableCell
                              className="overflow-hidden"
                              style={{
                                width: `${columnWidths['orders']}px`,
                                maxWidth: `${columnWidths['orders']}px`,
                                borderRight: '1px solid hsl(var(--border))'
                              }}
                            >
                              <div className="truncate">{store.orders}</div>
                            </TableCell>
                          )}
                          {visibleColumns.revenue && (
                            <TableCell
                              className="overflow-hidden"
                              style={{
                                width: `${columnWidths['revenue']}px`,
                                maxWidth: `${columnWidths['revenue']}px`,
                                borderRight: '1px solid hsl(var(--border))'
                              }}
                            >
                              <div className="truncate">{formatCurrency(store.revenue)}</div>
                            </TableCell>
                          )}
                          {visibleColumns.rating && (
                            <TableCell
                              className="overflow-hidden"
                              style={{
                                width: `${columnWidths['rating']}px`,
                                maxWidth: `${columnWidths['rating']}px`,
                                borderRight: '1px solid hsl(var(--border))'
                              }}
                            >
                              <div className="flex items-center">
                                <span className="text-yellow-500">★</span>
                                <span className="ml-1">{store.rating}</span>
                              </div>
                            </TableCell>
                          )}
                          {visibleColumns.lastActive && (
                            <TableCell
                              className="overflow-hidden"
                              style={{
                                width: `${columnWidths['lastActive']}px`,
                                maxWidth: `${columnWidths['lastActive']}px`,
                                borderRight: '1px solid hsl(var(--border))'
                              }}
                            >
                              <div className="truncate">
                                {formatDistanceToNow(new Date(store.last_active), { addSuffix: true, locale: id })}
                              </div>
                            </TableCell>
                          )}
                          {visibleColumns.joinDate && (
                            <TableCell
                              className="overflow-hidden"
                              style={{
                                width: `${columnWidths['joinDate']}px`,
                                maxWidth: `${columnWidths['joinDate']}px`,
                                borderRight: '1px solid hsl(var(--border))'
                              }}
                            >
                              <div className="truncate">
                                {new Date(store.join_date).toLocaleDateString('id-ID')}
                              </div>
                            </TableCell>
                          )}
                          <TableCell
                            className="overflow-hidden text-right"
                            style={{
                              width: `${columnWidths['actions']}px`,
                              maxWidth: `${columnWidths['actions']}px`,
                              borderRight: '1px solid hsl(var(--border))'
                            }}
                          >
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" size="icon" className="h-8 w-8">
                                  <MoreHorizontal className="h-4 w-4" />
                                  <span className="sr-only">Buka menu</span>
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuItem onClick={() => openDetailDialog(store)}>
                                  <Eye className="mr-2 h-4 w-4" />
                                  <span>Lihat Detail</span>
                                </DropdownMenuItem>
                                <DropdownMenuItem onClick={() => openEditDialog(store)}>
                                  <Edit className="mr-2 h-4 w-4" />
                                  <span>Edit</span>
                                </DropdownMenuItem>
                                <DropdownMenuItem
                                  onClick={() => openDeleteDialog(store)}
                                  className="text-red-600"
                                >
                                  <Trash2 className="h-4 w-4 mr-2" />
                                  Hapus Store
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </TableCell>
                        </TableRow>
                      ))
                    ) : (
                      <TableRow>
                        <TableCell
                          colSpan={Object.values(visibleColumns).filter(Boolean).length + 3}
                          className="h-24 text-center"
                        >
                          Tidak ada data yang ditemukan
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </div>

              {filteredStores.length === 0 && (
                <div className="text-center py-8">
                  <p className="text-muted-foreground">Tidak ada stores yang ditemukan</p>
                </div>
              )}
            </>
          )}
        </CardContent>
      </Card>

      {/* Dialog Tambah Store */}
      <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Tambah Store Baru</DialogTitle>
            <div className="text-sm text-muted-foreground">
              Isi form berikut untuk menambahkan store baru ke marketplace.
            </div>
          </DialogHeader>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(addStore)} className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Nama Store</FormLabel>
                      <FormControl>
                        <Input placeholder="Masukkan nama store" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="category"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Kategori</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Pilih kategori" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {storeCategories.map((category) => (
                            <SelectItem key={category} value={category}>
                              {category}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="owner"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Nama Pemilik</FormLabel>
                      <FormControl>
                        <Input placeholder="Masukkan nama pemilik" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Email</FormLabel>
                      <FormControl>
                        <Input placeholder="<EMAIL>" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="location"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Lokasi</FormLabel>
                      <FormControl>
                        <Input placeholder="Masukkan lokasi" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="plan"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Paket</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Pilih paket" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {storePlans.map((plan) => (
                            <SelectItem key={plan} value={plan}>
                              {plan}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="status"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Status</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Pilih status" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="active">Aktif</SelectItem>
                          <SelectItem value="pending">Pending</SelectItem>
                          <SelectItem value="suspended">Suspended</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <DialogFooter>
                <Button type="button" variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                  Batal
                </Button>
                <Button type="submit" disabled={form.formState.isSubmitting}>
                  {form.formState.isSubmitting && (
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  )}
                  Tambah Store
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      {/* Dialog Edit Store */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Edit Store</DialogTitle>
            <div className="text-sm text-muted-foreground">
              Edit informasi store berikut.
            </div>
          </DialogHeader>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(updateStoreData)} className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Nama Store</FormLabel>
                      <FormControl>
                        <Input placeholder="Masukkan nama store" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="category"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Kategori</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Pilih kategori" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {storeCategories.map((category) => (
                            <SelectItem key={category} value={category}>
                              {category}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="owner"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Nama Pemilik</FormLabel>
                      <FormControl>
                        <Input placeholder="Masukkan nama pemilik" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Email</FormLabel>
                      <FormControl>
                        <Input placeholder="<EMAIL>" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="location"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Lokasi</FormLabel>
                      <FormControl>
                        <Input placeholder="Masukkan lokasi" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="plan"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Paket</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Pilih paket" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {storePlans.map((plan) => (
                            <SelectItem key={plan} value={plan}>
                              {plan}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="status"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Status</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Pilih status" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="active">Aktif</SelectItem>
                          <SelectItem value="pending">Pending</SelectItem>
                          <SelectItem value="suspended">Suspended</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <DialogFooter>
                <Button type="button" variant="outline" onClick={() => setIsEditDialogOpen(false)}>
                  Batal
                </Button>
                <Button type="submit" disabled={form.formState.isSubmitting}>
                  {form.formState.isSubmitting && (
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  )}
                  Simpan Perubahan
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      {/* Dialog Konfirmasi Hapus Store */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Apakah Anda yakin?</AlertDialogTitle>
            <AlertDialogDescription>
              Tindakan ini akan menghapus store "{selectedStore?.name}" secara permanen dan tidak dapat dibatalkan.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Batal</AlertDialogCancel>
            <AlertDialogAction onClick={deleteStoreData} className="bg-red-600 hover:bg-red-700">
              Hapus Store
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Dialog Detail Store */}
      <Dialog open={isDetailDialogOpen} onOpenChange={setIsDetailDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Detail Store</DialogTitle>
            <div className="text-sm text-muted-foreground">
              Informasi lengkap tentang store {selectedStore?.name}
            </div>
          </DialogHeader>
          {selectedStore && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h3 className="font-semibold text-sm">Nama Store</h3>
                  <div>{selectedStore.name}</div>
                </div>
                <div>
                  <h3 className="font-semibold text-sm">Kategori</h3>
                  <div>{selectedStore.category}</div>
                </div>
                <div>
                  <h3 className="font-semibold text-sm">Pemilik</h3>
                  <div>{selectedStore.owner_name || selectedStore.owner}</div>
                </div>
                <div>
                  <h3 className="font-semibold text-sm">Email</h3>
                  <div>{selectedStore.email}</div>
                </div>
                <div>
                  <h3 className="font-semibold text-sm">Lokasi</h3>
                  <div>{selectedStore.location}</div>
                </div>
                <div>
                  <h3 className="font-semibold text-sm">Status</h3>
                  <div>{getStatusBadge(selectedStore.status)}</div>
                </div>
                <div>
                  <h3 className="font-semibold text-sm">Paket</h3>
                  <div>{getPlanBadge(selectedStore.plan)}</div>
                </div>
                <div>
                  <h3 className="font-semibold text-sm">Tanggal Bergabung</h3>
                  <div>{selectedStore.join_date}</div>
                </div>
                <div>
                  <h3 className="font-semibold text-sm">Total Produk</h3>
                  <div>{selectedStore.products}</div>
                </div>
                <div>
                  <h3 className="font-semibold text-sm">Total Order</h3>
                  <div>{selectedStore.orders}</div>
                </div>
                <div>
                  <h3 className="font-semibold text-sm">Revenue</h3>
                  <div>{formatCurrency(selectedStore.revenue)}</div>
                </div>
                <div>
                  <h3 className="font-semibold text-sm">Rating</h3>
                  <div className="flex items-center">
                    <span className="text-yellow-500">★</span>
                    <span className="ml-1">{selectedStore.rating}</span>
                  </div>
                </div>
              </div>

              <DialogFooter>
                <Button onClick={() => setIsDetailDialogOpen(false)}>Tutup</Button>
                <Button variant="outline" onClick={() => {
                  setIsDetailDialogOpen(false);
                  openEditDialog(selectedStore);
                }}>
                  Edit Store
                </Button>
              </DialogFooter>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Dialog Pengaturan Store */}
      <Dialog open={isSettingsDialogOpen} onOpenChange={setIsSettingsDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Pengaturan Store</DialogTitle>
            <div className="text-sm text-muted-foreground">
              Konfigurasi pengaturan untuk store {selectedStore?.name}
            </div>
          </DialogHeader>
          {selectedStore && (
            <div className="space-y-6">
              <div className="space-y-4">
                <h3 className="text-sm font-medium">Pengaturan Umum</h3>
                <div className="flex items-center justify-between border-b pb-3">
                  <div>
                    <h4 className="font-medium">Notifikasi Email</h4>
                    <div className="text-sm text-muted-foreground">Kirim notifikasi ke pemilik store</div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button
                      variant={emailNotificationsEnabled ? "default" : "outline"}
                      size="sm"
                      disabled={isUpdatingSettings}
                      onClick={() => toggleEmailNotifications(selectedStore.id, !emailNotificationsEnabled)}
                    >
                      {isUpdatingSettings ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Loading...
                        </>
                      ) : (
                        emailNotificationsEnabled ? "Aktif" : "Aktifkan"
                      )}
                    </Button>
                  </div>
                </div>

                <div className="flex items-center justify-between border-b pb-3">
                  <div>
                    <h4 className="font-medium">Verifikasi Produk</h4>
                    <div className="text-sm text-muted-foreground">Wajibkan verifikasi produk baru</div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button
                      variant={productVerificationEnabled ? "default" : "outline"}
                      size="sm"
                      disabled={isUpdatingSettings}
                      onClick={() => toggleProductVerification(selectedStore.id, !productVerificationEnabled)}
                    >
                      {isUpdatingSettings ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Loading...
                        </>
                      ) : (
                        productVerificationEnabled ? "Aktif" : "Aktifkan"
                      )}
                    </Button>
                  </div>
                </div>

                <div className="flex items-center justify-between border-b pb-3">
                  <div>
                    <h4 className="font-medium">Tampilkan di Marketplace</h4>
                    <div className="text-sm text-muted-foreground">Tampilkan store di halaman utama</div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button
                      variant={selectedStore.status === "active" ? "default" : "outline"}
                      size="sm"
                      disabled={isUpdatingSettings}
                      onClick={async () => {
                        setIsUpdatingSettings(true);
                        try {
                          const newStatus = selectedStore.status === "active" ? "pending" : "active";
                          const { success, error } = await updateStoreStatusService(selectedStore.id, newStatus as any);

                          if (error) throw error;

                          // Update local state jika operasi berhasil
                          selectedStore.status = newStatus as any;
                          setStoresData(prevStores =>
                            prevStores.map(store =>
                              store.id === selectedStore.id ? { ...store, status: newStatus } : store
                            )
                          );

                          alert(selectedStore.status === "active" ?
                            "Store sekarang ditampilkan di marketplace" :
                            "Store tidak lagi ditampilkan di marketplace");
                        } catch (error) {
                          console.error("Error updating store visibility:", error);
                          alert("Gagal mengubah visibilitas store. Silakan coba lagi.");
                        } finally {
                          setIsUpdatingSettings(false);
                        }
                      }}
                    >
                      {isUpdatingSettings ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Loading...
                        </>
                      ) : (
                        selectedStore.status === "active" ? "Aktif" : "Nonaktif"
                      )}
                    </Button>
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <h3 className="text-sm font-medium">Tindakan</h3>
                <div className="flex items-center justify-between border-b pb-3">
                  <div>
                    <h4 className="font-medium">Reset Password</h4>
                    <div className="text-sm text-muted-foreground">Kirim email reset password ke pemilik store</div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        sendResetPasswordEmail(selectedStore.email);
                      }}
                    >
                      Kirim
                    </Button>
                  </div>
                </div>

                <div className="flex items-center justify-between border-b pb-3">
                  <div>
                    <h4 className="font-medium">Ekspor Data</h4>
                    <div className="text-sm text-muted-foreground">Ekspor data store dalam format CSV</div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        exportStoreData(selectedStore);
                      }}
                    >
                      Ekspor
                    </Button>
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium text-red-500">Bekukan Store</h4>
                    <div className="text-sm text-muted-foreground">Bekukan store ini sementara</div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="destructive"
                      size="sm"
                      onClick={() => {
                        changeStoreStatus(selectedStore.id, "suspended");
                        setIsSettingsDialogOpen(false);
                        alert("Store telah dibekukan");
                      }}
                    >
                      Bekukan
                    </Button>
                  </div>
                </div>
              </div>

              <DialogFooter>
                <Button onClick={() => setIsSettingsDialogOpen(false)}>Tutup</Button>
              </DialogFooter>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
}
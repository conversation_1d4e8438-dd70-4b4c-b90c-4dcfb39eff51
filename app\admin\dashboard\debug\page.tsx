"use client"

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { useToast } from '@/hooks/use-toast'
import { 
  Bug, 
  Settings, 
  Globe, 
  CheckCircle, 
  AlertCircle,
  Loader2,
  RefreshCw
} from 'lucide-react'

export default function DebugPage() {
  const [envData, setEnvData] = useState<any>(null)
  const [dnsResult, setDnsResult] = useState<any>(null)
  const [cloudflareTest, setCloudflareTest] = useState<any>(null)
  const [testSubdomain, setTestSubdomain] = useState('test-debug')
  const [loading, setLoading] = useState(false)
  const { toast } = useToast()

  const checkEnvironment = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/debug/env-check')
      const data = await response.json()
      setEnvData(data)
      
      if (data.success) {
        toast({
          title: "Environment Check Complete",
          description: "Environment variables loaded successfully",
        })
      }
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to check environment",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  const testDNSCreation = async () => {
    if (!testSubdomain) {
      toast({
        title: "Error",
        description: "Please enter a subdomain to test",
        variant: "destructive"
      })
      return
    }

    setLoading(true)
    try {
      const response = await fetch('/api/dns/create-subdomain', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          subdomain: testSubdomain
        })
      })

      const data = await response.json()
      setDnsResult(data)
      
      if (data.success) {
        toast({
          title: "DNS Test Successful",
          description: `DNS record created for ${testSubdomain}.sellzio.my.id`,
        })
      } else {
        toast({
          title: "DNS Test Failed",
          description: data.error || "Failed to create DNS record",
          variant: "destructive"
        })
      }
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to test DNS creation",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  const checkDNSRecord = async () => {
    if (!testSubdomain) return

    setLoading(true)
    try {
      const response = await fetch(`/api/dns/create-subdomain?subdomain=${testSubdomain}`)
      const data = await response.json()

      toast({
        title: data.exists ? "DNS Record Found" : "DNS Record Not Found",
        description: `Record for ${testSubdomain}.sellzio.my.id ${data.exists ? 'exists' : 'does not exist'}`,
        variant: data.exists ? "default" : "destructive"
      })
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to check DNS record",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  const testCloudflareConnection = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/debug/cloudflare-test')
      const data = await response.json()
      setCloudflareTest(data)

      if (data.success && data.summary.allTestsPassed) {
        toast({
          title: "Cloudflare Connection OK",
          description: "All Cloudflare API tests passed successfully",
        })
      } else {
        toast({
          title: "Cloudflare Connection Issues",
          description: "Some Cloudflare API tests failed",
          variant: "destructive"
        })
      }
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to test Cloudflare connection",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  const testDirectDNSCreation = async () => {
    if (!testSubdomain) {
      toast({
        title: "Error",
        description: "Please enter a subdomain to test",
        variant: "destructive"
      })
      return
    }

    setLoading(true)
    try {
      const response = await fetch('/api/debug/cloudflare-test', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          testSubdomain: testSubdomain
        })
      })

      const data = await response.json()

      if (data.success) {
        toast({
          title: "Direct DNS Creation Successful",
          description: `DNS record created directly via Cloudflare API for ${testSubdomain}.sellzio.my.id`,
        })
      } else {
        toast({
          title: "Direct DNS Creation Failed",
          description: data.message || "Failed to create DNS record via direct API call",
          variant: "destructive"
        })
      }
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to test direct DNS creation",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Page Header */}
      <div className="space-y-2">
        <h1 className="text-3xl font-bold tracking-tight flex items-center gap-2">
          <Bug className="h-8 w-8" />
          Debug Dashboard
        </h1>
        <p className="text-muted-foreground">
          Debug dan test sistem Auto DNS untuk subdomain tenant
        </p>
      </div>

      {/* Environment Check */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Environment Variables Check
          </CardTitle>
          <CardDescription>
            Verifikasi konfigurasi environment variables
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <Button onClick={checkEnvironment} disabled={loading}>
            {loading ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Checking...
              </>
            ) : (
              <>
                <RefreshCw className="h-4 w-4 mr-2" />
                Check Environment
              </>
            )}
          </Button>

          {envData && (
            <div className="space-y-4">
              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <h4 className="font-medium">Cloudflare Configuration</h4>
                  <div className="space-y-1">
                    <div className="flex items-center gap-2">
                      <Badge variant={envData.environment.CLOUDFLARE_API_TOKEN?.exists ? "default" : "destructive"}>
                        {envData.environment.CLOUDFLARE_API_TOKEN?.exists ? (
                          <CheckCircle className="h-3 w-3 mr-1" />
                        ) : (
                          <AlertCircle className="h-3 w-3 mr-1" />
                        )}
                        API Token
                      </Badge>
                      <span className="text-sm text-muted-foreground">
                        {envData.environment.CLOUDFLARE_API_TOKEN?.preview}
                      </span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge variant={envData.environment.CLOUDFLARE_ZONE_ID?.exists ? "default" : "destructive"}>
                        {envData.environment.CLOUDFLARE_ZONE_ID?.exists ? (
                          <CheckCircle className="h-3 w-3 mr-1" />
                        ) : (
                          <AlertCircle className="h-3 w-3 mr-1" />
                        )}
                        Zone ID
                      </Badge>
                      <span className="text-sm text-muted-foreground">
                        {envData.environment.CLOUDFLARE_ZONE_ID?.preview}
                      </span>
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <h4 className="font-medium">System Configuration</h4>
                  <div className="space-y-1">
                    <div className="flex items-center gap-2">
                      <Badge variant="outline">Vercel IP</Badge>
                      <span className="text-sm text-muted-foreground">
                        {typeof envData.environment.VERCEL_IP === 'object'
                          ? envData.environment.VERCEL_IP?.value || 'NOT_SET'
                          : envData.environment.VERCEL_IP || 'NOT_SET'}
                      </span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge variant="outline">Environment</Badge>
                      <span className="text-sm text-muted-foreground">
                        {envData.environment.NODE_ENV || 'NOT_SET'}
                      </span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge variant="outline">Domain</Badge>
                      <span className="text-sm text-muted-foreground">
                        {envData.environment.NEXT_PUBLIC_MAIN_DOMAIN || 'NOT_SET'}
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              {(!envData.environment.CLOUDFLARE_API_TOKEN?.exists || !envData.environment.CLOUDFLARE_ZONE_ID?.exists) && (
                <Alert variant="destructive">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>
                    <strong>Missing Cloudflare Credentials!</strong> 
                    Tambahkan CLOUDFLARE_API_TOKEN dan CLOUDFLARE_ZONE_ID di Vercel Environment Variables.
                  </AlertDescription>
                </Alert>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Cloudflare Connection Test */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Cloudflare API Test
          </CardTitle>
          <CardDescription>
            Test koneksi langsung ke Cloudflare API
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <Button onClick={testCloudflareConnection} disabled={loading}>
            {loading ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Testing...
              </>
            ) : (
              <>
                <Globe className="h-4 w-4 mr-2" />
                Test Cloudflare Connection
              </>
            )}
          </Button>

          {cloudflareTest && (
            <div className="space-y-4">
              <div className="grid gap-4 md:grid-cols-3">
                <div className="space-y-2">
                  <h4 className="font-medium">Token Verification</h4>
                  <Badge variant={cloudflareTest.tests?.tokenVerification?.success ? "default" : "destructive"}>
                    {cloudflareTest.tests?.tokenVerification?.success ? (
                      <CheckCircle className="h-3 w-3 mr-1" />
                    ) : (
                      <AlertCircle className="h-3 w-3 mr-1" />
                    )}
                    {cloudflareTest.tests?.tokenVerification?.success ? 'Valid' : 'Invalid'}
                  </Badge>
                </div>
                <div className="space-y-2">
                  <h4 className="font-medium">Zone Access</h4>
                  <Badge variant={cloudflareTest.tests?.zoneAccess?.success ? "default" : "destructive"}>
                    {cloudflareTest.tests?.zoneAccess?.success ? (
                      <CheckCircle className="h-3 w-3 mr-1" />
                    ) : (
                      <AlertCircle className="h-3 w-3 mr-1" />
                    )}
                    {cloudflareTest.tests?.zoneAccess?.success ? 'Accessible' : 'No Access'}
                  </Badge>
                </div>
                <div className="space-y-2">
                  <h4 className="font-medium">DNS Access</h4>
                  <Badge variant={cloudflareTest.tests?.dnsAccess?.success ? "default" : "destructive"}>
                    {cloudflareTest.tests?.dnsAccess?.success ? (
                      <CheckCircle className="h-3 w-3 mr-1" />
                    ) : (
                      <AlertCircle className="h-3 w-3 mr-1" />
                    )}
                    {cloudflareTest.tests?.dnsAccess?.success ? 'Can Read' : 'No Access'}
                  </Badge>
                </div>
              </div>

              {!cloudflareTest.summary?.allTestsPassed && (
                <Alert variant="destructive">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>
                    <strong>Cloudflare API Issues Detected!</strong>
                    Periksa API Token permissions dan Zone ID.
                  </AlertDescription>
                </Alert>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* DNS Test */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Globe className="h-5 w-5" />
            DNS Creation Test
          </CardTitle>
          <CardDescription>
            Test pembuatan DNS record secara manual
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center space-x-2">
            <Label htmlFor="testSubdomain">Test Subdomain:</Label>
            <Input
              id="testSubdomain"
              value={testSubdomain}
              onChange={(e) => setTestSubdomain(e.target.value.toLowerCase())}
              placeholder="test-subdomain"
              className="max-w-xs"
            />
            <span className="text-sm text-muted-foreground">.sellzio.my.id</span>
          </div>

          <div className="flex gap-2 flex-wrap">
            <Button onClick={testDNSCreation} disabled={loading || !testSubdomain}>
              {loading ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Creating...
                </>
              ) : (
                <>
                  <Globe className="h-4 w-4 mr-2" />
                  Create DNS (Service)
                </>
              )}
            </Button>

            <Button variant="outline" onClick={testDirectDNSCreation} disabled={loading || !testSubdomain}>
              {loading ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Creating...
                </>
              ) : (
                <>
                  <Settings className="h-4 w-4 mr-2" />
                  Create DNS (Direct)
                </>
              )}
            </Button>

            <Button variant="outline" onClick={checkDNSRecord} disabled={loading || !testSubdomain}>
              Check Record
            </Button>
          </div>

          {dnsResult && (
            <div className="space-y-2">
              <h4 className="font-medium">DNS Test Result:</h4>
              <div className="p-4 bg-gray-50 rounded-lg">
                <pre className="text-sm overflow-auto">
                  {JSON.stringify(dnsResult, null, 2)}
                </pre>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Instructions */}
      <Alert>
        <Settings className="h-4 w-4" />
        <AlertDescription>
          <strong>Troubleshooting:</strong>
          <ol className="list-decimal list-inside mt-2 space-y-1">
            <li>Pastikan Cloudflare API Token dan Zone ID sudah dikonfigurasi</li>
            <li>Test DNS creation dengan subdomain unik</li>
            <li>Cek apakah DNS record berhasil dibuat di Cloudflare dashboard</li>
            <li>Tunggu 1-2 menit untuk propagasi DNS</li>
          </ol>
        </AlertDescription>
      </Alert>
    </div>
  )
}

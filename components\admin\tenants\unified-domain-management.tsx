"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON>bs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Textarea } from "@/components/ui/textarea"
import { useToast } from "@/hooks/use-toast"
import {
  CheckCircle,
  XCircle,
  AlertTriangle,
  Search,
  Clock,
  Globe,
  Settings,
  User,
  Calendar,
  ExternalLink,
  Shield,
  RefreshCw,
} from "lucide-react"
import Link from "next/link"

interface CustomDomainRequest {
  id: string
  domain: string
  tenant_id: string
  reason: string
  status: 'pending' | 'approved' | 'rejected' | 'configuration_failed'
  requested_at: string
  processed_at?: string
  admin_notes?: string
  tenants: {
    id: string
    name: string
    subdomain: string
    domain?: string
  }
}

interface TenantData {
  id: string
  name: string
  slug: string
  domain: string | null
  subscriptionPlan: string
  subscriptionStatus: string
  createdAt: string
}

export function UnifiedDomainManagement() {
  const [searchQuery, setSearchQuery] = useState("")
  const [activeTab, setActiveTab] = useState("requests")
  const [customDomainRequests, setCustomDomainRequests] = useState<CustomDomainRequest[]>([])
  const [tenants, setTenants] = useState<TenantData[]>([])
  const [loading, setLoading] = useState(true)
  const [processingId, setProcessingId] = useState<string | null>(null)
  const [adminNotes, setAdminNotes] = useState<string>('')
  const [selectedRequest, setSelectedRequest] = useState<CustomDomainRequest | null>(null)
  const { toast } = useToast()

  // Fetch data
  useEffect(() => {
    fetchCustomDomainRequests()
    fetchTenants()
  }, [])

  const fetchCustomDomainRequests = async () => {
    try {
      const response = await fetch('/api/admin/custom-domain-requests?status=all')
      const data = await response.json()
      
      if (data.success) {
        setCustomDomainRequests(data.requests || [])
      }
    } catch (error) {
      console.error('Error fetching custom domain requests:', error)
    } finally {
      setLoading(false)
    }
  }

  const fetchTenants = async () => {
    try {
      const response = await fetch('/api/tenants/list?orderBy=created_at&orderDirection=desc')
      const result = await response.json()
      
      if (result.success) {
        setTenants(result.data || [])
      }
    } catch (error) {
      console.error('Error fetching tenants:', error)
    }
  }

  const handleApproveReject = async (requestId: string, action: 'approve' | 'reject') => {
    if (!adminNotes.trim() && action === 'reject') {
      toast({
        title: "Error",
        description: "Please provide admin notes for rejection",
        variant: "destructive"
      })
      return
    }

    setProcessingId(requestId)
    try {
      const response = await fetch('/api/admin/custom-domain-requests', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          requestId,
          action,
          adminNotes: adminNotes.trim()
        })
      })

      const data = await response.json()
      
      if (data.success) {
        toast({
          title: "Success",
          description: data.message,
        })
        setAdminNotes('')
        setSelectedRequest(null)
        fetchCustomDomainRequests() // Refresh list
      } else {
        throw new Error(data.error)
      }
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || `Failed to ${action} domain request`,
        variant: "destructive"
      })
    } finally {
      setProcessingId(null)
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return <Badge variant="outline" className="text-yellow-600"><Clock className="h-3 w-3 mr-1" />Pending</Badge>
      case 'approved':
        return <Badge variant="default" className="text-green-600"><CheckCircle className="h-3 w-3 mr-1" />Approved</Badge>
      case 'rejected':
        return <Badge variant="destructive"><XCircle className="h-3 w-3 mr-1" />Rejected</Badge>
      case 'configuration_failed':
        return <Badge variant="destructive"><AlertTriangle className="h-3 w-3 mr-1" />Config Failed</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('id-ID', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const filteredRequests = customDomainRequests.filter(
    (request) =>
      request.domain.toLowerCase().includes(searchQuery.toLowerCase()) ||
      request.tenants?.name.toLowerCase().includes(searchQuery.toLowerCase()),
  )

  const filteredTenants = tenants.filter(
    (tenant) =>
      tenant.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      tenant.slug.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (tenant.domain && tenant.domain.toLowerCase().includes(searchQuery.toLowerCase()))
  )

  const pendingCount = customDomainRequests.filter(req => req.status === 'pending').length

  return (
    <div className="space-y-6">
      {/* Pending Requests Alert */}
      {pendingCount > 0 && (
        <Alert>
          <Clock className="h-4 w-4" />
          <AlertTitle>Pending Custom Domain Requests</AlertTitle>
          <AlertDescription>
            You have {pendingCount} pending custom domain request(s) awaiting approval.
          </AlertDescription>
        </Alert>
      )}

      {/* Search */}
      <div className="flex items-center justify-between">
        <div className="relative w-64">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search domains or tenants..."
            className="pl-8"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        <Link href="/admin/dashboard/debug">
          <Button variant="outline">
            <Settings className="mr-2 h-4 w-4" />
            DNS Debug Tools
          </Button>
        </Link>
      </div>

      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList>
          <TabsTrigger value="requests">
            Custom Domain Requests
            {pendingCount > 0 && (
              <Badge variant="destructive" className="ml-2 h-5 w-5 rounded-full p-0 text-xs">
                {pendingCount}
              </Badge>
            )}
          </TabsTrigger>
          <TabsTrigger value="tenants">Tenant Overview</TabsTrigger>
        </TabsList>

        {/* Custom Domain Requests Tab */}
        <TabsContent value="requests" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Custom Domain Requests</CardTitle>
              <CardDescription>
                Review and approve custom domain requests from tenants
              </CardDescription>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="text-center py-6">Loading requests...</div>
              ) : filteredRequests.length === 0 ? (
                <div className="text-center py-6 text-muted-foreground">
                  No custom domain requests found
                </div>
              ) : (
                <div className="space-y-4">
                  {filteredRequests.map((request) => (
                    <div key={request.id} className="border rounded-lg p-4 space-y-4">
                      <div className="flex items-center justify-between">
                        <div className="space-y-1">
                          <div className="flex items-center gap-2">
                            <Globe className="h-5 w-5" />
                            <span className="font-medium">{request.domain}</span>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => window.open(`https://${request.domain}`, '_blank')}
                            >
                              <ExternalLink className="h-4 w-4" />
                            </Button>
                          </div>
                          <div className="flex items-center gap-4 text-sm text-muted-foreground">
                            <span className="flex items-center gap-1">
                              <User className="h-4 w-4" />
                              {request.tenants?.name} ({request.tenants?.subdomain})
                            </span>
                            <span className="flex items-center gap-1">
                              <Calendar className="h-4 w-4" />
                              {formatDate(request.requested_at)}
                            </span>
                          </div>
                        </div>
                        {getStatusBadge(request.status)}
                      </div>

                      <div className="space-y-2">
                        <div>
                          <span className="text-sm font-medium">Reason:</span>
                          <p className="text-sm text-muted-foreground">{request.reason}</p>
                        </div>
                        
                        {request.admin_notes && (
                          <div>
                            <span className="text-sm font-medium">Admin Notes:</span>
                            <p className="text-sm text-muted-foreground">{request.admin_notes}</p>
                          </div>
                        )}
                      </div>

                      {request.status === 'pending' && (
                        <div className="space-y-4 pt-4 border-t">
                          <div className="space-y-2">
                            <label className="text-sm font-medium">Admin Notes:</label>
                            <Textarea
                              value={selectedRequest?.id === request.id ? adminNotes : ''}
                              onChange={(e) => {
                                setAdminNotes(e.target.value)
                                setSelectedRequest(request)
                              }}
                              placeholder="Add notes for this request..."
                              className="min-h-[80px]"
                            />
                          </div>
                          
                          <div className="flex gap-2">
                            <Button
                              onClick={() => handleApproveReject(request.id, 'approve')}
                              disabled={processingId === request.id}
                              className="bg-green-600 hover:bg-green-700"
                            >
                              <CheckCircle className="h-4 w-4 mr-2" />
                              {processingId === request.id ? 'Processing...' : 'Approve'}
                            </Button>
                            
                            <Button
                              variant="destructive"
                              onClick={() => handleApproveReject(request.id, 'reject')}
                              disabled={processingId === request.id}
                            >
                              <XCircle className="h-4 w-4 mr-2" />
                              {processingId === request.id ? 'Processing...' : 'Reject'}
                            </Button>
                          </div>
                        </div>
                      )}

                      {request.processed_at && (
                        <div className="text-sm text-muted-foreground pt-2 border-t">
                          Processed: {formatDate(request.processed_at)}
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Tenant Overview Tab */}
        <TabsContent value="tenants" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Tenant Domain Overview</CardTitle>
              <CardDescription>
                Overview of all tenant domains and their status
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Tenant</TableHead>
                      <TableHead>Subdomain</TableHead>
                      <TableHead>Custom Domain</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredTenants.map((tenant) => (
                      <TableRow key={tenant.id}>
                        <TableCell className="font-medium">{tenant.name}</TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <span>{tenant.slug}.sellzio.my.id</span>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => window.open(`https://${tenant.slug}.sellzio.my.id`, '_blank')}
                            >
                              <ExternalLink className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                        <TableCell>
                          {tenant.domain ? (
                            <div className="flex items-center gap-2">
                              <span>{tenant.domain}</span>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => window.open(`https://${tenant.domain}`, '_blank')}
                              >
                                <ExternalLink className="h-4 w-4" />
                              </Button>
                            </div>
                          ) : (
                            <span className="text-muted-foreground">Not set</span>
                          )}
                        </TableCell>
                        <TableCell>
                          <Badge variant={tenant.subscriptionStatus === 'active' ? 'default' : 'secondary'}>
                            {tenant.subscriptionPlan} - {tenant.subscriptionStatus}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <Button variant="outline" size="sm">
                              <RefreshCw className="h-4 w-4 mr-1" />
                              Verify
                            </Button>
                            <Button variant="outline" size="sm">
                              <Shield className="h-4 w-4 mr-1" />
                              SSL
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

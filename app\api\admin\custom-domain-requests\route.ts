import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

function getClient() {
  return createClient(supabaseUrl, supabaseServiceKey)
}

// GET - List all custom domain requests
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const status = searchParams.get('status') || 'all'

    console.log('🔥 API: Fetching custom domain requests, status:', status)

    const supabase = getClient()

    let query = supabase
      .from('custom_domain_requests')
      .select(`
        *,
        tenants (
          id,
          name,
          subdomain,
          domain
        )
      `)
      .order('created_at', { ascending: false })

    if (status !== 'all') {
      query = query.eq('status', status)
    }

    const { data, error } = await query

    if (error) {
      console.error('🔥 API: Error fetching custom domain requests:', error)
      return NextResponse.json(
        { error: 'Failed to fetch custom domain requests' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      requests: data || []
    })

  } catch (error) {
    console.error('🔥 API: Error in custom domain requests:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// POST - Create new custom domain request
export async function POST(request: NextRequest) {
  try {
    const { domain, tenantId, reason } = await request.json()

    if (!domain || !tenantId) {
      return NextResponse.json(
        { error: 'Domain and tenantId are required' },
        { status: 400 }
      )
    }

    console.log('🔥 API: Creating custom domain request:', { domain, tenantId })

    const supabase = getClient()

    // Check if request already exists for this domain
    const { data: existingRequest } = await supabase
      .from('custom_domain_requests')
      .select('id, status')
      .eq('domain', domain)
      .eq('tenant_id', tenantId)
      .single()

    if (existingRequest) {
      if (existingRequest.status === 'pending') {
        return NextResponse.json(
          { error: 'A request for this domain is already pending approval' },
          { status: 409 }
        )
      }
    }

    // Create new request
    const { data, error } = await supabase
      .from('custom_domain_requests')
      .insert({
        domain,
        tenant_id: tenantId,
        reason: reason || 'Custom domain request',
        status: 'pending',
        requested_at: new Date().toISOString()
      })
      .select()
      .single()

    if (error) {
      console.error('🔥 API: Error creating custom domain request:', error)
      return NextResponse.json(
        { error: 'Failed to create custom domain request' },
        { status: 500 }
      )
    }

    console.log('🔥 API: Custom domain request created:', data)

    return NextResponse.json({
      success: true,
      request: data,
      message: 'Custom domain request submitted successfully. Please wait for admin approval.'
    })

  } catch (error) {
    console.error('🔥 API: Error creating custom domain request:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// PATCH - Approve/Reject custom domain request
export async function PATCH(request: NextRequest) {
  try {
    const { requestId, action, adminNotes } = await request.json()

    if (!requestId || !action) {
      return NextResponse.json(
        { error: 'RequestId and action are required' },
        { status: 400 }
      )
    }

    if (!['approve', 'reject'].includes(action)) {
      return NextResponse.json(
        { error: 'Action must be approve or reject' },
        { status: 400 }
      )
    }

    console.log('🔥 API: Processing custom domain request:', { requestId, action })

    const supabase = getClient()

    // Get request details
    const { data: request, error: fetchError } = await supabase
      .from('custom_domain_requests')
      .select('*')
      .eq('id', requestId)
      .single()

    if (fetchError || !request) {
      return NextResponse.json(
        { error: 'Custom domain request not found' },
        { status: 404 }
      )
    }

    if (request.status !== 'pending') {
      return NextResponse.json(
        { error: 'Request has already been processed' },
        { status: 400 }
      )
    }

    // Update request status
    const { data: updatedRequest, error: updateError } = await supabase
      .from('custom_domain_requests')
      .update({
        status: action === 'approve' ? 'approved' : 'rejected',
        admin_notes: adminNotes,
        processed_at: new Date().toISOString()
      })
      .eq('id', requestId)
      .select()
      .single()

    if (updateError) {
      console.error('🔥 API: Error updating request status:', updateError)
      return NextResponse.json(
        { error: 'Failed to update request status' },
        { status: 500 }
      )
    }

    // If approved, configure the custom domain
    if (action === 'approve') {
      try {
        // Call the custom domain configuration API
        const configResponse = await fetch(`${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/api/tenants/custom-domain`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            domain: request.domain,
            tenantId: request.tenant_id,
            skipVerification: true // Skip verification for admin-approved domains
          })
        })

        const configData = await configResponse.json()

        if (!configResponse.ok || !configData.success) {
          console.error('🔥 API: Failed to configure approved domain:', configData)
          // Update request with error
          await supabase
            .from('custom_domain_requests')
            .update({
              status: 'configuration_failed',
              admin_notes: `${adminNotes}\n\nConfiguration Error: ${configData.error || 'Unknown error'}`
            })
            .eq('id', requestId)

          return NextResponse.json({
            success: false,
            error: 'Domain approved but configuration failed',
            details: configData.error
          }, { status: 500 })
        }

        console.log('🔥 API: Custom domain configured successfully after approval')

      } catch (configError) {
        console.error('🔥 API: Error configuring approved domain:', configError)
        
        // Update request with error
        await supabase
          .from('custom_domain_requests')
          .update({
            status: 'configuration_failed',
            admin_notes: `${adminNotes}\n\nConfiguration Error: ${configError}`
          })
          .eq('id', requestId)

        return NextResponse.json({
          success: false,
          error: 'Domain approved but configuration failed'
        }, { status: 500 })
      }
    }

    console.log('🔥 API: Custom domain request processed successfully:', updatedRequest)

    return NextResponse.json({
      success: true,
      request: updatedRequest,
      message: action === 'approve' 
        ? 'Custom domain approved and configured successfully'
        : 'Custom domain request rejected'
    })

  } catch (error) {
    console.error('🔥 API: Error processing custom domain request:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

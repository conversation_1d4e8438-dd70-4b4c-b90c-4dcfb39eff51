"use client"

import React, { useState } from 'react'
import { ArrowLeft, Search, X } from 'lucide-react'

interface Voucher {
  id: string
  type: string
  title: string
  description: string
  amount: string
  minPurchase: string
  validUntil: string
  isRecommended?: boolean
  isDisabled?: boolean
}

interface SellzioVoucherModalProps {
  isOpen: boolean
  onClose: () => void
  onApplyVouchers: (vouchers: any[]) => void
  appliedVouchers: any[]
}

export const SellzioVoucherModal: React.FC<SellzioVoucherModalProps> = ({
  isOpen,
  onClose,
  onApplyVouchers,
  appliedVouchers
}) => {
  const [activeTab, setActiveTab] = useState('semua')
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedVouchers, setSelectedVouchers] = useState<string[]>([])
  const [showSearchSuggestions, setShowSearchSuggestions] = useState(false)

  // Sample voucher data - Sesuai dengan halaman voucher
  const sampleVouchers: Voucher[] = [
    // Discount Vouchers
    {
      id: 'v1',
      type: 'discount',
      title: 'Diskon Rp50.000',
      description: 'Potongan langsung untuk pembelian fashion',
      amount: 'Rp50rb',
      minPurchase: 'Min. belanja Rp200rb',
      validUntil: 'Berlaku hingga 31 Des',
      isRecommended: true
    },
    {
      id: 'v2',
      type: 'discount',
      title: 'Diskon Rp20.000',
      description: 'Potongan langsung untuk pembelian elektronik',
      amount: 'Rp20rb',
      minPurchase: 'Min. belanja Rp100rb',
      validUntil: 'Berlaku hingga 30 Des'
    },
    {
      id: 'v3',
      type: 'discount',
      title: 'Diskon Rp15.000',
      description: 'Potongan untuk produk rumah tangga',
      amount: 'Rp15rb',
      minPurchase: 'Min. belanja Rp75rb',
      validUntil: 'Berlaku hingga 29 Des'
    },
    // Shipping Vouchers
    {
      id: 'v4',
      type: 'shipping',
      title: 'Gratis Ongkir',
      description: 'Gratis ongkos kirim ke seluruh Indonesia',
      amount: 'Gratis',
      minPurchase: 'Ongkir s/d 20rb',
      validUntil: 'Berlaku hingga 25 Des',
      isRecommended: true
    },
    {
      id: 'v5',
      type: 'shipping',
      title: 'Gratis Ongkir Express',
      description: 'Gratis ongkir untuk pengiriman express',
      amount: 'Gratis',
      minPurchase: 'Ongkir s/d 15rb',
      validUntil: 'Berlaku hingga 24 Des'
    },
    {
      id: 'v6',
      type: 'shipping',
      title: 'Gratis Ongkir Premium',
      description: 'Gratis ongkir untuk pengiriman premium',
      amount: 'Gratis',
      minPurchase: 'Ongkir s/d 30rb',
      validUntil: 'Berlaku hingga 26 Des'
    },
    {
      id: 'v7',
      type: 'shipping',
      title: 'Gratis Ongkir Same Day',
      description: 'Gratis ongkir untuk pengiriman same day',
      amount: 'Gratis',
      minPurchase: 'Ongkir s/d 25rb',
      validUntil: 'Berlaku hingga 23 Des'
    },
    // Cashback Vouchers
    {
      id: 'v8',
      type: 'cashback',
      title: 'Cashback 10%',
      description: 'Cashback untuk pembelian fashion',
      amount: '10%',
      minPurchase: 'Maks. Rp35rb',
      validUntil: 'Berlaku hingga 28 Des'
    },
    {
      id: 'v9',
      type: 'cashback',
      title: 'Cashback 15%',
      description: 'Cashback untuk pembelian elektronik',
      amount: '15%',
      minPurchase: 'Maks. Rp30rb',
      validUntil: 'Berlaku hingga 27 Des',
      isRecommended: true
    },
    // Payment Vouchers
    {
      id: 'v10',
      type: 'payment',
      title: 'Diskon COD',
      description: 'Diskon khusus pembayaran COD',
      amount: 'Rp10rb',
      minPurchase: 'Bayar dengan COD',
      validUntil: 'Berlaku hingga 31 Des'
    },
    {
      id: 'v11',
      type: 'payment',
      title: 'Diskon Transfer Bank',
      description: 'Diskon khusus transfer bank',
      amount: '5%',
      minPurchase: 'Maks. Rp25rb',
      validUntil: 'Berlaku hingga 30 Des'
    }
  ]

  const tabs = [
    { id: 'semua', label: 'Semua' },
    { id: 'discount', label: 'Diskon' },
    { id: 'shipping', label: 'Gratis Ongkir' },
    { id: 'cashback', label: 'Cashback' },
    { id: 'payment', label: 'Pembayaran' }
  ]

  const filteredVouchers = sampleVouchers.filter(voucher => {
    const matchesTab = activeTab === 'semua' || voucher.type === activeTab
    const matchesSearch = voucher.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         voucher.description.toLowerCase().includes(searchQuery.toLowerCase())
    return matchesTab && matchesSearch
  })

  const handleVoucherSelect = (voucherId: string) => {
    setSelectedVouchers(prev => {
      if (prev.includes(voucherId)) {
        return prev.filter(id => id !== voucherId)
      } else {
        return [...prev, voucherId]
      }
    })
  }

  const handleApplyVouchers = () => {
    const selectedVoucherData = sampleVouchers
      .filter(v => selectedVouchers.includes(v.id))
      .map(v => ({
        id: v.id,
        name: v.title,
        type: v.type === 'discount' ? 'voucher-badge-discount' : 'voucher-badge-shipping',
        amount: v.amount
      }))
    
    onApplyVouchers(selectedVoucherData)
    onClose()
  }

  const getVoucherTypeClass = (type: string) => {
    switch (type) {
      case 'shipping': return 'sellzio-voucher-shipping'
      case 'cashback': return 'sellzio-voucher-cashback'
      case 'payment': return 'sellzio-voucher-payment'
      case 'bank': return 'sellzio-voucher-bank'
      default: return 'sellzio-voucher-discount'
    }
  }

  if (!isOpen) return null

  return (
    <div className="sellzio-modal-overlay">
      <div className="sellzio-voucher-modal">
        {/* Header */}
        <div className="sellzio-modal-header">
          <button className="sellzio-modal-close" onClick={onClose}>
            <ArrowLeft size={20} />
          </button>
          <h2>Pilih Voucher</h2>
        </div>

        {/* Content */}
        <div className="sellzio-modal-content">
          {/* Search Bar */}
          <div className="sellzio-search-bar">
            <div className="sellzio-search-input">
              <Search className="sellzio-search-icon" size={18} />
              <input
                type="text"
                placeholder="Cari voucher..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
              {searchQuery && (
                <X 
                  className="sellzio-clear-icon" 
                  size={18}
                  onClick={() => setSearchQuery('')}
                />
              )}
            </div>
          </div>

          {/* Tabs */}
          <div className="sellzio-voucher-tabs">
            {tabs.map(tab => (
              <button
                key={tab.id}
                className={`sellzio-voucher-tab ${activeTab === tab.id ? 'active' : ''}`}
                onClick={() => setActiveTab(tab.id)}
              >
                {tab.label}
              </button>
            ))}
          </div>

          {/* Voucher List */}
          <div className="sellzio-voucher-list">
            {filteredVouchers.map(voucher => (
              <div 
                key={voucher.id}
                className={`sellzio-voucher-item ${getVoucherTypeClass(voucher.type)} ${voucher.isDisabled ? 'disabled' : ''}`}
              >
                {voucher.isRecommended && (
                  <div className="sellzio-recommended-badge">Direkomendasikan</div>
                )}

                <div className="sellzio-voucher-left">
                  <div className="sellzio-voucher-amount">{voucher.amount}</div>
                  <div className="sellzio-voucher-min">{voucher.minPurchase}</div>
                </div>

                <div className="sellzio-voucher-right">
                  <div className="sellzio-voucher-title">{voucher.title}</div>
                  <div className="sellzio-voucher-desc">{voucher.description}</div>
                  <div className="sellzio-voucher-info">
                    <div className="sellzio-voucher-date">{voucher.validUntil}</div>
                    <div className="sellzio-circular-checkbox">
                      <input
                        type="checkbox"
                        checked={selectedVouchers.includes(voucher.id)}
                        onChange={() => handleVoucherSelect(voucher.id)}
                        disabled={voucher.isDisabled}
                      />
                      <span className="sellzio-checkmark"></span>
                    </div>
                  </div>
                </div>
              </div>
            ))}

            {filteredVouchers.length === 0 && (
              <div className="sellzio-no-results">
                Tidak ada voucher yang sesuai dengan pencarian Anda
              </div>
            )}
          </div>
        </div>

        {/* Footer */}
        <div className="sellzio-modal-footer">
          <button 
            className="sellzio-btn-primary"
            onClick={handleApplyVouchers}
            disabled={selectedVouchers.length === 0}
          >
            Terapkan ({selectedVouchers.length})
          </button>
        </div>
      </div>
    </div>
  )
}

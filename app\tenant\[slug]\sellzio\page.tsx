"use client"

import React, { useState, useEffect, use } from 'react'
import { useRouter } from 'next/navigation'
import { SellzioHeader } from '@/components/themes/sellzio/sellzio-header'
import { SellzioFacet } from '@/components/themes/sellzio/sellzio-facet'
import SellzioCategories from '@/components/themes/sellzio/categories'
import ShopeeLiveVideo from '@/components/themes/sellzio/shopee-live-video'
import { SellzioCartModal } from '@/components/themes/sellzio/cart'

import { useCart } from '@/hooks/use-cart'
import '@/components/themes/sellzio/sellzio-styles.css'
import Head from 'next/head'
// Import data dari file terpisah
import { sampleProducts, type Product as ProductType } from '@/components/data/products'
import { keywordPredictionDB } from '@/components/data/keywords'
// Import komponen masonry dan product card Sellzio
import { SellzioMasonryLayout } from '@/components/themes/sellzio/masonry/sellzio-masonry-layout'
import { SellzioProductCard } from '@/components/themes/sellzio/product-card/sellzio-product-card'
import { useTenantTheme } from '@/components/tenant/tenant-theme-provider'

// Type definitions
interface Prediction {
  text: string
  type: string
  relevance: number
}

interface TenantSellzioPageProps {
  params: Promise<{ slug: string }>
}

export default function TenantSellzioPage({ params }: TenantSellzioPageProps) {
  const router = useRouter()
  const { theme, loading: themeLoading } = useTenantTheme()
  const { items, addItem, getTotalItems } = useCart()

  // Unwrap params using React.use()
  const resolvedParams = use(params)
  const tenantSlug = resolvedParams.slug

  // State management - Enhanced like main sellzio page
  const [searchValue, setSearchValue] = useState('')
  const [isSearchExpanded, setIsSearchExpanded] = useState(false)
  const [showSuggestions, setShowSuggestions] = useState(false)
  const [showMoreSuggestions, setShowMoreSuggestions] = useState(false)
  const [searchHistory, setSearchHistory] = useState<string[]>([])
  const [showPredictions, setShowPredictions] = useState(false)
  const [predictions, setPredictions] = useState<Prediction[]>([])
  const [searchResults, setSearchResults] = useState<any[]>([])
  const [originalSearchResults, setOriginalSearchResults] = useState<ProductType[]>([])
  const [isSearchResultShown, setIsSearchResultShown] = useState(false)
  const [hideMainContent, setHideMainContent] = useState(false)
  const [userInteractionHistory, setUserInteractionHistory] = useState<string[]>([])
  const [searchFrequency, setSearchFrequency] = useState<{ [key: string]: number }>({})
  const [showFilterTabs, setShowFilterTabs] = useState(false)
  const [activeFilterTab, setActiveFilterTab] = useState('terkait')
  const [priceSortDirection, setPriceSortDirection] = useState<'asc' | 'desc'>('asc')
  const [showFilterIcon, setShowFilterIcon] = useState(false)
  const [isMobile, setIsMobile] = useState(false)
  const [showFacetPanel, setShowFacetPanel] = useState(false)
  const [facetFilters, setFacetFilters] = useState<{[key: string]: string[]}>({})
  const [filterBadgeCount, setFilterBadgeCount] = useState(0)
  const [subcategoryContext, setSubcategoryContext] = useState<{
    category: string
    selectedSubcategory: string
    allSubcategories: any[]
  } | null>(null)
  const [showSuggestionsPopup, setShowSuggestionsPopup] = useState(false)
  const [successfulSearchHistory, setSuccessfulSearchHistory] = useState<string[]>([])
  const [isCartOpen, setIsCartOpen] = useState(false)
  const [filteredProducts, setFilteredProducts] = useState<ProductType[]>(sampleProducts)

  // Initialize search functionality like main sellzio page
  useEffect(() => {
    const checkScreenSize = () => {
      setIsMobile(window.innerWidth < 1024)
    }

    checkScreenSize()
    window.addEventListener('resize', checkScreenSize)

    // Initialize search history
    const savedHistory = localStorage.getItem('searchHistory')
    if (savedHistory) {
      setSearchHistory(JSON.parse(savedHistory))
    } else {
      const defaultKeywords = [
        'tas pria', 'sepatu sneakers', 'smartphone android', 'headphone bluetooth',
        'keyboard gaming', 'mouse wireless', 'laptop gaming', 'power bank',
        'smart tv', 'kamera mirrorless', 'jam tangan pintar', 'speaker bluetooth'
      ]
      setSearchHistory(defaultKeywords)
      localStorage.setItem('searchHistory', JSON.stringify(defaultKeywords))
    }

    return () => window.removeEventListener('resize', checkScreenSize)
  }, [])

  // Theme loading
  if (themeLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading {tenantSlug} marketplace...</p>
        </div>
      </div>
    )
  }

  // Enhanced search functionality like main sellzio page
  const handleSearchFocus = () => {
    setIsSearchExpanded(true)
    setShowSuggestions(true)
    document.body.classList.add('show-suggestions')
  }

  const handleSearchBlur = () => {
    if (!isSearchExpanded) {
      setTimeout(() => {
        if (!searchValue) {
          setShowSuggestions(false)
          document.body.classList.remove('show-suggestions')
        }
      }, 150)
    }
  }

  const handleSearchChange = (value: string) => {
    setSearchValue(value)

    if (showFilterTabs) {
      setShowFilterTabs(false)
      setShowFilterIcon(false)
    }

    if (showFacetPanel) {
      setShowFacetPanel(false)
    }

    if (isSearchResultShown && value.trim().length >= 1) {
      setIsSearchResultShown(false)
      setSearchResults([])
      setHideMainContent(false)
      setShowPredictions(true)
      setShowSuggestions(false)
      setFacetFilters({})
      setFilterBadgeCount(0)
      setSubcategoryContext(null)
      generatePredictions(value)
      document.body.classList.add('show-suggestions')
      return
    }

    if (value.trim().length >= 1 && isSearchExpanded) {
      setShowPredictions(true)
      setShowSuggestions(false)
      setIsSearchResultShown(false)
      setSearchResults([])
      generatePredictions(value)
      document.body.classList.add('show-suggestions')
    } else if (value.trim() === '' && isSearchExpanded) {
      setShowPredictions(false)
      setShowSuggestions(true)
      setIsSearchResultShown(false)
      setSearchResults([])
      document.body.classList.add('show-suggestions')
    } else {
      setShowPredictions(false)
      setShowSuggestions(false)
      document.body.classList.remove('show-suggestions')
    }
  }

  const generatePredictions = (query: string) => {
    // Simple prediction generation
    const mockPredictions: Prediction[] = [
      { text: `${query} murah`, type: 'suggestion', relevance: 0.9 },
      { text: `${query} terbaru`, type: 'suggestion', relevance: 0.8 },
      { text: `${query} original`, type: 'suggestion', relevance: 0.7 },
    ]
    setPredictions(mockPredictions)
  }

  const applyFilters = (filters: {[key: string]: string[]}) => {
    let filtered = sampleProducts

    // Apply category filters
    if (filters.category && filters.category.length > 0) {
      filtered = filtered.filter(product =>
        filters.category.includes(product.category || '')
      )
    }

    // Apply price filters
    if (filters.price && filters.price.length > 0) {
      filtered = filtered.filter(product => {
        const price = parseFloat(product.price.replace(/[^\d.-]/g, ''))
        return filters.price.some(range => {
          if (range === 'under-100k') return price < 100000
          if (range === '100k-500k') return price >= 100000 && price <= 500000
          if (range === '500k-1m') return price >= 500000 && price <= 1000000
          if (range === 'over-1m') return price > 1000000
          return false
        })
      })
    }

    // Apply rating filters
    if (filters.rating && filters.rating.length > 0) {
      filtered = filtered.filter(product => {
        const rating = parseFloat(product.rating || '0')
        return filters.rating.some(r => {
          const minRating = parseFloat(r)
          return rating >= minRating
        })
      })
    }

    setFilteredProducts(filtered)
  }

  // Category filtering
  const handleCategorySelect = (category: string | null, subcategory: string | null = null) => {
    let filtered = sampleProducts

    if (subcategory) {
      filtered = sampleProducts.filter(product => product.subcategory === subcategory)
    } else if (category) {
      filtered = sampleProducts.filter(product => product.category === category)
    }

    setFilteredProducts(filtered)
    setShowFacetPanel(false)
  }

  // Product click handler
  const handleProductClick = (productId: string) => {
    router.push(`/tenant/${tenantSlug}/sellzio/products/${productId}`)
  }

  // Cart handlers
  const handleAddToCart = (product: ProductType) => {
    addItem({
      id: product.id.toString(),
      name: product.name,
      price: parseFloat(product.price.replace(/[^\d.-]/g, '')),
      image: product.image,
      quantity: 1
    })
  }

  return (
    <>
      <Head>
        <title>{tenantSlug} - Sellzio Marketplace</title>
        <meta name="description" content={`Marketplace ${tenantSlug} powered by Sellzio`} />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
      </Head>

      <div className="sellzio-page">
        {/* Header */}
        <SellzioHeader
          searchValue={searchValue}
          onSearchChange={handleSearchChange}
          onSearchFocus={handleSearchFocus}
          onSearchBlur={handleSearchBlur}
          isExpanded={isSearchExpanded}
          onToggleExpanded={() => setIsSearchExpanded(!isSearchExpanded)}
          onCartClick={() => setIsCartOpen(true)}
          cartCount={getTotalItems()}
          onFilterClick={() => setShowFacetPanel(!showFacetPanel)}
          showFilterIcon={showFilterIcon}
          filterBadgeCount={filterBadgeCount}
        />

        {/* Search Suggestions */}
        {showSuggestions && (
          <SellzioSearchSuggestions
            searchHistory={searchHistory}
            showMore={showMoreSuggestions}
            onToggleMore={() => setShowMoreSuggestions(!showMoreSuggestions)}
            onSuggestionClick={(suggestion) => {
              setSearchValue(suggestion)
              handleSearchChange(suggestion)
            }}
          />
        )}

        {/* Search Predictions */}
        {showPredictions && predictions.length > 0 && (
          <SellzioSearchPredictions
            predictions={predictions}
            onPredictionClick={(prediction) => {
              setSearchValue(prediction.text)
              handleSearchChange(prediction.text)
            }}
          />
        )}

        {/* Facet Panel */}
        <SellzioFacet
          searchResults={searchResults.length > 0 ? searchResults : sampleProducts}
          displayedProducts={filteredProducts}
          activeFilters={facetFilters}
          onFiltersChange={(filters) => {
            setFacetFilters(filters)
            setFilterBadgeCount(Object.values(filters).flat().length)
            // Apply filters to products
            applyFilters(filters)
          }}
          isVisible={showFacetPanel}
          onClose={() => setShowFacetPanel(false)}
          isDesktopSidebar={!isMobile}
          allProducts={sampleProducts}
          subcategoryContext={subcategoryContext}
        />

        {/* Main Content */}
        <main className="main-content pt-20 container mx-auto py-4 px-2">
          {/* Categories */}
          <SellzioCategories
            products={sampleProducts}
          />

          {/* Shopee Live Video */}
          <ShopeeLiveVideo />

          {/* Products Section */}
          <section className="mb-12">
            <div className="sellzio-products-container">
              <SellzioMasonryLayout 
                columnCount={{ mobile: 2, tablet: 3, desktop: 4 }} 
                gap={{ mobile: 6, tablet: 10, desktop: 12 }}
              >
                {filteredProducts.slice(0, 24).map((product, index) => {
                  const rating = product.rating ? parseFloat(product.rating) : 0
                  const sold = product.sold ? parseInt(product.sold.replace(/\D/g, '')) || 0 : 0

                  let badgeType: "mall" | "star" | "star-lite" | "termurah" | "terlaris" | "komisi-xtra" | "none" = "none"
                  
                  if (product.isMall) badgeType = "mall"
                  else if ((product as any).isTerlaris) badgeType = "terlaris"
                  else if (rating >= 4.5) badgeType = "star"
                  else if (rating >= 4.0) badgeType = "star-lite"

                  let cardType: "standard" | "video" | "flash-sale" = "standard"
                  if ((product as any).isLive) cardType = "video"
                  else if (product.discount && parseFloat(product.discount) > 0) cardType = "flash-sale"

                  // Props tambahan untuk flash sale
                  const additionalProps: any = {}
                  if (cardType === "flash-sale") {
                    const endTime = new Date()
                    endTime.setHours(endTime.getHours() + 2) // Flash sale berakhir dalam 2 jam
                    additionalProps.endTime = endTime
                    additionalProps.stockSold = Math.floor(Math.random() * 80) + 10 // 10-90 terjual
                    additionalProps.totalStock = 100
                  }

                  return (
                    <SellzioProductCard
                      key={product.id}
                      id={product.id}
                      type={cardType}
                      name={product.name}
                      price={product.price}
                      originalPrice={product.originalPrice}
                      discount={product.discount}
                      image={product.image}
                      rating={rating}
                      sold={sold}
                      hasCod={(product as any).hasCod}
                      isMall={product.isMall}
                      isTerlaris={(product as any).isTerlaris}
                      isLive={(product as any).isLive}
                      shipping={product.shipping}
                      badgeType={badgeType}
                      onClick={() => handleProductClick(product.id.toString())}
                      onAddToCart={() => handleAddToCart(product)}
                      {...additionalProps}
                    />
                  )
                })}
              </SellzioMasonryLayout>
            </div>
          </section>
        </main>

        {/* Cart Modal */}
        <SellzioCartModal
          isOpen={isCartOpen}
          onClose={() => setIsCartOpen(false)}
          cartItems={items.map(item => ({
            id: item.id,
            productId: item.id,
            name: item.name,
            price: item.price,
            image: item.image,
            store: "Default Store",
            storeId: "store-1",
            quantity: item.quantity,
            selected: false
          }))}
        />
      </div>
    </>
  )
}

'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'

export default function TestSubdomainPage() {
  const [subdomain, setSubdomain] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [result, setResult] = useState<any>(null)

  const testCreateSubdomain = async () => {
    if (!subdomain.trim()) {
      alert('Please enter a subdomain')
      return
    }

    setIsLoading(true)
    setResult(null)

    try {
      const response = await fetch('/api/dns/create-subdomain', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          subdomain: subdomain.trim()
        })
      })

      const data = await response.json()
      setResult({
        status: response.status,
        success: response.ok,
        data
      })

    } catch (error) {
      setResult({
        status: 500,
        success: false,
        data: { error: 'Network error', details: error }
      })
    } finally {
      setIsLoading(false)
    }
  }

  const testCheckSubdomain = async () => {
    if (!subdomain.trim()) {
      alert('Please enter a subdomain')
      return
    }

    setIsLoading(true)
    setResult(null)

    try {
      const response = await fetch(`/api/dns/create-subdomain?subdomain=${subdomain.trim()}`)
      const data = await response.json()
      setResult({
        status: response.status,
        success: response.ok,
        data
      })

    } catch (error) {
      setResult({
        status: 500,
        success: false,
        data: { error: 'Network error', details: error }
      })
    } finally {
      setIsLoading(false)
    }
  }

  const testUpdateTenantSubdomain = async () => {
    if (!subdomain.trim()) {
      alert('Please enter a subdomain')
      return
    }

    setIsLoading(true)
    setResult(null)

    try {
      const response = await fetch('/api/tenants/update-subdomain', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          subdomain: subdomain.trim(),
          tenantId: '96111d56-b82d-43e7-9926-8f0530dc6063' // test1 tenant
        })
      })

      const data = await response.json()
      setResult({
        status: response.status,
        success: response.ok,
        data
      })

    } catch (error) {
      setResult({
        status: 500,
        success: false,
        data: { error: 'Network error', details: error }
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">Test Subdomain Creation</h1>
        <Badge variant="outline">Debug Tool</Badge>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Subdomain Test</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-2">
            <Input
              placeholder="Enter subdomain (e.g., test123)"
              value={subdomain}
              onChange={(e) => setSubdomain(e.target.value)}
              className="flex-1"
            />
            <span className="flex items-center text-sm text-muted-foreground">
              .sellzio.my.id
            </span>
          </div>

          <div className="flex gap-2 flex-wrap">
            <Button 
              onClick={testCreateSubdomain}
              disabled={isLoading}
              variant="default"
            >
              {isLoading ? 'Creating...' : 'Create DNS Record'}
            </Button>

            <Button 
              onClick={testCheckSubdomain}
              disabled={isLoading}
              variant="outline"
            >
              {isLoading ? 'Checking...' : 'Check DNS Record'}
            </Button>

            <Button 
              onClick={testUpdateTenantSubdomain}
              disabled={isLoading}
              variant="secondary"
            >
              {isLoading ? 'Updating...' : 'Update Tenant Subdomain'}
            </Button>
          </div>

          {result && (
            <Card className={`mt-4 ${result.success ? 'border-green-200' : 'border-red-200'}`}>
              <CardHeader>
                <CardTitle className={`text-sm ${result.success ? 'text-green-700' : 'text-red-700'}`}>
                  {result.success ? '✅ Success' : '❌ Error'} - Status: {result.status}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <pre className="text-xs bg-gray-50 p-3 rounded overflow-auto">
                  {JSON.stringify(result.data, null, 2)}
                </pre>
              </CardContent>
            </Card>
          )}

          <div className="text-sm text-muted-foreground space-y-1">
            <p><strong>Create DNS Record:</strong> Directly creates DNS A record in Cloudflare</p>
            <p><strong>Check DNS Record:</strong> Checks if DNS record exists</p>
            <p><strong>Update Tenant Subdomain:</strong> Updates tenant subdomain + auto-creates DNS</p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

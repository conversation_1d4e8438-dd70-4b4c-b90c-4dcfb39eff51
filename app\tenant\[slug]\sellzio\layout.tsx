import type React from "react"
import { use } from "react"
import { Metadata } from "next"
import { SellzioTenantWrapper } from "./sellzio-tenant-wrapper"

interface SellzioTenantLayoutProps {
  params: Promise<{ slug: string }>
  children: React.ReactNode
}

// Metadata untuk SEO
export async function generateMetadata({ params }: { params: Promise<{ slug: string }> }): Promise<Metadata> {
  const resolvedParams = await params
  return {
    title: `${resolvedParams.slug} - Sellzio Marketplace`,
    description: `Marketplace ${resolvedParams.slug} powered by Sellzio - Jual beli online dengan mudah dan aman`,
    keywords: ["marketplace", "jual beli", "online shop", resolvedParams.slug, "sellzio"],
    openGraph: {
      title: `${resolvedParams.slug} - Sellzio Marketplace`,
      description: `Marketplace ${resolvedParams.slug} powered by Sellzio`,
      type: "website",
      siteName: `${resolvedParams.slug} Marketplace`,
    },
    twitter: {
      card: "summary_large_image",
      title: `${resolvedParams.slug} - Sellzio Marketplace`,
      description: `Marketplace ${resolvedParams.slug} powered by Sellzio`,
    },
  }
}

export default function SellzioTenantLayout({ params, children }: SellzioTenantLayoutProps) {
  const resolvedParams = use(params)
  return (
    <SellzioTenantWrapper tenantSlug={resolvedParams.slug}>
      {children}
    </SellzioTenantWrapper>
  )
}

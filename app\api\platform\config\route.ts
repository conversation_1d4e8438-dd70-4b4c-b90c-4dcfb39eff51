import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

function getClient() {
  return createClient(supabaseUrl, supabaseServiceKey)
}

export async function GET(request: NextRequest) {
  try {
    console.log('🔥 API: Fetching platform configuration')

    const supabase = getClient()

    const { data, error } = await supabase
      .from('PlatformSettings')
      .select('key, value')
      .in('key', [
        'platform_domain',
        'platform_ip',
        'cdn_domain',
        'api_domain',
        'ssl_enabled',
        'wildcard_ssl',
        'nameserver_1',
        'nameserver_2',
        'dns_provider'
      ])

    if (error) {
      console.error('🔥 API: Error fetching platform config:', error)
      return NextResponse.json(
        { error: 'Failed to fetch platform configuration' },
        { status: 500 }
      )
    }

    // Convert array to object
    const config: Record<string, string> = {}
    data?.forEach(setting => {
      config[setting.key] = setting.value
    })

    // Add computed values
    const platformDomain = config.platform_domain || 'sellzio.com'
    const sslEnabled = config.ssl_enabled === 'true'
    const protocol = sslEnabled ? 'https' : 'http'

    const platformConfig = {
      domain: platformDomain,
      ip: config.platform_ip || '127.0.0.1',
      cdnDomain: config.cdn_domain || `cdn.${platformDomain}`,
      apiDomain: config.api_domain || `api.${platformDomain}`,
      ssl: sslEnabled,
      wildcardSSL: config.wildcard_ssl === 'true',
      protocol,
      baseUrl: `${protocol}://${platformDomain}`,
      subdomainPattern: `*.${platformDomain}`,
      nameservers: {
        primary: config.nameserver_1 || `ns1.${platformDomain}`,
        secondary: config.nameserver_2 || `ns2.${platformDomain}`
      },
      dnsProvider: config.dns_provider || 'cloudflare',
      dnsInstructions: {
        aRecord: {
          name: '@',
          value: config.platform_ip || '127.0.0.1',
          type: 'A'
        },
        wildcardRecord: {
          name: '*',
          value: config.platform_ip || '127.0.0.1',
          type: 'A'
        },
        wwwRecord: {
          name: 'www',
          value: platformDomain,
          type: 'CNAME'
        },
        apiRecord: {
          name: 'api',
          value: platformDomain,
          type: 'CNAME'
        },
        cdnRecord: {
          name: 'cdn',
          value: platformDomain,
          type: 'CNAME'
        }
      }
    }

    console.log('🔥 API: Platform configuration:', platformConfig)

    return NextResponse.json({
      success: true,
      config: platformConfig
    })
  } catch (error) {
    console.error('🔥 API: Error fetching platform config:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

"use client"

import { <PERSON>, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { PageHeader } from "@/components/admin/ui/page-header"
import { But<PERSON> } from "@/components/ui/button"
import { ArrowLeft } from "lucide-react"
import Link from "next/link"
import { UnifiedDomainManagement } from "@/components/admin/tenants/unified-domain-management"

export function DomainManagementClient() {
  return (
    <div className="flex flex-col gap-6">
      <PageHeader
        title="Domain Management"
        description="Manage tenant domains, custom domain requests, and SSL certificates"
        breadcrumbs={[
          { title: "Dashboard", href: "/admin/dashboard" },
          { title: "Tenants", href: "/admin/dashboard/tenants" },
          { title: "Domains", href: "/admin/dashboard/tenants/domains" },
        ]}
        actions={
          <Link href="/admin/dashboard/tenants">
            <Button variant="outline">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Tenants
            </Button>
          </Link>
        }
      />

      <UnifiedDomainManagement />
    </div>
  )
}

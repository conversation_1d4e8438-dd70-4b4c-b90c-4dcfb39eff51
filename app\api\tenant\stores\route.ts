import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const tenantId = searchParams.get('tenantId')
    
    if (!tenantId) {
      return NextResponse.json({ error: 'Tenant ID is required' }, { status: 400 })
    }

    const { data: stores, error } = await supabase
      .from('stores')
      .select(`
        id,
        name,
        owner,
        owner_name,
        email,
        status,
        plan,
        products,
        orders,
        revenue,
        rating,
        join_date,
        last_active,
        category,
        location,
        tenant_id,
        created_at,
        updated_at
      `)
      .eq('tenant_id', tenantId)
      .order('created_at', { ascending: false })

    if (error) {
      console.error('Error fetching tenant stores:', error)
      return NextResponse.json({ error: 'Failed to fetch stores' }, { status: 500 })
    }

    return NextResponse.json({
      stores,
      total: stores?.length || 0
    })

  } catch (error) {
    console.error('Error in tenant stores API:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

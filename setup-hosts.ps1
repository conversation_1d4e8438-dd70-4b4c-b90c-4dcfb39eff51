# Script untuk menambahkan subdomain localhost ke file hosts
# Jalankan sebagai Administrator

$hostsFile = "C:\Windows\System32\drivers\etc\hosts"
$entries = @(
    "127.0.0.1 app.localhost",
    "127.0.0.1 admin.localhost"
)

Write-Host "Menambahkan entries ke file hosts..." -ForegroundColor Green

foreach ($entry in $entries) {
    $exists = Get-Content $hostsFile | Select-String -Pattern $entry
    if (-not $exists) {
        Add-Content -Path $hostsFile -Value $entry
        Write-Host "Ditambahkan: $entry" -ForegroundColor Yellow
    } else {
        Write-Host "Sudah ada: $entry" -ForegroundColor Cyan
    }
}

Write-Host "Selesai! Sekarang Anda bisa mengakses:" -ForegroundColor Green
Write-Host "- http://app.localhost:3000/login (User Login)" -ForegroundColor White
Write-Host "- http://admin.localhost:3000/login (Admin Login)" -ForegroundColor White
Write-Host "- http://localhost:3000/admin/login (Admin Login)" -ForegroundColor White

Write-Host "`nTekan Enter untuk melanjutkan..." -ForegroundColor Gray
Read-Host

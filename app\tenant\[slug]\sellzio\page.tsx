"use client"

import React, { useState, useEffect, use } from 'react'
import { useRouter } from 'next/navigation'
import { Sell<PERSON>Header } from '@/components/themes/sellzio/sellzio-header'
import { SellzioFacet } from '@/components/themes/sellzio/sellzio-facet'
import SellzioCategories from '@/components/themes/sellzio/categories'
import ShopeeLiveVideo from '@/components/themes/sellzio/shopee-live-video'
import { SellzioCartModal } from '@/components/themes/sellzio/cart'
import { useCart } from '@/hooks/use-cart'
import '@/components/themes/sellzio/sellzio-styles.css'
import Head from 'next/head'
// Import data dari file terpisah
import { sampleProducts, type Product as ProductType } from '@/components/data/products'
import { keywordPredictionDB } from '@/components/data/keywords'
// Import komponen masonry dan product card Sellzio
import { SellzioMasonryLayout } from '@/components/themes/sellzio/masonry/sellzio-masonry-layout'
import { SellzioProductCard } from '@/components/themes/sellzio/product-card/sellzio-product-card'
import { useTenantTheme } from '@/components/tenant/tenant-theme-provider'

// Type definitions
interface Prediction {
  text: string
  type: string
  relevance: number
}

interface TenantSellzioPageProps {
  params: Promise<{ slug: string }>
}

export default function TenantSellzioPage({ params }: TenantSellzioPageProps) {
  const router = useRouter()
  const { theme, loading: themeLoading } = useTenantTheme()
  const { items, addItem, removeItem, updateQuantity, clearCart, getTotalItems, getTotalPrice } = useCart()

  // Unwrap params using React.use()
  const resolvedParams = use(params)
  const tenantSlug = resolvedParams.slug

  // State management
  const [searchQuery, setSearchQuery] = useState('')
  const [searchResults, setSearchResults] = useState<ProductType[]>([])
  const [predictions, setPredictions] = useState<Prediction[]>([])
  const [isSearchExpanded, setIsSearchExpanded] = useState(false)
  const [showSuggestions, setShowSuggestions] = useState(false)
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null)
  const [selectedSubcategory, setSelectedSubcategory] = useState<string | null>(null)
  const [isFacetOpen, setIsFacetOpen] = useState(false)
  const [isCartOpen, setIsCartOpen] = useState(false)
  const [filteredProducts, setFilteredProducts] = useState<ProductType[]>(sampleProducts)

  // Theme loading
  if (themeLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading {tenantSlug} marketplace...</p>
        </div>
      </div>
    )
  }

  // Search functionality
  const handleSearch = (query: string) => {
    setSearchQuery(query)
    if (query.trim()) {
      const results = sampleProducts.filter(product =>
        product.name.toLowerCase().includes(query.toLowerCase()) ||
        product.category?.toLowerCase().includes(query.toLowerCase()) ||
        product.subcategory?.toLowerCase().includes(query.toLowerCase())
      )
      setSearchResults(results)
      setFilteredProducts(results)
    } else {
      setSearchResults([])
      setFilteredProducts(sampleProducts)
    }
  }

  // Category filtering
  const handleCategorySelect = (category: string | null, subcategory: string | null = null) => {
    setSelectedCategory(category)
    setSelectedSubcategory(subcategory)
    
    let filtered = sampleProducts
    
    if (subcategory) {
      filtered = sampleProducts.filter(product => product.subcategory === subcategory)
    } else if (category) {
      filtered = sampleProducts.filter(product => product.category === category)
    }
    
    setFilteredProducts(filtered)
    setIsFacetOpen(false)
  }

  // Product click handler
  const handleProductClick = (productId: string) => {
    router.push(`/tenant/${tenantSlug}/sellzio/products/${productId}`)
  }

  // Cart handlers
  const handleAddToCart = (product: ProductType) => {
    addItem({
      id: product.id,
      name: product.name,
      price: parseFloat(product.price.replace(/[^\d.-]/g, '')),
      image: product.image,
      quantity: 1
    })
  }

  return (
    <>
      <Head>
        <title>{tenantSlug} - Sellzio Marketplace</title>
        <meta name="description" content={`Marketplace ${tenantSlug} powered by Sellzio`} />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
      </Head>

      <div className="sellzio-page">
        {/* Header */}
        <SellzioHeader
          searchQuery={searchQuery}
          onSearchChange={handleSearch}
          predictions={predictions}
          showSuggestions={showSuggestions}
          onSuggestionClick={(suggestion) => handleSearch(suggestion)}
          isSearchExpanded={isSearchExpanded}
          onSearchExpand={setIsSearchExpanded}
          onCartClick={() => setIsCartOpen(true)}
          cartItemCount={getTotalItems()}
          onFacetToggle={() => setIsFacetOpen(!isFacetOpen)}
          tenantName={tenantSlug}
        />

        {/* Facet Panel */}
        <SellzioFacet
          searchResults={searchResults.length > 0 ? searchResults : sampleProducts}
          displayedProducts={filteredProducts}
          activeFilters={{}}
          onFiltersChange={(filters) => {
            // Handle filter changes here
            console.log('Filters changed:', filters)
          }}
          isVisible={isFacetOpen}
          onClose={() => setIsFacetOpen(false)}
          isDesktopSidebar={false}
          allProducts={sampleProducts}
        />

        {/* Main Content */}
        <main className="main-content pt-20 container mx-auto py-4 px-2">
          {/* Categories */}
          <SellzioCategories
            products={sampleProducts}
          />

          {/* Shopee Live Video */}
          <ShopeeLiveVideo />

          {/* Products Section */}
          <section className="mb-12">
            <div className="sellzio-products-container">
              <SellzioMasonryLayout 
                columnCount={{ mobile: 2, tablet: 3, desktop: 4 }} 
                gap={{ mobile: 6, tablet: 10, desktop: 12 }}
              >
                {filteredProducts.slice(0, 24).map((product, index) => {
                  const rating = product.rating ? parseFloat(product.rating) : 0
                  const sold = product.sold ? parseInt(product.sold.replace(/\D/g, '')) || 0 : 0

                  let badgeType: "mall" | "star" | "star-lite" | "termurah" | "terlaris" | "komisi-xtra" | "none" = "none"
                  
                  if (product.isMall) badgeType = "mall"
                  else if ((product as any).isTerlaris) badgeType = "terlaris"
                  else if (rating >= 4.5) badgeType = "star"
                  else if (rating >= 4.0) badgeType = "star-lite"

                  let cardType: "standard" | "video" | "flash-sale" = "standard"
                  if (product.isLive) cardType = "video"
                  else if (product.discount && parseFloat(product.discount) > 0) cardType = "flash-sale"

                  // Props tambahan untuk flash sale
                  const additionalProps: any = {}
                  if (cardType === "flash-sale") {
                    const endTime = new Date()
                    endTime.setHours(endTime.getHours() + 2) // Flash sale berakhir dalam 2 jam
                    additionalProps.endTime = endTime
                    additionalProps.stockSold = Math.floor(Math.random() * 80) + 10 // 10-90 terjual
                    additionalProps.totalStock = 100
                  }

                  return (
                    <SellzioProductCard
                      key={product.id}
                      id={product.id}
                      type={cardType}
                      name={product.name}
                      price={product.price}
                      originalPrice={product.originalPrice}
                      discount={product.discount}
                      image={product.image}
                      rating={rating}
                      sold={sold}
                      hasCod={(product as any).hasCod}
                      isMall={product.isMall}
                      isTerlaris={(product as any).isTerlaris}
                      isLive={(product as any).isLive}
                      shipping={product.shipping}
                      badgeType={badgeType}
                      onClick={() => handleProductClick(product.id.toString())}
                      onAddToCart={() => handleAddToCart(product)}
                      {...additionalProps}
                    />
                  )
                })}
              </SellzioMasonryLayout>
            </div>
          </section>
        </main>

        {/* Cart Modal */}
        <SellzioCartModal
          isOpen={isCartOpen}
          onClose={() => setIsCartOpen(false)}
          cartItems={items.map(item => ({
            id: item.id,
            productId: item.id,
            name: item.name,
            price: item.price,
            image: item.image,
            store: "Default Store",
            storeId: "store-1",
            quantity: item.quantity,
            selected: false
          }))}
        />
      </div>
    </>
  )
}

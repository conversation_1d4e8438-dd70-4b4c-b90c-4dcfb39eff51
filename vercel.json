{"version": 2, "buildCommand": "next build", "devCommand": "next dev", "installCommand": "bun install", "framework": "nextjs", "outputDirectory": ".next", "regions": ["iad1"], "functions": {"app/admin/dashboard/**/*": {"memory": 1024, "maxDuration": 10}}, "env": {"NEXT_PUBLIC_MAIN_DOMAIN": "sellzio.my.id", "NEXT_PUBLIC_DASHBOARD_SUBDOMAIN": "app", "NEXT_PUBLIC_APP_URL": "https://sellzio.my.id"}, "headers": [{"source": "/(.*)", "headers": [{"key": "Cache-Control", "value": "no-store, max-age=0"}]}]}
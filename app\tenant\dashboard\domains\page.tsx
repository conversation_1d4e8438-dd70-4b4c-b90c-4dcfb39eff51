"use client"

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { CustomDomainManager } from '@/components/tenant/custom-domain-manager'
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbPage, BreadcrumbSeparator } from '@/components/ui/breadcrumb'
import { Globe, ExternalLink, Settings, Shield } from 'lucide-react'
import { getClient } from '@/lib/supabase'

interface TenantData {
  id: string
  name: string
  domain: string | null
  slug: string
}

export default function DomainsPage() {
  const [tenantData, setTenantData] = useState<TenantData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    fetchTenantData()
  }, [])

  const fetchTenantData = async () => {
    try {
      setLoading(true)
      const supabase = getClient()

      // For demo purposes, we'll fetch the sellzio tenant
      // In production, this would be based on the current user's tenant
      const { data, error: fetchError } = await supabase
        .from('Tenant')
        .select('id, name, domain, slug')
        .eq('id', 'sellzio')
        .single()

      if (fetchError) {
        throw fetchError
      }

      setTenantData(data)
    } catch (err) {
      console.error('Error fetching tenant data:', err)
      setError(err instanceof Error ? err.message : 'Failed to fetch tenant data')

      // Fallback to demo data
      setTenantData({
        id: 'sellzio',
        name: 'Sellzio Store',
        domain: null,
        slug: 'sellzio'
      })
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="container mx-auto py-6">
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-500"></div>
          <span className="ml-2 text-gray-600">Loading domain settings...</span>
        </div>
      </div>
    )
  }

  if (!tenantData) {
    return (
      <div className="container mx-auto py-6">
        <div className="text-center py-12">
          <p className="text-red-600">Failed to load tenant data</p>
          {error && <p className="text-sm text-gray-500 mt-2">{error}</p>}
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Page Header */}
      <div className="space-y-2">
        <h1 className="text-3xl font-bold tracking-tight">Domain Settings</h1>
        <p className="text-muted-foreground">
          Manage your store's domain configuration and custom domain setup
        </p>
      </div>

      {/* Domain Overview Cards */}
      <div className="grid gap-4 md:grid-cols-3">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Default Subdomain</CardTitle>
            <Globe className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">Active</div>
            <p className="text-xs text-muted-foreground">
              {tenantData.id}.sellzio.com
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Custom Domain</CardTitle>
            <Settings className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">
              {tenantData.domain ? 'Configured' : 'Not Set'}
            </div>
            <p className="text-xs text-muted-foreground">
              {tenantData.domain || 'No custom domain'}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">SSL Certificate</CardTitle>
            <Shield className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">Secured</div>
            <p className="text-xs text-muted-foreground">
              Auto-managed SSL
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Domain Management */}
      <CustomDomainManager
        tenantId={tenantData.id}
        currentDomain={tenantData.domain}
      />
    </div>
  )
}

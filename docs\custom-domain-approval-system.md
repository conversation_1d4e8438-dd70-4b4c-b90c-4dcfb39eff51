# Custom Domain Approval System

## Overview

Sistem approval untuk custom domain yang memungkinkan admin mengontrol dan menyetujui permintaan custom domain dari tenant sebelum dikonfigurasi secara otomatis.

## Flow Approval

### **1. Tenant Request Flow**
```
Tenant Submit Domain → Pending Approval → Admin Review → Approve/Reject → Auto Configure (if approved)
```

### **2. Admin Dashboard Flow**
```
View Requests → Review Details → Approve/Reject → Auto Configuration → Domain Active
```

## Database Schema

### **Table: custom_domain_requests**
```sql
CREATE TABLE custom_domain_requests (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  tenant_id UUID REFERENCES tenants(id),
  domain VARCHAR(255) NOT NULL,
  reason TEXT,
  status VARCHAR(50) DEFAULT 'pending', -- pending, approved, rejected, configuration_failed
  admin_notes TEXT,
  requested_at TIMESTAMP DEFAULT NOW(),
  processed_at TIMESTAMP,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

## API Endpoints

### **1. Submit Request (Tenant)**
```javascript
POST /api/admin/custom-domain-requests
{
  "domain": "example.com",
  "tenantId": "tenant-uuid",
  "reason": "Business expansion to custom domain"
}
```

### **2. List Requests (Admin)**
```javascript
GET /api/admin/custom-domain-requests?status=pending
```

### **3. Approve/Reject (Admin)**
```javascript
PATCH /api/admin/custom-domain-requests
{
  "requestId": "request-uuid",
  "action": "approve", // or "reject"
  "adminNotes": "Domain approved for business use"
}
```

## Request Status

### **Status Types:**
- **`pending`** - Menunggu review admin
- **`approved`** - Disetujui dan dikonfigurasi
- **`rejected`** - Ditolak oleh admin
- **`configuration_failed`** - Approved tapi gagal konfigurasi

## Admin Dashboard Features

### **1. Request Management**
- ✅ View all pending requests
- ✅ Filter by status (pending, approved, rejected)
- ✅ View tenant details
- ✅ Add admin notes
- ✅ Bulk approve/reject

### **2. Domain Verification**
- ✅ Check domain ownership
- ✅ Verify DNS configuration
- ✅ Check domain reputation
- ✅ Validate business use case

### **3. Auto Configuration**
- ✅ Cloudflare DNS setup (🟠 Proxied)
- ✅ Vercel domain addition
- ✅ SSL certificate provisioning
- ✅ Health checks

## Tenant Experience

### **1. Request Submission**
```javascript
// Tenant dashboard
const submitDomainRequest = async () => {
  const response = await fetch('/api/admin/custom-domain-requests', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      domain: 'mybusiness.com',
      tenantId: 'tenant-id',
      reason: 'Professional business domain for customer trust'
    })
  })
  
  const result = await response.json()
  // Show success message: "Request submitted, awaiting admin approval"
}
```

### **2. Request Status Tracking**
- **Pending:** "Your domain request is under review"
- **Approved:** "Domain approved! Configuration in progress..."
- **Rejected:** "Domain request rejected. Reason: [admin notes]"

## Cloudflare Configuration

### **🟠 ORANGE (Proxied) Benefits:**
- ✅ **DDoS Protection:** Unlimited mitigation
- ✅ **Global CDN:** 200+ data centers
- ✅ **WAF Security:** Advanced threat protection
- ✅ **SSL/TLS:** Full encryption + HSTS
- ✅ **Performance:** HTTP/3, Brotli compression
- ✅ **Analytics:** Traffic insights

### **DNS Configuration:**
```javascript
{
  type: 'CNAME',
  name: 'example.com',
  content: 'cname.vercel-dns.com',
  ttl: 1, // Auto TTL
  proxied: true // 🟠 ORANGE for Pro features
}
```

## Security & Validation

### **1. Domain Validation**
- **Format Check:** Valid domain format
- **Ownership Verification:** TXT record verification
- **Reputation Check:** Not in blacklists
- **Business Validation:** Legitimate business use

### **2. Admin Controls**
- **Manual Review:** Human verification
- **Bulk Operations:** Efficient processing
- **Audit Trail:** Complete request history
- **Rollback Capability:** Undo configurations

## Environment Variables

### **Required for Production:**
```bash
# Cloudflare (for DNS)
CLOUDFLARE_API_TOKEN=your_token
CLOUDFLARE_ZONE_ID=your_zone_id

# Vercel (for domain management)
VERCEL_TOKEN=your_vercel_token
VERCEL_PROJECT_ID=your_project_id

# Supabase (for database)
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
SUPABASE_SERVICE_ROLE_KEY=your_service_key
```

### **File Structure:**
- **Development:** `.env.local` only
- **Production:** Vercel Dashboard Environment Variables
- **Removed:** `.env.production` (redundant)

## Cost Analysis

### **Centralized Benefits:**
- **Platform Cost:** $20/month Cloudflare Pro
- **Tenant Cost:** $0 (included)
- **Admin Overhead:** Minimal (approval system)

### **Value Proposition:**
- **For Tenants:** Professional domain + Pro features
- **For Platform:** Controlled growth + revenue opportunity
- **For Users:** Better performance + security

## Implementation Checklist

### **Backend:**
- ✅ Custom domain request API
- ✅ Admin approval system
- ✅ Auto configuration after approval
- ✅ Status tracking & notifications

### **Frontend:**
- ⏳ Tenant request form
- ⏳ Admin approval dashboard
- ⏳ Status tracking UI
- ⏳ Notification system

### **Database:**
- ⏳ Create custom_domain_requests table
- ⏳ Add indexes for performance
- ⏳ Setup audit logging

## Best Practices

### **1. Admin Guidelines**
- **Review Criteria:** Business legitimacy, domain reputation
- **Response Time:** 24-48 hours for approval
- **Communication:** Clear rejection reasons
- **Documentation:** Detailed admin notes

### **2. Tenant Guidelines**
- **Clear Instructions:** Domain ownership requirements
- **Business Justification:** Explain use case
- **Technical Requirements:** DNS access needed
- **Timeline Expectations:** 1-3 days total process

### **3. Monitoring**
- **Success Rate:** Track approval/rejection ratios
- **Configuration Success:** Monitor auto-setup failures
- **Performance Impact:** Domain load times
- **Security Alerts:** Suspicious domain requests

## Future Enhancements

### **1. Automated Validation**
- **Domain Reputation API:** Automatic blacklist checking
- **Business Verification:** Company registration validation
- **SSL Pre-validation:** Certificate readiness check

### **2. Self-Service Options**
- **Verified Domains:** Pre-approved domain list
- **Business Accounts:** Auto-approval for verified businesses
- **API Integration:** Programmatic domain management

### **3. Advanced Features**
- **Multi-Domain Support:** Multiple domains per tenant
- **Subdomain Wildcards:** *.example.com support
- **Geographic Routing:** Region-specific configurations

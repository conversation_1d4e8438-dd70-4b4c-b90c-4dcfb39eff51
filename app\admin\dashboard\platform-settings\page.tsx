"use client"

import { PlatformSettings } from '@/components/admin/platform-settings'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { Settings, Globe, Server, Shield, Info } from 'lucide-react'

export default function AdminPlatformSettingsPage() {
  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Page Header */}
      <div className="space-y-2">
        <h1 className="text-3xl font-bold tracking-tight">Platform Settings</h1>
        <p className="text-muted-foreground">
          Configure your main platform domain, IP address, and DNS settings. All tenant subdomains will inherit these configurations.
        </p>
      </div>

      {/* Overview Cards */}
      <div className="grid gap-4 md:grid-cols-3">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Platform Domain</CardTitle>
            <Globe className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">sellzio.com</div>
            <p className="text-xs text-muted-foreground">
              Main platform domain
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">IP Address</CardTitle>
            <Server className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">Auto-Managed</div>
            <p className="text-xs text-muted-foreground">
              Vercel/Platform IP
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">SSL Status</CardTitle>
            <Shield className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">Secured</div>
            <p className="text-xs text-muted-foreground">
              Wildcard SSL enabled
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Important Notice */}
      <Alert>
        <Info className="h-4 w-4" />
        <AlertTitle>Important Notice</AlertTitle>
        <AlertDescription>
          <div className="space-y-2">
            <p>
              These settings control the main platform configuration. When you change the platform domain:
            </p>
            <ul className="list-disc list-inside space-y-1 text-sm">
              <li>All tenant subdomains will use the new domain (e.g., tenant.yourdomain.com)</li>
              <li>You must configure DNS records in your domain provider (Vercel, Cloudflare, etc.)</li>
              <li>SSL certificates will be automatically managed for the new domain</li>
              <li>Existing tenant custom domains will continue to work independently</li>
            </ul>
          </div>
        </AlertDescription>
      </Alert>

      {/* Platform Settings Component */}
      <PlatformSettings />

      {/* Deployment Instructions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Deployment Instructions
          </CardTitle>
          <CardDescription>
            Steps to configure your custom domain on different platforms
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Vercel Instructions */}
          <div className="space-y-2">
            <h4 className="font-medium flex items-center gap-2">
              <Badge variant="outline">Vercel</Badge>
              Configuration Steps
            </h4>
            <ol className="list-decimal list-inside space-y-1 text-sm text-muted-foreground">
              <li>Go to your Vercel project dashboard</li>
              <li>Navigate to Settings → Domains</li>
              <li>Add your custom domain (e.g., yourdomain.com)</li>
              <li>Add wildcard subdomain (*.yourdomain.com)</li>
              <li>Configure DNS records as shown above in your domain provider</li>
              <li>Wait for DNS propagation (up to 24 hours)</li>
            </ol>
          </div>

          <div className="space-y-2">
            <h4 className="font-medium flex items-center gap-2">
              <Badge variant="outline">Cloudflare</Badge>
              DNS Configuration
            </h4>
            <ol className="list-decimal list-inside space-y-1 text-sm text-muted-foreground">
              <li>Login to your Cloudflare dashboard</li>
              <li>Select your domain</li>
              <li>Go to DNS → Records</li>
              <li>Add the DNS records as shown in the instructions above</li>
              <li>Ensure proxy status is set correctly (orange cloud for CDN)</li>
            </ol>
          </div>

          <div className="space-y-2">
            <h4 className="font-medium flex items-center gap-2">
              <Badge variant="outline">Other Providers</Badge>
              General Steps
            </h4>
            <ol className="list-decimal list-inside space-y-1 text-sm text-muted-foreground">
              <li>Access your domain provider's DNS management</li>
              <li>Add A records pointing to your server IP</li>
              <li>Add CNAME records for subdomains</li>
              <li>Enable wildcard subdomain support if available</li>
              <li>Test configuration after DNS propagation</li>
            </ol>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

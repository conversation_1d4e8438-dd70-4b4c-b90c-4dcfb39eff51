"use client"

import { useState } from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { Bell, Menu, Search, ShoppingCart, Sun, Moon, User } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Badge } from "@/components/ui/badge"
import { useSidebar } from "@/components/ui/sidebar"
import { useTheme } from "next-themes"
import { useAuth } from "@/contexts/auth-context"

export function StoreHeader() {
  const { toggle } = useSidebar()
  const { setTheme, theme } = useTheme()
  const { logout } = useAuth()
  const [searchQuery, setSearchQuery] = useState("")
  const pathname = usePathname()

  // Fungsi untuk mendapatkan judul halaman berdasarkan pathname
  const getPageTitle = () => {
    const path = pathname.split("/").filter(Boolean)

    if (path.length === 2 && path[0] === "store" && path[1] === "dashboard") {
      return "Dashboard"
    }

    if (path.length > 2) {
      // Capitalize first letter and replace hyphens with spaces
      return path[2].charAt(0).toUpperCase() + path[2].slice(1).replace(/-/g, " ")
    }

    return "Dashboard"
  }

  return (
    <header className="sticky top-0 z-30 flex h-16 items-center justify-between border-b border-border/40 bg-background/95 px-4 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="flex items-center gap-2 lg:hidden">
        <Button variant="ghost" size="icon" onClick={toggle}>
          <Menu className="h-5 w-5" />
          <span className="sr-only">Toggle Menu</span>
        </Button>
        <span className="text-lg font-semibold">{getPageTitle()}</span>
      </div>

      <div className="hidden w-full max-w-sm lg:flex">
        <div className="relative w-full">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            type="search"
            placeholder="Cari produk, pesanan, pelanggan..."
            className="w-full rounded-md border-border/40 bg-background pl-8 shadow-sm"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
      </div>

      <div className="flex items-center gap-4">
        <Button variant="ghost" size="icon" className="text-muted-foreground" asChild>
          <Link href="/store/dashboard/orders">
            <ShoppingCart className="h-5 w-5" />
            <Badge className="absolute -right-1 -top-1 h-4 w-4 rounded-full bg-primary p-0 text-[10px] text-primary-foreground">
              3
            </Badge>
            <span className="sr-only">Pesanan Baru</span>
          </Link>
        </Button>

        <Button variant="ghost" size="icon" className="text-muted-foreground" asChild>
          <Link href="/store/dashboard/notifications">
            <Bell className="h-5 w-5" />
            <Badge className="absolute -right-1 -top-1 h-4 w-4 rounded-full bg-primary p-0 text-[10px] text-primary-foreground">
              5
            </Badge>
            <span className="sr-only">Notifikasi</span>
          </Link>
        </Button>

        <Button
          variant="ghost"
          size="icon"
          className="text-muted-foreground"
          onClick={() => setTheme(theme === "dark" ? "light" : "dark")}
        >
          {theme === "dark" ? <Sun className="h-5 w-5" /> : <Moon className="h-5 w-5" />}
          <span className="sr-only">Toggle Theme</span>
        </Button>

        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="icon" className="rounded-full">
              <Avatar className="h-8 w-8">
                <AvatarImage src="/friendly-store-owner.png" alt="Store Owner" />
                <AvatarFallback>SO</AvatarFallback>
              </Avatar>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>Toko Saya</DropdownMenuLabel>
            <DropdownMenuItem asChild>
              <Link href="/store/dashboard/settings/profile">
                <User className="mr-2 h-4 w-4" />
                Profil Toko
              </Link>
            </DropdownMenuItem>
            <DropdownMenuItem asChild>
              <Link href="/store/dashboard/settings">Pengaturan</Link>
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem asChild>
              <Link href="/store/view">Lihat Toko</Link>
            </DropdownMenuItem>
            <DropdownMenuItem asChild>
              <Link href="/buyer/dashboard">Kembali ke Buyer</Link>
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={logout}>
              Logout
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </header>
  )
}

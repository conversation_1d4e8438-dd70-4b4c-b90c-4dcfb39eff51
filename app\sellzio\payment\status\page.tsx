"use client"

import React, { useState, useEffect } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { SellzioPaymentStatusPage } from '@/components/themes/sellzio/payment/sellzio-payment-status-page'
import { SellzioErrorBoundary } from '@/components/themes/sellzio/common/sellzio-error-boundary'
import { paymentAPI } from '@/lib/api/payment'
import type { Payment } from '@/lib/models/payment'

export default function PaymentStatusPage() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const [isLoading, setIsLoading] = useState(true)
  const [payment, setPayment] = useState<Payment | null>(null)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    // Apply Sellzio payment status styles
    const applyPaymentStatusStyles = () => {
      const paymentStatusPage = document.querySelector('.sellzio-payment-status-page') as HTMLElement
      if (paymentStatusPage) {
        paymentStatusPage.style.margin = '0'
        paymentStatusPage.style.padding = '0'
        console.log('Payment status styles applied')
      }
    }

    const paymentId = searchParams.get('paymentId')
    
    if (!paymentId) {
      setError('ID Pembayaran tidak ditemukan')
      setIsLoading(false)
      return
    }

    const fetchPayment = async () => {
      try {
        const paymentData = await paymentAPI.getPayment(paymentId)
        setPayment(paymentData)
        setIsLoading(false)
      } catch (error) {
        console.error('Error fetching payment:', error)
        setError('Gagal memuat data pembayaran')
        setIsLoading(false)
      }
    }

    fetchPayment()

    // Apply styles immediately and after a short delay
    applyPaymentStatusStyles()
    const timeoutId = setTimeout(applyPaymentStatusStyles, 50)

    return () => clearTimeout(timeoutId)
  }, [router, searchParams])

  const handleBackToHome = () => {
    router.push('/sellzio')
  }

  const handleRefreshStatus = async () => {
    const paymentId = searchParams.get('paymentId')
    if (!paymentId) return

    try {
      const paymentData = await paymentAPI.checkPaymentStatus(paymentId)
      setPayment(paymentData)
    } catch (error) {
      console.error('Error checking payment status:', error)
      setError('Gagal memeriksa status pembayaran')
    }
  }

  const handleCancelPayment = async () => {
    const paymentId = searchParams.get('paymentId')
    if (!paymentId || !payment || payment.status !== 'pending') return

    try {
      const paymentData = await paymentAPI.cancelPayment(paymentId)
      setPayment(paymentData)
    } catch (error) {
      console.error('Error canceling payment:', error)
      setError('Gagal membatalkan pembayaran')
    }
  }

  if (isLoading) {
    return (
      <div style={{
        minHeight: '100vh',
        backgroundColor: '#f5f5f5',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      }}>
        <div style={{
          textAlign: 'center',
          backgroundColor: 'white',
          padding: '2rem',
          borderRadius: '8px',
          boxShadow: '0 1px 3px rgba(0,0,0,0.1)'
        }}>
          <div style={{
            width: '48px',
            height: '48px',
            border: '3px solid #f3f3f3',
            borderTop: '3px solid #ee4d2d',
            borderRadius: '50%',
            animation: 'spin 1s linear infinite',
            margin: '0 auto 16px'
          }}></div>
          <p style={{ color: '#666', fontWeight: '500' }}>Memuat status pembayaran...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div style={{
        minHeight: '100vh',
        backgroundColor: '#f5f5f5',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      }}>
        <div style={{
          textAlign: 'center',
          backgroundColor: 'white',
          padding: '2rem',
          borderRadius: '8px',
          boxShadow: '0 1px 3px rgba(0,0,0,0.1)'
        }}>
          <p style={{ color: '#e74c3c', fontWeight: '500', marginBottom: '1rem' }}>{error}</p>
          <button 
            onClick={handleBackToHome}
            style={{
              padding: '0.5rem 1rem',
              backgroundColor: '#ee4d2d',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer'
            }}
          >
            Kembali ke Beranda
          </button>
        </div>
      </div>
    )
  }

  return (
    <SellzioErrorBoundary>
      <SellzioPaymentStatusPage
        payment={payment}
        onBack={handleBackToHome}
        onRefreshStatus={handleRefreshStatus}
        onCancelPayment={handleCancelPayment}
      />
    </SellzioErrorBoundary>
  )
}

interface CloudflareConfig {
  apiToken: string
  zoneId: string
  domain: string
}

interface DNSRecord {
  id?: string
  type: string
  name: string
  content: string
  ttl?: number
  proxied?: boolean
}

export class CloudflareDNSService {
  private config: CloudflareConfig

  constructor(config: CloudflareConfig) {
    this.config = config
  }

  private async makeRequest(endpoint: string, method: string = 'GET', data?: any) {
    const url = `https://api.cloudflare.com/client/v4${endpoint}`
    
    const options: RequestInit = {
      method,
      headers: {
        'Authorization': `Bearer ${this.config.apiToken}`,
        'Content-Type': 'application/json',
      },
    }

    if (data && (method === 'POST' || method === 'PUT' || method === 'PATCH')) {
      options.body = JSON.stringify(data)
    }

    const response = await fetch(url, options)
    const result = await response.json()

    if (!response.ok) {
      throw new Error(`Cloudflare API error: ${result.errors?.[0]?.message || 'Unknown error'}`)
    }

    return result
  }

  async createSubdomainRecord(subdomain: string, targetIP: string = '***********'): Promise<boolean> {
    try {
      console.log('🔥 CLOUDFLARE: Creating DNS record for:', `${subdomain}.${this.config.domain}`)

      // Check if record already exists
      const existingRecord = await this.getRecord(subdomain)
      if (existingRecord) {
        console.log('🔥 CLOUDFLARE: Record already exists, updating...')
        return await this.updateSubdomainRecord(subdomain, targetIP)
      }

      const recordData: DNSRecord = {
        type: 'A',
        name: subdomain,
        content: targetIP,
        ttl: 1, // Auto TTL
        proxied: true // Enable Cloudflare proxy for SSL
      }

      const result = await this.makeRequest(
        `/zones/${this.config.zoneId}/dns_records`,
        'POST',
        recordData
      )

      console.log('🔥 CLOUDFLARE: DNS record created successfully:', result.result)
      return true

    } catch (error) {
      console.error('🔥 CLOUDFLARE: Error creating DNS record:', error)
      return false
    }
  }

  async updateSubdomainRecord(subdomain: string, targetIP: string = '***********'): Promise<boolean> {
    try {
      const existingRecord = await this.getRecord(subdomain)
      if (!existingRecord) {
        return await this.createSubdomainRecord(subdomain, targetIP)
      }

      const recordData: DNSRecord = {
        type: 'A',
        name: subdomain,
        content: targetIP,
        ttl: 1,
        proxied: true
      }

      const result = await this.makeRequest(
        `/zones/${this.config.zoneId}/dns_records/${existingRecord.id}`,
        'PUT',
        recordData
      )

      console.log('🔥 CLOUDFLARE: DNS record updated successfully:', result.result)
      return true

    } catch (error) {
      console.error('🔥 CLOUDFLARE: Error updating DNS record:', error)
      return false
    }
  }

  async deleteSubdomainRecord(subdomain: string): Promise<boolean> {
    try {
      const existingRecord = await this.getRecord(subdomain)
      if (!existingRecord) {
        console.log('🔥 CLOUDFLARE: Record not found, nothing to delete')
        return true
      }

      await this.makeRequest(
        `/zones/${this.config.zoneId}/dns_records/${existingRecord.id}`,
        'DELETE'
      )

      console.log('🔥 CLOUDFLARE: DNS record deleted successfully')
      return true

    } catch (error) {
      console.error('🔥 CLOUDFLARE: Error deleting DNS record:', error)
      return false
    }
  }

  async getRecord(subdomain: string): Promise<any> {
    try {
      const result = await this.makeRequest(
        `/zones/${this.config.zoneId}/dns_records?name=${subdomain}.${this.config.domain}&type=A`
      )

      return result.result?.[0] || null

    } catch (error) {
      console.error('🔥 CLOUDFLARE: Error getting DNS record:', error)
      return null
    }
  }

  async listSubdomainRecords(): Promise<any[]> {
    try {
      const result = await this.makeRequest(
        `/zones/${this.config.zoneId}/dns_records?type=A`
      )

      return result.result.filter((record: any) => 
        record.name.endsWith(`.${this.config.domain}`) && 
        record.name !== this.config.domain
      )

    } catch (error) {
      console.error('🔥 CLOUDFLARE: Error listing DNS records:', error)
      return []
    }
  }

  async verifyRecord(subdomain: string): Promise<boolean> {
    try {
      const record = await this.getRecord(subdomain)
      return !!record

    } catch (error) {
      console.error('🔥 CLOUDFLARE: Error verifying DNS record:', error)
      return false
    }
  }
}

// Factory function to create DNS service instance
export function createCloudflareService(): CloudflareDNSService {
  const config: CloudflareConfig = {
    apiToken: process.env.CLOUDFLARE_API_TOKEN || '',
    zoneId: process.env.CLOUDFLARE_ZONE_ID || '',
    domain: process.env.NEXT_PUBLIC_MAIN_DOMAIN || 'sellzio.my.id'
  }

  if (!config.apiToken || !config.zoneId) {
    throw new Error('Cloudflare API token and Zone ID are required')
  }

  return new CloudflareDNSService(config)
}

// Helper function for Vercel IP (you need to get this from Vercel dashboard)
export function getVercelIP(): string {
  // This should be your Vercel deployment IP
  // You can get this from Vercel dashboard or use a CNAME to your Vercel domain
  return process.env.VERCEL_IP || '***********' // Vercel's IP range
}

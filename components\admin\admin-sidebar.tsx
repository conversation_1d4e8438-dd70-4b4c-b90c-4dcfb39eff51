"use client"

import type React from "react"

import Link from "next/link"
import { usePathname, useRouter } from "next/navigation"
import { useState } from "react"
import { cn } from "@/lib/utils"
import {
  LayoutDashboard,
  Store,
  ShoppingBag,
  Users,
  CreditCard,
  FileText,
  Settings,
  BarChart,
  HelpCircle,
  MessageSquare,
  Building,
  Megaphone,
  PlusCircle,
  List,
  ClipboardCheck,
  FolderTree,
  Palette,
  Tag,
  BadgeCheck,
  ShieldCheck,
  UserCog,
  Shield,
  Users2,
  ScrollText,
  Percent,
  Receipt,
  Calculator,
  PaintBucket,
  Layout,
  BookOpen,
  Mail,
  HelpingHand,
  Ticket,
  DotIcon as DocIcon,
  History,
  User,
  Bell,
  Lock,
  UsersRound,
  LineChart,
  PieChart,
  BarChart2,
  FileBarChart,
  Cog,
  Plug,
  Code,
  FileCode,
  ShieldAlert,
  Database,
  Home,
  Briefcase,
  DollarSign,
  Search,
  Share2,
  Globe,
  Award,
} from "lucide-react"

interface AdminSidebarProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function AdminSidebar({ open, onOpenChange }: AdminSidebarProps) {
  const pathname = usePathname()
  const router = useRouter()
  const [isNavigating, setIsNavigating] = useState(false)

  const isActive = (path: string) => {
    return pathname === path || pathname.startsWith(`${path}/`)
  }

  const handleNavigation = (href: string) => (e: React.MouseEvent<HTMLAnchorElement>) => {
    if (isNavigating) {
      e.preventDefault()
      return
    }

    // Jika navigasi tidak menggunakan Link (misalnya karena onClick custom)
    if (!e.defaultPrevented) {
      e.preventDefault()
      setIsNavigating(true)

      // Gunakan timeout untuk mencegah multiple clicks
      setTimeout(() => {
        router.push(href)
        setIsNavigating(false)
      }, 100)
    }
  }

  const menuItems = [
    {
      title: "Dashboard",
      icon: <LayoutDashboard className="h-5 w-5" />,
      href: "/admin/dashboard",
      active: isActive("/admin/dashboard") && !pathname.includes("/admin/dashboard/"),
    },
    {
      title: "Tenants",
      icon: <Building className="h-5 w-5" />,
      href: "/admin/dashboard/tenants",
      active: isActive("/admin/dashboard/tenants"),
      submenu: [
        {
          title: "All Tenants",
          href: "/admin/dashboard/tenants",
          active: pathname === "/admin/dashboard/tenants",
          icon: <List className="h-4 w-4" />,
        },
        {
          title: "Create Tenant",
          href: "/admin/dashboard/tenants/create",
          active: pathname === "/admin/dashboard/tenants/create",
          icon: <PlusCircle className="h-4 w-4" />,
        },
        {
          title: "Applications",
          href: "/admin/dashboard/tenants/applications",
          active: pathname === "/admin/dashboard/tenants/applications",
          icon: <ClipboardCheck className="h-4 w-4" />,
        },
        {
          title: "Subscription Plans",
          href: "/admin/dashboard/tenants/plans",
          active: pathname === "/admin/dashboard/tenants/plans",
          icon: <ScrollText className="h-4 w-4" />,
        },
        {
          title: "Domain Management",
          href: "/admin/dashboard/tenants/domains",
          active: pathname === "/admin/dashboard/tenants/domains",
          icon: <Globe className="h-4 w-4" />,
        },
      ],
    },
    {
      title: "Stores",
      icon: <Store className="h-5 w-5" />,
      href: "/admin/dashboard/stores",
      active: isActive("/admin/dashboard/stores"),
      submenu: [
        {
          title: "All Stores",
          href: "/admin/dashboard/stores",
          active: pathname === "/admin/dashboard/stores",
          icon: <List className="h-4 w-4" />,
        },
        {
          title: "Create Store",
          href: "/admin/dashboard/stores/create",
          active: pathname === "/admin/dashboard/stores/create",
          icon: <PlusCircle className="h-4 w-4" />,
        },
        {
          title: "Verification Queue",
          href: "/admin/dashboard/stores/verification",
          active: pathname === "/admin/dashboard/stores/verification",
          icon: <BadgeCheck className="h-4 w-4" />,
        },
        {
          title: "Store Categories",
          href: "/admin/dashboard/stores/categories",
          active: pathname === "/admin/dashboard/stores/categories",
          icon: <FolderTree className="h-4 w-4" />,
        },
        {
          title: "Store Templates",
          href: "/admin/dashboard/stores/templates",
          active: pathname === "/admin/dashboard/stores/templates",
          icon: <Palette className="h-4 w-4" />,
        },
      ],
    },
    {
      title: "Products",
      icon: <ShoppingBag className="h-5 w-5" />,
      href: "/admin/dashboard/products",
      active: isActive("/admin/dashboard/products"),
      submenu: [
        {
          title: "All Products",
          href: "/admin/dashboard/products",
          active: pathname === "/admin/dashboard/products",
          icon: <List className="h-4 w-4" />,
        },
        {
          title: "Categories & Attributes",
          href: "/admin/dashboard/products/categories",
          active: pathname === "/admin/dashboard/products/categories",
          icon: <Tag className="h-4 w-4" />,
        },
        {
          title: "Brand Management",
          href: "/admin/dashboard/products/brands",
          active: pathname === "/admin/dashboard/products/brands",
          icon: <Award className="h-4 w-4" />,
        },
        {
          title: "Product Moderation",
          href: "/admin/dashboard/products/moderation",
          active: pathname === "/admin/dashboard/products/moderation",
          icon: <ShieldCheck className="h-4 w-4" />,
        },
      ],
    },
    {
      title: "Users",
      icon: <Users className="h-5 w-5" />,
      href: "/admin/dashboard/users",
      active: isActive("/admin/dashboard/users"),
      submenu: [
        {
          title: "All Users",
          href: "/admin/dashboard/users",
          active: pathname === "/admin/dashboard/users",
          icon: <List className="h-4 w-4" />,
        },
        {
          title: "Create User",
          href: "/admin/dashboard/users/create",
          active: pathname === "/admin/dashboard/users/create",
          icon: <PlusCircle className="h-4 w-4" />,
        },
        {
          title: "Admins",
          href: "/admin/dashboard/users/admins",
          active: pathname === "/admin/dashboard/users/admins",
          icon: <UserCog className="h-4 w-4" />,
        },
        {
          title: "Tenant Admins",
          href: "/admin/dashboard/users/tenant-admins",
          active: pathname === "/admin/dashboard/users/tenant-admins",
          icon: <Briefcase className="h-4 w-4" />,
        },
        {
          title: "Store Owners",
          href: "/admin/dashboard/users/store-owners",
          active: pathname === "/admin/dashboard/users/store-owners",
          icon: <Store className="h-4 w-4" />,
        },
        {
          title: "End Users",
          href: "/admin/dashboard/users/end-users",
          active: pathname === "/admin/dashboard/users/end-users",
          icon: <Users2 className="h-4 w-4" />,
        },
        {
          title: "Roles & Permissions",
          href: "/admin/dashboard/users/roles",
          active: pathname === "/admin/dashboard/users/roles",
          icon: <Shield className="h-4 w-4" />,
        },
      ],
    },
    {
      title: "Financial",
      icon: <CreditCard className="h-5 w-5" />,
      href: "/admin/dashboard/financial",
      active: isActive("/admin/dashboard/financial"),
      submenu: [
        {
          title: "Overview",
          href: "/admin/dashboard/financial",
          active: pathname === "/admin/dashboard/financial",
          icon: <Home className="h-4 w-4" />,
        },
        {
          title: "Commissions",
          href: "/admin/dashboard/financial/commissions",
          active: pathname === "/admin/dashboard/financial/commissions",
          icon: <Percent className="h-4 w-4" />,
        },
        {
          title: "Payouts",
          href: "/admin/dashboard/financial/payouts",
          active: pathname === "/admin/dashboard/financial/payouts",
          icon: <DollarSign className="h-4 w-4" />,
        },
        {
          title: "Invoices & Billing",
          href: "/admin/dashboard/financial/invoices",
          active: pathname === "/admin/dashboard/financial/invoices",
          icon: <Receipt className="h-4 w-4" />,
        },
        {
          title: "Tax Management",
          href: "/admin/dashboard/financial/tax",
          active: pathname === "/admin/dashboard/financial/tax",
          icon: <Calculator className="h-4 w-4" />,
        },
      ],
    },
    {
      title: "Content",
      icon: <FileText className="h-5 w-5" />,
      href: "/admin/dashboard/content",
      active: isActive("/admin/dashboard/content"),
      submenu: [
        {
          title: "Overview",
          href: "/admin/dashboard/content",
          active: pathname === "/admin/dashboard/content",
          icon: <Home className="h-4 w-4" />,
        },
        {
          title: "Themes",
          href: "/admin/dashboard/content/themes",
          active: pathname === "/admin/dashboard/content/themes",
          icon: <PaintBucket className="h-4 w-4" />,
        },
        {
          title: "Landing Pages",
          href: "/admin/dashboard/content/landing-pages",
          active: pathname === "/admin/dashboard/content/landing-pages",
          icon: <Layout className="h-4 w-4" />,
        },
        {
          title: "Blog",
          href: "/admin/dashboard/content/blog",
          active: pathname === "/admin/dashboard/content/blog",
          icon: <BookOpen className="h-4 w-4" />,
        },
        {
          title: "Email Templates",
          href: "/admin/dashboard/content/email-templates",
          active: pathname === "/admin/dashboard/content/email-templates",
          icon: <Mail className="h-4 w-4" />,
        },
        {
          title: "Help Center",
          href: "/admin/dashboard/content/help-center",
          active: pathname === "/admin/dashboard/content/help-center",
          icon: <HelpingHand className="h-4 w-4" />,
        },
      ],
    },
    {
      title: "Marketing",
      icon: <Megaphone className="h-5 w-5" />,
      href: "/admin/dashboard/marketing",
      active: isActive("/admin/dashboard/marketing"),
      submenu: [
        {
          title: "Overview",
          href: "/admin/dashboard/marketing",
          active: pathname === "/admin/dashboard/marketing",
          icon: <Home className="h-4 w-4" />,
        },
        {
          title: "Promotions",
          href: "/admin/dashboard/marketing/promotions",
          active: pathname === "/admin/dashboard/marketing/promotions",
          icon: <Percent className="h-4 w-4" />,
        },
        {
          title: "Featured Content",
          href: "/admin/dashboard/marketing/featured",
          active: pathname === "/admin/dashboard/marketing/featured",
          icon: <Award className="h-4 w-4" />,
        },
        {
          title: "SEO Management",
          href: "/admin/dashboard/marketing/seo",
          active: pathname === "/admin/dashboard/marketing/seo",
          icon: <Search className="h-4 w-4" />,
        },
        {
          title: "Affiliate Program",
          href: "/admin/dashboard/marketing/affiliate",
          active: pathname === "/admin/dashboard/marketing/affiliate",
          icon: <Share2 className="h-4 w-4" />,
        },
      ],
    },
    {
      title: "Domain Management",
      icon: <Globe className="h-5 w-5" />,
      href: "/admin/domains",
      active: isActive("/admin/domains"),
    },
    {
      title: "Analytics",
      icon: <BarChart className="h-5 w-5" />,
      href: "/admin/dashboard/analytics",
      active: isActive("/admin/dashboard/analytics"),
    },
    {
      title: "Reports",
      icon: <BarChart className="h-5 w-5" />,
      href: "/admin/dashboard/reports",
      active: isActive("/admin/dashboard/reports"),
      submenu: [
        {
          title: "Overview",
          href: "/admin/dashboard/reports",
          active: pathname === "/admin/dashboard/reports",
          icon: <Home className="h-4 w-4" />,
        },
        {
          title: "Sales Reports",
          href: "/admin/dashboard/reports/sales",
          active: pathname === "/admin/dashboard/reports/sales",
          icon: <LineChart className="h-4 w-4" />,
        },
        {
          title: "Tenant Reports",
          href: "/admin/dashboard/reports/tenants",
          active: pathname === "/admin/dashboard/reports/tenants",
          icon: <PieChart className="h-4 w-4" />,
        },
        {
          title: "User Reports",
          href: "/admin/dashboard/reports/users",
          active: pathname === "/admin/dashboard/reports/users",
          icon: <BarChart2 className="h-4 w-4" />,
        },
        {
          title: "Custom Reports",
          href: "/admin/dashboard/reports/custom",
          active: pathname === "/admin/dashboard/reports/custom",
          icon: <FileBarChart className="h-4 w-4" />,
        },
      ],
    },
    {
      title: "Systems",
      icon: <Settings className="h-5 w-5" />,
      href: "/admin/dashboard/systems",
      active: isActive("/admin/dashboard/systems"),
      submenu: [
        {
          title: "Overview",
          href: "/admin/dashboard/systems",
          active: pathname === "/admin/dashboard/systems",
          icon: <Home className="h-4 w-4" />,
        },
        {
          title: "Platform Settings",
          href: "/admin/dashboard/systems/settings",
          active: pathname === "/admin/dashboard/systems/settings",
          icon: <Cog className="h-4 w-4" />,
        },
        {
          title: "Integration Hub",
          href: "/admin/dashboard/systems/integrations",
          active: pathname === "/admin/dashboard/systems/integrations",
          icon: <Plug className="h-4 w-4" />,
        },
        {
          title: "API Management",
          href: "/admin/dashboard/systems/api",
          active: pathname === "/admin/dashboard/systems/api",
          icon: <Code className="h-4 w-4" />,
        },
        {
          title: "Logs & Monitoring",
          href: "/admin/dashboard/systems/logs",
          active: pathname === "/admin/dashboard/systems/logs",
          icon: <FileCode className="h-4 w-4" />,
        },
        {
          title: "Security Center",
          href: "/admin/dashboard/systems/security",
          active: pathname === "/admin/dashboard/systems/security",
          icon: <ShieldAlert className="h-4 w-4" />,
        },
        {
          title: "Backup & Restore",
          href: "/admin/dashboard/systems/backup",
          active: pathname === "/admin/dashboard/systems/backup",
          icon: <Database className="h-4 w-4" />,
        },
      ],
    },
    {
      title: "Help & Support",
      icon: <HelpCircle className="h-5 w-5" />,
      href: "/admin/dashboard/help",
      active: isActive("/admin/dashboard/help"),
      submenu: [
        {
          title: "Support Dashboard",
          href: "/admin/dashboard/help",
          active: pathname === "/admin/dashboard/help",
          icon: <Home className="h-4 w-4" />,
        },
        {
          title: "Support Tickets",
          href: "/admin/dashboard/help/tickets",
          active: pathname === "/admin/dashboard/help/tickets",
          icon: <Ticket className="h-4 w-4" />,
        },
        {
          title: "Documentation",
          href: "/admin/dashboard/help/documentation",
          active: pathname === "/admin/dashboard/help/documentation",
          icon: <DocIcon className="h-4 w-4" />,
        },
        {
          title: "Release Notes",
          href: "/admin/dashboard/help/releases",
          active: pathname === "/admin/dashboard/help/releases",
          icon: <History className="h-4 w-4" />,
        },
      ],
    },
    {
      title: "Account",
      icon: <MessageSquare className="h-5 w-5" />,
      href: "/admin/dashboard/account",
      active: isActive("/admin/dashboard/account"),
      submenu: [
        {
          title: "Overview",
          href: "/admin/dashboard/account",
          active: pathname === "/admin/dashboard/account",
          icon: <Home className="h-4 w-4" />,
        },
        {
          title: "Profile",
          href: "/admin/dashboard/account/profile",
          active: pathname === "/admin/dashboard/account/profile",
          icon: <User className="h-4 w-4" />,
        },
        {
          title: "Notifications",
          href: "/admin/dashboard/account/notifications",
          active: pathname === "/admin/dashboard/account/notifications",
          icon: <Bell className="h-4 w-4" />,
        },
        {
          title: "Security",
          href: "/admin/dashboard/account/security",
          active: pathname === "/admin/dashboard/account/security",
          icon: <Lock className="h-4 w-4" />,
        },
        {
          title: "Team",
          href: "/admin/dashboard/account/team",
          active: pathname === "/admin/dashboard/account/team",
          icon: <UsersRound className="h-4 w-4" />,
        },
      ],
    },
  ]

  return (
    <div
      className={cn(
        "fixed inset-y-0 left-0 z-20 flex w-64 flex-col border-r bg-background transition-transform lg:static lg:translate-x-0",
        open ? "translate-x-0" : "-translate-x-full",
      )}
    >
      <div className="flex h-14 items-center border-b px-4">
        <Link href="/admin/dashboard" className="flex items-center gap-2 font-semibold">
          <span className="flex h-6 w-6 items-center justify-center rounded-md bg-primary text-xs font-bold text-primary-foreground">
            S
          </span>
          <span>SELLZIO OS</span>
        </Link>
      </div>
      <div className="flex-1 overflow-auto py-2">
        <div className="px-3 py-2">
          <div className="flex items-center gap-2 rounded-md bg-muted px-3 py-2">
            <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary text-xs font-bold text-primary-foreground">
              SA
            </div>
            <div className="flex flex-col">
              <span className="text-sm font-medium">Sellzio Admin</span>
              <span className="text-xs text-muted-foreground"><EMAIL></span>
            </div>
          </div>
        </div>
        <nav className="space-y-1 px-2 py-2">
          {menuItems.map((item, index) => (
            <div key={index}>
              <Link
                href={item.href}
                className={cn(
                  "flex items-center gap-3 rounded-md px-3 py-2 text-sm font-medium transition-colors",
                  item.active
                    ? "bg-muted text-foreground"
                    : "text-muted-foreground hover:bg-muted hover:text-foreground",
                )}
              >
                {item.icon}
                {item.title}
              </Link>
              {item.submenu && item.active && (
                <div className="ml-4 mt-1 space-y-1 border-l pl-3">
                  {item.submenu.map((subitem, subindex) => (
                    <Link
                      key={subindex}
                      href={subitem.href}
                      className={cn(
                        "flex items-center gap-3 rounded-md px-3 py-2 text-sm font-medium transition-colors",
                        subitem.active
                          ? "bg-muted text-foreground"
                          : "text-muted-foreground hover:bg-muted hover:text-foreground",
                      )}
                    >
                      {subitem.icon && <span className="mr-1">{subitem.icon}</span>}
                      {subitem.title}
                    </Link>
                  ))}
                </div>
              )}
            </div>
          ))}
        </nav>
      </div>
    </div>
  )
}

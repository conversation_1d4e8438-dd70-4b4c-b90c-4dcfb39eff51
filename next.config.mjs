/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  images: {
    domains: ['localhost', 'app.localhost', 'admin.localhost', 'res.cloudinary.com', 'images.unsplash.com'],
    unoptimized: true,
  },
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },
  // Hapus properti yang tidak dikenali
  experimental: {
    // Izinkan subdomain development
    allowedDevOrigins: ['app.localhost:3000', 'admin.localhost:3000']
  },
  // Tambahkan konfigurasi untuk mencegah prerendering pada halaman tertentu
  output: 'standalone',
};

export default nextConfig;

"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON>bs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { useToast } from "@/hooks/use-toast"
import {
  CheckCircle,
  XCircle,
  AlertTriangle,
  Search,
  Plus,
  MoreHorizontal,
  ExternalLink,
  RefreshCw,
  Shield,
  Trash2,
  Clock,
  Globe,
  Settings,
  User,
  Calendar,
} from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import Link from "next/link"

// Interfaces
interface TenantData {
  id: string
  name: string
  slug: string
  domain: string | null
  subscriptionPlan: string
  subscriptionStatus: string
  createdAt: string
}

interface CustomDomainRequest {
  id: string
  domain: string
  tenant_id: string
  reason: string
  status: 'pending' | 'approved' | 'rejected' | 'configuration_failed'
  requested_at: string
  processed_at?: string
  tenants: {
    id: string
    name: string
    subdomain: string
    domain?: string
  }
}

// Mock data for domains
const domains = [
  {
    id: "dom_1",
    domain: "fashionista.sellzio.my.id",
    tenantName: "Fashionista",
    tenantId: "tenant_1",
    type: "subdomain",
    status: "verified",
    sslStatus: "active",
    sslExpiry: "2024-12-15",
    createdAt: "2023-05-10",
  },
  {
    id: "dom_2",
    domain: "beautysecrets.com",
    tenantName: "Beauty Secrets",
    tenantId: "tenant_2",
    type: "custom",
    status: "verified",
    sslStatus: "active",
    sslExpiry: "2024-11-22",
    createdAt: "2023-06-05",
  },
  {
    id: "dom_3",
    domain: "homeessentials.sellzio.my.id",
    tenantName: "Home Essentials",
    tenantId: "tenant_3",
    type: "subdomain",
    status: "pending",
    sslStatus: "pending",
    sslExpiry: null,
    createdAt: "2023-09-18",
  },
  {
    id: "dom_5",
    domain: "sportsgear.sellzio.com",
    tenantName: "Sports Gear",
    status: "failed",
    sslStatus: "failed",
    sslExpiry: null,
    createdAt: "2023-08-25",
  },
]

export function DomainManagementList() {
  const [searchQuery, setSearchQuery] = useState("")
  const [activeTab, setActiveTab] = useState("all")
  const [tenants, setTenants] = useState<TenantData[]>([])
  const [customDomainRequests, setCustomDomainRequests] = useState<CustomDomainRequest[]>([])
  const [loading, setLoading] = useState(true)
  const [processingId, setProcessingId] = useState<string | null>(null)
  const { toast } = useToast()

  // Fetch data
  useEffect(() => {
    fetchTenants()
    fetchCustomDomainRequests()
  }, [])

  const fetchTenants = async () => {
    try {
      const response = await fetch('/api/tenants/list?orderBy=created_at&orderDirection=desc')
      const result = await response.json()

      if (result.success) {
        setTenants(result.data || [])
      }
    } catch (error) {
      console.error('Error fetching tenants:', error)
    } finally {
      setLoading(false)
    }
  }

  const fetchCustomDomainRequests = async () => {
    try {
      const response = await fetch('/api/admin/custom-domain-requests?status=all')
      const data = await response.json()

      if (data.success) {
        setCustomDomainRequests(data.requests || [])
      }
    } catch (error) {
      console.error('Error fetching custom domain requests:', error)
    }
  }

  const handleApproveReject = async (requestId: string, action: 'approve' | 'reject', adminNotes: string = '') => {
    setProcessingId(requestId)
    try {
      const response = await fetch('/api/admin/custom-domain-requests', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          requestId,
          action,
          adminNotes
        })
      })

      const data = await response.json()

      if (data.success) {
        toast({
          title: "Success",
          description: data.message,
        })
        fetchCustomDomainRequests() // Refresh list
      } else {
        throw new Error(data.error)
      }
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || `Failed to ${action} domain request`,
        variant: "destructive"
      })
    } finally {
      setProcessingId(null)
    }
  }

  const filteredDomains = domains.filter(
    (domain) =>
      domain.domain.toLowerCase().includes(searchQuery.toLowerCase()) ||
      domain.tenantName.toLowerCase().includes(searchQuery.toLowerCase()),
  )

  const filteredRequests = customDomainRequests.filter(
    (request) =>
      request.domain.toLowerCase().includes(searchQuery.toLowerCase()) ||
      request.tenants?.name.toLowerCase().includes(searchQuery.toLowerCase()),
  )

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return <Badge variant="outline" className="text-yellow-600"><Clock className="h-3 w-3 mr-1" />Pending</Badge>
      case 'approved':
        return <Badge variant="default" className="text-green-600"><CheckCircle className="h-3 w-3 mr-1" />Approved</Badge>
      case 'rejected':
        return <Badge variant="destructive"><XCircle className="h-3 w-3 mr-1" />Rejected</Badge>
      case 'configuration_failed':
        return <Badge variant="destructive"><AlertTriangle className="h-3 w-3 mr-1" />Config Failed</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('id-ID', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  return (
    <div className="space-y-6">
      {/* Pending Requests Alert */}
      {customDomainRequests.filter(req => req.status === 'pending').length > 0 && (
        <Alert>
          <Clock className="h-4 w-4" />
          <AlertTitle>Pending Custom Domain Requests</AlertTitle>
          <AlertDescription>
            You have {customDomainRequests.filter(req => req.status === 'pending').length} pending custom domain request(s) awaiting approval.
          </AlertDescription>
        </Alert>
      )}

      {/* Search */}
      <div className="flex items-center justify-between">
        <div className="relative w-64">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search domains or tenants..."
            className="pl-8"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        <Link href="/admin/dashboard/debug">
          <Button variant="outline">
            <Settings className="mr-2 h-4 w-4" />
            DNS Debug Tools
          </Button>
        </Link>
      </div>

      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList>
          <TabsTrigger value="all">All Domains</TabsTrigger>
          <TabsTrigger value="requests">
            Custom Domain Requests
            {customDomainRequests.filter(req => req.status === 'pending').length > 0 && (
              <Badge variant="destructive" className="ml-2 h-5 w-5 rounded-full p-0 text-xs">
                {customDomainRequests.filter(req => req.status === 'pending').length}
              </Badge>
            )}
          </TabsTrigger>
          <TabsTrigger value="tenants">Tenant Overview</TabsTrigger>
        </TabsList>

        {/* All Domains Tab */}
        <TabsContent value="all" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>All Domains</CardTitle>
              <CardDescription>
                Overview of all tenant domains (subdomains and custom domains)
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Domain</TableHead>
                      <TableHead>Tenant</TableHead>
                      <TableHead>Type</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>SSL</TableHead>
                      <TableHead>Created</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredDomains.map((domain) => (
                      <TableRow key={domain.id}>
                        <TableCell className="font-medium">{domain.domain}</TableCell>
                        <TableCell>{domain.tenantName}</TableCell>
                        <TableCell>
                          <Badge variant={domain.type === 'custom' ? 'default' : 'outline'}>
                            {domain.type === 'custom' ? 'Custom' : 'Subdomain'}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          {domain.status === "verified" ? (
                            <Badge className="bg-green-100 text-green-800">
                              <CheckCircle className="mr-1 h-3 w-3" />
                              Verified
                            </Badge>
                          ) : domain.status === "pending" ? (
                            <Badge variant="outline" className="bg-yellow-100 text-yellow-800">
                              <Clock className="mr-1 h-3 w-3" />
                              Pending
                            </Badge>
                          ) : (
                            <Badge variant="destructive">
                              <XCircle className="mr-1 h-3 w-3" />
                              Failed
                            </Badge>
                          )}
                        </TableCell>
                        <TableCell>
                          {domain.sslStatus === "active" ? (
                            <Badge className="bg-green-100 text-green-800">
                              <Shield className="mr-1 h-3 w-3" />
                              Active
                            </Badge>
                          ) : domain.sslStatus === "expiring" ? (
                            <Badge variant="outline" className="bg-orange-100 text-orange-800">
                              <AlertTriangle className="mr-1 h-3 w-3" />
                              Expiring
                            </Badge>
                          ) : domain.sslStatus === "pending" ? (
                            <Badge variant="outline" className="bg-yellow-100 text-yellow-800">
                              <Clock className="mr-1 h-3 w-3" />
                              Pending
                            </Badge>
                          ) : (
                            <Badge variant="destructive">
                              <XCircle className="mr-1 h-3 w-3" />
                              Failed
                            </Badge>
                          )}
                        </TableCell>
                        <TableCell>{new Date(domain.createdAt).toLocaleDateString()}</TableCell>
                        <TableCell className="text-right">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" className="h-8 w-8 p-0">
                                <span className="sr-only">Open menu</span>
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>Actions</DropdownMenuLabel>
                              <DropdownMenuItem onClick={() => window.open(`https://${domain.domain}`, "_blank")}>
                                <ExternalLink className="mr-2 h-4 w-4" />
                                Visit Domain
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem>
                                <RefreshCw className="mr-2 h-4 w-4" />
                                Verify Domain
                              </DropdownMenuItem>
                              <DropdownMenuItem>
                                <Shield className="mr-2 h-4 w-4" />
                                Renew SSL
                              </DropdownMenuItem>
                              <DropdownMenuItem className="text-red-600">
                                <Trash2 className="mr-2 h-4 w-4" />
                                Delete Domain
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
                      <AlertTriangle className="mr-1 h-3 w-3" />
                      Pending
                    </Badge>
                  ) : (
                    <Badge variant="outline" className="bg-red-100 text-red-800">
                      <XCircle className="mr-1 h-3 w-3" />
                      Failed
                    </Badge>
                  )}
                </TableCell>
                <TableCell>
                  {domain.sslStatus === "active" ? (
                    <Badge className="bg-green-100 text-green-800">
                      <Shield className="mr-1 h-3 w-3" />
                      Active
                    </Badge>
                  ) : domain.sslStatus === "expiring" ? (
                    <Badge variant="outline" className="bg-yellow-100 text-yellow-800">
                      <AlertTriangle className="mr-1 h-3 w-3" />
                      Expiring
                    </Badge>
                  ) : domain.sslStatus === "pending" ? (
                    <Badge variant="outline" className="bg-blue-100 text-blue-800">
                      <RefreshCw className="mr-1 h-3 w-3" />
                      Pending
                    </Badge>
                  ) : (
                    <Badge variant="outline" className="bg-red-100 text-red-800">
                      <XCircle className="mr-1 h-3 w-3" />
                      Failed
                    </Badge>
                  )}
                </TableCell>
                <TableCell>{new Date(domain.createdAt).toLocaleDateString()}</TableCell>
                <TableCell className="text-right">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" className="h-8 w-8 p-0">
                        <span className="sr-only">Open menu</span>
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuLabel>Actions</DropdownMenuLabel>
                      <DropdownMenuItem onClick={() => window.open(`https://${domain.domain}`, "_blank")}>
                        <ExternalLink className="mr-2 h-4 w-4" />
                        Visit Domain
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      {domain.status !== "verified" && (
                        <DropdownMenuItem onClick={() => handleVerifyDomain(domain.id)}>
                          <CheckCircle className="mr-2 h-4 w-4" />
                          Verify Domain
                        </DropdownMenuItem>
                      )}
                      {(domain.sslStatus === "expiring" || domain.sslStatus === "failed") && (
                        <DropdownMenuItem onClick={() => handleRenewSSL(domain.id)}>
                          <RefreshCw className="mr-2 h-4 w-4" />
                          Renew SSL
                        </DropdownMenuItem>
                      )}
                      <DropdownMenuItem className="text-red-600" onClick={() => handleDeleteDomain(domain.id)}>
                        <Trash2 className="mr-2 h-4 w-4" />
                        Delete Domain
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  )
}

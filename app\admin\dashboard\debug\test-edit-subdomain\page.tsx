"use client"

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { useToast } from '@/hooks/use-toast'
import { 
  Edit, 
  CheckCircle, 
  AlertCircle,
  Loader2,
  ArrowRight,
  Trash2
} from 'lucide-react'

export default function TestEditSubdomainPage() {
  const [currentSubdomain, setCurrentSubdomain] = useState('test-old')
  const [newSubdomain, setNewSubdomain] = useState('test-new')
  const [testTenantId, setTestTenantId] = useState('96111d56-b82d-43e7-9926-8f0530dc6063') // test1 tenant
  const [loading, setLoading] = useState(false)
  const [result, setResult] = useState<any>(null)
  const { toast } = useToast()

  const testEditSubdomain = async () => {
    if (!currentSubdomain || !newSubdomain || !testTenantId) {
      toast({
        title: "Error",
        description: "Please fill all fields",
        variant: "destructive"
      })
      return
    }

    setLoading(true)
    setResult(null)

    try {
      console.log('🔥 TEST: Testing subdomain edit from', currentSubdomain, 'to', newSubdomain)

      // First, set current subdomain
      console.log('🔥 TEST: Step 1 - Setting current subdomain:', currentSubdomain)
      const step1Response = await fetch('/api/tenants/update-subdomain', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          subdomain: currentSubdomain.trim(),
          tenantId: testTenantId.trim()
        })
      })

      const step1Data = await step1Response.json()
      
      if (!step1Response.ok) {
        throw new Error(`Step 1 failed: ${step1Data.error}`)
      }

      // Wait a moment
      await new Promise(resolve => setTimeout(resolve, 2000))

      // Then, change to new subdomain
      console.log('🔥 TEST: Step 2 - Changing to new subdomain:', newSubdomain)
      const step2Response = await fetch('/api/tenants/update-subdomain', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          subdomain: newSubdomain.trim(),
          tenantId: testTenantId.trim()
        })
      })

      const step2Data = await step2Response.json()

      setResult({
        step1: {
          status: step1Response.status,
          success: step1Response.ok,
          data: step1Data
        },
        step2: {
          status: step2Response.status,
          success: step2Response.ok,
          data: step2Data
        }
      })

      if (step2Response.ok && step2Data.success) {
        toast({
          title: "Subdomain Edit Test Successful",
          description: `Successfully changed from ${currentSubdomain} to ${newSubdomain}`,
        })
      } else {
        toast({
          title: "Subdomain Edit Test Failed",
          description: step2Data.error || "Failed to change subdomain",
          variant: "destructive"
        })
      }

    } catch (error: any) {
      console.error('🔥 TEST: Error:', error)
      setResult({
        error: error.message,
        step1: null,
        step2: null
      })
      toast({
        title: "Error",
        description: error.message || "Failed to test subdomain edit",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  const testDomainAccess = async (subdomain: string) => {
    if (!subdomain) return

    const fullDomain = `${subdomain}.sellzio.my.id`
    
    toast({
      title: "Testing Domain Access",
      description: `Opening ${fullDomain} in new tab...`,
    })

    // Open in new tab
    window.open(`https://${fullDomain}`, '_blank')
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Page Header */}
      <div className="space-y-2">
        <h1 className="text-3xl font-bold tracking-tight flex items-center gap-2">
          <Edit className="h-8 w-8" />
          Test Edit Subdomain
        </h1>
        <p className="text-muted-foreground">
          Test sistem edit subdomain - memastikan domain lama dihapus dan domain baru ditambahkan
        </p>
      </div>

      {/* Test Configuration */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Edit className="h-5 w-5" />
            Edit Subdomain Test
          </CardTitle>
          <CardDescription>
            Test perubahan subdomain dari lama ke baru
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid gap-4 md:grid-cols-3">
            <div className="space-y-2">
              <Label htmlFor="currentSubdomain">Current Subdomain:</Label>
              <Input
                id="currentSubdomain"
                value={currentSubdomain}
                onChange={(e) => setCurrentSubdomain(e.target.value.toLowerCase())}
                placeholder="test-old"
              />
              <p className="text-sm text-muted-foreground">
                {currentSubdomain}.sellzio.my.id
              </p>
            </div>

            <div className="flex items-center justify-center">
              <ArrowRight className="h-6 w-6 text-muted-foreground" />
            </div>

            <div className="space-y-2">
              <Label htmlFor="newSubdomain">New Subdomain:</Label>
              <Input
                id="newSubdomain"
                value={newSubdomain}
                onChange={(e) => setNewSubdomain(e.target.value.toLowerCase())}
                placeholder="test-new"
              />
              <p className="text-sm text-muted-foreground">
                {newSubdomain}.sellzio.my.id
              </p>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="testTenantId">Test Tenant ID:</Label>
            <Input
              id="testTenantId"
              value={testTenantId}
              onChange={(e) => setTestTenantId(e.target.value)}
              placeholder="tenant-uuid"
            />
            <p className="text-sm text-muted-foreground">
              Default: test1 tenant
            </p>
          </div>

          <div className="flex gap-2 flex-wrap">
            <Button 
              onClick={testEditSubdomain} 
              disabled={loading || !currentSubdomain || !newSubdomain || !testTenantId}
            >
              {loading ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Testing Edit...
                </>
              ) : (
                <>
                  <Edit className="h-4 w-4 mr-2" />
                  Test Edit Subdomain
                </>
              )}
            </Button>

            <Button 
              variant="outline" 
              onClick={() => testDomainAccess(currentSubdomain)} 
              disabled={!currentSubdomain}
            >
              Test Old Domain
            </Button>

            <Button 
              variant="outline" 
              onClick={() => testDomainAccess(newSubdomain)} 
              disabled={!newSubdomain}
            >
              Test New Domain
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Test Results */}
      {result && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              {result.step2?.success ? (
                <CheckCircle className="h-5 w-5 text-green-500" />
              ) : (
                <AlertCircle className="h-5 w-5 text-red-500" />
              )}
              Test Results
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {result.error ? (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  <strong>Test Error:</strong> {result.error}
                </AlertDescription>
              </Alert>
            ) : (
              <>
                {/* Step 1 Results */}
                <div className="space-y-2">
                  <h4 className="font-medium">Step 1: Set Current Subdomain ({currentSubdomain})</h4>
                  <div className="grid gap-4 md:grid-cols-3">
                    <div className="space-y-2">
                      <Badge variant={result.step1?.success ? "default" : "destructive"}>
                        Status: {result.step1?.status}
                      </Badge>
                    </div>
                    <div className="space-y-2">
                      <Badge variant={result.step1?.data?.dnsConfigured ? "default" : "secondary"}>
                        DNS: {result.step1?.data?.dnsConfigured ? 'Configured' : 'Not Configured'}
                      </Badge>
                    </div>
                    <div className="space-y-2">
                      <Badge variant={result.step1?.data?.vercelConfigured ? "default" : "secondary"}>
                        Vercel: {result.step1?.data?.vercelConfigured ? 'Configured' : 'Not Configured'}
                      </Badge>
                    </div>
                  </div>
                </div>

                {/* Step 2 Results */}
                <div className="space-y-2">
                  <h4 className="font-medium">Step 2: Change to New Subdomain ({newSubdomain})</h4>
                  <div className="grid gap-4 md:grid-cols-3">
                    <div className="space-y-2">
                      <Badge variant={result.step2?.success ? "default" : "destructive"}>
                        Status: {result.step2?.status}
                      </Badge>
                    </div>
                    <div className="space-y-2">
                      <Badge variant={result.step2?.data?.dnsConfigured ? "default" : "secondary"}>
                        DNS: {result.step2?.data?.dnsConfigured ? 'Updated' : 'Not Updated'}
                      </Badge>
                    </div>
                    <div className="space-y-2">
                      <Badge variant={result.step2?.data?.vercelConfigured ? "default" : "secondary"}>
                        Vercel: {result.step2?.data?.vercelConfigured ? 'Updated' : 'Not Updated'}
                      </Badge>
                    </div>
                  </div>
                </div>

                {/* Full Response */}
                <div className="space-y-2">
                  <h4 className="font-medium">Full Response:</h4>
                  <div className="p-4 bg-gray-50 rounded-lg">
                    <pre className="text-sm overflow-auto">
                      {JSON.stringify(result, null, 2)}
                    </pre>
                  </div>
                </div>
              </>
            )}
          </CardContent>
        </Card>
      )}

      {/* Instructions */}
      <Alert>
        <Edit className="h-4 w-4" />
        <AlertDescription>
          <strong>Expected Behavior:</strong>
          <ol className="list-decimal list-inside mt-2 space-y-1">
            <li><strong>Cloudflare:</strong> DNS record untuk subdomain lama di-update/replace dengan yang baru</li>
            <li><strong>Vercel:</strong> Domain lama dihapus dari project, domain baru ditambahkan</li>
            <li><strong>Result:</strong> Hanya domain baru yang aktif, domain lama tidak bisa diakses</li>
            <li><strong>Vercel Dashboard:</strong> Tidak ada duplikasi domain, hanya domain baru yang terlihat</li>
          </ol>
        </AlertDescription>
      </Alert>
    </div>
  )
}

# User Table Migration Strategy - Multi-Role System

## 🚨 **JANGAN HAPUS Tabel User!**

### **📋 Alasan Tabel User Harus Dipertahankan:**

1. **Authentication Base** - Masih digunakan untuk login dan session management
2. **Foreign Key References** - Tabel baru (buyers, store_owners, affiliates) reference ke User
3. **Admin Management** - Admin dashboard masih menggunakan User untuk user management
4. **Backward Compatibility** - Existing code masih bergantung pada User table

## 🏗️ **Strategi Migration yang Benar**

### **✅ Hybrid Approach: User + Role Tables**

```
User Table (Base Identity)
├── id, email, name, password
├── role (primary role: ADMIN, TENANT, STORE_OWNER, USER)
├── tenantId, createdAt, updatedAt
└── Basic user information

Role-Specific Tables (Capabilities)
├── buyers (user_id → User.id)
├── store_owners (user_id → User.id)
└── affiliates (user_id → User.id)

View: user_roles (Unified Query)
└── Combines User + all role tables
```

### **🔄 Migration Steps:**

#### **Phase 1: ✅ COMPLETED**
- [x] Create role-specific tables (buyers, store_owners, affiliates)
- [x] Create user_roles view
- [x] Populate test data with multi-role combinations

#### **Phase 2: 🔄 IN PROGRESS**
- [x] Update `/api/tenant/buyers` to use user_roles view
- [x] Update `/api/tenant/users` to use user_roles view
- [ ] Update tenant dashboard to show multi-role capabilities
- [ ] Update store dashboard to show buyer capabilities

#### **Phase 3: 📋 TODO**
- [ ] Update authentication to support role progression
- [ ] Update admin dashboard to show multi-role users
- [ ] Add role management UI (promote buyer to store owner, etc.)
- [ ] Add business logic for role transitions

## 📊 **Current Data Structure**

### **User Table (Base Identity):**
```sql
User {
  id: TEXT PRIMARY KEY
  email: TEXT
  name: TEXT
  role: ENUM (ADMIN, TENANT, STORE_OWNER, USER)
  tenantId: TEXT
  storeId: TEXT
  createdAt: TIMESTAMP
  updatedAt: TIMESTAMP
}
```

### **Role Tables (Capabilities):**
```sql
buyers {
  user_id → User.id
  tenant_id → tenants.id
  is_buyer: BOOLEAN
  is_store_owner: BOOLEAN
  is_affiliate: BOOLEAN
  buyer_level: VARCHAR (bronze, silver, gold, platinum)
  total_orders: INTEGER
  total_spent: DECIMAL
}

store_owners {
  user_id → User.id
  tenant_id → tenants.id
  store_id → stores.id
  ownership_percentage: DECIMAL
  role: VARCHAR (owner, manager, staff)
}

affiliates {
  user_id → User.id
  tenant_id → tenants.id
  affiliate_code: VARCHAR
  commission_rate: DECIMAL
  total_commission: DECIMAL
  affiliate_level: VARCHAR
}
```

## 🔍 **API Endpoints Updated**

### **Before (Single Role):**
```javascript
// /api/tenant/users?role=USER
{
  "users": [
    {
      "id": "user1",
      "email": "<EMAIL>",
      "role": "USER",
      "tenantId": "tenant1"
    }
  ]
}
```

### **After (Multi-Role):**
```javascript
// /api/tenant/buyers?role=buyer-store-affiliate
{
  "buyers": [
    {
      "id": "user1",
      "email": "<EMAIL>",
      "primaryRole": "USER",
      "capabilities": {
        "isBuyer": true,
        "isStoreOwner": true,
        "isAffiliate": true
      },
      "buyerData": {
        "level": "platinum",
        "totalOrders": 20,
        "totalSpent": "5000000.00"
      },
      "storeData": {
        "storeId": "store123"
      },
      "affiliateData": {
        "code": "AFF123",
        "level": "platinum",
        "totalCommission": "500000.00"
      }
    }
  ]
}
```

## 🎯 **Business Logic Support**

### **Role Progression:**
1. **New User** → User table (role: USER)
2. **Becomes Buyer** → buyers table (is_buyer: true)
3. **Opens Store** → store_owners table + buyers.is_store_owner = true
4. **Joins Affiliate** → affiliates table + buyers.is_affiliate = true

### **Query Examples:**

#### **Get All Users in Tenant:**
```sql
SELECT * FROM user_roles WHERE tenant_id = 'tenant123';
```

#### **Get Buyers Only:**
```sql
SELECT * FROM user_roles 
WHERE tenant_id = 'tenant123' 
AND is_buyer = true 
AND is_store_owner = false 
AND is_affiliate = false;
```

#### **Get Multi-Role Users:**
```sql
SELECT * FROM user_roles 
WHERE tenant_id = 'tenant123' 
AND is_buyer = true 
AND is_store_owner = true 
AND is_affiliate = true;
```

## ✅ **Benefits of This Approach**

### **1. Backward Compatibility**
- Existing authentication code still works
- Admin dashboard still functional
- No breaking changes to current features

### **2. Forward Compatibility**
- Support for complex multi-role scenarios
- Easy to add new roles (influencer, wholesaler, etc.)
- Granular permissions per role

### **3. Data Integrity**
- User table remains single source of truth for identity
- Role tables handle specific capabilities
- Foreign key constraints ensure consistency

### **4. Performance**
- user_roles view provides efficient querying
- Indexed properly for fast lookups
- Minimal impact on existing queries

## 🚀 **Next Steps**

### **Immediate (This Week):**
1. ✅ Fix API endpoints to use user_roles view
2. 🔄 Update dashboard components to show multi-role data
3. 📋 Test all existing functionality still works

### **Short Term (Next 2 Weeks):**
1. Add role management UI in admin dashboard
2. Implement role progression logic (buyer → store → affiliate)
3. Update authentication to handle multi-role scenarios

### **Long Term (Next Month):**
1. Add business logic for role-based features
2. Implement role-based pricing and commissions
3. Add analytics for multi-role users
4. Performance optimization for large datasets

## 🎉 **Conclusion**

**Tabel User TIDAK BOLEH DIHAPUS** karena masih menjadi foundation untuk:
- Authentication & Authorization
- Admin Management
- Foreign Key References
- Backward Compatibility

**Strategi yang benar** adalah menggunakan **Hybrid Approach** dengan:
- User table sebagai base identity
- Role tables untuk specific capabilities
- View untuk unified querying
- Gradual migration tanpa breaking changes

import { NextResponse } from 'next/server'

export async function GET() {
  try {
    const apiToken = process.env.CLOUDFLARE_API_TOKEN
    const zoneId = process.env.CLOUDFLARE_ZONE_ID

    if (!apiToken || !zoneId) {
      return NextResponse.json({
        success: false,
        error: 'Missing Cloudflare credentials',
        details: {
          hasApiToken: !!apiToken,
          hasZoneId: !!zoneId
        }
      }, { status: 400 })
    }

    console.log('🔥 CLOUDFLARE TEST: Testing API connection...')

    // Test 1: Verify API Token
    const tokenTestUrl = 'https://api.cloudflare.com/client/v4/user/tokens/verify'
    const tokenResponse = await fetch(tokenTestUrl, {
      headers: {
        'Authorization': `Bearer ${apiToken}`,
        'Content-Type': 'application/json',
      },
    })

    const tokenResult = await tokenResponse.json()
    console.log('🔥 CLOUDFLARE TEST: Token verification:', tokenResult)

    // Test 2: Get Zone Info
    const zoneTestUrl = `https://api.cloudflare.com/client/v4/zones/${zoneId}`
    const zoneResponse = await fetch(zoneTestUrl, {
      headers: {
        'Authorization': `Bearer ${apiToken}`,
        'Content-Type': 'application/json',
      },
    })

    const zoneResult = await zoneResponse.json()
    console.log('🔥 CLOUDFLARE TEST: Zone info:', zoneResult)

    // Test 3: List DNS Records
    const dnsTestUrl = `https://api.cloudflare.com/client/v4/zones/${zoneId}/dns_records?type=A&per_page=5`
    const dnsResponse = await fetch(dnsTestUrl, {
      headers: {
        'Authorization': `Bearer ${apiToken}`,
        'Content-Type': 'application/json',
      },
    })

    const dnsResult = await dnsResponse.json()
    console.log('🔥 CLOUDFLARE TEST: DNS records:', dnsResult)

    return NextResponse.json({
      success: true,
      tests: {
        tokenVerification: {
          success: tokenResponse.ok && tokenResult.success,
          status: tokenResponse.status,
          result: tokenResult
        },
        zoneAccess: {
          success: zoneResponse.ok && zoneResult.success,
          status: zoneResponse.status,
          result: zoneResult
        },
        dnsAccess: {
          success: dnsResponse.ok && dnsResult.success,
          status: dnsResponse.status,
          result: dnsResult
        }
      },
      summary: {
        allTestsPassed: tokenResponse.ok && tokenResult.success && 
                       zoneResponse.ok && zoneResult.success && 
                       dnsResponse.ok && dnsResult.success
      }
    })

  } catch (error: any) {
    console.error('🔥 CLOUDFLARE TEST: Error:', error)
    return NextResponse.json({
      success: false,
      error: error.message,
      stack: error.stack
    }, { status: 500 })
  }
}

export async function POST(request: Request) {
  try {
    const { testSubdomain } = await request.json()
    
    if (!testSubdomain) {
      return NextResponse.json({
        success: false,
        error: 'testSubdomain is required'
      }, { status: 400 })
    }

    const apiToken = process.env.CLOUDFLARE_API_TOKEN
    const zoneId = process.env.CLOUDFLARE_ZONE_ID
    const targetIP = process.env.VERCEL_IP || '***********'

    if (!apiToken || !zoneId) {
      return NextResponse.json({
        success: false,
        error: 'Missing Cloudflare credentials'
      }, { status: 400 })
    }

    console.log('🔥 CLOUDFLARE TEST: Creating test DNS record:', { testSubdomain, targetIP })

    // Create DNS record directly via API
    const createUrl = `https://api.cloudflare.com/client/v4/zones/${zoneId}/dns_records`
    const recordData = {
      type: 'A',
      name: testSubdomain,
      content: targetIP,
      ttl: 1,
      proxied: true
    }

    console.log('🔥 CLOUDFLARE TEST: Record data:', recordData)

    const createResponse = await fetch(createUrl, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiToken}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(recordData)
    })

    const createResult = await createResponse.json()
    console.log('🔥 CLOUDFLARE TEST: Create response:', {
      status: createResponse.status,
      ok: createResponse.ok,
      result: createResult
    })

    return NextResponse.json({
      success: createResponse.ok && createResult.success,
      request: {
        url: createUrl,
        method: 'POST',
        data: recordData
      },
      response: {
        status: createResponse.status,
        ok: createResponse.ok,
        result: createResult
      },
      message: createResponse.ok && createResult.success ? 
        'DNS record created successfully' : 
        `Failed to create DNS record: ${createResult.errors?.[0]?.message || 'Unknown error'}`
    })

  } catch (error: any) {
    console.error('🔥 CLOUDFLARE TEST: Error:', error)
    return NextResponse.json({
      success: false,
      error: error.message,
      stack: error.stack
    }, { status: 500 })
  }
}

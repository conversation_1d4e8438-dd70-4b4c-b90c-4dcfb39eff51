import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  try {
    // Check environment variables (without exposing sensitive values)
    const envCheck = {
      CLOUDFLARE_API_TOKEN: {
        exists: !!process.env.CLOUDFLARE_API_TOKEN,
        length: process.env.CLOUDFLARE_API_TOKEN?.length || 0,
        preview: process.env.CLOUDFLARE_API_TOKEN ?
          process.env.CLOUDFLARE_API_TOKEN.substring(0, 10) + '...' + process.env.CLOUDFLARE_API_TOKEN.slice(-4) : 'NOT_SET',
        startsWithCorrect: process.env.CLOUDFLARE_API_TOKEN?.startsWith('7bfG4kr6') || false
      },
      CLOUDFLARE_ZONE_ID: {
        exists: !!process.env.CLOUDFLARE_ZONE_ID,
        length: process.env.CLOUDFLARE_ZONE_ID?.length || 0,
        preview: process.env.CLOUDFLARE_ZONE_ID ?
          process.env.CLOUDFLARE_ZONE_ID.substring(0, 8) + '...' + process.env.CLOUDFLARE_ZONE_ID.slice(-4) : 'NOT_SET',
        startsWithCorrect: process.env.CLOUDFLARE_ZONE_ID?.startsWith('fdba5550') || false
      },
      VERCEL_IP: {
        exists: !!process.env.VERCEL_IP,
        value: process.env.VERCEL_IP || 'NOT_SET'
      },
      NODE_ENV: process.env.NODE_ENV,
      VERCEL_ENV: process.env.VERCEL_ENV,
      VERCEL_URL: process.env.VERCEL_URL
    }

    return NextResponse.json({
      success: true,
      environment: envCheck,
      timestamp: new Date().toISOString(),
      deploymentId: Math.random().toString(36).substring(7)
    })

  } catch (error) {
    console.error('❌ Error checking environment:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}

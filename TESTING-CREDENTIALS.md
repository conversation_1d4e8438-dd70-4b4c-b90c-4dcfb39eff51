# Testing Credentials - SaaS Multi-Tenant System

## 🔐 Login Credentials untuk Testing

### **Admin (Super Admin)**
- **URL**: `http://localhost:3000/admin/login`
- **Email**: `<EMAIL>`
- **Password**: `admin123`
- **Akses**: Semua data di platform

---

## **TENANT 1** (Test Store 1)
**Tenant ID**: `96111d56-b82d-43e7-9926-8f0530dc6063`

### 1. **Tenant Owner**
- **URL**: `http://localhost:3000/app/login`
- **Email**: `<EMAIL>`
- **Password**: `user123`
- **Akses**: Semua store dan buyer dalam Tenant 1

### 2. **Store Owners dalam Tenant 1**

#### Store 1 Owner
- **URL**: `http://localhost:3000/app/login`
- **Email**: `<EMAIL>`
- **Password**: `user123`
- **Store**: nama5 (ID: 179700a1-d6c1-40eb-9fd1-317754ba3c57)
- **Akses**: Data store sendiri + buyer dalam Tenant 1

#### Store 2 Owner
- **URL**: `http://localhost:3000/app/login`
- **Email**: `<EMAIL>`
- **Password**: `user123`
- **Store**: tes (ID: a70cae23-39af-4ff5-b1d8-b227a28585f8)
- **Akses**: Data store sendiri + buyer dalam Tenant 1

### 3. **Buyers dalam Tenant 1**

#### Buyer 1
- **URL**: `http://localhost:3000/app/login`
- **Email**: `<EMAIL>`
- **Password**: `user123`
- **Akses**: Data pribadi + produk dari semua store dalam Tenant 1

#### Buyer 2
- **URL**: `http://localhost:3000/app/login`
- **Email**: `<EMAIL>`
- **Password**: `user123`
- **Akses**: Data pribadi + produk dari semua store dalam Tenant 1

---

## **TENANT 2** (Test Store 2)
**Tenant ID**: `9bb06506-e2a9-4f8d-876b-a1a10ef5f240`

### 1. **Tenant Owner**
- **URL**: `http://localhost:3000/app/login`
- **Email**: `<EMAIL>`
- **Password**: `user123`
- **Akses**: Semua store dan buyer dalam Tenant 2

### 2. **Store Owners dalam Tenant 2**

#### Store 3 Owner
- **URL**: `http://localhost:3000/app/login`
- **Email**: `<EMAIL>`
- **Password**: `user123`
- **Store**: nama3 (ID: 62a64062-f017-4cb7-a608-575cab102560)
- **Akses**: Data store sendiri + buyer dalam Tenant 2

#### Store 4 Owner
- **URL**: `http://localhost:3000/app/login`
- **Email**: `<EMAIL>`
- **Password**: `user123`
- **Store**: nama6 (ID: 07f4637f-d360-4f29-9b83-d326e60f6941)
- **Akses**: Data store sendiri + buyer dalam Tenant 2

### 3. **Buyers dalam Tenant 2**

#### Buyer 3
- **URL**: `http://localhost:3000/app/login`
- **Email**: `<EMAIL>`
- **Password**: `user123`
- **Akses**: Data pribadi + produk dari semua store dalam Tenant 2

#### Buyer 4
- **URL**: `http://localhost:3000/app/login`
- **Email**: `<EMAIL>`
- **Password**: `user123`
- **Akses**: Data pribadi + produk dari semua store dalam Tenant 2

---

## 🧪 **Skenario Testing Data Isolation**

### **Test 1: Tenant Isolation**
1. **Login sebagai Tenant 1** (`<EMAIL>`)
   - Buka `/tenant/dashboard/stores`
   - **Harus melihat**: Store "nama5" dan "tes" (2 stores)
   - **Tidak boleh melihat**: Store "nama3" dan "nama6" (dari Tenant 2)

2. **Login sebagai Tenant 2** (`<EMAIL>`)
   - Buka `/tenant/dashboard/stores`
   - **Harus melihat**: Store "nama3" dan "nama6" (2 stores)
   - **Tidak boleh melihat**: Store "nama5" dan "tes" (dari Tenant 1)

### **Test 2: Store Isolation**
1. **Login sebagai Store 1 Owner** (`<EMAIL>`)
   - Buka `/store/dashboard/buyers`
   - **Harus melihat**: buyer1@sellzio.<NAME_EMAIL> (buyers dalam Tenant 1)
   - **Tidak boleh melihat**: buyer3@sellzio.<NAME_EMAIL> (buyers dari Tenant 2)

2. **Login sebagai Store 3 Owner** (`<EMAIL>`)
   - Buka `/store/dashboard/buyers`
   - **Harus melihat**: buyer3@sellzio.<NAME_EMAIL> (buyers dalam Tenant 2)
   - **Tidak boleh melihat**: buyer1@sellzio.<NAME_EMAIL> (buyers dari Tenant 1)

### **Test 3: Buyer Isolation**
1. **Login sebagai Buyer 1** (`<EMAIL>`)
   - **Harus melihat**: Produk dari store "nama5" dan "tes" (dalam Tenant 1)
   - **Tidak boleh melihat**: Produk dari store "nama3" dan "nama6" (dari Tenant 2)

2. **Login sebagai Buyer 3** (`<EMAIL>`)
   - **Harus melihat**: Produk dari store "nama3" dan "nama6" (dalam Tenant 2)
   - **Tidak boleh melihat**: Produk dari store "nama5" dan "tes" (dari Tenant 1)

---

## 📊 **Expected Results**

### **Tenant Dashboard**
- **Tenant 1**: Melihat 2 stores, 2 buyers
- **Tenant 2**: Melihat 2 stores, 2 buyers
- **Cross-tenant data**: TIDAK TERLIHAT

### **Store Dashboard**
- **Store dalam Tenant 1**: Melihat 2 buyers dari Tenant 1
- **Store dalam Tenant 2**: Melihat 2 buyers dari Tenant 2
- **Cross-tenant buyers**: TIDAK TERLIHAT
- **Data store lain**: TIDAK TERLIHAT

### **API Endpoints**
- `/api/tenant/stores?tenantId=96111d56...` → 2 stores (Tenant 1)
- `/api/tenant/stores?tenantId=9bb06506...` → 2 stores (Tenant 2)
- `/api/tenant/users?tenantId=96111d56...&role=USER` → 2 buyers (Tenant 1)
- `/api/tenant/users?tenantId=9bb06506...&role=USER` → 2 buyers (Tenant 2)

---

## ✅ **Validation Checklist**

- [ ] Tenant 1 hanya melihat data dalam tenant mereka
- [ ] Tenant 2 hanya melihat data dalam tenant mereka
- [ ] Store owners hanya melihat buyers dalam tenant yang sama
- [ ] Store owners tidak melihat data store lain
- [ ] Buyers hanya melihat produk dalam tenant mereka
- [ ] API endpoints mengembalikan data yang benar berdasarkan tenant
- [ ] Tidak ada data cross-tenant yang bocor
- [ ] Dashboard menampilkan jumlah data yang benar

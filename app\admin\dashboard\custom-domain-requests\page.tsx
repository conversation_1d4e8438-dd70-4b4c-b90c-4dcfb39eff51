"use client"

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { useToast } from '@/hooks/use-toast'
import { 
  Globe, 
  CheckCircle, 
  XCircle,
  Clock,
  AlertTriangle,
  ExternalLink,
  User,
  Calendar
} from 'lucide-react'

interface DomainRequest {
  id: string
  domain: string
  tenant_id: string
  reason: string
  status: 'pending' | 'approved' | 'rejected' | 'configuration_failed'
  admin_notes?: string
  requested_at: string
  processed_at?: string
  tenants: {
    id: string
    name: string
    subdomain: string
    domain?: string
  }
}

export default function CustomDomainRequestsPage() {
  const [requests, setRequests] = useState<DomainRequest[]>([])
  const [loading, setLoading] = useState(true)
  const [filter, setFilter] = useState<string>('pending')
  const [processingId, setProcessingId] = useState<string | null>(null)
  const [adminNotes, setAdminNotes] = useState<string>('')
  const [selectedRequest, setSelectedRequest] = useState<DomainRequest | null>(null)
  const { toast } = useToast()

  const fetchRequests = async () => {
    try {
      const response = await fetch(`/api/admin/custom-domain-requests?status=${filter}`)
      const data = await response.json()
      
      if (data.success) {
        setRequests(data.requests)
      } else {
        throw new Error(data.error)
      }
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to fetch domain requests",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchRequests()
  }, [filter])

  const handleApproveReject = async (requestId: string, action: 'approve' | 'reject') => {
    if (!adminNotes.trim() && action === 'reject') {
      toast({
        title: "Error",
        description: "Please provide admin notes for rejection",
        variant: "destructive"
      })
      return
    }

    setProcessingId(requestId)
    try {
      const response = await fetch('/api/admin/custom-domain-requests', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          requestId,
          action,
          adminNotes: adminNotes.trim()
        })
      })

      const data = await response.json()
      
      if (data.success) {
        toast({
          title: "Success",
          description: data.message,
        })
        setAdminNotes('')
        setSelectedRequest(null)
        fetchRequests() // Refresh list
      } else {
        throw new Error(data.error)
      }
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || `Failed to ${action} domain request`,
        variant: "destructive"
      })
    } finally {
      setProcessingId(null)
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return <Badge variant="outline" className="text-yellow-600"><Clock className="h-3 w-3 mr-1" />Pending</Badge>
      case 'approved':
        return <Badge variant="default" className="text-green-600"><CheckCircle className="h-3 w-3 mr-1" />Approved</Badge>
      case 'rejected':
        return <Badge variant="destructive"><XCircle className="h-3 w-3 mr-1" />Rejected</Badge>
      case 'configuration_failed':
        return <Badge variant="destructive"><AlertTriangle className="h-3 w-3 mr-1" />Config Failed</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('id-ID', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Page Header */}
      <div className="space-y-2">
        <h1 className="text-3xl font-bold tracking-tight flex items-center gap-2">
          <Globe className="h-8 w-8" />
          Custom Domain Requests
        </h1>
        <p className="text-muted-foreground">
          Review and approve custom domain requests from tenants
        </p>
      </div>

      {/* Filter Tabs */}
      <div className="flex gap-2">
        {['pending', 'approved', 'rejected', 'all'].map((status) => (
          <Button
            key={status}
            variant={filter === status ? "default" : "outline"}
            onClick={() => setFilter(status)}
            className="capitalize"
          >
            {status}
          </Button>
        ))}
      </div>

      {/* Requests List */}
      <div className="grid gap-4">
        {loading ? (
          <Card>
            <CardContent className="p-6">
              <div className="text-center">Loading domain requests...</div>
            </CardContent>
          </Card>
        ) : requests.length === 0 ? (
          <Card>
            <CardContent className="p-6">
              <div className="text-center text-muted-foreground">
                No domain requests found for status: {filter}
              </div>
            </CardContent>
          </Card>
        ) : (
          requests.map((request) => (
            <Card key={request.id}>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <CardTitle className="flex items-center gap-2">
                      <Globe className="h-5 w-5" />
                      {request.domain}
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => window.open(`https://${request.domain}`, '_blank')}
                      >
                        <ExternalLink className="h-4 w-4" />
                      </Button>
                    </CardTitle>
                    <CardDescription className="flex items-center gap-4">
                      <span className="flex items-center gap-1">
                        <User className="h-4 w-4" />
                        {request.tenants.name} ({request.tenants.subdomain})
                      </span>
                      <span className="flex items-center gap-1">
                        <Calendar className="h-4 w-4" />
                        {formatDate(request.requested_at)}
                      </span>
                    </CardDescription>
                  </div>
                  {getStatusBadge(request.status)}
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Request Details */}
                <div className="space-y-2">
                  <Label className="text-sm font-medium">Reason:</Label>
                  <p className="text-sm text-muted-foreground">{request.reason}</p>
                </div>

                {/* Admin Notes (if any) */}
                {request.admin_notes && (
                  <div className="space-y-2">
                    <Label className="text-sm font-medium">Admin Notes:</Label>
                    <p className="text-sm text-muted-foreground">{request.admin_notes}</p>
                  </div>
                )}

                {/* Actions for Pending Requests */}
                {request.status === 'pending' && (
                  <div className="space-y-4 pt-4 border-t">
                    <div className="space-y-2">
                      <Label htmlFor={`notes-${request.id}`}>Admin Notes:</Label>
                      <Textarea
                        id={`notes-${request.id}`}
                        value={selectedRequest?.id === request.id ? adminNotes : ''}
                        onChange={(e) => {
                          setAdminNotes(e.target.value)
                          setSelectedRequest(request)
                        }}
                        placeholder="Add notes for this request..."
                        className="min-h-[80px]"
                      />
                    </div>
                    
                    <div className="flex gap-2">
                      <Button
                        onClick={() => handleApproveReject(request.id, 'approve')}
                        disabled={processingId === request.id}
                        className="bg-green-600 hover:bg-green-700"
                      >
                        <CheckCircle className="h-4 w-4 mr-2" />
                        {processingId === request.id ? 'Processing...' : 'Approve'}
                      </Button>
                      
                      <Button
                        variant="destructive"
                        onClick={() => handleApproveReject(request.id, 'reject')}
                        disabled={processingId === request.id}
                      >
                        <XCircle className="h-4 w-4 mr-2" />
                        {processingId === request.id ? 'Processing...' : 'Reject'}
                      </Button>
                    </div>
                  </div>
                )}

                {/* Processed Info */}
                {request.processed_at && (
                  <div className="text-sm text-muted-foreground pt-2 border-t">
                    Processed: {formatDate(request.processed_at)}
                  </div>
                )}
              </CardContent>
            </Card>
          ))
        )}
      </div>
    </div>
  )
}

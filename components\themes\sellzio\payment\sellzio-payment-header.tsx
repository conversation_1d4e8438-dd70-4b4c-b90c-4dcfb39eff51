"use client"

import React from 'react'
import { ArrowLeft } from 'lucide-react'

interface SellzioPaymentHeaderProps {
  onBack: () => void
}

export const SellzioPaymentHeader: React.FC<SellzioPaymentHeaderProps> = ({
  onBack
}) => {
  return (
    <header className="sellzio-payment-header">
      <div className="sellzio-payment-header-inner">
        <button 
          className="sellzio-back-btn"
          onClick={onBack}
          aria-label="Kembali"
        >
          <ArrowLeft size={20} />
        </button>
        <h1 className="sellzio-payment-title">Pembayaran</h1>
        <div className="sellzio-header-spacer"></div>
      </div>
      
      <style jsx>{`
        .sellzio-payment-header {
          position: sticky;
          top: 0;
          z-index: 100;
          background: white;
          border-bottom: 1px solid #e5e5e5;
          box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .sellzio-payment-header-inner {
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 12px 16px;
          max-width: 100%;
          margin: 0 auto;
        }

        .sellzio-back-btn {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 40px;
          height: 40px;
          background: none;
          border: none;
          border-radius: 50%;
          cursor: pointer;
          transition: background-color 0.2s ease;
          color: #333;
        }

        .sellzio-back-btn:hover {
          background-color: #f5f5f5;
        }

        .sellzio-back-btn:active {
          background-color: #e5e5e5;
        }

        .sellzio-payment-title {
          font-size: 18px;
          font-weight: 600;
          color: #333;
          margin: 0;
          text-align: center;
          flex: 1;
        }

        .sellzio-header-spacer {
          width: 40px;
          height: 40px;
        }

        @media (max-width: 768px) {
          .sellzio-payment-header-inner {
            padding: 10px 12px;
          }

          .sellzio-payment-title {
            font-size: 16px;
          }

          .sellzio-back-btn {
            width: 36px;
            height: 36px;
          }

          .sellzio-header-spacer {
            width: 36px;
            height: 36px;
          }
        }
      `}</style>
    </header>
  )
}

"use client"

import React from 'react'
import type { PaymentMethod } from '@/lib/models/payment'

interface SellzioPaymentFooterProps {
  total: number
  selectedMethod: PaymentMethod | null
  onProcessPayment: () => void
  isProcessing?: boolean
}

export const SellzioPaymentFooter: React.FC<SellzioPaymentFooterProps> = ({
  total,
  selectedMethod,
  onProcessPayment,
  isProcessing = false
}) => {
  const formatPrice = (price: number) => {
    return `Rp${price.toLocaleString('id-ID')}`
  }

  return (
    <footer className="sellzio-payment-footer">
      <div className="sellzio-payment-footer-inner">
        <div className="sellzio-payment-total">
          <span className="sellzio-total-label">Total Pembayaran</span>
          <span className="sellzio-total-amount">{formatPrice(total)}</span>
        </div>
        <button
          className="sellzio-payment-btn"
          onClick={onProcessPayment}
          disabled={!selectedMethod || isProcessing}
        >
          {isProcessing ? (
            <>
              <div className="sellzio-spinner"></div>
              Memproses...
            </>
          ) : (
            'Bayar Sekarang'
          )}
        </button>
      </div>

      <style jsx>{`
        .sellzio-payment-footer {
          position: fixed;
          bottom: 0;
          left: 0;
          right: 0;
          background: white;
          border-top: 1px solid #e5e5e5;
          box-shadow: 0 -2px 8px rgba(0,0,0,0.1);
          z-index: 100;
        }

        .sellzio-payment-footer-inner {
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 16px;
          max-width: 100%;
          margin: 0 auto;
        }

        .sellzio-payment-total {
          display: flex;
          flex-direction: column;
          align-items: flex-start;
        }

        .sellzio-total-label {
          font-size: 12px;
          color: #666;
          margin-bottom: 2px;
        }

        .sellzio-total-amount {
          font-size: 18px;
          font-weight: 700;
          color: #ee4d2d;
        }

        .sellzio-payment-btn {
          background: #ee4d2d;
          color: white;
          border: none;
          border-radius: 8px;
          padding: 12px 24px;
          font-size: 16px;
          font-weight: 600;
          cursor: pointer;
          transition: all 0.2s ease;
          display: flex;
          align-items: center;
          justify-content: center;
          min-width: 140px;
          height: 48px;
        }

        .sellzio-payment-btn:hover:not(:disabled) {
          background: #d73527;
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(238, 77, 45, 0.3);
        }

        .sellzio-payment-btn:active:not(:disabled) {
          transform: translateY(0);
          box-shadow: 0 2px 6px rgba(238, 77, 45, 0.3);
        }

        .sellzio-payment-btn:disabled {
          background: #ccc;
          cursor: not-allowed;
          transform: none;
          box-shadow: none;
        }

        .sellzio-spinner {
          width: 16px;
          height: 16px;
          border: 2px solid transparent;
          border-top: 2px solid white;
          border-radius: 50%;
          animation: spin 1s linear infinite;
          margin-right: 8px;
        }

        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }

        @media (max-width: 768px) {
          .sellzio-payment-footer-inner {
            padding: 12px;
          }

          .sellzio-total-amount {
            font-size: 16px;
          }

          .sellzio-payment-btn {
            padding: 10px 20px;
            font-size: 14px;
            min-width: 120px;
            height: 44px;
          }
        }

        @media (max-width: 480px) {
          .sellzio-payment-footer-inner {
            flex-direction: column;
            align-items: stretch;
            gap: 12px;
          }

          .sellzio-payment-total {
            align-items: center;
            text-align: center;
          }

          .sellzio-payment-btn {
            width: 100%;
          }
        }
      `}</style>
    </footer>
  )
}

<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Keranjang Belanja - Shopee Style</title>
	<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        /* Reset CSS */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: Arial, sans-serif;
        }

        html, body {
            height: 100%;
            background-color: #f5f5f5;
            overflow: hidden; /* Mencegah scroll pada seluruh halaman */
        }

        body {
            color: #333;
            position: relative;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        /* Container utama */
        .page-container {
            max-width: 768px;
            width: 100%;
            margin: 0 auto;
            background-color: #f5f5f5;
            display: flex;
            flex-direction: column;
            flex: 1;
        }

        /* Header - Menggunakan struktur kode 1 tapi tampilan kode 2 */
        .header {
            position: sticky;
            top: 0;
            width: 100%;
            background-color: #fff;
            padding: 16px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            z-index: 10;
            width: 100%;
            max-width: 768px;
            margin: 0 auto;
        }

        .header-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }

        .header h1 {
            font-size: 18px;
            color: #333;
            display: flex;
            align-items: center;
        }
        
        .back-arrow {
           margin-right: 10px;
    color: #ee4d2d; /* Sesuaikan warna sesuai kebutuhan */
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    vertical-align: middle;
}

.back-arrow i {
    font-size: 20px;
    transform: scaleX(1.3);
}

        .header .edit-btn {
            color: #ee4d2d;
            background: none;
            border: none;
            font-size: 14px;
            cursor: pointer;
        }

        /* Cart Section - Perbaikan untuk tampilan desktop dan mobile */
        .cart-section {
            margin: 0;
            background-color: #fff;
            border-radius: 3px 3px 0 0; /* Mengubah radius di sisi bawah */
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            width: 100%;
            overflow-y: auto; /* Tambahkan scroll ke daftar produk */
            overflow-x: hidden; /* Mencegah scroll horizontal */
            flex: 1; /* Akan mengisi ruang yang tersisa */
            margin-bottom: 0; /* Menghilangkan margin bottom */
        }

        /* Mobile: tampilkan secara default tepat 3 produk */
        @media (max-width: 768px) {
            .cart-section {
                /* Menyesuaikan tinggi agar menampilkan tepat 3 produk */ 
                height: calc(109px * 3 + 130px); /* 3 produk (109px) + header shop pertama (44px) */
                max-height: calc(109px * 3 + 130px);
                overflow-y: auto;
            }
            
            html, body {
                overflow: hidden;
            }
            
            .page-container {
                overflow: hidden;
            }
        }

        /* Desktop: tampilkan semua produk */
        @media (min-width: 769px) {
            .cart-section {
                max-height: none;
                height: auto;
                overflow-y: visible;
            }
            
            html, body {
                overflow: auto;
            }
            
            .page-container {
                overflow: visible;
            }
        }

        /* Cart Item */
        .cart-item {
            display: flex;
            padding: 12px;
            border-bottom: 1px solid #f5f5f5;
            height: 109px; /* Fixed height untuk konsistensi */
        }

        /* Shop header height */
        .shop-header {
            height: 44px; /* Menentukan tinggi header toko */
        }

        .item-checkbox {
            margin-right: 10px;
            align-self: flex-start;
            margin-top: 30px;
        }

        .item-checkbox input {
            width: 18px;
            height: 18px;
            cursor: pointer;
            accent-color: #ee4d2d;
        }

        .item-image {
            width: 80px;
            height: 80px;
            min-width: 80px; /* Tambahkan ini untuk mencegah gambar menyusut */
            margin-right: 0px;
            background-color: #f5f5f5;
            border: 1px solid #e0e0e0;
            overflow: hidden;
            display: flex; /* Tambahkan ini */
            align-items: center; /* Tambahkan ini */
            justify-content: center; /* Tambahkan ini */
        }

        .item-image img {
            width: 100%;
            height: 100%;
            object-fit: cover; /* Mempertahankan ini */
        }

        .item-details {
            flex: 1;
            overflow: hidden; /* Untuk mencegah overflow ke samping */
            min-width: 0; /* Penting untuk membuat flex child bisa menyusut */
        }

        .item-name {
            font-size: 14px;
            margin: 6px 0px 0px 10px;
            line-height: 1.4;
            white-space: nowrap; /* Memaksa teks tetap dalam 1 baris */
            overflow: hidden; /* Sembunyikan teks yang melebihi container */
            text-overflow: ellipsis; /* Tambahkan elipsis (...) untuk teks terpotong */
            max-width: 100%; /* Pastikan tidak melebihi container */
        }

        /* Mobile: nama produk dibatasi 1 baris dan max 4 kata */
        @media (max-width: 768px) {
            .item-name {
                -webkit-line-clamp: 1;
                white-space: nowrap;
                text-overflow: ellipsis;
                max-width: 100%;
                overflow: hidden;
            }
        }

        .item-variant {
            font-size: 12px;
            color: #888;
            margin: 3px 0px 0px 10px;
            cursor: pointer; /* Menandakan bisa diklik */
            display: inline-flex; /* Mengubah dari inline-block ke inline-flex */
            align-items: center; /* Vertical centering */
            justify-content: space-between; /* Ruang antara teks dan ikon */
            border: 1px solid #ddd; /* Border untuk varian */
            border-radius: 3px; /* Border radius untuk varian */
            padding: 2px 6px; /* Padding untuk varian */
            position: relative; /* Untuk positioning icon panah */
            min-width: 60px; /* Minimal width agar bisa menampung ikon dengan baik */
        }

        .item-variant:after {
            content: "\25BC"; /* Unicode untuk panah bawah */
            font-size: 8px;
            margin-left: 5px;
            color: #888;
        }

        .item-variant:hover {
            border-color: #ee4d2d; /* Border merah saat hover */
            color: #ee4d2d; /* Teks merah saat hover */
        }

        .item-variant:hover:after {
            color: #ee4d2d; /* Ikon juga berubah warna saat hover */
        }

        .item-price {
            margin: 0px 0px 10px 10px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .price-section {
            display: flex;
            align-items: center;
            width: 60%; 
        }

        /* Mobile: efek terpotong pada harga coret */
        @media (max-width: 768px) {
            .price-section {
                max-width: 130px;
                overflow: hidden;
            }
            
            .original-price {
                text-decoration: line-through;
                color: #888;
                font-size: 12px;
                font-weight: normal;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                flex-shrink: 1;
                max-width: 70px;
            }
        }

        /* Desktop: tampilkan harga coret penuh */
        @media (min-width: 769px) {
            .price-section {
                max-width: none;
                overflow: visible;
            }
            
            .original-price {
                text-decoration: line-through;
                color: #888;
                font-size: 12px;
                font-weight: normal;
                white-space: nowrap;
                overflow: visible;
                text-overflow: clip;
                flex-shrink: 0;
                max-width: none;
            }
        }

        .discounted-price {
            font-weight: bold;
            font-size: 14px;
            color: #ee4d2d;
            margin-right: 5px;
            white-space: nowrap;
            flex-shrink: 0;
        }

        .item-quantity {
            display: flex;
            align-items: center;
            margin-left: auto;
            flex-shrink: 0;
        }

        .quantity-btn {
            width: 26px;
            height: 26px;
            border: 1px solid #e0e0e0;
            background-color: #fff;
            font-size: 16px;
            display: flex;
            justify-content: center;
            align-items: center;
            cursor: pointer;
        }

        .minus-btn {
            border-radius: 3px 0 0 3px;
        }

        .plus-btn {
            border-radius: 0 3px 3px 0;
        }

        .quantity-input {
            width: 40px;
            height: 26px;
            border: 1px solid #e0e0e0;
            border-left: none;
            border-right: none;
            text-align: center;
            font-size: 14px;
            outline: none;
        }

        /* Footer - Menggunakan struktur kode 1 tapi tampilan kode 2 */
        .footer {
            position: sticky;
            bottom: 0;
            width: 100%;
            background-color: #fff;
            box-shadow: 0 -2px 4px rgba(0,0,0,0.1);
            z-index: 50;
            margin-top: 0; /* Menghilangkan margin top */
            border-radius: 0; /* Menghilangkan radius agar tidak ada gap */
        }

        /* Footer content wrapper */
        .footer-content {
            max-width: 768px; /* Sama dengan page-container */
            margin: 0 auto;
            width: 100%;
        }
        
        /* Voucher Section in Footer */
        .voucher-section {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 16px;
            border-bottom: 1px solid #f5f5f5;
            height: 44px;
        }

        /* Voucher Badge Style */
			.voucher-badge {
			padding: 4px 10px;
			font-weight: bold;
			font-size: 12px;
			border-width: 1.5px;
			border-style: solid;
			border-radius: 3px;
			display: inline-flex;
    align-items: center;
    white-space: nowrap;
    height: 20px;
    box-sizing: border-box;
    margin-right: 6px;
    max-width: 150px; /* Batasi lebar maksimum */
    overflow: hidden;
    text-overflow: ellipsis;
}

        .voucher-badge-discount {
            border-color: #ee4d2d;
            color: #ee4d2d;
        }

        .voucher-badge-shipping {
            border-color: #00bfa5;
            color: #00bfa5;
        }

        /* Close button for voucher badge */
        .voucher-badge-close {
            margin-left: 6px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-size: 10px;
            border: 1px solid;
            line-height: 1;
        }

        .voucher-badge-discount .voucher-badge-close {
            border-color: #ee4d2d;
            color: #ee4d2d;
        }

        .voucher-badge-shipping .voucher-badge-close {
            border-color: #00bfa5;
            color: #00bfa5;
        }

        .voucher-badges {
            display: flex;
            flex-wrap: nowrap;
            align-items: center;
            overflow-x: auto;
            scrollbar-width: none;
            -ms-overflow-style: none;
        }

        .voucher-badges::-webkit-scrollbar {
            display: none;
        }

        .section-title {
            display: flex;
            align-items: center;
        }

        .section-title svg {
            margin-right: 8px;
            width: 22px;
            height: 22px;
            color: #ee4d2d;
        }

        /* Voucher Button */
        .voucher-button {
            background-color: #fff0f0;
            border: 1px solid #ee4d2d;
            color: #ee4d2d;
            border-radius: 4px;
            padding: 6px 12px;
            font-size: 13px;
            font-weight: 500;
            cursor: pointer;
            display: inline-block;
            text-align: center;
            white-space: nowrap;
        }

        .voucher-button:hover {
            background-color: #ffecec;
        }

        .voucher-button:disabled {
            background-color: #f5f5f5;
            border-color: #ddd;
            color: #999;
            cursor: not-allowed;
        }

        .section-action {
            margin-left: 10px;
        }
		/* Discount info */
        .discount-info {
            font-size: 12px;
            color: #ee4d2d;
            padding: 5px 16px;
            background-color: #fff9f5;
            border-bottom: 1px solid #f5f5f5;
            display: none;
            line-height: 1.6;
        }
        
        /* Checkout row */
        .checkout-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 16px;
        }

        .select-all {
            display: flex;
            align-items: center;
        }

        .select-all input {
            width: 18px;
            height: 18px;
            margin-right: 8px;
            accent-color: #ee4d2d;
        }

        .select-all label {
            font-size: 14px;
        }

        .checkout-section {
            display: flex;
            align-items: center;
        }

        /* Perbaikan untuk tampilan mobile */
        @media (max-width: 768px) {
            .checkout-section {
                flex-shrink: 0;
            }
            
            .total-price {
                margin-right: 8px; /* Lebih dekat ke tombol pada mobile */
                min-width: 0; /* Izinkan menyusut jika perlu */
                flex-shrink: 1; /* Izinkan menyusut jika perlu */
            }
            
            .checkout-btn {
                flex-shrink: 0; /* Jangan biarkan tombol menyusut */
                min-width: 90px; /* Berikan lebar minimal */
                white-space: nowrap; /* Jangan turunkan teks "Checkout (X)" */
            }

            /* Penyelarasan footer agar tidak ada gap abu-abu */
            .shop-group:last-child {
                border-bottom: 0; /* Menghilangkan border abu-abu di grup terakhir */
            }
        }

        .total-price {
            margin-right: 12px;
            text-align: right;
        }

        .total-label {
            font-size: 12px;
            color: #888;
        }

        .total-amount {
            font-size: 16px;
            font-weight: bold;
            color: #ee4d2d;
        }

        .checkout-btn {
            background-color: #ee4d2d;
            color: #fff;
            border: none;
            padding: 10px 16px;
            border-radius: 3px;
            font-size: 14px;
            font-weight: bold;
            cursor: pointer;
            opacity: 0.7;
            pointer-events: none;
        }

        .checkout-btn.active {
            opacity: 1;
            pointer-events: auto;
        }

        /* Discount label in total price */
        .discount-label {
            display: flex;
            font-size: 12px;
            color: #ee4d2d;
            align-items: center;
            cursor: pointer;
        }

        .discount-label:after {
            content: "\25BC";
            font-size: 8px;
            margin-left: 5px;
        }

        .discount-details {
            position: absolute;
            bottom: 80px;
            right: 16px;
            background-color: #fff;
            border: 1px solid #f0f0f0;
            border-radius: 4px;
            width: 220px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            padding: 10px;
            z-index: 100;
            display: none;
        }

        .detail-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 12px;
        }

        .detail-label {
            color: #666;
        }

        .detail-value {
            font-weight: bold;
        }

        .detail-discount {
            color: #ee4d2d;
        }

        /* Modal */
        .modal {
            display: none;
            position: fixed;
            z-index: 999;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            justify-content: center;
            align-items: center;
        }

        .modal-content {
            background-color: #fff;
            border-radius: 8px;
            width: 300px;
            text-align: center;
            overflow: hidden;
        }

        .modal-message {
            padding: 20px;
            font-size: 14px;
        }

        .modal-actions {
            display: flex;
            border-top: 1px solid #f5f5f5;
        }

        .modal-btn {
            flex: 1;
            padding: 12px;
            border: none;
            background: none;
            font-size: 14px;
            cursor: pointer;
        }

        .modal-btn-confirm {
            color: #ee4d2d;
            border-left: 1px solid #f5f5f5;
            font-weight: bold;
        }

        /* Ubah Voucher Button */
        .voucher-btn {
            background-color: #ee4d2d;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 3px;
            font-size: 12px;
            cursor: pointer;
        }
        
        /* Peringatan voucher */
        .voucher-warning {
            color: #ee4d2d;
            font-size: 12px;
            margin-top: 5px;
            display: none;
        }
        
        /* Discount display */
        .discount-label {
            display: block;
            line-height: 1.4;
        }
        
        /* New line after applied voucher text */
        .discount-info-break {
            display: block;
            margin-top: 3px;
        }

        /* Minify button display */
        .minus-btn:after {
            content: "-";
        }
        .plus-btn:after {
            content: "+";
        }

        /* Modal Pilih Varian */
        .variant-modal {
            display: none;
            position: fixed;
            z-index: 999;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            justify-content: center;
            align-items: center;
        }

        .variant-modal-content {
            background-color: #fff;
            border-radius: 8px;
            width: 300px;
            max-width: 90%;
            text-align: left;
            overflow: hidden;
        }

        .variant-modal-header {
            padding: 15px;
            border-bottom: 1px solid #f5f5f5;
        }

        .variant-modal-header h3 {
            font-size: 16px;
            margin: 0;
        }

        .variant-modal-body {
            padding: 15px;
            max-height: 300px;
            overflow-y: auto;
        }

        .variant-group {
            margin-bottom: 15px;
        }

        .variant-title {
            font-size: 14px;
            font-weight: bold;
            margin-bottom: 8px;
        }

        .variant-options {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }

        .variant-option {
            padding: 6px 12px;
            border: 1px solid #ddd;
            border-radius: 3px;
            font-size: 12px;
            cursor: pointer;
            background-color: #fff; /* Latar belakang putih untuk semua opsi */
            color: #333; /* Warna teks default untuk semua opsi */
        }

        .variant-option.selected {
            color: #ee4d2d; /* Warna teks oranye untuk opsi terpilih */
            border-color: #ee4d2d; /* Border oranye untuk opsi terpilih */
            background-color: #fff1f0; /* Latar belakang oranye muda untuk opsi terpilih */
        }

       

        .variant-option:hover {
            border-color: #ee4d2d;
        }

        .variant-modal-footer {
            padding: 15px;
            border-top: 1px solid #f5f5f5;
            text-align: right;
        }

        .variant-confirm-btn {
            background-color: #ee4d2d;
            color: #fff;
            border: none;
            padding: 8px 16px;
            border-radius: 3px;
            font-size: 14px;
            font-weight: bold;
            cursor: pointer;
        }

        /* Penambahan style untuk nama toko */
        .shop-header {
            display: flex;
            align-items: center;
            padding: 8px 12px;
            background-color: #fff;
            border-bottom: 1px solid #f5f5f5;
        }

        .shop-checkbox {
            margin-right: 8px;
        }

        .shop-checkbox input {
            width: 18px;
            height: 18px;
            cursor: pointer;
            accent-color: #ee4d2d;
        }

        .shop-name {
            font-size: 14px;
            font-weight: bold;
            flex: 1;
        }
		 /* Label Live dengan Sound Wave */
.live-shopee-3 {
  display: inline-flex;
  align-items: center;
  background-color: #ee4d2d;
  color: white;
  font-size: 9px;
  font-weight: bold;
  padding: 2px 8px;
  border-radius: 3px;
  margin-right: 5px;
  vertical-align: middle;
  position: relative;
}

.live-shopee-3 .sound-wave {
  display: inline-flex;
  align-items: center;
  height: 7px;
  margin-right: 4px; /* Memperbesar margin di sini */
}

.live-shopee-3 .wave-bar {
  width: 1px;
  background-color: white;
  margin-right: 1px;
}

/* Tambahkan margin kiri pada teks setelah gelombang suara */
.live-shopee-3 .live-text {
  margin-left: 1px; /* Tambahkan ini */
}

.live-shopee-3 .wave-bar:nth-child(1) {
  height: 7px;
  animation: soundwave 0.8s infinite alternate;
}

.live-shopee-3 .wave-bar:nth-child(2) {
  height: 9px;
  animation: soundwave 0.7s infinite alternate 0.1s;
}

.live-shopee-3 .wave-bar:nth-child(3) {
  height: 7px;
  animation: soundwave 0.6s infinite alternate 0.2s;
}

@keyframes soundwave {
  0% { height: 2px; }
  100% { height: 6px; }
}
		
		
		/* Star plus label model lama */
		.shopee-star-icon {
            display: inline-flex;
            align-items: center;
            background-color: #ee4d2d;
            color: white;
            font-size: 9px;
			font-weight: 700;
            font-weight: bold;
            padding: 1px 2px;
            border-radius: 3px;
            margin-right: 3px;
            vertical-align: middle;
        }
		/* Star plus label style */
.star-label {
    display: inline-flex;
    align-items: center;
    border-radius: 3px; 
    overflow: hidden;
    box-shadow: 0 1px 2px rgba(0,0,0,0.08);
    font-size: 9px;
    font-weight: 700;
    height: 16px;
    margin-right: 4px;
    margin-bottom: 2px;
    vertical-align: middle;
}

.star-primary {
    background-color: #ee4d2d; /* Warna orange-merah khas Shopee */
    color: white;
    padding: 0 5px;
    height: 100%;
    display: flex;
    align-items: center;
    letter-spacing: -0.1px;
}

.star-secondary {
    background-color: #fff0e5; /* Warna orange muda */
    color: #ee4d2d; /* Warna teks sesuai dengan warna primary */
    padding: 0 5px;
    height: 100%;
    display: flex;
    align-items: center;
    letter-spacing: -0.1px;
}
		/* Mall label style */
        .mall-label {
    display: inline-flex;
    align-items: center;
    border-radius: 3px; /* Kurangi sedikit border radius */
    overflow: hidden;
    box-shadow: 0 1px 2px rgba(0,0,0,0.08); /* Kurangi shadow */
    font-size: 9px; /* Sesuaikan ukuran font */
    font-weight: 700; /* Font weight lebih tebal */
    height: 15px; /* Kurangi sedikit tinggi */
    margin-right: 4px;
    margin-bottom: 2px; /* Tambah margin bottom untuk jarak dengan teks */
    vertical-align: middle;
}

.mall-primary {
    background-color: #ee4d2d; /* Warna merah Shopee */
    color: white;
    padding: 0 5px; /* Sesuaikan padding */
    height: 100%;
    display: flex;
    align-items: center;
    letter-spacing: -0.1px; /* Kurangi sedikit jarak antar huruf */
}

.mall-secondary {
    background-color: #fff7f7; /* Warna latar belakang sedikit lebih pink */
    color: #ee4d2d; /* Warna teks sesuai dengan warna merah Shopee */
    padding: 0 5px; /* Sesuaikan padding */
    height: 100%;
    display: flex;
    align-items: center;
    letter-spacing: -0.1px; /* Kurangi sedikit jarak antar huruf */
}

        /* Class untuk mengelompokkan produk dari toko yang sama */
        .shop-group {
            border-bottom: 8px solid #f5f5f5;
        }
		/* CSS dari Kode 2 - Voucher Page */
        /* Css yang dimodifikasi agar tidak sama dengan kode 1 */
        .voucher-page {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 50px;
            background-color: #f5f5f5;
            z-index: 1000;
            overflow-y: auto;
        }
 .voucher-page-wrapper {
    display: flex;
    flex-direction: column;
    height: 100%;
    width: 100%;
    max-width: 768px;
    margin: 0 auto;
    background-color: #fff;
    border-left: 1px solid #e0e0e0;
    border-right: 1px solid #e0e0e0;
}

        .voucher-page-header {
            position: sticky;
            top: 0;
            width: 100%;
            background-color: #fff;
            padding: 16px 0;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            z-index: 100;
        }

        .voucher-header-container {
            max-width: 768px;
            width: 100%;
            padding: 0 16px;
            position: relative;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        @media (min-width: 800px) {
            .voucher-header-container {
                max-width: 768px;
                margin: 0 auto;
                position: relative;
                width: 100%;
            }
        }

        .voucher-page-header h1 {
            font-size: 1.2rem;
            font-weight: bold;
        }

        .voucher-back-btn {
            position: absolute;
            left: 20px;
            top: 50%;
            transform: translateY(-50%);
            border: none;
            background: none;
            font-size: 25px;
            cursor: pointer;
            color: #ee4d2d;
            line-height: 1;
        }
		.voucher-back-btn i {
    transform: scaleX(1.3); /* Membuat panah sedikit lebih panjang secara horizontal */
}

        .voucher-page-content {
            flex: 1;
            overflow-y: auto;
            padding-bottom: 65px; /* Add padding to account for footer height */
        }

        .voucher-container {
            max-width: 768px;
            width: 100%;
            margin: 0 auto;
            padding: 15px 16px;
        }

        .voucher-section-container {
            background-color: #fff;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 8px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        /* Search */
        .search-bar {
            padding: 10px 16px;
            background-color: #fff;
            position: relative;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            margin-bottom: 8px;
        }

        .search-input {
            display: flex;
            align-items: center;
            background-color: #f5f5f5;
            border-radius: 10px;
            padding: 8px 12px;
            margin-top: 2px;
        }

        .search-input input {
            flex: 1;
            border: none;
            background: none;
            outline: none;
            font-size: 14px;
        }

        .search-icon {
            margin-left: 8px;
            font-size: 18px;
            color: #757575;
            cursor: pointer;
        }
        
        .search-icon.active {
            color: #ee4d2d;
        }

        .clear-icon {
            margin-right: 8px;
            font-size: 18px;
            color: #999;
            cursor: pointer;
            display: none;
        }

        /* Search suggestions */
        .search-suggestions {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background-color: #fff;
            border-radius: 0 0 8px 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            z-index: 100;
            max-height: 300px;
            overflow-y: auto;
            display: none;
        }

        .suggestion-item {
            padding: 12px 16px;
            border-bottom: 1px solid #f0f0f0;
            font-size: 14px;
            cursor: pointer;
        }

        .suggestion-item:hover {
            background-color: #f9f9f9;
        }

        .suggestion-title {
            font-weight: bold;
            margin-bottom: 4px;
        }

        .suggestion-desc {
            font-size: 12px;
            color: #757575;
        }
  .suggestion-keyword {color: #ee4d2d;
            padding: 4px 0;
        }

        /* Tabs as buttons */
        .tabs {
            display: flex;
            background-color: #fff;
            padding: 16px 16px 16px 16px;
            margin-bottom: 8px;
            overflow-x: auto;
            white-space: nowrap;
            scrollbar-width: none;
            -ms-overflow-style: none;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .tabs::-webkit-scrollbar {
            display: none;
        }

        .tab {
            display: inline-block;
            padding: 8px 16px;
            margin-right: 8px;
            border-radius: 16px;
            font-size: 14px;
            font-weight: bold;
            color: #757575;
            background-color: #f5f5f5;
            cursor: pointer;
            border: 1px solid #e0e0e0;
        }

        .tab.active {
            color: #fff;
            background-color: #ee4d2d;
            border-color: #ee4d2d;
        }

        /* Main container */
        .content-area {
            background-color: #f5f5f5;
            border-radius: 8px;
            margin-bottom: 8px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            padding: 15px;
        }

        /* Voucher List */
        .voucher-list {
            padding: 0;
        }

        .voucher-group {
            margin-bottom: 20px;
        }

        .group-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
            padding: 0 4px;
        }

        .group-title {
            font-size: 16px;
            font-weight: bold;
            color: #333;
        }

        .show-more {
            font-size: 13px;
            color: #ee4d2d;
            cursor: pointer;
            display: flex;
            align-items: center;
        }

        .show-more-icon {
            margin-left: 4px;
            font-size: 14px;
        }

        .voucher-item {
            display: flex;
            margin-bottom: 12px;
            border-radius: 4px;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            position: relative;
            width: 100%;
            background-color: #fff;
        }

        .voucher-left {
            width: 80px;
            min-width: 80px;
            background-color: #ee4d2d;
            color: #fff;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: 12px 0;
            position: relative;
        }

        .voucher-left::after {
            content: '';
            position: absolute;
            right: 0;
            top: 0;
            bottom: 0;
            width: 8px;
            background-image: radial-gradient(circle at 0 50%, transparent 8px, #fff 8px);
            background-size: 8px 16px;
            background-repeat: repeat-y;
        }

        .voucher-amount {
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 4px;
            line-height: 1;
        }

        .voucher-min {
            font-size: 10px;
            opacity: 0.8;
            text-align: center;
            padding: 5px 10px;
            margin: 0px 0;
        }

        .voucher-right {
            flex: 1;
            background-color: #fff;
            padding: 12px;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }

        .voucher-title {
            font-size: 14px;
            font-weight: bold;
            margin-bottom: 4px;
            color: #212121;
        }

        .voucher-desc {
            font-size: 12px;
            color: #757575;
            margin-bottom: 8px;
        }

        .voucher-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .voucher-date {
            font-size: 11px;
            color: #999;
        }

        /* Circular checkbox for vouchers */
        .circular-checkbox {
            width: 24px;
            height: 24px;
            position: relative;
        }

        .circular-checkbox input {
            opacity: 0;
            width: 0;
            height: 0;
            position: absolute;
        }

        .checkmark {
            position: absolute;
            top: 0;
            left: 0;
            height: 24px;
            width: 24px;
            background-color: #fff;
            border: 2px solid #ddd;
            border-radius: 50%;
        }

        .circular-checkbox input:checked ~ .checkmark {
            background-color: #ee4d2d;
            border-color: #ee4d2d;
        }

        .checkmark:after {
            content: "";
            position: absolute;
            display: none;
        }

        .circular-checkbox input:checked ~ .checkmark:after {
            display: block;
            left: 8px;
            top: 4px;
            width: 5px;
            height: 10px;
            border: solid white;
            border-width: 0 2px 2px 0;
            transform: rotate(45deg);
        }
        
		/* Selected vouchers summary */
        .selected-vouchers-summary {
            background-color: #fff9f5;
            padding: 8px 8px 8px 8px;
            margin-bottom: 8px;
            border-top: 1px solid #ffe5d9;
            font-size: 8px;
            border-radius: 80px;
            display: none !important;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .selected-voucher-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background-color: #fff9f5;
            border-radius: 8px;
            padding: 8px 15px;
            margin-bottom: 6px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            border: 1px solid #ffe0d0;
            position: relative;
        }

        .selected-voucher-name {
            font-size: 14px;
            color: #333;
        }

        .selected-voucher-value {
            font-size: 14px;
            font-weight: bold;
            color: #ee4d2d;
            padding-right: 24px; /* Espacio para el botón de cierre */
        }

        .selected-voucher-close {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            width: 16px;
            height: 16px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            cursor: pointer;
            border: 1px solid #ee4d2d;
            color: #ee4d2d;
            background-color: white;
        }

        /* Footer */
       .voucher-footer {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    width: 100%;
    background-color: #fff;
    box-shadow: 0 -2px 4px rgba(0,0,0,0.1);
    z-index: 101;
    border-left: 1px solid #e0e0e0; /* Tambahkan border */
    border-right: 1px solid #e0e0e0; /* Tambahkan border */
    padding-bottom: env(safe-area-inset-bottom);
}

.voucher-page-wrapper .voucher-footer {
    max-width: 768px;
    margin: 0 auto;
}

.voucher-footer-content {
    max-width: 768px;
    width: 100%;
    margin: 0 auto;
    padding: 15px 16px;
}

        .footer-summary {
            margin-bottom: 15px;
            font-size: 13px;
            display: none; /* Ocultado por defecto */
        }

        .apply-btn {
            background-color: #ee4d2d;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 4px;
            font-weight: bold;
            cursor: pointer;
            transition: background-color 0.3s;
            width: 100%;
        }

        .apply-btn:hover {
            background-color: #d63c1e;
        }

        .apply-btn:disabled {
            background-color: #aaa;
            cursor: not-allowed;
        }

        /* Recommended badge */
        .recommended-badge {
            position: absolute;
            top: 0;
            right: 0;
            background-color: #ff9800;
            color: white;
            font-size: 10px;
            font-weight: bold;
            padding: 3px 8px;
            border-radius: 0 4px 0 4px;
            z-index: 1;
        }

        /* Variant styling */
        .shipping .voucher-left {
            background-color: #00bfa5;
        }

        .cashback .voucher-left {
            background-color: #9c27b0;
        }

        .payment .voucher-left {
            background-color: #3f51b5;
        }

        .bank .voucher-left {
            background-color: #4CAF50;
        }

        /* Divider */
        .divider {
            height: 8px;
            background-color: #f5f5f5;
            margin: 16px 0;
        }
        
        /* Default show only 2 vouchers per group */
        .voucher-item:nth-child(n+10) {
            display: none;
        }
        
        .voucher-item.visible {
            display: flex !important;
        }

        /* Tab content areas */
        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }
        
        /* Untuk grup voucher gratis ongkir, hanya tampilkan 1 voucher default */
        .voucher-group[data-group="shipping"] .voucher-item:nth-child(n+2) {
            display: none;
        }

        /* Toast Notification */
        .toast {
            position: fixed;
            top: 70px;
            left: 50%;
            transform: translateX(-50%);
            background-color: rgba(0,0,0,0.7);
            color: white;
            padding: 10px 20px;
            border-radius: 4px;
            z-index: 1000;
            display: none;
        }

        /* Scrollbar styling */
        .voucher-list::-webkit-scrollbar {
            width: 6px;
        }

        .voucher-list::-webkit-scrollbar-track {
            background: #f1f1f1;
        }

        .voucher-list::-webkit-scrollbar-thumb {
            background: #ddd;
            border-radius: 4px;
        }

        .voucher-list::-webkit-scrollbar-thumb:hover {
            background: #ccc;
        }

        /* Responsive */
        @media (max-width: 480px) {
            .voucher-container {
                padding: 10px;
            }

            .voucher-section-container {
                padding: 12px;
            }

            .voucher-footer-content {
                padding: 10px;
            }

            .apply-btn {
                padding: 10px 20px;
            }
			.voucher-badge {
        max-width: 120px; /* Batasi lebar maksimum pada mobile */
        padding: 3px 8px; /* Kurangi padding */
        font-size: 11px; /* Kurangi ukuran font */
        margin-right: 4px; /* Kurangi margin */
    }
    
    .voucher-badge-close {
        width: 10px; /* Kurangi ukuran tombol close */
        height: 10px;
        margin-left: 4px; /* Kurangi margin */
    }
    
    /* Jika parent container perlu penyesuaian */
    .voucher-badges {
        max-width: 80%; /* Batasi lebar container badge */
    }
    
    /* Opsional: Jika ruang terlalu sempit, buat badge bisa wrap */
    .voucher-section {
        flex-wrap: wrap;
    }
        }

        /* Voucher yang tidak aktif */
        .voucher-item.disabled .voucher-left {
            background-color: #aaaaaa !important;
            opacity: 0.7;
        }

        .voucher-item.disabled .voucher-right {
            opacity: 0.7;
        }

        .voucher-item.disabled .recommended-badge {
            background-color: #888888;
        }

        /* Checkbox tidak aktif */
        .voucher-item.disabled .circular-checkbox {
            cursor: not-allowed;
        }

        .voucher-item.disabled .circular-checkbox input {
            pointer-events: none;
        }

        .voucher-item.disabled .checkmark {
            background-color: #e0e0e0;
            border-color: #cccccc;
        }
        
        /* Card peringatan tidak ada hasil */
        .no-results-card {
            background-color: #fff8e1;
            border-radius: 8px;
            padding: 15px;
            margin-top: 10px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            display: none;
            text-align: center;
            color: #ff6d00;
            font-size: 14px;
            border-left: 4px solid #ff9800;
        }
    </style>
</head>
<body>
    <div class="page-container">
        <header class="header">
            <div class="header-container">
                <h1><span class="back-arrow"><i class="fa fa-arrow-left"></i></span> Keranjang Saya&nbsp;&nbsp;<span id="cart-count">(8)</span></h1>
                <button class="edit-btn">Ubah</button>
            </div>
        </header>

        <div class="cart-section" id="cart-items">
        <!-- Toko 1: Fashion Store -->
<div class="shop-group" data-shop-id="1">
    <div class="shop-header">
        <div class="shop-checkbox">
            <input type="checkbox" class="shop-checkbox-input" data-shop-id="1">
        </div>
        <div class="live-shopee-3">
            <div class="sound-wave">
                <div class="wave-bar"></div>
                <div class="wave-bar"></div>
                <div class="wave-bar"></div>
            </div>
            <span class="live-text">LIVE</span>
        </div>
        <div class="shop-name">Fashion Store</div>
    </div>

				<!-- Produk 1 -->
                <div class="cart-item" data-id="1" data-shop-id="1">
                    <div class="item-checkbox">
                        <input type="checkbox" class="product-checkbox" data-id="1" data-shop-id="1">
                    </div>
                    <div class="item-image">
                        <img src="https://cdn.scalev.id/Image/2ebcb98892ab4a5ea33a4cb49f42c73c.webp" alt="Kaos Polos Premium">
                    </div>
                    <div class="item-details">
                        <div class="item-name">Kaos Polos Premium Katun Combed 30s</div>
                        <div class="item-variant" data-id="1">Putih, L</div>
                        <div class="item-price">
                            <div class="price-section">
                                <span class="discounted-price">Rp59.000</span>
                                <span class="original-price">Rp79.000</span>
                            </div>
                            <div class="item-quantity">
                                <button class="quantity-btn minus-btn" data-id="1"></button>
                                <input type="text" class="quantity-input" value="1" readonly>
                                <button class="quantity-btn plus-btn" data-id="1"></button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Produk 2 -->
                <div class="cart-item" data-id="2" data-shop-id="1">
                    <div class="item-checkbox">
                        <input type="checkbox" class="product-checkbox" data-id="2" data-shop-id="1">
                    </div>
                    <div class="item-image">
                        <img src="https://cdn.scalev.id/Image/2ebcb98892ab4a5ea33a4cb49f42c73c.webp" alt="Celana Jeans">
                    </div>
                    <div class="item-details">
                        <div class="item-name">Celana Jeans Pria Slim Fit Stretch Denim</div>
                        <div class="item-variant" data-id="2">Blue Black, 32</div>
                        <div class="item-price">
                            <div class="price-section">
                                <span class="discounted-price">Rp159.000</span>
                                <span class="original-price">Rp199.000</span>
                            </div>
                            <div class="item-quantity">
                                <button class="quantity-btn minus-btn" data-id="2"></button>
                                <input type="text" class="quantity-input" value="2" readonly>
                                <button class="quantity-btn plus-btn" data-id="2"></button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
          <!-- Toko 2: Sport World -->
            <div class="shop-group" data-shop-id="2">
                <div class="shop-header">
                    <div class="shop-checkbox">
                        <input type="checkbox" class="shop-checkbox-input" data-shop-id="2">
                    </div>
                    <div class="star-label">
                    <div class="star-primary">Star</div>
                    <div class="star-secondary">Plus</div>
                        <svg viewBox="0 0 32 32">
                            <path d="M28,13h-2c0-5.5-4.5-10-10-10S6,7.5,6,13H4c-1.1,0-2,0.9-2,2v4c0,1.1,0.9,2,2,2h1v7c0,1.1,0.9,2,2,2h18c1.1,0,2-0.9,2-2v-7h1c1.1,0,2-0.9,2-2v-4C30,13.9,29.1,13,28,13z M16,5c4.4,0,8,3.6,8,8H8C8,8.6,11.6,5,16,5z M25,28H7v-7h18V28z M28,19H4v-4h24V19z"></path>
                        </svg>
                    </div>
                    <div class="shop-name">Sport World</div>
                </div>
                
                <!-- Produk 3 -->
                <div class="cart-item" data-id="3" data-shop-id="2">
                    <div class="item-checkbox">
                        <input type="checkbox" class="product-checkbox" data-id="3" data-shop-id="2">
                    </div>
                    <div class="item-image">
                        <img src="https://cdn.scalev.id/Image/2ebcb98892ab4a5ea33a4cb49f42c73c.webp" alt="Sepatu Sneakers">
                    </div>
                    <div class="item-details">
                        <div class="item-name">Sepatu Sneakers Casual Pria Wanita Unisex</div>
                        <div class="item-variant" data-id="3">Hitam, 42</div>
                        <div class="item-price">
                            <div class="price-section">
                                <span class="discounted-price">Rp129.000</span>
                            </div>
                            <div class="item-quantity">
                                <button class="quantity-btn minus-btn" data-id="3"></button>
                                <input type="text" class="quantity-input" value="1" readonly>
                                <button class="quantity-btn plus-btn" data-id="3"></button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Toko 3: Style Hub -->
            <div class="shop-group" data-shop-id="3">
                <div class="shop-header">
                    <div class="shop-checkbox">
                        <input type="checkbox" class="shop-checkbox-input" data-shop-id="3">
                    </div>
                   <div class="star-label">
                    <div class="star-primary">Star</div>
                    <div class="star-secondary">Plus</div>
                        <svg viewBox="0 0 32 32">
                            <path d="M28,13h-2c0-5.5-4.5-10-10-10S6,7.5,6,13H4c-1.1,0-2,0.9-2,2v4c0,1.1,0.9,2,2,2h1v7c0,1.1,0.9,2,2,2h18c1.1,0,2-0.9,2-2v-7h1c1.1,0,2-0.9,2-2v-4C30,13.9,29.1,13,28,13z M16,5c4.4,0,8,3.6,8,8H8C8,8.6,11.6,5,16,5z M25,28H7v-7h18V28z M28,19H4v-4h24V19z"></path>
                        </svg>
                    </div>
                    <div class="shop-name">Style Hub</div>
                </div>
                
                <!-- Produk 4 -->
                <div class="cart-item" data-id="4" data-shop-id="3">
                    <div class="item-checkbox">
                        <input type="checkbox" class="product-checkbox" data-id="4" data-shop-id="3">
                    </div>
                    <div class="item-image">
                        <img src="https://cdn.scalev.id/Image/2ebcb98892ab4a5ea33a4cb49f42c73c.webp" alt="Hoodie Unisex">
                    </div>
                    <div class="item-details">
                        <div class="item-name">Hoodie Unisex Premium Cotton Fleece</div>
                        <div class="item-variant" data-id="4">Navy, XL</div>
                        <div class="item-price">
                            <div class="price-section">
                                <span class="discounted-price">Rp189.000</span>
                                <span class="original-price">Rp250.000</span>
                            </div>
                            <div class="item-quantity">
                                <button class="quantity-btn minus-btn" data-id="4"></button>
                                <input type="text" class="quantity-input" value="1" readonly>
                                <button class="quantity-btn plus-btn" data-id="4"></button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Toko 4: Bags & More -->
            <div class="shop-group" data-shop-id="4">
                <div class="shop-header">
                    <div class="shop-checkbox">
                        <input type="checkbox" class="shop-checkbox-input" data-shop-id="4">
                    </div>
                    <div class="mall-label">
                <div class="mall-primary">Mall</div>
                <div class="mall-secondary">ORI</div>
                        <svg viewBox="0 0 32 32">
                            <path d="M28,13h-2c0-5.5-4.5-10-10-10S6,7.5,6,13H4c-1.1,0-2,0.9-2,2v4c0,1.1,0.9,2,2,2h1v7c0,1.1,0.9,2,2,2h18c1.1,0,2-0.9,2-2v-7h1c1.1,0,2-0.9,2-2v-4C30,13.9,29.1,13,28,13z M16,5c4.4,0,8,3.6,8,8H8C8,8.6,11.6,5,16,5z M25,28H7v-7h18V28z M28,19H4v-4h24V19z"></path>
                        </svg>
                    </div>
                    <div class="shop-name">Bags & More</div>
                </div>
                
                <!-- Produk 5 -->
                <div class="cart-item" data-id="5" data-shop-id="4">
                    <div class="item-checkbox">
                        <input type="checkbox" class="product-checkbox" data-id="5" data-shop-id="4">
                    </div>
                    <div class="item-image">
                        <img src="https://cdn.scalev.id/Image/2ebcb98892ab4a5ea33a4cb49f42c73c.webp" alt="Tas Ransel Laptop">
                    </div>
                    <div class="item-details">
                        <div class="item-name">Tas Ransel Laptop Anti Air Premium</div>
                        <div class="item-variant" data-id="5">Hitam</div>
                        <div class="item-price">
                            <div class="price-section">
                                <span class="discounted-price">Rp279.000</span>
                                <span class="original-price">Rp350.000</span>
                            </div>
                            <div class="item-quantity">
                                <button class="quantity-btn minus-btn" data-id="5"></button>
                                <input type="text" class="quantity-input" value="1" readonly>
                                <button class="quantity-btn plus-btn" data-id="5"></button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Toko 5: Urban Clothes -->
            <div class="shop-group" data-shop-id="5">
                <div class="shop-header">
                    <div class="shop-checkbox">
                        <input type="checkbox" class="shop-checkbox-input" data-shop-id="5">
                    </div>
                    <div class="shopee-star-icon">
                            <span class="shopee-star-icon">Star+</span>
                        <svg viewBox="0 0 32 32">
                            <path d="M28,13h-2c0-5.5-4.5-10-10-10S6,7.5,6,13H4c-1.1,0-2,0.9-2,2v4c0,1.1,0.9,2,2,2h1v7c0,1.1,0.9,2,2,2h18c1.1,0,2-0.9,2-2v-7h1c1.1,0,2-0.9,2-2v-4C30,13.9,29.1,13,28,13z M16,5c4.4,0,8,3.6,8,8H8C8,8.6,11.6,5,16,5z M25,28H7v-7h18V28z M28,19H4v-4h24V19z"></path>
                        </svg>
                    </div>
                    <div class="shop-name">Urban Clothes</div>
                </div>
                
                <!-- Produk 6 -->
                <div class="cart-item" data-id="6" data-shop-id="5">
                    <div class="item-checkbox">
                        <input type="checkbox" class="product-checkbox" data-id="6" data-shop-id="5">
                    </div>
                    <div class="item-image">
                        <img src="https://cdn.scalev.id/Image/2ebcb98892ab4a5ea33a4cb49f42c73c.webp" alt="Kemeja Flannel">
                    </div>
                    <div class="item-details">
                        <div class="item-name">Kemeja Flannel Premium Quality</div>
                        <div class="item-variant" data-id="6">Merah Kotak, L</div>
                        <div class="item-price">
                            <div class="price-section">
                                <span class="discounted-price">Rp149.000</span>
                                <span class="original-price">Rp179.000</span>
                              </div>
                            <div class="item-quantity">
                                <button class="quantity-btn minus-btn" data-id="6"></button>
                                <input type="text" class="quantity-input" value="1" readonly>
                                <button class="quantity-btn plus-btn" data-id="6"></button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Toko 6: Accessories Store -->
            <div class="shop-group" data-shop-id="6">
                <div class="shop-header">
                    <div class="shop-checkbox">
                        <input type="checkbox" class="shop-checkbox-input" data-shop-id="6">
                    </div>
                    <div class="star-label">
                    <div class="star-primary">Star</div>
                    <div class="star-secondary">Plus</div>
                        <svg viewBox="0 0 32 32">
                            <path d="M28,13h-2c0-5.5-4.5-10-10-10S6,7.5,6,13H4c-1.1,0-2,0.9-2,2v4c0,1.1,0.9,2,2,2h1v7c0,1.1,0.9,2,2,2h18c1.1,0,2-0.9,2-2v-7h1c1.1,0,2-0.9,2-2v-4C30,13.9,29.1,13,28,13z M16,5c4.4,0,8,3.6,8,8H8C8,8.6,11.6,5,16,5z M25,28H7v-7h18V28z M28,19H4v-4h24V19z"></path>
                        </svg>
                    </div>
                    <div class="shop-name">Accessories Store</div>
                </div>
                
                <!-- Produk 7 -->
                <div class="cart-item" data-id="7" data-shop-id="6">
                    <div class="item-checkbox">
                        <input type="checkbox" class="product-checkbox" data-id="7" data-shop-id="6">
                    </div>
                    <div class="item-image">
                        <img src="https://cdn.scalev.id/Image/2ebcb98892ab4a5ea33a4cb49f42c73c.webp" alt="Topi Baseball">
                    </div>
                    <div class="item-details">
                        <div class="item-name">Topi Baseball Premium Cotton Twill</div>
                        <div class="item-variant" data-id="7">Hitam</div>
                        <div class="item-price">
                            <div class="price-section">
                                <span class="discounted-price">Rp49.000</span>
                            </div>
                            <div class="item-quantity">
                                <button class="quantity-btn minus-btn" data-id="7"></button>
                                <input type="text" class="quantity-input" value="1" readonly>
                                <button class="quantity-btn plus-btn" data-id="7"></button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Produk 8 -->
                <div class="cart-item" data-id="8" data-shop-id="6">
                    <div class="item-checkbox">
                        <input type="checkbox" class="product-checkbox" data-id="8" data-shop-id="6">
                    </div>
                    <div class="item-image">
                        <img src="https://cdn.scalev.id/Image/2ebcb98892ab4a5ea33a4cb49f42c73c.webp" alt="Dompet Kulit">
                    </div>
                    <div class="item-details">
                        <div class="item-name">Dompet Kulit Asli Handmade Premium</div>
                        <div class="item-variant" data-id="8">Coklat Tua</div>
                        <div class="item-price">
                            <div class="price-section">
                                <span class="discounted-price">Rp199.000</span>
                                <span class="original-price">Rp249.000</span>
                            </div>
                            <div class="item-quantity">
                                <button class="quantity-btn minus-btn" data-id="8"></button>
                                <input type="text" class="quantity-input" value="1" readonly>
                                <button class="quantity-btn plus-btn" data-id="8"></button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Footer dengan struktur kode 1 tapi tampilan kode 2 -->
        <footer class="footer">
            <div class="footer-content">
                <div class="voucher-section">
                    <div class="section-title">
                        <svg viewBox="0 0 100 100" width="22" height="22">
                            <path d="M15,40 C15,34.5 19.5,30 25,30 L75,30 C80.5,30 85,34.5 85,40 L85,43 C78,43 78,57 85,57 L85,60 C85,65.5 80.5,70 75,70 L25,70 C19.5,70 15,65.5 15,60 L15,57 C22,57 22,43 15,43 Z" fill="none" stroke="#ee4d2d" stroke-width="3"/>
                            <line x1="40" y1="30" x2="40" y2="70" stroke="#ee4d2d" stroke-width="2" stroke-dasharray="4,2"/>
                            <line x1="60" y1="30" x2="60" y2="70" stroke="#ee4d2d" stroke-width="2" stroke-dasharray="4,2"/>
                        </svg>
                        <span>Voucher</span>
                    </div>
                    <div class="section-action" id="voucher-select">
                        <button class="voucher-button" disabled>Pilih Voucher</button>
                    </div>
                </div>
                
                <div class="discount-info" id="discount-info">
                    Voucher <span id="applied-voucher"></span> berhasil diterapkan.
                    <span class="discount-info-break">Potongan: <span id="discount-amount"></span></span>
                </div>
                
                <div class="checkout-row">
                    <div class="select-all">
                        <input type="checkbox" id="select-all-checkbox">
                        <label for="select-all-checkbox">Pilih Semua</label>
                    </div>
                    <div class="checkout-section">
                        <div class="total-price">
                            <div class="total-label">Total Harga</div>
                            <div class="total-amount" id="total-amount">Rp0</div>
                            <div class="discount-label" id="discount-label" style="display: none;">
                                Hemat: <span id="discount-display">Rp0</span>
                                <span class="discount-arrow"></span>
                            </div>
                        </div>
                        <button class="checkout-btn" id="checkout-btn">Checkout</button>
                    </div>
                </div>
                
                <!-- Discount details panel -->
                <div class="discount-details" id="discount-details">
                    <div class="detail-item">
                        <span class="detail-label">Subtotal:</span>
                        <span class="detail-value" id="subtotal-value">Rp0</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">Voucher Diskon:</span>
                        <span class="detail-value detail-discount" id="voucher-discount-value">-Rp0</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">Diskon Produk:</span>
                        <span class="detail-value detail-discount" id="product-discount-value">-Rp0</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">Total Pembayaran:</span>
                        <span class="detail-value" id="payment-total">Rp0</span>
                    </div>
                </div>
            </div>
        </footer>
    </div>
	<!-- Confirmation Modal -->
    <div class="modal" id="confirm-modal">
        <div class="modal-content">
            <div class="modal-message">
                Hapus produk dari keranjang?
            </div>
            <div class="modal-actions">
                <button class="modal-btn" id="modal-cancel">Tidak</button>
                <button class="modal-btn modal-btn-confirm" id="modal-confirm">Iya</button>
            </div>
        </div>
    </div>
  <!-- Modal Pilih Varian -->
    <div class="variant-modal" id="variant-modal">
        <div class="variant-modal-content">
            <div class="variant-modal-header">
                <h3>Pilih Varian</h3>
            </div>
            <div class="variant-modal-body">
                <div class="variant-group">
                    <div class="variant-title">Warna</div>
                    <div class="variant-options" id="color-options">
                        <!-- Options will be populated by JavaScript -->
                    </div>
                </div>
                <div class="variant-group">
                    <div class="variant-title">Ukuran</div>
                    <div class="variant-options" id="size-options">
                        <!-- Options will be populated by JavaScript -->
                    </div>
                </div>
            </div>
            <div class="variant-modal-footer">
                <button class="variant-confirm-btn" id="variant-confirm">Konfirmasi</button>
            </div>
        </div>
    </div>

   <!-- Voucher Page - PERBAIKAN STRUKTUR CONTAINER -->
<div class="voucher-page" id="voucher-page">
    <div class="voucher-page-wrapper">
        <header class="voucher-page-header">
            <div class="voucher-header-container">
                <button class="voucher-back-btn" id="voucher-back-btn">
                    <i class="fa fa-arrow-left"></i>
                </button>
                <h1>Voucher Saya</h1>
            </div>
        </header>

            <div class="voucher-page-content">
                <div class="voucher-container">
                    <!-- Search Bar -->
                    <div class="search-bar">
                        <div class="search-input">
                            <input type="text" placeholder="Cari voucher..." id="search-input">
                            <svg class="clear-icon" xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <circle cx="12" cy="12" r="10"></circle>
                                <line x1="15" y1="9" x2="9" y2="15"></line>
                                <line x1="9" y1="9" x2="15" y2="15"></line>
                            </svg>
                            <svg class="search-icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <circle cx="11" cy="11" r="8"></circle>
                                <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
                            </svg>
                        </div>
                        <div class="search-suggestions" id="search-suggestions">
                            <!-- Search suggestions will be dynamically populated here -->
                        </div>
                        <div class="no-results-card" id="no-results-card">
                            Maaf, voucher yang Anda cari tidak ditemukan.
                        </div>
                    </div>
                    
                    <!-- Tabs -->
                    <div class="tabs">
                        <div class="tab active" data-tab="all">Semua</div>
                        <div class="tab" data-tab="shipping">Gratis Ongkir</div>
                        <div class="tab" data-tab="discount">Diskon</div>
                        <div class="tab" data-tab="cashback">Cashback</div>
                    </div>
                    
                    <!-- Main Content -->
                    <div class="content-area">
                        <!-- All Vouchers Tab -->
                        <div class="tab-content active" id="tab-all">
                            <div class="voucher-list">
                                <!-- Voucher Diskon Group -->
                                <div class="voucher-group" data-group="discount">
                                    <div class="group-header">
                                        <div class="group-title">Voucher Diskon</div>
                                    </div>
                                    
                                    <div class="voucher-item visible">
                                        <div class="recommended-badge">Recommended</div>
                                        <div class="voucher-left">
                                            <div class="voucher-amount">Rp50rb</div>
                                            <div class="voucher-min">Min. belanja Rp200rb</div>
                                        </div>
                                        <div class="voucher-right">
                                            <div>
                                                <div class="voucher-title">Diskon Belanja</div>
                                                <div class="voucher-desc">Diskon Rp50.000 untuk semua kategori produk</div>
                                            </div>
                                            <div class="voucher-info">
                                                <div class="voucher-date" data-expiry-date="2025-03-30">Berlaku hingga: 30 Mar 2025</div>
                                                <label class="circular-checkbox">
                                                    <input type="checkbox" class="voucher-checkbox" data-value="50000" data-min="200000" data-group="discount" data-title="Diskon Belanja">
                                                    <span class="checkmark"></span>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="voucher-item visible">
                                        <div class="voucher-left">
                                            <div class="voucher-amount">Rp20rb</div>
                                            <div class="voucher-min">Min. belanja Rp100rb</div>
                                        </div>
                                        <div class="voucher-right">
                                            <div>
                                                <div class="voucher-title">Diskon Fashion</div>
                                                <div class="voucher-desc">Diskon Rp20.000 khusus kategori Fashion</div>
                                            </div>
                                            <div class="voucher-info">
                                                <div class="voucher-date" data-expiry-date="2025-03-25">Berlaku hingga: 25 Mar 2025</div>
                                                <label class="circular-checkbox">
                                                    <input type="checkbox" class="voucher-checkbox" data-value="20000" data-min="100000" data-group="discount" data-title="Diskon Fashion">
                                                    <span class="checkmark"></span>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="divider"></div>
                                
                                <!-- Gratis Ongkir Group -->
                                <div class="voucher-group" data-group="shipping">
                                    <div class="group-header">
                                        <div class="group-title">Gratis Ongkir</div>
                                        <div class="show-more" id="show-more-shipping">
                                            Lihat Lainnya
                                            <svg class="show-more-icon" xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                <polyline points="6 9 12 15 18 9"></polyline>
                                            </svg>
                                        </div>
                                    </div>
                                    
                                    <div class="voucher-item shipping visible">
                                        <div class="recommended-badge">Recommended</div>
                                        <div class="voucher-left">
                                            <div class="voucher-amount">Gratis</div>
                                            <div class="voucher-min">Ongkir s/d 20rb</div>
                                        </div>
                                        <div class="voucher-right">
                                            <div>
                                                <div class="voucher-title">Gratis Ongkir</div>
                                                <div class="voucher-desc">Gratis ongkir hingga Rp20.000 untuk semua toko</div>
                                            </div>
                                            <div class="voucher-info">
                                                <div class="voucher-date" data-expiry-date="2025-04-05">Berlaku hingga: 5 Apr 2025</div>
                                                <label class="circular-checkbox">
                                                    <input type="checkbox" class="voucher-checkbox" data-value="20000" data-type="shipping" data-group="shipping" data-title="Gratis Ongkir Rp20rb">
                                                    <span class="checkmark"></span>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="voucher-item shipping">
                                        <div class="voucher-left">
                                            <div class="voucher-amount">Gratis</div>
                                            <div class="voucher-min">Ongkir s/d 15rb</div>
                                        </div>
                                        <div class="voucher-right">
                                            <div>
                                                <div class="voucher-title">Gratis Ongkir Reguler</div>
                                                <div class="voucher-desc">Gratis ongkir hingga Rp15.000 dengan pengiriman reguler</div>
                                            </div>
                                            <div class="voucher-info">
                                                <div class="voucher-date" data-expiry-date="2025-04-10">Berlaku hingga: 10 Apr 2025</div>
                                                <label class="circular-checkbox">
                                                    <input type="checkbox" class="voucher-checkbox" data-value="15000" data-type="shipping" data-group="shipping" data-title="Gratis Ongkir Reguler">
                                                    <span class="checkmark"></span>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="voucher-item shipping">
                                        <div class="voucher-left">
                                            <div class="voucher-amount">Gratis</div>
                                            <div class="voucher-min">Ongkir s/d 30rb</div>
                                        </div>
                                        <div class="voucher-right">
                                            <div>
                                                <div class="voucher-title">Gratis Ongkir Premium</div>
                                                <div class="voucher-desc">Gratis ongkir hingga Rp30.000 dengan min. belanja Rp150.000</div>
                                            </div>
                                            <div class="voucher-info">
                                                <div class="voucher-date" data-expiry-date="2025-04-15">Berlaku hingga: 15 Apr 2025</div>
                                                <label class="circular-checkbox">
                                                    <input type="checkbox" class="voucher-checkbox" data-value="30000" data-type="shipping" data-min="150000" data-group="shipping" data-title="Gratis Ongkir Premium">
                                                    <span class="checkmark"></span>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="voucher-item shipping">
                                        <div class="voucher-left">
                                            <div class="voucher-amount">Gratis</div>
                                            <div class="voucher-min">Ongkir s/d 25rb</div>
                                        </div>
                                        <div class="voucher-right">
                                            <div>
                                                <div class="voucher-title">Gratis Ongkir COD</div>
                                                <div class="voucher-desc">Gratis ongkir hingga Rp25.000 untuk metode pembayaran COD</div>
                                            </div>
                                            <div class="voucher-info">
                                                <div class="voucher-date" data-expiry-date="2025-04-18">Berlaku hingga: 18 Apr 2025</div>
                                                <label class="circular-checkbox">
                                                    <input type="checkbox" class="voucher-checkbox" data-value="25000" data-type="shipping" data-group="shipping" data-title="Gratis Ongkir COD">
                                                    <span class="checkmark"></span>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="divider"></div>
                                
                                <!-- Cashback Group -->
                                <div class="voucher-group" data-group="cashback">
                                    <div class="group-header">
                                        <div class="group-title">Voucher Cashback</div>
                                    </div>
                                    
                                    <div class="voucher-item cashback visible">
                                        <div class="voucher-left">
                                            <div class="voucher-amount">10%</div>
                                            <div class="voucher-min">Maks. Rp35rb</div>
                                        </div>
                                        <div class="voucher-right">
                                            <div>
                                                <div class="voucher-title">Diskon Elektronik</div>
                                                <div class="voucher-desc">Diskon 10% untuk kategori Elektronik</div>
                                            </div>
                                            <div class="voucher-info">
                                                <div class="voucher-date" data-expiry-date="2025-03-10">Berlaku hingga: 10 Mar 2025</div>
                                                <label class="circular-checkbox">
                                                    <input type="checkbox" class="voucher-checkbox" data-value="35000" data-type="percentage" data-percentage="10" data-group="cashback" data-title="Diskon Elektronik 10%">
                                                    <span class="checkmark"></span>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="voucher-item cashback visible">
                                        <div class="recommended-badge">Recommended</div>
                                        <div class="voucher-left">
                                            <div class="voucher-amount">15%</div>
                                            <div class="voucher-min">Maks. Rp30rb</div>
                                        </div>
                                        <div class="voucher-right">
                                            <div>
                                                <div class="voucher-title">Cashback Belanja</div>
                                                <div class="voucher-desc">Cashback 15% dalam bentuk koin Shopee</div>
                                            </div>
                                            <div class="voucher-info">
                                                <div class="voucher-date" data-expiry-date="2025-04-12">Berlaku hingga: 12 Apr 2025</div>
                                                <label class="circular-checkbox">
                                                    <input type="checkbox" class="voucher-checkbox" data-value="30000" data-type="cashback" data-percentage="15" data-group="cashback" data-title="Cashback 15%">
                                                    <span class="checkmark"></span>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="divider"></div>
                              <!-- Payment Method Group -->
                                <div class="voucher-group" data-group="payment">
                                    <div class="group-header">
                                        <div class="group-title">Voucher Metode Pembayaran</div>
                                    </div>
                                    
                                    <div class="voucher-item payment visible">
                                        <div class="voucher-left">
                                            <div class="voucher-amount">Rp10rb</div>
                                            <div class="voucher-min">Bayar dengan COD</div>
                                        </div>
                                        <div class="voucher-right">
                                            <div>
                                                <div class="voucher-title">Diskon COD</div>
                                                <div class="voucher-desc">Diskon Rp10.000 untuk pembayaran COD min. Rp100.000</div>
                                            </div>
                                            <div class="voucher-info">
                                                <div class="voucher-date" data-expiry-date="2025-04-08">Berlaku hingga: 8 Apr 2025</div>
                                                <label class="circular-checkbox">
                                                    <input type="checkbox" class="voucher-checkbox" data-value="10000" data-type="payment" data-method="cod" data-min="100000" data-group="payment" data-title="Diskon COD">
                                                    <span class="checkmark"></span>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="voucher-item bank visible">
                                        <div class="voucher-left">
                                            <div class="voucher-amount">5%</div>
                                            <div class="voucher-min">Maks. Rp25rb</div>
                                        </div>
                                        <div class="voucher-right">
                                            <div>
                                                <div class="voucher-title">Diskon Transfer Bank</div>
                                                <div class="voucher-desc">Diskon 5% untuk pembayaran via Transfer Bank</div>
                                            </div>
                                            <div class="voucher-info">
                                                <div class="voucher-date" data-expiry-date="2025-04-20">Berlaku hingga: 20 Apr 2025</div>
                                                <label class="circular-checkbox">
    <input type="checkbox" class="voucher-checkbox" data-value="25000" data-type="payment" data-method="bank" data-percentage="5" data-group="payment" data-title="Diskon Transfer 5%">
    <span class="checkmark"></span>
</label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Shipping Vouchers Tab -->
                        <div class="tab-content" id="tab-shipping">
                            <div class="voucher-list">
                                <div class="voucher-group" data-group="shipping">
                                    <div class="group-header">
                                        <div class="group-title">Gratis Ongkir</div>
                                        <div class="show-more" id="show-more-shipping-tab">
                                            Lihat Lainnya
                                            <svg class="show-more-icon" xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                <polyline points="6 9 12 15 18 9"></polyline>
                                            </svg>
                                        </div>
                                    </div>
                                    
                                    <div class="voucher-item shipping visible">
                                        <div class="recommended-badge">Recommended</div>
                                        <div class="voucher-left">
                                            <div class="voucher-amount">Gratis</div>
                                            <div class="voucher-min">Ongkir s/d 20rb</div>
                                        </div>
                                        <div class="voucher-right">
                                            <div>
                                                <div class="voucher-title">Gratis Ongkir</div>
                                                <div class="voucher-desc">Gratis ongkir hingga Rp20.000 untuk semua toko</div>
                                            </div>
                                            <div class="voucher-info">
                                                <div class="voucher-date" data-expiry-date="2025-04-05">Berlaku hingga: 5 Apr 2025</div>
                                                <label class="circular-checkbox">
                                                    <input type="checkbox" class="voucher-checkbox" data-value="20000" data-type="shipping" data-group="shipping" data-title="Gratis Ongkir Rp20rb">
                                                    <span class="checkmark"></span>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="voucher-item shipping">
                                        <div class="voucher-left">
                                            <div class="voucher-amount">Gratis</div>
                                            <div class="voucher-min">Ongkir s/d 15rb</div>
                                        </div>
                                        <div class="voucher-right">
                                            <div>
                                                <div class="voucher-title">Gratis OngkirReguler</div>
                                                <div class="voucher-desc">Gratis ongkir hingga Rp15.000 dengan pengiriman reguler</div>
                                            </div>
                                            <div class="voucher-info">
                                                <div class="voucher-date" data-expiry-date="2025-04-10">Berlaku hingga: 10 Apr 2025</div>
                                                <label class="circular-checkbox">
                                                    <input type="checkbox" class="voucher-checkbox" data-value="15000" data-type="shipping" data-group="shipping" data-title="Gratis Ongkir Reguler">
                                                    <span class="checkmark"></span>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="voucher-item shipping">
                                        <div class="voucher-left">
                                            <div class="voucher-amount">Gratis</div>
                                            <div class="voucher-min">Ongkir s/d 30rb</div>
                                        </div>
                                        <div class="voucher-right">
                                            <div>
                                                <div class="voucher-title">Gratis Ongkir Premium</div>
                                                <div class="voucher-desc">Gratis ongkir hingga Rp30.000 dengan min. belanja Rp150.000</div>
                                            </div>
                                            <div class="voucher-info">
                                                <div class="voucher-date" data-expiry-date="2025-04-15">Berlaku hingga: 15 Apr 2025</div>
                                                <label class="circular-checkbox">
                                                    <input type="checkbox" class="voucher-checkbox" data-value="30000" data-type="shipping" data-min="150000" data-group="shipping" data-title="Gratis Ongkir Premium">
                                                    <span class="checkmark"></span>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="voucher-item shipping">
                                        <div class="voucher-left">
                                            <div class="voucher-amount">Gratis</div>
                                            <div class="voucher-min">Ongkir s/d 25rb</div>
                                        </div>
                                        <div class="voucher-right">
                                            <div>
                                                <div class="voucher-title">Gratis Ongkir COD</div>
                                                <div class="voucher-desc">Gratis ongkir hingga Rp25.000 untuk metode pembayaran COD</div>
                                            </div>
                                            <div class="voucher-info">
                                                <div class="voucher-date" data-expiry-date="2025-04-18">Berlaku hingga: 18 Apr 2025</div>
                                                <label class="circular-checkbox">
                                                    <input type="checkbox" class="voucher-checkbox" data-value="25000" data-type="shipping" data-group="shipping" data-title="Gratis Ongkir COD">
                                                    <span class="checkmark"></span>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Discount Vouchers Tab -->
                        <div class="tab-content" id="tab-discount">
                            <div class="voucher-list">
                                <div class="voucher-group" data-group="discount">
                                    <div class="group-header">
                                        <div class="group-title">Voucher Diskon</div>
                                    </div>
                                    
                                    <div class="voucher-item visible">
                                        <div class="recommended-badge">Recommended</div>
                                        <div class="voucher-left">
                                            <div class="voucher-amount">Rp50rb</div>
                                            <div class="voucher-min">Min. belanja Rp200rb</div>
                                        </div>
                                        <div class="voucher-right">
                                            <div>
                                                <div class="voucher-title">Diskon Belanja</div>
                                                <div class="voucher-desc">Diskon Rp50.000 untuk semua kategori produk</div>
                                            </div>
                                            <div class="voucher-info">
                                                <div class="voucher-date" data-expiry-date="2025-03-30">Berlaku hingga: 30 Mar 2025</div>
                                                <label class="circular-checkbox">
                                                    <input type="checkbox" class="voucher-checkbox" data-value="50000" data-min="200000" data-group="discount" data-title="Diskon Belanja">
                                                    <span class="checkmark"></span>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="voucher-item visible">
                                        <div class="voucher-left">
                                            <div class="voucher-amount">Rp20rb</div>
                                            <div class="voucher-min">Min. belanja Rp100rb</div>
                                        </div>
                                        <div class="voucher-right">
                                            <div>
                                                <div class="voucher-title">Diskon Fashion</div>
                                                <div class="voucher-desc">Diskon Rp20.000 khusus kategori Fashion</div>
                                            </div>
                                            <div class="voucher-info">
                                                <div class="voucher-date" data-expiry-date="2025-03-25">Berlaku hingga: 25 Mar 2025</div>
                                                <label class="circular-checkbox">
                                                    <input type="checkbox" class="voucher-checkbox" data-value="20000" data-min="100000" data-group="discount" data-title="Diskon Fashion">
                                                    <span class="checkmark"></span>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Cashback Vouchers Tab -->
                        <div class="tab-content" id="tab-cashback">
                            <div class="voucher-list">
                                <div class="voucher-group" data-group="cashback">
                                    <div class="group-header">
                                        <div class="group-title">Voucher Cashback</div>
                                    </div>
                                    
                                    <div class="voucher-item cashback visible">
                                        <div class="voucher-left">
                                            <div class="voucher-amount">10%</div>
                                            <div class="voucher-min">Maks. Rp35rb</div>
                                        </div>
                                        <div class="voucher-right">
                                            <div>
                                                <div class="voucher-title">Diskon Elektronik</div>
                                                <div class="voucher-desc">Diskon 10% untuk kategori Elektronik</div>
                                            </div>
                                            <div class="voucher-info">
                                                <div class="voucher-date" data-expiry-date="2025-03-10">Berlaku hingga: 10 Mar 2025</div>
                                                <label class="circular-checkbox">
                                                    <input type="checkbox" class="voucher-checkbox" data-value="35000" data-type="percentage" data-percentage="10" data-group="cashback" data-title="Diskon Elektronik 10%">
                                                    <span class="checkmark"></span>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="voucher-item cashback visible">
                                        <div class="recommended-badge">Recommended</div>
                                        <div class="voucher-left">
                                            <div class="voucher-amount">15%</div>
                                          <div class="voucher-min">Maks. Rp30rb</div>
                                        </div>
                                        <div class="voucher-right">
                                            <div>
                                                <div class="voucher-title">Cashback Belanja</div>
                                                <div class="voucher-desc">Cashback 15% dalam bentuk koin Shopee</div>
                                            </div>
                                            <div class="voucher-info">
                                                <div class="voucher-date" data-expiry-date="2025-04-12">Berlaku hingga: 12 Apr 2025</div>
                                                <label class="circular-checkbox">
                                                    <input type="checkbox" class="voucher-checkbox" data-value="30000" data-type="cashback" data-percentage="15" data-group="cashback" data-title="Cashback 15%">
                                                    <span class="checkmark"></span>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                            
                    <!-- Selected vouchers summary -->
                    <div class="selected-vouchers-summary" id="selected-vouchers-summary">
                        <div id="selected-vouchers-list"></div>
                    </div>
                    
                    <div class="toast" id="toast">Notification message</div>
                </div>
            </div>
            
            <footer class="voucher-footer">
                <div class="voucher-footer-content">
                    <div class="footer-summary" id="footer-summary"></div>
                    <button class="apply-btn" id="apply-btn" disabled>Terapkan Voucher</button>
                </div>
            </footer>
        </div>
    </div>
  <script>
        // Product data structure (based on HTML)
        const products = [
            {
                id: 1,
                name: "Kaos Polos Premium Katun Combed 30s",
                variant: "Putih, L",
                price: 59000,
                originalPrice: 79000,
                quantity: 1,
                colorOptions: ["Putih", "Hitam", "Navy", "Merah", "Abu-abu"],
                sizeOptions: ["S", "M", "L", "XL", "XXL"],
                shopId: 1
            },
            {
                id: 2,
                name: "Celana Jeans Pria Slim Fit Stretch Denim",
                variant: "Blue Black, 32",
                price: 159000,
                originalPrice: 199000,
                quantity: 2,
                colorOptions: ["Blue Black", "Blue", "Black", "Dark Grey"],
                sizeOptions: ["28", "30", "32", "34", "36"],
                shopId: 1
            },
            {
                id: 3,
                name: "Sepatu Sneakers Casual Pria Wanita Unisex",
                variant: "Hitam, 42",
                price: 129000,
                originalPrice: null,
                quantity: 1,
                colorOptions: ["Hitam", "Putih", "Navy", "Abu-abu"],
                sizeOptions: ["39", "40", "41", "42", "43", "44"],
                shopId: 2
            },
            {
                id: 4,
                name: "Hoodie Unisex Premium Cotton Fleece",
                variant: "Navy, XL",
                price: 189000,
                originalPrice: 250000,
                quantity: 1,
                colorOptions: ["Navy", "Hitam", "Abu-abu", "Maroon"],
                sizeOptions: ["M", "L", "XL", "XXL"],
                shopId: 3
            },
            {
                id: 5,
                name: "Tas Ransel Laptop Anti Air Premium",
                variant: "Hitam",
                price: 279000,
                originalPrice: 350000,
                quantity: 1,
                colorOptions: ["Hitam", "Abu-abu", "Navy", "Maroon"],
                sizeOptions: [],
                shopId: 4
            },
            {
                id: 6,
                name: "Kemeja Flannel Premium Quality",
                variant: "Merah Kotak, L",
                price: 149000,
                originalPrice: 179000,
                quantity: 1,
                colorOptions: ["Merah Kotak", "Biru Kotak", "Hijau Kotak", "Kuning Kotak"],
                sizeOptions: ["M", "L", "XL", "XXL"],
                shopId: 5
            },
            {
                id: 7,
                name: "Topi Baseball Premium Cotton Twill",
                variant: "Hitam",
                price: 49000,
                originalPrice: null,
                quantity: 1,
                colorOptions: ["Hitam", "Navy", "Merah", "Abu-abu", "Putih"],
                sizeOptions: [],
                shopId: 6
            },
            {
                id: 8,
                name: "Dompet Kulit Asli Handmade Premium",
                variant: "Coklat Tua",
                price: 199000,
                originalPrice: 249000,
                quantity: 1,
                colorOptions: ["Coklat Tua", "Coklat Muda", "Hitam", "Tan"],
                sizeOptions: [],
                shopId: 6
            }
        ];
		// Shop data structure
        const shops = [
            { id: 1, name: "Fashion Store" },
            { id: 2, name: "Sport World" },
            { id: 3, name: "Style Hub" },
            { id: 4, name: "Bags & More" },
            { id: 5, name: "Urban Clothes" },
            { id: 6, name: "Accessories Store" }
        ];

        // DOM elements
        const cartItemsContainer = document.getElementById('cart-items');
        const selectAllCheckbox = document.getElementById('select-all-checkbox');
        const totalAmountElement = document.getElementById('total-amount');
        const checkoutBtn = document.getElementById('checkout-btn');
        const confirmModal = document.getElementById('confirm-modal');
        const modalCancel = document.getElementById('modal-cancel');
        const modalConfirm = document.getElementById('modal-confirm');
        const discountInfoElement = document.getElementById('discount-info');
        const appliedVoucherElement = document.getElementById('applied-voucher');
        const discountAmountElement = document.getElementById('discount-amount');
        const discountLabelElement = document.getElementById('discount-label');
        const discountDisplayElement = document.getElementById('discount-display');
        const backArrow = document.querySelector('.back-arrow');
        const bodyElement = document.body;
        const voucherSelectBtn = document.getElementById('voucher-select');
        const discountDetailsPanel = document.getElementById('discount-details');

        // Variant Modal elements
        const variantModal = document.getElementById('variant-modal');
        const colorOptions = document.getElementById('color-options');
        const sizeOptions = document.getElementById('size-options');
        const variantConfirm = document.getElementById('variant-confirm');

        // Voucher elements
        const voucherPage = document.getElementById('voucher-page');
        const voucherBackBtn = document.getElementById('voucher-back-btn');
        const applyBtn = document.getElementById('apply-btn');
        const showMoreShipping = document.getElementById('show-more-shipping');
        const showMoreShippingTab = document.getElementById('show-more-shipping-tab');

        // Add back button functionality
        backArrow.addEventListener('click', function() {
            // Redirect to home page
            alert('Kembali ke halaman utama');
            // In a real app, you would use: window.location.href = '/';
        });

        // Current product to be removed (for modal)
        let currentProductToRemove = null;

        // Current product to update variant
        let currentVariantProduct = null;
        
        // Voucher state
        let activeVoucher = {
            code: null,
            discount: 0,
            minPurchase: 0,
            vouchers: []
        };

        // Format price to Indonesian Rupiah
        function formatPrice(price) {
            return 'Rp' + price.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ".");
        }

        // Initialize event listeners
        function initEventListeners() {
            // Product checkboxes
            const productCheckboxes = document.querySelectorAll('.product-checkbox');
            productCheckboxes.forEach(checkbox => {
                checkbox.addEventListener('change', function() {
                    updateTotalPrice();
                    updateShopCheckboxState(this.getAttribute('data-shop-id'));
                    updateVoucherButtonState();
                    
                    // Reset active vouchers when product selection changes
                    resetActiveVouchers();
                });
            });
            
            // Shop checkboxes
            const shopCheckboxes = document.querySelectorAll('.shop-checkbox-input');
            shopCheckboxes.forEach(checkbox => {
                checkbox.addEventListener('change', function() {
                    const shopId = this.getAttribute('data-shop-id');
                    toggleShopProducts(shopId, this.checked);
                    updateTotalPrice();
                    updateVoucherButtonState();
                    
                    // Reset active vouchers when product selection changes
                    resetActiveVouchers();
                });
            });
            
            // Quantity buttons
            const minusButtons = document.querySelectorAll('.minus-btn');
            const plusButtons = document.querySelectorAll('.plus-btn');
            
            minusButtons.forEach(button => {
                button.addEventListener('click', decreaseQuantity);
            });
            
            plusButtons.forEach(button => {
                button.addEventListener('click', increaseQuantity);
            });
            
            // Edit button
            const editBtn = document.querySelector('.edit-btn');
            editBtn.addEventListener('click', function() {
                // Uncheck all checkboxes
                const allCheckboxes = document.querySelectorAll('.product-checkbox');
                allCheckboxes.forEach(checkbox => {
                    checkbox.checked = false;
                });
                
                const allShopCheckboxes = document.querySelectorAll('.shop-checkbox-input');
                allShopCheckboxes.forEach(checkbox => {
                    checkbox.checked = false;
                });
                
                // Reset active voucher
                activeVoucher = {
                    code: null,
                    discount: 0,
                    minPurchase: 0,
                    vouchers: []
                };
                
                // Reset voucher display
                updateVoucherDisplay([]);
                updateVoucherButtonState();
                
                // Reset voucher checkboxes
                const voucherCheckboxes = document.querySelectorAll('.voucher-checkbox');
                voucherCheckboxes.forEach(checkbox => {
                    checkbox.checked = false;
                });
                
                // Update select all checkbox and total price
                selectAllCheckbox.checked = false;
                updateTotalPrice();
            });
            
            // Select all checkbox
            selectAllCheckbox.addEventListener('change', function() {
                const allCheckboxes = document.querySelectorAll('.product-checkbox');
                const allShopCheckboxes = document.querySelectorAll('.shop-checkbox-input');
                
                allCheckboxes.forEach(checkbox => {
                    checkbox.checked = selectAllCheckbox.checked;
                });
                
                allShopCheckboxes.forEach(checkbox => {
                    checkbox.checked = selectAllCheckbox.checked;
                });
                
                updateTotalPrice();
                updateVoucherButtonState();
                
                // Reset active vouchers when product selection changes
                resetActiveVouchers();
            });
            
            // Modal cancel button
            modalCancel.addEventListener('click', function() {
                confirmModal.style.display = 'none';
                currentProductToRemove = null;
            });
            
            // Modal confirm button
            modalConfirm.addEventListener('click', function() {
                confirmModal.style.display = 'none';
                
                if (currentProductToRemove) {
                    removeProduct(currentProductToRemove);
                    currentProductToRemove = null;
                }
            });
            
            // Checkout button
            checkoutBtn.addEventListener('click', function() {
                if (checkoutBtn.classList.contains('active')) {
                    const selectedProducts = [];
                    const selectedCheckboxes = document.querySelectorAll('.product-checkbox:checked');
                    
                    selectedCheckboxes.forEach(checkbox => {
                        const productId = parseInt(checkbox.dataset.id);
                        const product = products.find(p => p.id === productId);
                        
                        if (product) {
                            selectedProducts.push({
                                id: product.id,
                                name: product.name,
                                quantity: product.quantity,
                                price: product.price
                            });
                        }
                    });
                    
                    alert('Checkout dengan produk: ' + JSON.stringify(selectedProducts));
                }
            });
            
            // Variant click handlers
            const variantElements = document.querySelectorAll('.item-variant');
            variantElements.forEach(element => {
                element.addEventListener('click', openVariantModal);
            });

            // Variant confirm button
            variantConfirm.addEventListener('click', function() {
                if (currentVariantProduct) {
                    updateProductVariant(currentVariantProduct);
                    variantModal.style.display = 'none';
                    currentVariantProduct = null;
                }
            });

            // Voucher section handlers
            if (voucherSelectBtn) {
                voucherSelectBtn.addEventListener('click', function() {
                    if (!voucherSelectBtn.querySelector('button').disabled) {
                        voucherPage.style.display = 'block';
                        initVoucherPage();
                    }
                });
            }

            voucherBackBtn.addEventListener('click', function() {
                voucherPage.style.display = 'none';
            });

            // Apply voucher button
            applyBtn.addEventListener('click', function() {
                // Get selected vouchers
                const selectedVouchers = getSelectedVouchers();
                
                if (selectedVouchers.length > 0) {
                    // Apply vouchers to cart
                    applyVouchersToCart(selectedVouchers);
                    
                    // Hide voucher page
                    voucherPage.style.display = 'none';
                    
                    
                }
            });
            
            // Show more shipping vouchers
            if (showMoreShipping) {
                showMoreShipping.addEventListener('click', function() {
                    toggleShippingVouchers('#tab-all .voucher-group[data-group="shipping"]');
                });
            }
            
            if (showMoreShippingTab) {
                showMoreShippingTab.addEventListener('click', function() {
                    toggleShippingVouchers('#tab-shipping .voucher-group[data-group="shipping"]');
                });
            }
            
            // Discount details toggle
            if (discountLabelElement) {
                discountLabelElement.addEventListener('click', function() {
                    if (discountDetailsPanel.style.display === 'block') {
                        discountDetailsPanel.style.display = 'none';
                    } else {
                        discountDetailsPanel.style.display = 'block';
                        updateDiscountDetails();
                    }
                });
            }
            
            // Close discount details when clicking elsewhere
            document.addEventListener('click', function(event) {
                if (!discountLabelElement.contains(event.target) && !discountDetailsPanel.contains(event.target)) {
                    discountDetailsPanel.style.display = 'none';
                }
            });
        }
		// Update voucher button state based on product selection
        function updateVoucherButtonState() {
            const anyProductSelected = document.querySelectorAll('.product-checkbox:checked').length > 0;
            const voucherButton = document.querySelector('#voucher-select button');
            
            if (voucherButton) {
                if (anyProductSelected) {
                    voucherButton.disabled = false;
                    voucherButton.style.backgroundColor = '#fff0f0';
                    voucherButton.style.color = '#ee4d2d';
                } else {
                    voucherButton.disabled = true;
                    voucherButton.style.backgroundColor = '#f5f5f5';
                    voucherButton.style.color = '#999';
                }
            }
        }

        // Toggle shipping vouchers visibility
        function toggleShippingVouchers(groupSelector) {
            const shippingVouchers = document.querySelectorAll(groupSelector + ' .voucher-item.shipping');
            const showMoreButton = document.querySelector(groupSelector + ' + .group-header .show-more') || 
                                   document.querySelector(groupSelector + ' .show-more');
            
            // Check if products are selected
            const anyProductSelected = document.querySelectorAll('.product-checkbox:checked').length > 0;
            
            // If nothing selected, don't toggle
            if (!anyProductSelected) {
                showToast('Pilih produk terlebih dahulu untuk melihat voucher lainnya');
                return;
            }
            
            const isExpanded = showMoreButton.getAttribute('data-expanded') === 'true';
            
            if (!isExpanded) {
                // Show all vouchers
                shippingVouchers.forEach((item, index) => {
                    if (index > 0) { // Skip first one as it's already visible
                        item.classList.add('visible');
                    }
                });
                showMoreButton.innerHTML = 'Sembunyikan <svg class="show-more-icon" xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="18 15 12 9 6 15"></polyline></svg>';
                showMoreButton.setAttribute('data-expanded', 'true');
            } else {
                // Hide all except first voucher
                shippingVouchers.forEach((item, index) => {
                    if (index > 0) {
                        item.classList.remove('visible');
                    }
                });
                showMoreButton.innerHTML = 'Lihat Lainnya <svg class="show-more-icon" xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="6 9 12 15 18 9"></polyline></svg>';
                showMoreButton.setAttribute('data-expanded', 'false');
            }
        }

        // Update discount details panel
        function updateDiscountDetails() {
            const subtotal = calculateSubtotal();
            
            // Calculate product discount (difference between original price and discounted price)
            let productDiscount = 0;
            const selectedCheckboxes = document.querySelectorAll('.product-checkbox:checked');
            
            selectedCheckboxes.forEach(checkbox => {
                const productId = parseInt(checkbox.dataset.id);
                const product = products.find(p => p.id === productId);
                
                if (product && product.originalPrice) {
                    productDiscount += (product.originalPrice - product.price) * product.quantity;
                }
            });
            
            // Voucher discount amount
          let voucherDiscount = 0;
if (activeVoucher.vouchers) {
    activeVoucher.vouchers.forEach(v => {
        if (v.type !== 'shipping') {
            // PERBAIKAN #5: Penanganan voucher persentase
            if (v.percentage) {
                const percentageValue = parseFloat(v.percentage) / 100;
                const maxDiscount = parseInt(v.value);
                
                // Hitung diskon berdasarkan persentase
                const calculatedDiscount = Math.round(subtotal * percentageValue);
                
                // Batasi diskon dengan nilai maksimum
                voucherDiscount += Math.min(calculatedDiscount, maxDiscount);
            } else {
                voucherDiscount += parseInt(v.value);
            }
        }
    });
}
            
            const totalPayment = subtotal - voucherDiscount - productDiscount;
            
            document.getElementById('subtotal-value').textContent = formatPrice(subtotal);
            document.getElementById('voucher-discount-value').textContent = '-' + formatPrice(voucherDiscount);
            document.getElementById('product-discount-value').textContent = '-' + formatPrice(productDiscount);
            document.getElementById('payment-total').textContent = formatPrice(totalPayment);
        }

        // Initialize voucher page
        function initVoucherPage(){
            // Store all voucher data for search functionality
            const allVouchers = [
                {
                    id: 'discount-1',
                    title: 'Diskon Belanja',
                    desc: 'Diskon Rp50.000 untuk semua kategori produk',
                    value: '50000',
                    min: '200000',
                    group: 'discount',
                    recommended: true
                },
                {
                    id: 'discount-2',
                    title: 'Diskon Fashion',
                    desc: 'Diskon Rp20.000 khusus kategori Fashion',
                    value: '20000',
                    min: '100000',
                    group: 'discount'
                },
                {
                    id: 'shipping-1',
                    title: 'Gratis Ongkir',
                    desc: 'Gratis ongkir hingga Rp20.000 untuk semua toko',
                    value: '20000',
                    type: 'shipping',
                    group: 'shipping',
                    recommended: true
                },
                {
                    id: 'shipping-2',
                    title: 'Gratis Ongkir Reguler',
                    desc: 'Gratis ongkir hingga Rp15.000 dengan pengiriman reguler',
                    value: '15000',
                    type: 'shipping',
                    group: 'shipping'
                },
                {
                    id: 'shipping-3',
                    title: 'Gratis Ongkir Premium',
                    desc: 'Gratis ongkir hingga Rp30.000 dengan min. belanja Rp150.000',
                    value: '30000',
                    type: 'shipping',
                    min: '150000',
                    group: 'shipping'
                },
                {
                    id: 'shipping-4',
                    title: 'Gratis Ongkir COD',
                    desc: 'Gratis ongkir hingga Rp25.000 untuk metode pembayaran COD',
                    value: '25000',
                    type: 'shipping',
                    group: 'shipping'
                  },
                {
                    id: 'cashback-1',
                    title: 'Diskon Elektronik',
                    desc: 'Diskon 10% untuk kategori Elektronik',
                    value: '35000',
                    type: 'percentage',
                    percentage: '10',
                    group: 'cashback'
                },
                {
                    id: 'cashback-2',
                    title: 'Cashback Belanja',
                    desc: 'Cashback 15% dalam bentuk koin Shopee',
                    value: '30000',
                    type: 'cashback',
                    percentage: '15',
                    group: 'cashback',
                    recommended: true
                },
                {
                    id: 'payment-1',
                    title: 'Diskon COD',
                    desc: 'Diskon Rp10.000 untuk pembayaran COD min. Rp100.000',
                    value: '10000',
                    type: 'payment',
                    method: 'cod',
                    min: '100000',
                    group: 'payment'
                },
                {
                    id: 'payment-2',
                    title: 'Diskon Transfer Bank',
                    desc: 'Diskon 5% untuk pembayaran via Transfer Bank',
                    value: '25000',
                    type: 'payment',
                    method: 'bank',
                    percentage: '5',
                    group: 'payment'
                }
            ];
            
            // Keyword suggestions for search
            const searchKeywords = [
                { keyword: "diskon", matches: ["discount", "diskon", "potongan", "potong harga"] },
                { keyword: "ongkir", matches: ["ongkir", "kirim", "shipping", "gratis ongkir", "pengiriman"] },
                { keyword: "cashback", matches: ["cashback", "koin", "coin", "kembalian"] },
                { keyword: "cod", matches: ["cod", "bayar ditempat", "cash on delivery", "bayar tunai"] },
                { keyword: "bank", matches: ["bank", "transfer", "e-wallet", "pembayaran"] },
                { keyword: "minimum", matches: ["min", "minimum", "minimal", "min belanja"] },
                { keyword: "fashion", matches: ["fashion", "pakaian", "baju", "celana"] },
                { keyword: "elektronik", matches: ["elektronik", "gadget", "device", "perangkat"] },
                { keyword: "rekomendasi", matches: ["recommended", "recommend", "rekomendasi", "terbaik"] }
            ];

            // Tab navigation
            const tabs = document.querySelectorAll('.tab');
            const tabContents = document.querySelectorAll('.tab-content');
            
            tabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    const tabId = this.getAttribute('data-tab');
                    
                    // Remove active class from all tabs and contents
                    tabs.forEach(t => t.classList.remove('active'));
                    tabContents.forEach(c => c.classList.remove('active'));
                    
                    // Add active class to clicked tab and corresponding content
                    this.classList.add('active');
                    document.getElementById('tab-' + tabId).classList.add('active');
                    
                    // PERBAIKAN #3: Mengosongkan kolom pencarian saat tab diklik
                    const searchInput = document.getElementById('search-input');
                    if (searchInput) {
                        searchInput.value = '';
                        toggleSearchIcons();
                    }
                    
                    // Reset tampilan voucher saat tab berubah
                    resetVoucherView();
                });
            });
            
            // Voucher checkboxes - limit to 1 per category and 2 total
            const voucherCheckboxes = document.querySelectorAll('.voucher-checkbox');
            const summaryContainer = document.getElementById('selected-vouchers-summary');
            const summaryList = document.getElementById('selected-vouchers-list');
            const footerSummary = document.getElementById('footer-summary');
            
            // Fungsi untuk memeriksa dan memperbarui status voucher
            function updateVoucherStatus() {
                const voucherItems = document.querySelectorAll('.voucher-item');
                const today = new Date();
                
                // Get total from cart
                const subtotal = calculateSubtotal();
                
                voucherItems.forEach(item => {
                    let isDisabled = false;
                    const checkbox = item.querySelector('.voucher-checkbox');
                    const dateDiv = item.querySelector('.voucher-date');
                    
                    // Cek tanggal kedaluwarsa
                    if (dateDiv) {
                        if (dateDiv.hasAttribute('data-expiry-date')) {
                            // Cara manual
                            const dateStr = dateDiv.getAttribute('data-expiry-date');
                            const expiryDate = new Date(dateStr);
                            
                            if (today > expiryDate) {
                                isDisabled = true;
                            }
                        }
                    }
                    
                    // Cek minimum belanja
                    if (checkbox && checkbox.hasAttribute('data-min')) {
                        const minSpend = parseInt(checkbox.getAttribute('data-min'));
                        if (minSpend > subtotal) {
                            isDisabled = true;
                        }
                    }
                    
                    // Terapkan status disabled
                    if (isDisabled) {
                        item.classList.add('disabled');
                        if (checkbox) {
                            checkbox.disabled = true;
                        }
                    } else {
                        item.classList.remove('disabled');
                        if (checkbox) {
                            checkbox.disabled = false;
                        }
                    }
                });
            }
            
            // Set dynamic expiry dates
            function updateExpiryDates() {
                const dateDivs = document.querySelectorAll('.voucher-date');
                
                dateDivs.forEach(div => {
                    // Cek apakah menggunakan data-expiry-date (manual)
                    if (div.hasAttribute('data-expiry-date')) {
                        // Cara manual
                        const dateStr = div.getAttribute('data-expiry-date'); // Format: YYYY-MM-DD
                        const expiryDate = new Date(dateStr);
                        
                        const day = expiryDate.getDate();
                        const month = expiryDate.toLocaleString('id-ID', { month: 'short' });
                        const year = expiryDate.getFullYear();
                        
                        div.textContent = `Berlaku hingga: ${day} ${month} ${year}`;
                    }
                });
            }
            
            // Update selected vouchers in the summary
            function updateSelectedVouchers(selectedVouchers) {
                if (!summaryContainer || !summaryList) return;
                
                // Reset content
                summaryList.innerHTML = '';
                if (footerSummary) footerSummary.innerHTML = '';
                
                // If no vouchers selected
                if (!selectedVouchers || selectedVouchers.length === 0) {
                    summaryContainer.style.display = 'none';
                    if (footerSummary) footerSummary.style.display = 'none';
                    applyBtn.disabled = true;
                    return;
                }
                
                // Display containers
                summaryContainer.style.display = 'block';
                if (footerSummary) footerSummary.style.display = 'block';
                
                // Add each selected voucher to the list
                selectedVouchers.forEach(voucher => {
                    // Determine voucher name and value
                    let voucherName = voucher.title || '';
                    let voucherValue = formatPrice(voucher.value);
                    
                    // Special format for shipping vouchers
                    if (voucher.type === 'shipping') {
                        voucherName = 'Gratis Ongkir Rp20rb';
                    }
                    
                    // Special format for cashback vouchers
                    if (voucher.type === 'cashback' || voucher.percentage) {
						badgeLabel = 'Cashback';
					}
                    
                    // Create voucher item for summary
// Create voucher item for summary
const item = document.createElement('div');
item.className = 'selected-voucher-item';

const nameSpan = document.createElement('span');
nameSpan.className = 'selected-voucher-name';
nameSpan.textContent = voucherName;

const valueSpan = document.createElement('span');
valueSpan.className = 'selected-voucher-value';

// Hitung nilai sebenarnya untuk voucher persentase
if (voucher.percentage) {
    const subtotal = calculateSubtotal();
    const percentageValue = parseFloat(voucher.percentage) / 100;
    const maxDiscount = parseInt(voucher.value);
    
    // Hitung diskon berdasarkan persentase
    const calculatedDiscount = Math.round(subtotal * percentageValue);
    
    // Tampilkan nilai yang lebih kecil antara hasil perhitungan dan maksimum
    const actualDiscount = Math.min(calculatedDiscount, maxDiscount);
    valueSpan.textContent = formatPrice(actualDiscount);
} else {
    valueSpan.textContent = voucherValue;
}
                    
                    // Add close button for voucher
                    const closeBtn = document.createElement('span');
                    closeBtn.className = 'selected-voucher-close';
                    closeBtn.textContent = '×';
                    closeBtn.setAttribute('data-voucher-id', voucher.id || '');
                    closeBtn.setAttribute('data-voucher-type', voucher.type || '');
                    closeBtn.setAttribute('data-voucher-title', voucher.title || '');
                    closeBtn.setAttribute('data-voucher-value', voucher.value || '0');
                    
                    // Add event listener to close button
                    closeBtn.addEventListener('click', function(e) {
                        e.stopPropagation(); // Prevent event bubbling
                        
                        // Get attributes for identifying the voucher
                        const voucherId = this.getAttribute('data-voucher-id') || '';
                        const voucherType = this.getAttribute('data-voucher-type') || '';
                        const voucherTitle = this.getAttribute('data-voucher-title') || '';
                        const voucherValue = this.getAttribute('data-voucher-value') || '0';
                        
                        // Uncheck related checkbox
                        const voucherCheckboxes = document.querySelectorAll('.voucher-checkbox');
                        
                        voucherCheckboxes.forEach(checkbox => {
                            const checkboxTitle = checkbox.getAttribute('data-title') || '';
                            const checkboxValue = checkbox.getAttribute('data-value') || '0';
                            const checkboxType = checkbox.getAttribute('data-type') || 'discount';
                            
                            // Match using multiple attributes for better accuracy
                            if ((checkboxTitle === voucherTitle || (voucherTitle.includes('Fashion') && checkboxTitle.includes('Fashion'))) && 
                                checkboxValue === voucherValue && 
                                checkboxType === voucherType) {
                                checkbox.checked = false;
                            }
                        });
                        
                        // Update selected vouchers
                        const updatedVouchers = getSelectedVouchers();
                        updateSelectedVouchers(updatedVouchers);
                    });
                  item.appendChild(nameSpan);
                    item.appendChild(valueSpan);
                    item.appendChild(closeBtn);
                    summaryList.appendChild(item);
                    
                    // Add to footer summary if exists
                    if (footerSummary) {
                        const footerItem = item.cloneNode(true);
                        // Add event listener to this new close button
                        footerItem.querySelector('.selected-voucher-close').addEventListener('click', function(e) {
                            e.stopPropagation();
                            
                            // Get attributes for identifying the voucher
                            const voucherId = this.getAttribute('data-voucher-id') || '';
                            const voucherType = this.getAttribute('data-voucher-type') || '';
                            const voucherTitle = this.getAttribute('data-voucher-title') || '';
                            const voucherValue = this.getAttribute('data-voucher-value') || '0';
                            
                            // Uncheck related checkbox
                            const voucherCheckboxes = document.querySelectorAll('.voucher-checkbox');
                            
                            voucherCheckboxes.forEach(checkbox => {
                                const checkboxTitle = checkbox.getAttribute('data-title') || '';
                                const checkboxValue = checkbox.getAttribute('data-value') || '0';
                                const checkboxType = checkbox.getAttribute('data-type') || 'discount';
                                
                                // Match using multiple attributes for better accuracy
                                if ((checkboxTitle === voucherTitle || (voucherTitle.includes('Fashion') && checkboxTitle.includes('Fashion'))) && 
                                    checkboxValue === voucherValue && 
                                    checkboxType === voucherType) {
                                    checkbox.checked = false;
                                }
                            });
                            
                            // Update selected vouchers
                            const updatedVouchers = getSelectedVouchers();
                            updateSelectedVouchers(updatedVouchers);
                        });
                        
                        footerSummary.appendChild(footerItem);
                    }
                });
                
                // Enable apply button
                applyBtn.disabled = false;
            }
			// Function to get selected vouchers
            function getSelectedVouchers() {
                const selectedVouchers = [];
                
                voucherCheckboxes.forEach(checkbox => {
                    if (checkbox.checked) {
                        selectedVouchers.push({
                            id: checkbox.getAttribute('id') || '',
                            title: checkbox.getAttribute('data-title'),
                            value: checkbox.getAttribute('data-value'),
                            type: checkbox.getAttribute('data-type') || 'discount',
                            percentage: checkbox.getAttribute('data-percentage'),
                            min: checkbox.getAttribute('data-min') || '0',
                            group: checkbox.getAttribute('data-group')
                        });
                    }
                });
                
                return selectedVouchers;
            }
            
            // Manage checkbox selection
            function manageCheckboxSelection(checkbox) {
                if (checkbox.checked) {
                    const currentGroup = checkbox.getAttribute('data-group');
                    const allChecked = Array.from(voucherCheckboxes).filter(cb => cb.checked);
                    
                    // If more than 2 vouchers selected, uncheck the previously checked one
                    if (allChecked.length > 2) {
                        for (let i = 0; i < allChecked.length; i++) {
                            if (allChecked[i] !== checkbox) {
                                allChecked[i].checked = false;
                                break;
                            }
                        }
                    }
                    
                    const sameGroupChecked = Array.from(voucherCheckboxes).filter(
                        cb => cb.checked && cb !== checkbox && cb.getAttribute('data-group') === currentGroup
                    );
                    
                    if (sameGroupChecked.length > 0) {
                        sameGroupChecked[0].checked = false;
                    }
                }
                
                // Update selected vouchers display
                const selectedVouchers = getSelectedVouchers();
                updateSelectedVouchers(selectedVouchers);
            }
            
            // Add event listeners to checkboxes
            voucherCheckboxes.forEach(checkbox => {
                checkbox.addEventListener('change', function() {
                    manageCheckboxSelection(this);
                });
            });
            
            // Search functionality with suggestions
            const searchInput = document.getElementById('search-input');
            const searchSuggestions = document.getElementById('search-suggestions');
            const clearIcon = document.querySelector('.clear-icon');
            const searchIcon = document.querySelector('.search-icon');
            const noResultsCard = document.getElementById('no-results-card');
            
            // Function to toggle search icon and clear icon based on input content
            function toggleSearchIcons() {
                const hasText = searchInput.value.length > 0;
                
                // Toggle clear icon visibility
                clearIcon.style.display = hasText ? 'block' : 'none';
                
                // Toggle search icon color
                if (hasText) {
                    searchIcon.classList.add('active');
                } else {
                    searchIcon.classList.remove('active');
                }
            }
            
            // Function to clear search input
            function clearSearchInput() {
                searchInput.value = '';
                toggleSearchIcons();
                searchSuggestions.style.display = 'none';
                noResultsCard.style.display = 'none';
                resetVoucherView(); // PERBAIKAN #2: Reset tampilan voucher saat pencarian dihapus
            }
            
            // Add click event for clear icon
            clearIcon.addEventListener('click', clearSearchInput);
            
            // PERBAIKAN #1: Menampilkan grup dan voucher yang sesuai dengan keyword, sembunyikan yang tidak relevan
            function filterVouchersByKeyword(keyword) {
                keyword = keyword.toLowerCase();
                let found = false;
                
                // Reset semua voucher terlebih dahulu
                resetVoucherView();
                
                // Sembunyikan semua grup voucher terlebih dahulu
                const voucherGroups = document.querySelectorAll('.voucher-group');
                voucherGroups.forEach(group => {
                    group.style.display = 'none';
                });
                
                // Temukan voucher yang relevan
                const matchingKeywords = searchKeywords.filter(item => 
                    item.matches.some(match => match.includes(keyword))
                );
                
                // Tampilkan grup berdasarkan kata kunci
                if (matchingKeywords.length > 0) {
                    matchingKeywords.forEach(item => {
                        let groupSelector = '';
                        
                        // Tentukan grup yang perlu ditampilkan berdasarkan keyword
                        switch(item.keyword) {
                            case 'diskon':
                                groupSelector = 'discount';
                                break;
                            case 'ongkir':
                                groupSelector = 'shipping';
                                break;
                            case 'cashback':
                                groupSelector = 'cashback';
                                break;
                            case 'cod':
                            case 'bank':
                                groupSelector = 'payment';
                                break;
                            case 'fashion':
                            case 'elektronik':
                                // Cari voucher yang mengandung keyword spesifik ini
                                const voucherItems = document.querySelectorAll('.voucher-item');
                                voucherItems.forEach(vItem => {
                                    const title = vItem.querySelector('.voucher-title').textContent.toLowerCase();
                                    const desc = vItem.querySelector('.voucher-desc').textContent.toLowerCase();
                                    
                                    if (title.includes(item.keyword) || desc.includes(item.keyword)) {
                                        vItem.classList.add('visible');
                                        vItem.parentNode.style.display = 'block';
                                        found = true;
                                    }
                                });
                                break;
                            case 'rekomendasi':
                                // Tampilkan voucher dengan badge "Recommended"
                                const recommendedItems = document.querySelectorAll('.voucher-item .recommended-badge');
                                recommendedItems.forEach(recItem => {
                                    recItem.closest('.voucher-item').classList.add('visible');
                                    recItem.closest('.voucher-group').style.display = 'block';
                                    found = true;
                                });
                                break;
                            default:
                                break;
                        }
                        
                        if (groupSelector) {
                            const groups = document.querySelectorAll(`.voucher-group[data-group="${groupSelector}"]`);
                            groups.forEach(g => {
                                g.style.display = 'block';
                                g.querySelectorAll('.voucher-item').forEach(v => {
                                    v.classList.add('visible');
                                });
                                found = true;
                            });
                        }
                    });
                } else {
                    // Cari berdasarkan judul dan deskripsi voucher
                    const voucherItems = document.querySelectorAll('.voucher-item');
                    voucherItems.forEach(vItem => {
                        const title = vItem.querySelector('.voucher-title').textContent.toLowerCase();
                        const desc = vItem.querySelector('.voucher-desc').textContent.toLowerCase();
                        
                        if (title.includes(keyword) || desc.includes(keyword)) {
                            vItem.classList.add('visible');
                            vItem.parentNode.style.display = 'block';
                            found = true;
                        }
                    });
                }
                
                // Tampilkan pesan jika tidak ditemukan hasil
                if (!found) {
                    noResultsCard.style.display = 'block';
                } else {
                    noResultsCard.style.display = 'none';
                }
            }
            
            // Reset tampilan voucher ke default
            function resetVoucherView() {
                // Tampilkan semua grup voucher
                const voucherGroups = document.querySelectorAll('.voucher-group');
                voucherGroups.forEach(group => {
                    group.style.display = 'block';
                });
                
                // Reset visibility voucher item
                const voucherItems = document.querySelectorAll('.voucher-item');
                voucherItems.forEach((item, index) => {
                    if (item.classList.contains('shipping')) {
                        // Untuk shipping, hanya tampilkan yang pertama
                        if (index === 0 || item.parentNode.querySelectorAll('.voucher-item.shipping')[0] === item) {
                            item.classList.add('visible');
                        } else {
                            item.classList.remove('visible');
                        }
                    } else {
                        // Untuk voucher lain, tampilkan semua
                        item.classList.add('visible');
                    }
                });
                
                // Sembunyikan pesan no results
                noResultsCard.style.display = 'none';
            }
            
            // Add click event for search icon
            searchIcon.addEventListener('click', function() {
                const searchTerm = searchInput.value.toLowerCase().trim();
                
                if (searchTerm.length > 0) {
                    // Perform search using filter function
                    filterVouchersByKeyword(searchTerm);
                }
            });
            
            // Function to display initial keyword suggestions on focus
            function showInitialSuggestions() {
                searchSuggestions.innerHTML = '';
                searchSuggestions.style.display = 'block';
                noResultsCard.style.display = 'none';
                
                // Add popular keywords suggestions
                const popularKeywords = ['diskon', 'ongkir', 'cashback', 'rekomendasi'];
                
                // Add a heading for suggestions
                const heading = document.createElement('div');
                heading.style.padding = '8px 16px';
                heading.style.fontSize = '12px';
                heading.style.color = '#757575';
                heading.style.borderBottom = '1px solid #f0f0f0';
                heading.textContent = 'Kata Kunci Populer';
                searchSuggestions.appendChild(heading);
                
                popularKeywords.forEach(keyword => {
                    const keywordData = searchKeywords.find(k => k.keyword === keyword);
                    if (keywordData) {
                        const suggestionItem = document.createElement('div');
                        suggestionItem.className = 'suggestion-item';
                        
                        const keywordSpan = document.createElement('div');
                        keywordSpan.className = 'suggestion-keyword';
                        keywordSpan.textContent = `"${keywordData.keyword}"`;
                        
                        suggestionItem.appendChild(keywordSpan);
                        searchSuggestions.appendChild(suggestionItem);
                        
                        suggestionItem.addEventListener('click', function() {
                            searchInput.value = keywordData.keyword;
                            toggleSearchIcons();
                            searchSuggestions.style.display = 'none';
                            
                            // Panggil fungsi filter untuk menampilkan hanya voucher yang relevan
                            filterVouchersByKeyword(keywordData.keyword);
                        });
                    }
                });
                
                // Add some recommended vouchers
                const recommendedHeading = document.createElement('div');
                recommendedHeading.style.padding = '8px 16px';
                recommendedHeading.style.fontSize = '12px';
                recommendedHeading.style.color = '#757575';
                recommendedHeading.style.borderBottom = '1px solid #f0f0f0';
                recommendedHeading.style.borderTop = '1px solid #f0f0f0';
                recommendedHeading.style.marginTop = '8px';
                recommendedHeading.textContent = 'Voucher Rekomendasi';
                searchSuggestions.appendChild(recommendedHeading);
                
                // Get recommended vouchers
                const recommendedVouchers = allVouchers.filter(v => v.recommended).slice(0, 2);
                
                recommendedVouchers.forEach(voucher => {
                    const suggestionItem = document.createElement('div');
                    suggestionItem.className = 'suggestion-item';
                    suggestionItem.setAttribute('data-id', voucher.id);
                    
                    const titleDiv = document.createElement('div');
                    titleDiv.className = 'suggestion-title';
                    titleDiv.textContent = voucher.title;
                    
                    const descDiv = document.createElement('div');
                    descDiv.className = 'suggestion-desc';
                    descDiv.textContent = voucher.desc;
                    
                    suggestionItem.appendChild(titleDiv);
                    suggestionItem.appendChild(descDiv);
                    searchSuggestions.appendChild(suggestionItem);
                    
                    // Add click event
                    suggestionItem.addEventListener('click', function() {
                        // Find related checkbox and check it
                        const relevantCheckbox = findCheckboxById(this.getAttribute('data-id'));
                        if (relevantCheckbox && !relevantCheckbox.disabled && !relevantCheckbox.closest('.voucher-item').classList.contains('disabled')) {
                            relevantCheckbox.checked = true;
                            manageCheckboxSelection(relevantCheckbox);
                        }
                        
                        // Update search input with voucher title
                        searchInput.value = voucher.title;
                        toggleSearchIcons();
                        
                        // Hide suggestions
                        searchSuggestions.style.display = 'none';
                    });
                });
            }
          // Show initial suggestions when search input is focused
            if (searchInput) {
                searchInput.addEventListener('focus', showInitialSuggestions);
                
                // Update search icons when input changes
                searchInput.addEventListener('input', function() {
                    toggleSearchIcons();
                    
                    const searchTerm = this.value.toLowerCase().trim();
                    
                    // PERBAIKAN #2: Reset tampilan voucher saat kolom pencarian kosong
                    if (searchTerm.length === 0) {
                        resetVoucherView();
                        showInitialSuggestions();
                        return;
                    }
                    
                    if (searchTerm.length > 1) {
                        // Reset suggestions
                        searchSuggestions.innerHTML = '';
                        let hasMatches = false;
                        
                        // Hide no results card - only show it when search icon is clicked
                        noResultsCard.style.display = 'none';
                        
                        // Find matching keywords first
                        const matchingKeywords = searchKeywords.filter(item => 
                            item.matches.some(match => match.includes(searchTerm))
                        );
                        
                        // Add keyword suggestions
                        if (matchingKeywords.length > 0) {
                            hasMatches = true;
                            matchingKeywords.forEach(item => {
                                const suggestionItem = document.createElement('div');
                                suggestionItem.className = 'suggestion-item';
                                
                                const keywordSpan = document.createElement('div');
                                keywordSpan.className = 'suggestion-keyword';
                                keywordSpan.textContent = `"${item.keyword}"`;
                                
                                suggestionItem.appendChild(keywordSpan);
                                searchSuggestions.appendChild(suggestionItem);
                                
                                suggestionItem.addEventListener('click', function() {
                                    searchInput.value = item.keyword;
                                    toggleSearchIcons();
                                    searchSuggestions.style.display = 'none';
                                    
                                    // Find vouchers matching this keyword
                                    filterVouchersByKeyword(item.keyword);
                                });
                            });
                        }
                        
                        // Find matching vouchers
                        const matchingVouchers = allVouchers.filter(voucher => 
                            voucher.title.toLowerCase().includes(searchTerm) || 
                            voucher.desc.toLowerCase().includes(searchTerm)
                        );
                        
                        // Add voucher suggestions
                        if (matchingVouchers.length > 0) {
                            hasMatches = true;
                            matchingVouchers.forEach(voucher => {
                                const suggestionItem = document.createElement('div');
                                suggestionItem.className = 'suggestion-item';
                                suggestionItem.setAttribute('data-id', voucher.id);
                                
                                const titleDiv = document.createElement('div');
                                titleDiv.className = 'suggestion-title';
                                titleDiv.textContent = voucher.title;
                                
                                const descDiv = document.createElement('div');
                                descDiv.className = 'suggestion-desc';
                                descDiv.textContent = voucher.desc;
                                
                                suggestionItem.appendChild(titleDiv);
                                suggestionItem.appendChild(descDiv);
                                searchSuggestions.appendChild(suggestionItem);
                                
                                // Add click event
                                suggestionItem.addEventListener('click', function() {
                                    // Find related checkbox and check it
                                    const relevantCheckbox = findCheckboxById(this.getAttribute('data-id'));
                                    if (relevantCheckbox) {
                                        relevantCheckbox.checked = true;
                                        manageCheckboxSelection(relevantCheckbox);
                                    }
                                    
                                    // Update search input with voucher title
                                    searchInput.value = voucher.title;
                                    toggleSearchIcons();
                                    
                                    // Hide suggestions
                                    searchSuggestions.style.display = 'none';
                                });
                            });
                        }
                        
                        // Display or hide suggestions
                        if (hasMatches) {
                            searchSuggestions.style.display = 'block';
                        } else {
                            searchSuggestions.style.display = 'none';
                        }
                    } else if (searchTerm.length === 0) {
                        showInitialSuggestions();
                        noResultsCard.style.display = 'none';
                    } else {
                        searchSuggestions.style.display = 'none';
                        noResultsCard.style.display = 'none';
                    }
                });
                
                // Handle enter key in search input
                searchInput.addEventListener('keyup', function(event) {
                    if (event.key === 'Enter') {
                        searchIcon.click();
                    }
                });
            }
			// Find vouchers that are relevant to a keyword
            function findRelevantVouchers(keyword) {
                keyword = keyword.toLowerCase();
                
                // Map keywords to voucher types
                const keywordMapping = {
                    'diskon': allVouchers.filter(v => v.group === 'discount'),
                    'ongkir': allVouchers.filter(v => v.group === 'shipping'),
                    'cashback': allVouchers.filter(v => v.group === 'cashback'),
                    'cod': allVouchers.filter(v => v.type === 'payment' && v.method === 'cod'),
                    'bank': allVouchers.filter(v => v.type === 'payment' && v.method === 'bank'),
                    'minimum': allVouchers.sort((a, b) => parseInt(a.min || 0) - parseInt(b.min || 0)),
                    'fashion': allVouchers.filter(v => v.title.toLowerCase().includes('fashion') || v.desc.toLowerCase().includes('fashion')),
                    'elektronik': allVouchers.filter(v => v.title.toLowerCase().includes('elektronik') || v.desc.toLowerCase().includes('elektronik')),
                    'rekomendasi': allVouchers.filter(v => v.recommended)
                };
                
                // Return relevant vouchers
                return keywordMapping[keyword] || [];
            }
            
            // Helper to find checkbox by voucher ID
            function findCheckboxById(voucherId) {
                // Map voucher IDs to DOM elements
                const voucherIdMap = {
                    'discount-1': document.querySelector('.voucher-checkbox[data-title="Diskon Belanja"]'),
                    'discount-2': document.querySelector('.voucher-checkbox[data-title="Diskon Fashion"]'),
                    'shipping-1': document.querySelector('.voucher-checkbox[data-title="Gratis Ongkir Rp20rb"]'),
                    'shipping-2': document.querySelector('.voucher-checkbox[data-title="Gratis Ongkir Reguler"]'),
                    'shipping-3': document.querySelector('.voucher-checkbox[data-title="Gratis Ongkir Premium"]'),
                    'shipping-4': document.querySelector('.voucher-checkbox[data-title="Gratis Ongkir COD"]'),
                    'cashback-1':document.querySelector('.voucher-checkbox[data-title="Diskon Elektronik 10%"]'),
                    'cashback-2': document.querySelector('.voucher-checkbox[data-title="Cashback 15%"]'),
                    'payment-1': document.querySelector('.voucher-checkbox[data-title="Diskon COD"]'),
                    'payment-2': document.querySelector('.voucher-checkbox[data-title="Diskon Transfer 5%"]')
                };
                
                return voucherIdMap[voucherId];
            }
            
            // Close suggestions when clicking outside
            document.addEventListener('click', function(event) {
                if (!event.target.closest('#search-input') && 
                    !event.target.closest('#search-suggestions') && 
                    !event.target.closest('.clear-icon') && 
                    !event.target.closest('.search-icon')) {
                    searchSuggestions.style.display = 'none';
                }
            });
            
            // Initialize voucher page
            updateExpiryDates();
            updateVoucherStatus();
            
            // Check if we had previously selected vouchers
            if (activeVoucher && activeVoucher.vouchers && activeVoucher.vouchers.length > 0) {
                // Check the previously selected vouchers
                activeVoucher.vouchers.forEach(voucher => {
                    const checkbox = document.querySelector(`.voucher-checkbox[data-value="${voucher.value}"][data-type="${voucher.type || 'discount'}"]`);
                    if (checkbox) {
                        checkbox.checked = true;
                    }
                });
                
                // Update selected vouchers display
                const selectedVouchers = getSelectedVouchers();
                updateSelectedVouchers(selectedVouchers);
            } else {
                // Reset selected vouchers
                const selectedVouchers = [];
                updateSelectedVouchers(selectedVouchers);
            }
            
            // Initialize toggle search icons
            toggleSearchIcons();

            // Perbaikan: Tambahkan listener khusus untuk tombol close voucher di halaman voucher
            document.querySelectorAll('#selected-vouchers-list .selected-voucher-close, #footer-summary .selected-voucher-close').forEach(btn => {
                btn.addEventListener('click', function(e) {
                    e.stopPropagation();
                    
                    // Dapatkan teks dari parent element (item voucher)
                    const parentItem = this.closest('.selected-voucher-item');
                    const voucherNameEl = parentItem ? parentItem.querySelector('.selected-voucher-name') : null;
                    const voucherName = voucherNameEl ? voucherNameEl.textContent.trim() : '';
                    
                    // Cek apakah ini voucher Fashion
                    if (voucherName.includes('Fashion')) {
                        // Uncheck related checkbox
                        const voucherCheckboxes = document.querySelectorAll('.voucher-checkbox');
                        voucherCheckboxes.forEach(checkbox => {
                            if (checkbox.getAttribute('data-title') === 'Diskon Fashion') {
                                checkbox.checked = false;
                            }
                        });
                        
                        // Update selected vouchers
                        const updatedVouchers = getSelectedVouchers();
                        updateSelectedVouchers(updatedVouchers);
                    }
                });
            });
            
            // PERBAIKAN #4: Sesuaikan padding voucher footer ketika ada voucher dipilih
            applyBtn.addEventListener('click', function() {
                adjustVoucherFooterPadding();
            });
            
            // Fungsi untuk menyesuaikan padding footer ketika ada voucher terpilih
           function adjustVoucherFooterPadding() {
    // Check if we have selected vouchers
    const selectedVouchers = getSelectedVouchers();
    const voucherPageContent = document.querySelector('.voucher-page-content');
    
    if (selectedVouchers.length > 0) {
        // Increase bottom padding to avoid overflow
        voucherPageContent.style.paddingBottom = '100px';
    } else {
        voucherPageContent.style.paddingBottom = '65px';
    }
}
        }

        // Function to display vouchers in the main cart view
        function updateVoucherDisplay(selectedVouchers) {
    const voucherSection = document.querySelector('.voucher-section');
    if (!voucherSection) return;
    
    // If no vouchers selected
    if (!selectedVouchers || selectedVouchers.length === 0) {
        voucherSection.innerHTML = `
            <div class="section-title">
                <svg viewBox="0 0 100 100" width="22" height="22">
                    <path d="M15,40 C15,34.5 19.5,30 25,30 L75,30 C80.5,30 85,34.5 85,40 L85,43 C78,43 78,57 85,57 L85,60 C85,65.5 80.5,70 75,70 L25,70 C19.5,70 15,65.5 15,60 L15,57 C22,57 22,43 15,43 Z" fill="none" stroke="#ee4d2d" stroke-width="3"/>
                    <line x1="40" y1="30" x2="40" y2="70" stroke="#ee4d2d" stroke-width="2" stroke-dasharray="4,2"/>
                    <line x1="60" y1="30" x2="60" y2="70" stroke="#ee4d2d" stroke-width="2" stroke-dasharray="4,2"/>
                </svg>
                <span>Voucher</span>
            </div>
            <div class="section-action" id="voucher-select">
                <button class="voucher-button" ${isAnyProductSelected() ? '' : 'disabled'}>Pilih Voucher</button>
            </div>
        `;
        
        // Add click event listener
        const newVoucherSelect = document.getElementById('voucher-select');
        if (newVoucherSelect) {
            newVoucherSelect.addEventListener('click', function() {
                if (!newVoucherSelect.querySelector('button').disabled) {
                    voucherPage.style.display = 'block';
                    initVoucherPage();
                }
            });
        }
        
        return;
    }
    
    // Display voucher badges - SAMA UNTUK SEMUA JUMLAH VOUCHER
    let badges = '';
    
    selectedVouchers.forEach(voucher => {
        // Tambahkan atribut data yang lebih lengkap untuk identifikasi voucher
        if (voucher.type === 'shipping') {
            badges += `
                <div class="voucher-badge voucher-badge-shipping">
                    Gratis Ongkir
                    <span class="voucher-badge-close" 
                        data-voucher-id="${voucher.id || ''}" 
                        data-voucher-type="${voucher.type || ''}" 
                        data-voucher-title="${voucher.title || ''}"
                        data-voucher-value="${voucher.value || '0'}">×</span>
                </div>
            `;
        } else if (voucher.type === 'cashback' || voucher.percentage) {
            badges += `
                <div class="voucher-badge voucher-badge-discount">
                    Cashback
                    <span class="voucher-badge-close" 
                        data-voucher-id="${voucher.id || ''}" 
                        data-voucher-type="${voucher.type || ''}" 
                        data-voucher-title="${voucher.title || ''}"
                        data-voucher-value="${voucher.value || '0'}">×</span>
                </div>
            `;
        } else {
            badges += `
                <div class="voucher-badge voucher-badge-discount">
                    -${formatPrice(voucher.value)}
                    <span class="voucher-badge-close" 
                        data-voucher-id="${voucher.id || ''}" 
                        data-voucher-type="${voucher.type || ''}" 
                        data-voucher-title="${voucher.title || ''}"
                        data-voucher-value="${voucher.value || '0'}">×</span>
                </div>
            `;
        }
    });
    
    voucherSection.innerHTML = `
        <div class="voucher-badges">
            ${badges}
        </div>
        <div class="section-action" id="voucher-select">
            <button class="voucher-button">Ubah Voucher</button>
        </div>
    `;
            
            // Add event listeners for close buttons - PERBAIKAN TOMBOL CLOSE
            const closeButtons = voucherSection.querySelectorAll('.voucher-badge-close');
            closeButtons.forEach(button => {
                button.addEventListener('click', function(e) {
                    e.stopPropagation(); // Prevent event bubbling
                    
                    // Get explicit voucher details from data attributes
                    const voucherId = this.getAttribute('data-voucher-id') || '';
                    const voucherType = this.getAttribute('data-voucher-type') || '';
                    const voucherTitle = this.getAttribute('data-voucher-title') || '';
                    const voucherValue = this.getAttribute('data-voucher-value') || '0';
                    
                    // Find matching voucher using multiple criteria
                    const matchingVoucher = activeVoucher.vouchers.find(v => {
                        return (
                            // Match by ID if available
                            ((voucherId && v.id === voucherId) || !voucherId) &&
                            // Match by type
                            ((voucherType && (v.type || 'discount') === voucherType) || !voucherType) &&
                            // Match by title - including partial matches for Fashion vouchers
                            ((voucherTitle && (v.title === voucherTitle || 
                                (voucherTitle.includes('Fashion') && v.title && v.title.includes('Fashion')))) || !voucherTitle) &&
                            // Match by value
                            ((voucherValue && v.value === voucherValue) || !voucherValue)
                        );
                    });
                    
                    // If not found with direct matching, try to find by group
                    if (!matchingVoucher && voucherTitle) {
                        // Special case for Fashion vouchers
                        if (voucherTitle.includes('Fashion')) {
                            const fashionVoucher = activeVoucher.vouchers.find(v => 
                                v.title && v.title.includes('Fashion'));
                            
                            if (fashionVoucher) {
                                removeVoucher(fashionVoucher);
                                return;
                            }
                        }
                        
                        // Try to find by similar title
                        const similarVoucher = activeVoucher.vouchers.find(v => 
                            v.title && voucherTitle && 
                            (v.title.includes(voucherTitle) || voucherTitle.includes(v.title)));
                        
                        if (similarVoucher) {
                            removeVoucher(similarVoucher);
                            return;
                        }
                    }
                    
                    // If found by direct matching, remove it
                    if (matchingVoucher) {
                        removeVoucher(matchingVoucher);
                    } else {
                        // Last resort: just remove the first voucher that matches the type
                        const fallbackVoucher = activeVoucher.vouchers.find(v => 
                            (v.type || 'discount') === (voucherType || 'discount'));
                        
                        if (fallbackVoucher) {
                            removeVoucher(fallbackVoucher);
                        }
                    }
                });
            });
            
            // Add click event for change button
            const newVoucherSelect = document.getElementById('voucher-select');
            if (newVoucherSelect) {
                newVoucherSelect.addEventListener('click', function() {
                    voucherPage.style.display = 'block';
                    initVoucherPage();
                });
            }
        }
        
        // Check if any product is selected
        function isAnyProductSelected() {
            return document.querySelectorAll('.product-checkbox:checked').length > 0;
        }

        // Toggle all products in a shop
        function toggleShopProducts(shopId, checked) {
            const shopProducts = document.querySelectorAll(`.product-checkbox[data-shop-id="${shopId}"]`);
            shopProducts.forEach(checkbox => {
                checkbox.checked = checked;
            });
        }

        // Update shop checkbox state based on product checkboxes
        function updateShopCheckboxState(shopId) {
            const shopProducts = document.querySelectorAll(`.product-checkbox[data-shop-id="${shopId}"]`);
            const shopCheckbox = document.querySelector(`.shop-checkbox-input[data-shop-id="${shopId}"]`);
            
            let allChecked = true;
            let allUnchecked = true;
            
            shopProducts.forEach(checkbox => {
                if (checkbox.checked) {
                    allUnchecked = false;} else {
                    allChecked = false;
                }
            });
            
            if (shopCheckbox) {
                shopCheckbox.checked = allChecked;
                // Indeterminate state could be added here if needed
                // shopCheckbox.indeterminate = !allChecked && !allUnchecked;
            }
        }
		// Open variant modal
        function openVariantModal(event) {
            const productId = parseInt(event.currentTarget.getAttribute('data-id'));
            const product = products.find(p => p.id === productId);
            if (product) {
                currentVariantProduct = product;
                
                // Populate color options
                populateVariantOptions(colorOptions, product.colorOptions, product.variant);
                
                // Populate size options if available
                if (product.sizeOptions && product.sizeOptions.length > 0) {
                    sizeOptions.parentNode.style.display = 'block';
                    populateVariantOptions(sizeOptions, product.sizeOptions, product.variant);
                } else {
                    sizeOptions.parentNode.style.display = 'none';
                }
                
                // Show modal
                variantModal.style.display = 'flex';
            }
        }

        // Populate variant options without highlighting other than selected
       function populateVariantOptions(container, options, currentVariant) {
            container.innerHTML = '';
            
            options.forEach((option) => {
                const div = document.createElement('div');
                div.className = 'variant-option';
                div.textContent = option;
                div.setAttribute('data-value', option);
                
                // Check if this option is currently selected
                const variantParts = currentVariant.split(', ');
                if (variantParts.includes(option)) {
                    div.classList.add('selected');
                } else {
                    div.classList.remove('selected');
                }
                div.addEventListener('click', function() {
                    // Deselect all siblings
                    const siblings = this.parentNode.querySelectorAll('.variant-option');
                    siblings.forEach(sib => sib.classList.remove('selected'));
                    
                    // Select this option
                    this.classList.add('selected');
                });
                
                container.appendChild(div);
            });
        }

        // Update product variant
        function updateProductVariant(product) {
            // Get selected color
            const selectedColor = colorOptions.querySelector('.variant-option.selected');
            let newVariant = selectedColor ? selectedColor.getAttribute('data-value') : '';
            
            // Get selected size if available
            if (sizeOptions.parentNode.style.display !== 'none') {
                const selectedSize = sizeOptions.querySelector('.variant-option.selected');
                if (selectedSize) {
                    newVariant += ', ' + selectedSize.getAttribute('data-value');
                }
            }
            
            // Update product variant
            if (newVariant) {
                product.variant = newVariant;
                
                // Update display
                const variantElement = document.querySelector(`.item-variant[data-id="${product.id}"]`);
                if (variantElement) {
                    variantElement.textContent = newVariant;
                }
            }
        }

        // Increase product quantity
        function increaseQuantity(event) {
            const productId = parseInt(event.currentTarget.dataset.id);
            const product = products.find(p => p.id === productId);
            
            if (product) {
                product.quantity++;
                updateProductDisplay(product);
                updateTotalPrice();
                
                // Reset active vouchers when product quantity is changed
                resetActiveVouchers();
            }
        }

        // Decrease product quantity
        function decreaseQuantity(event) {
            const productId = parseInt(event.currentTarget.dataset.id);
            const product = products.find(p => p.id === productId);
            
            if (product && product.quantity > 0) {
                if (product.quantity === 1) {
                    // Show confirmation modal
                    currentProductToRemove = product;
                    confirmModal.style.display = 'flex';
                } else {
                    product.quantity--;
                    updateProductDisplay(product);
                    updateTotalPrice();
                    
                    // Reset active vouchers when product quantity is changed
                    resetActiveVouchers();
                }
            }
        }
        
        // PERBAIKAN #1: Reset voucher pilihan saat produk diubah
        function resetActiveVouchers() {
            // Hapus semua voucher aktif
            activeVoucher = {
                code: null,
                discount: 0,
                minPurchase: 0,
                vouchers: []
            };
            
            // Update tampilan voucher
            updateVoucherDisplay([]);
            
            // Reset voucher checkboxes
            const voucherCheckboxes = document.querySelectorAll('.voucher-checkbox');
            voucherCheckboxes.forEach(checkbox => {
                checkbox.checked = false;
            });
        }

        // Update product display after quantity change
        function updateProductDisplay(product) {
            const cartItem = document.querySelector(`.cart-item[data-id="${product.id}"]`);
            if (cartItem) {
                const quantityInput = cartItem.querySelector('.quantity-input');
                quantityInput.value = product.quantity;
            }
        }

        // Remove product from cart
        function removeProduct(product) {
            const cartItem = document.querySelector(`.cart-item[data-id="${product.id}"]`);
            if (cartItem) {
                cartItem.remove();
                
                // Also remove from products array
                const index = products.findIndex(p => p.id === product.id);
                if (index !== -1) {
                    products.splice(index, 1);
                }
                
                // Update cart count
                const cartCountElement = document.getElementById('cart-count');
                cartCountElement.textContent = `(${products.length})`;
                
                // Check if this was the last product in the shop
                const shopId = product.shopId;
                const shopProducts = products.filter(p => p.shopId === shopId);
                
                // If no more products from this shop, remove the shop header
                if (shopProducts.length === 0) {
                    const shopGroup = document.querySelector(`.shop-group[data-shop-id="${shopId}"]`);
                    if (shopGroup) {
                        shopGroup.remove();
                    }
                }
                
                // Update shop checkbox state
                updateShopCheckboxState(shopId);
                
                // Update total price
                updateTotalPrice();
                
                // Reset active vouchers when product is removed
                resetActiveVouchers();
            }
        }

        // Remove voucher from active vouchers
        function removeVoucher(voucher) {
            // Reset active voucher if no vouchers will be left
            if (activeVoucher.vouchers.length <= 1) {
                activeVoucher = {
                    code: null,
                    discount: 0,
                    minPurchase: 0,
                    vouchers: []
                };
                
                // Update display
                updateVoucherDisplay([]);
            } else {
                // Remove this particular voucher
                activeVoucher.vouchers = activeVoucher.vouchers.filter(v => 
                    v.value !== voucher.value || v.type !== voucher.type
                );
                
                // Recalculate discount
                let totalDiscount = 0;
                activeVoucher.vouchers.forEach(v => {
                    totalDiscount += parseInt(v.value);
                });
                
                activeVoucher.discount = totalDiscount;
                
                // Update display
                updateVoucherDisplay(activeVoucher.vouchers);
            }
			 // TAMBAHAN KODE: Uncheck checkbox yang sesuai di halaman voucher
    // Cari checkbox yang sesuai berdasarkan value dan type dari voucher
    const voucherCheckboxes = document.querySelectorAll('.voucher-checkbox');
    
    voucherCheckboxes.forEach(checkbox => {
        const checkboxValue = checkbox.getAttribute('data-value');
        const checkboxType = checkbox.getAttribute('data-type') || 'discount';
        
        // Jika value dan type (atau group) cocok, hapus tanda ceklis
        if (checkboxValue === voucher.value && 
            (checkboxType === voucher.type || checkbox.getAttribute('data-group') === voucher.group)) {
            checkbox.checked = false;
        }
    });
            
            // Update total price to reflect voucher removal
            updateTotalPrice();
        }

        // Calculate subtotal (cart total without discount)
        function calculateSubtotal() {
            const selectedCheckboxes = document.querySelectorAll('.product-checkbox:checked');
            let subtotal = 0;
            
            selectedCheckboxes.forEach(checkbox => {
                const productId = parseInt(checkbox.dataset.id);
                const product = products.find(p => p.id === productId);
                
                if (product) {
                    // PERBAIKAN #2: Untuk subtotal gunakan harga coret jika ada, jika tidak gunakan harga orange
                    if (product.originalPrice) {
                        subtotal += product.originalPrice * product.quantity;
                    } else {
                        subtotal += product.price * product.quantity;
                    }
                }
            });
            
            return subtotal;
        }

        // Calculate and update total price
        function updateTotalPrice() {
            // PERBAIKAN #2: Ubah cara menghitung subtotal dan total
            const subtotal = calculateSubtotal();
            const selectedCount = document.querySelectorAll('.product-checkbox:checked').length;
            
            // Calculate product discount (difference between original price and discounted price)
            let productDiscount = 0;
            const selectedCheckboxes = document.querySelectorAll('.product-checkbox:checked');
            
            selectedCheckboxes.forEach(checkbox => {
                const productId = parseInt(checkbox.dataset.id);
                const product = products.find(p => p.id === productId);
                
                if (product && product.originalPrice) {
                    productDiscount += (product.originalPrice - product.price) * product.quantity;
                }
            });
            
            // Voucher discount amount
           
            
            // Check if we have vouchers applied
            let voucherDiscount = 0;
if (activeVoucher.vouchers) {
    activeVoucher.vouchers.forEach(v => {
        if (v.type !== 'shipping') {
            // PERBAIKAN #5: Penanganan voucher persentase
            if (v.percentage) {
                const percentageValue = parseFloat(v.percentage) / 100;
                const maxDiscount = parseInt(v.value);
                
                // Hitung diskon berdasarkan persentase
                const calculatedDiscount = Math.round(subtotal * percentageValue);
                
                // Batasi diskon dengan nilai maksimum
                voucherDiscount += Math.min(calculatedDiscount, maxDiscount);
            } else {
                voucherDiscount += parseInt(v.value);
            }
        }
    });
}
            
            // Total discount (voucher + product)
            const totalDiscount = voucherDiscount + productDiscount;
            
            // Calculate final total
            const total = Math.max(0, subtotal - totalDiscount);
            
            totalAmountElement.textContent = formatPrice(total);
            
            // Show total discount in label if there's any discount
            if (totalDiscount > 0) {
                discountLabelElement.style.display = 'flex';
                discountDisplayElement.textContent = formatPrice(totalDiscount);
                
                // Update discount details
                updateDiscountDetails();
            } else {
                discountLabelElement.style.display = 'none';
            }
            
            // Update checkout button text to include item count
            if (selectedCount > 0) {
                checkoutBtn.textContent = `Checkout (${selectedCount})`;
                checkoutBtn.classList.add('active');
            } else {
                checkoutBtn.textContent = 'Checkout';
                checkoutBtn.classList.remove('active');
            }
            
            // Update select all checkbox
            updateSelectAllCheckbox();
            
            // Update voucher button state
            updateVoucherButtonState();
        }

        // Update select all checkbox state
        function updateSelectAllCheckbox() {
            const allCheckboxes = document.querySelectorAll('.product-checkbox');
            const checkedCheckboxes = document.querySelectorAll('.product-checkbox:checked');
            
            selectAllCheckbox.checked = allCheckboxes.length > 0 && allCheckboxes.length === checkedCheckboxes.length;
            
            // Also update all shop checkboxes
            const shopIds = [...new Set(products.map(p => p.shopId))];
            shopIds.forEach(shopId => {
                updateShopCheckboxState(shopId);
            });
        }

        // Apply selected vouchers to cart
        function applyVouchersToCart(selectedVouchers) {
            // Reset active voucher
            activeVoucher = {
                code: null,
                discount: 0,
                minPurchase: 0,
                vouchers: []
            };
            
            // Apply the vouchers
            if (selectedVouchers.length > 0) {
                // Store voucher details
                activeVoucher.vouchers = selectedVouchers;
                
                // Calculate total discount
                let totalDiscount = 0;
                let voucherTitles = [];
                let minPurchase = 0;
                
                selectedVouchers.forEach(voucher => {
    // Calculate discount for percentage vouchers
    if (voucher.percentage) {
        const subtotal = calculateSubtotal();
        const percentageValue = parseFloat(voucher.percentage) / 100;
        const maxDiscount = parseInt(voucher.value);
        
        // Hitung diskon berdasarkan persentase
        const calculatedDiscount = Math.round(subtotal * percentageValue);
        
        // Batasi diskon dengan nilai maksimum
        totalDiscount += Math.min(calculatedDiscount, maxDiscount);
    } else {
        // Add to total discount
        totalDiscount += parseInt(voucher.value);
    }
    
    // Add to voucher titles
    voucherTitles.push(voucher.title);
    
    // Set minimum purchase (use the highest)
    const voucherMin = parseInt(voucher.min) || 0;
    if (voucherMin > minPurchase) {
        minPurchase = voucherMin;
    }
});
                
                // Update active voucher
                activeVoucher.code = voucherTitles.join(", ");
                activeVoucher.discount = totalDiscount;
                activeVoucher.minPurchase = minPurchase;
                
                // Update voucher display in main cart
                updateVoucherDisplay(selectedVouchers);
                
                // Show discount in total price section
                discountLabelElement.style.display = 'flex';
                discountDisplayElement.textContent = formatPrice(totalDiscount);
                
                // Update discount details
                updateDiscountDetails();
            } else {
                // Reset display
                updateVoucherDisplay([]);
                
                // Hide discounts
                discountInfoElement.style.display = 'none';
                discountLabelElement.style.display = 'none';
            }
            
            // Update total price
            updateTotalPrice();
        }

        // Get selected vouchers from checkboxes
        function getSelectedVouchers() {
            const selectedVouchers = [];
            const voucherCheckboxes = document.querySelectorAll('.voucher-checkbox:checked');
            
            voucherCheckboxes.forEach(checkbox => {
                selectedVouchers.push({
                    id: checkbox.getAttribute('id') || '',
                    title: checkbox.getAttribute('data-title'),
                    value: checkbox.getAttribute('data-value'),
                    type: checkbox.getAttribute('data-type') || 'discount',
                    percentage: checkbox.getAttribute('data-percentage'),
                    min: checkbox.getAttribute('data-min') || '0',
                    group: checkbox.getAttribute('data-group')
                });
            });
            
            return selectedVouchers;
        }

        // Toast notification
        function showToast(message) {
            const toast = document.getElementById('toast');
            if (!toast) return;
            
            toast.textContent = message;
            toast.style.display = 'block';
            
            setTimeout(() => {
                toast.style.display = 'none';
            }, 3000);
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            initEventListeners();
            updateTotalPrice();
            updateVoucherButtonState();
        });
    </script>
</body>
</html>
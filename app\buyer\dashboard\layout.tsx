"use client"

import type React from "react"

import { useEffect } from "react"
import { useRouter } from "next/navigation"
import { BuyerHeader } from "@/components/buyer/buyer-header"
import { BuyerSidebar } from "@/components/buyer/buyer-sidebar"
import { useSession } from "@/hooks/use-session"
import { Skeleton } from "@/components/ui/skeleton"
import { SidebarProvider } from "@/components/ui/sidebar"
import { BottomNavigation } from "@/components/buyer/mobile/bottom-navigation"
import { useIsMobile } from "@/hooks/use-mobile"

export default function BuyerDashboardLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const { user, loading } = useSession()
  const router = useRouter()
  const isMobile = useIsMobile()

  useEffect(() => {
    if (!loading && !user && typeof window !== 'undefined') {
      if (!window.location.pathname.includes('/login')) {
        router.push("/login")
      }
    }
    // Tambahkan validasi role buyer di sini jika diperlukan
  }, [loading, user, router])

  if (loading) {
    return (
      <div className="flex min-h-screen flex-col">
        <div className="h-14 border-b border-border/40">
          <Skeleton className="h-full w-full" />
        </div>
        <div className="flex flex-1">
          <div className="w-64 border-r border-border/40">
            <Skeleton className="h-full w-full" />
          </div>
          <main className="flex-1 p-6">
            <Skeleton className="h-8 w-64 mb-6" />
            <Skeleton className="h-64 w-full" />
          </main>
        </div>
      </div>
    )
  }

  if (!user) {
    return null
  }

  return (
    <SidebarProvider>
      <div className="flex min-h-screen flex-col bg-background">
        <BuyerHeader />
        <div className="flex flex-1">
          {/* Sidebar untuk desktop (tampilan yang sama dengan tenant) */}
          {!isMobile && <BuyerSidebar />}
          <main className="flex-1 overflow-auto pb-20 md:pb-6 p-6">{children}</main>
        </div>
        {/* Bottom Navigation hanya ditampilkan pada mobile */}
        {isMobile && <BottomNavigation />}
      </div>
    </SidebarProvider>
  )
}

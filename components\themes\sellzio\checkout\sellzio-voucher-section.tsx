"use client"

import React from 'react'

interface Voucher {
  id: string
  name: string
  amount: number
  type: string
}

interface SellzioVoucherSectionProps {
  vouchers: Voucher[]
  onSelect: () => void
}

export const SellzioVoucherSection: React.FC<SellzioVoucherSectionProps> = ({
  vouchers,
  onSelect
}) => {
  const hasVouchers = vouchers && vouchers.length > 0

  return (
    <div className="sellzio-checkout-section">
      <div className="sellzio-voucher-option" onClick={onSelect}>
        <div className="sellzio-voucher-label">
          <span className="sellzio-voucher-icon">🎟️</span>
          <span>Voucher</span>
        </div>
        {hasVouchers ? (
          <div className="sellzio-voucher-applied">
            <span className="sellzio-voucher-count">
              {vouchers.length} voucher diterapkan
            </span>
            <span className="sellzio-edit-btn">Ubah</span>
          </div>
        ) : (
          <span className="sellzio-edit-btn">Pilih Voucher</span>
        )}
      </div>
    </div>
  )
}

"use client"

import type React from "react"
import { TenantThemeProvider } from "@/components/tenant/tenant-theme-provider"

interface SellzioTenantWrapperProps {
  tenantSlug: string
  children: React.ReactNode
}

export function SellzioTenantWrapper({ tenantSlug, children }: SellzioTenantWrapperProps) {
  return (
    <TenantThemeProvider tenantId={tenantSlug}>
      <div className="sellzio-tenant-layout">
        {/* Inject Sellzio specific styles */}
        <style jsx global>{`
          /* Sellzio tenant specific overrides */
          :root {
            --sellzio-primary: #ee4d2d;
            --sellzio-secondary: #ff6b35;
            --sellzio-accent: #f5a623;
          }
          
          .sellzio-tenant-layout {
            min-height: 100vh;
            background: #f5f5f5;
          }
          
          /* Ensure Sellzio styles take precedence */
          .sellzio-tenant-layout * {
            box-sizing: border-box;
          }
          
          /* Mobile optimizations for tenant */
          @media (max-width: 768px) {
            .sellzio-tenant-layout {
              padding: 0;
            }
          }
        `}</style>
        {children}
      </div>
    </TenantThemeProvider>
  )
}

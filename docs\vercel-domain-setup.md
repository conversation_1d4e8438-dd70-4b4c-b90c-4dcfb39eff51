# Vercel Domain Setup untuk Subdomain

## Masalah: DEPLOYMENT_NOT_FOUND

Ketika subdomain sudah dikonfigurasi di DNS (Cloudflare) tapi masih menampilkan error:
```
404. NOT_FOUND
Code: DEPLOYMENT_NOT_FOUND
```

**Root Cause:** Vercel belum dikonfigurasi untuk menerima subdomain tersebut.

## Solusi

### 1. Manual Setup (Recommended)

#### A. Tambah Wildcard Domain di Vercel
1. **Buka Vercel Project Dashboard**
   - Go to: https://vercel.com/sellzios-projects/v0-sellzio
   - Atau project Sellzio kamu

2. **Add Wildcard Domain**
   - Settings → Domains
   - Add domain: `*.sellzio.my.id`
   - Vercel akan memberikan instruksi DNS

3. **Verifikasi Domain**
   - Vercel akan meminta verifikasi ownership
   - Ikuti instruksi yang diberikan

#### B. Tambah Domain Spesifik
Alternatif jika wildcard tidak bisa:
- Add domain: `tes.sellzio.my.id`
- Add domain: `app.sellzio.my.id`
- Dst untuk setiap subdomain yang dibutuhkan

### 2. Programmatic Setup (Advanced)

#### A. Setup Vercel API Token
1. **Generate Vercel Token**
   - Go to: https://vercel.com/account/tokens
   - Create new token dengan scope: `read:project`, `write:project`

2. **Add Environment Variables**
   ```bash
   VERCEL_TOKEN=your_vercel_token_here
   VERCEL_PROJECT_ID=prj_IMIuXcUvgE7Z9BRfwQFi1fxgkwmq
   ```

#### B. Gunakan Debug Page
1. **Akses Debug Page**
   - Go to: http://localhost:3001/admin/dashboard/debug

2. **Load Vercel Domains**
   - Click "Load Vercel Domains" untuk melihat domain yang sudah ada

3. **Add Domain**
   - Input domain: `*.sellzio.my.id` atau `tes.sellzio.my.id`
   - Click "Add Domain"

## Verifikasi Setup

### 1. Cek Domain di Vercel
- Vercel Dashboard → Settings → Domains
- Pastikan domain/wildcard sudah terdaftar
- Status harus "Verified"

### 2. Test Subdomain
- Tunggu 2-5 menit setelah add domain
- Akses: https://tes.sellzio.my.id
- Seharusnya tidak lagi menampilkan DEPLOYMENT_NOT_FOUND

### 3. DNS Propagation
- Gunakan: https://dnschecker.org
- Check: `tes.sellzio.my.id`
- Pastikan resolve ke Vercel IPs

## Troubleshooting

### Domain Tidak Bisa Ditambah
- **Cause:** Vercel token tidak valid atau expired
- **Solution:** Generate token baru dengan permissions yang benar

### Wildcard Tidak Bekerja
- **Cause:** Beberapa Vercel plans tidak support wildcard
- **Solution:** Add domain spesifik satu per satu

### Masih 404 Setelah Add Domain
- **Cause:** DNS belum propagasi atau salah konfigurasi
- **Solution:** 
  1. Tunggu 5-10 menit
  2. Clear browser cache
  3. Cek DNS record di Cloudflare

## Best Practices

### 1. Gunakan Wildcard
- Add `*.sellzio.my.id` untuk cover semua subdomain
- Lebih efisien daripada add satu per satu

### 2. Monitor Domain Status
- Gunakan debug page untuk monitoring
- Check status domain secara berkala

### 3. Automation
- Integrate Vercel API dengan tenant creation
- Auto-add domain ketika tenant buat subdomain baru

## API Reference

### Add Domain to Vercel
```javascript
POST /api/vercel/add-domain
{
  "domain": "*.sellzio.my.id"
}
```

### List Vercel Domains
```javascript
GET /api/vercel/add-domain
```

## Environment Variables Required

```bash
# Vercel API
VERCEL_TOKEN=your_vercel_token
VERCEL_PROJECT_ID=prj_IMIuXcUvgE7Z9BRfwQFi1fxgkwmq

# Cloudflare (untuk DNS)
CLOUDFLARE_API_TOKEN=your_cloudflare_token
CLOUDFLARE_ZONE_ID=your_zone_id
```

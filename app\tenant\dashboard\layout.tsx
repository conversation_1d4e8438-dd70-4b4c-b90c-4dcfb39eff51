"use client"

import type React from "react"

import { useEffect } from "react"
import { useRouter } from "next/navigation"
import { TenantHeader } from "@/components/tenant/tenant-header"
import { TenantSidebar } from "@/components/tenant/tenant-sidebar"
import { useSession } from "@/hooks/use-session"
import { Skeleton } from "@/components/ui/skeleton"
import { SidebarProvider } from "@/components/ui/sidebar"
import { ImpersonationBanner } from "@/components/admin/tenants/impersonation-banner"
import { NotificationsProvider } from "@/components/providers/notifications-provider"

export default function TenantDashboardLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const { user, loading } = useSession()
  const router = useRouter()

  useEffect(() => {
    if (!loading && !user && typeof window !== 'undefined') {
      // Only redirect if not already on login page
      if (!window.location.pathname.includes('/login')) {
        router.push("/login")
      }
    }
    // Tambahkan validasi role tenant di sini jika diperlukan
  }, [loading, user, router])

  if (loading) {
    return (
      <div className="flex min-h-screen flex-col">
        <div className="h-14 border-b border-border/40">
          <Skeleton className="h-full w-full" />
        </div>
        <div className="flex flex-1">
          <div className="w-64 border-r border-border/40">
            <Skeleton className="h-full w-full" />
          </div>
          <main className="flex-1 p-6">
            <Skeleton className="h-8 w-64 mb-6" />
            <Skeleton className="h-64 w-full" />
          </main>
        </div>
      </div>
    )
  }

  if (!user) {
    return null
  }

  return (
    <NotificationsProvider>
      <SidebarProvider>
        <div className="flex min-h-screen flex-col bg-background">
          <ImpersonationBanner />
          <TenantHeader />
          <div className="flex flex-1">
            <TenantSidebar />
            <main className="flex-1 overflow-auto p-6">{children}</main>
          </div>
        </div>
      </SidebarProvider>
    </NotificationsProvider>
  )
}

"use client"

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { useToast } from '@/hooks/use-toast'
import { Copy, ExternalLink, CheckCircle, AlertCircle, Globe, Server, Shield, Save, RefreshCw } from 'lucide-react'

interface PlatformSetting {
  id: number
  key: string
  value: string
  description: string
  category: string
  updatedAt: string
  updatedBy: string
}

export function PlatformSettings() {
  const [settings, setSettings] = useState<PlatformSetting[]>([])
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [editedSettings, setEditedSettings] = useState<Record<string, string>>({})
  const { toast } = useToast()

  useEffect(() => {
    fetchSettings()
  }, [])

  const fetchSettings = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/admin/platform-settings')
      const data = await response.json()

      if (data.success) {
        setSettings(data.settings)
        // Initialize edited settings
        const edited: Record<string, string> = {}
        data.settings.forEach((setting: PlatformSetting) => {
          edited[setting.key] = setting.value
        })
        setEditedSettings(edited)
      }
    } catch (error) {
      console.error('Error fetching platform settings:', error)
      toast({
        title: "Error",
        description: "Failed to fetch platform settings",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  const handleSaveSettings = async () => {
    try {
      setSaving(true)

      // Prepare settings for update
      const updatedSettings = settings.map(setting => ({
        key: setting.key,
        value: editedSettings[setting.key] || setting.value,
        description: setting.description,
        category: setting.category
      }))

      console.log('🔥 CLIENT: Saving platform settings:', updatedSettings)

      const response = await fetch('/api/admin/platform-settings', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ settings: updatedSettings })
      })

      console.log('🔥 CLIENT: Response status:', response.status)

      const data = await response.json()
      console.log('🔥 CLIENT: Response data:', data)

      if (response.ok && data.success) {
        await fetchSettings() // Refresh data
        toast({
          title: "Success",
          description: "Platform settings updated successfully",
        })
      } else {
        throw new Error(data.error || `HTTP ${response.status}: Failed to update settings`)
      }
    } catch (error) {
      console.error('🔥 CLIENT: Error saving platform settings:', error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to save platform settings",
        variant: "destructive"
      })
    } finally {
      setSaving(false)
    }
  }

  const handleInputChange = (key: string, value: string) => {
    setEditedSettings(prev => ({
      ...prev,
      [key]: value
    }))
  }

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
    toast({
      title: "Copied",
      description: "Copied to clipboard",
    })
  }

  const getDNSInstructions = () => {
    const platformDomain = editedSettings['platform_domain'] || 'sellzio.com'
    const platformIP = editedSettings['platform_ip'] || '127.0.0.1'
    
    return `DNS Configuration for ${platformDomain}:

A Record:
Name: @
Value: ${platformIP}

A Record:
Name: *
Value: ${platformIP}

CNAME Record:
Name: www
Value: ${platformDomain}

CNAME Record:
Name: api
Value: ${platformDomain}

CNAME Record:
Name: cdn
Value: ${platformDomain}`
  }

  const copyDNSInstructions = () => {
    copyToClipboard(getDNSInstructions())
  }

  if (loading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-500"></div>
          <span className="ml-2 text-gray-600">Loading platform settings...</span>
        </CardContent>
      </Card>
    )
  }

  const domainSettings = settings.filter(s => s.category === 'domain')
  const dnsSettings = settings.filter(s => s.category === 'dns')
  const securitySettings = settings.filter(s => s.category === 'security')

  return (
    <div className="space-y-6">
      {/* Domain Configuration */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Globe className="h-5 w-5" />
            Domain Configuration
          </CardTitle>
          <CardDescription>
            Configure your main platform domain and IP address. All tenant subdomains will use these settings.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {domainSettings.map((setting) => (
            <div key={setting.key} className="space-y-2">
              <Label htmlFor={setting.key}>
                {setting.description}
                <Badge variant="outline" className="ml-2 text-xs">
                  {setting.key}
                </Badge>
              </Label>
              <div className="flex gap-2">
                <Input
                  id={setting.key}
                  value={editedSettings[setting.key] || ''}
                  onChange={(e) => handleInputChange(setting.key, e.target.value)}
                  placeholder={setting.value}
                />
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={() => copyToClipboard(editedSettings[setting.key] || setting.value)}
                >
                  <Copy className="h-4 w-4" />
                </Button>
              </div>
            </div>
          ))}
        </CardContent>
      </Card>

      {/* DNS Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Server className="h-5 w-5" />
            DNS Settings
          </CardTitle>
          <CardDescription>
            Configure name servers and DNS provider settings
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {dnsSettings.map((setting) => (
            <div key={setting.key} className="space-y-2">
              <Label htmlFor={setting.key}>
                {setting.description}
                <Badge variant="outline" className="ml-2 text-xs">
                  {setting.key}
                </Badge>
              </Label>
              <div className="flex gap-2">
                <Input
                  id={setting.key}
                  value={editedSettings[setting.key] || ''}
                  onChange={(e) => handleInputChange(setting.key, e.target.value)}
                  placeholder={setting.value}
                />
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => copyToClipboard(editedSettings[setting.key] || setting.value)}
                >
                  <Copy className="h-4 w-4" />
                </Button>
              </div>
            </div>
          ))}
        </CardContent>
      </Card>

      {/* Security Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Security Settings
          </CardTitle>
          <CardDescription>
            SSL and security configuration for the platform
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {securitySettings.map((setting) => (
            <div key={setting.key} className="space-y-2">
              <Label htmlFor={setting.key}>
                {setting.description}
                <Badge variant="outline" className="ml-2 text-xs">
                  {setting.key}
                </Badge>
              </Label>
              <div className="flex gap-2">
                <Input
                  id={setting.key}
                  value={editedSettings[setting.key] || ''}
                  onChange={(e) => handleInputChange(setting.key, e.target.value)}
                  placeholder={setting.value}
                />
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={() => copyToClipboard(editedSettings[setting.key] || setting.value)}
                >
                  <Copy className="h-4 w-4" />
                </Button>
              </div>
            </div>
          ))}
        </CardContent>
      </Card>

      {/* DNS Instructions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Server className="h-5 w-5" />
            DNS Configuration Instructions
          </CardTitle>
          <CardDescription>
            Configure these DNS records in your domain provider (Vercel, Cloudflare, etc.)
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>DNS Records Required</AlertTitle>
            <AlertDescription className="mt-2">
              <pre className="bg-gray-50 p-3 rounded text-sm font-mono border whitespace-pre-wrap">
                {getDNSInstructions()}
              </pre>
              <div className="flex gap-2 mt-3">
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={copyDNSInstructions}
                >
                  <Copy className="h-4 w-4 mr-2" />
                  Copy DNS Instructions
                </Button>
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={() => window.open('https://vercel.com/docs/concepts/projects/domains', '_blank')}
                >
                  <ExternalLink className="h-4 w-4 mr-2" />
                  Vercel Docs
                </Button>
              </div>
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>

      {/* Save Actions */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex gap-2">
            <Button 
              onClick={handleSaveSettings}
              disabled={saving}
              className="flex-1"
            >
              <Save className="h-4 w-4 mr-2" />
              {saving ? 'Saving...' : 'Save Platform Settings'}
            </Button>
            <Button 
              variant="outline"
              onClick={fetchSettings}
              disabled={loading}
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

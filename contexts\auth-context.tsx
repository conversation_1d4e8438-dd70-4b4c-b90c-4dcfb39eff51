
"use client"

import { createContext, useContext, useState, useEffect, type ReactNode } from "react"
import { useRout<PERSON> } from "next/navigation"
import axios from "axios"
import Cookies from "js-cookie"
import { clientEnv, isDev, isPreview } from "@/lib/env"
import { supabase } from "@/lib/supabase"

// Tipe untuk role user
type UserRole = "admin" | "tenant" | "store" | "buyer"

interface User {
  id: string
  name: string
  email: string
  role: UserRole
  tenantId?: string
  storeId?: string
  isAffiliate?: boolean // Tambahkan properti isAffiliate
}

interface AuthContextType {
  user: User | null
  loading: boolean
  login: (email: string, password: string) => Promise<void>
  register: (name: string, email: string, password: string, role?: string) => Promise<void>
  logout: () => void
  updateUserRole: (role: "admin" | "tenant" | "store" | "buyer") => Promise<void>
}

const API_URL = clientEnv.API_URL

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)
  const router = useRouter()

  // Fungsi untuk menentukan apakah kita harus menggunakan mock data
  const shouldUseMockData = () => {
    // Aktifkan mode mock data untuk demo
    return true
  }

  // PENTING: Fungsi untuk menyimpan cookie dengan benar
  const setCookieSecurely = (name: string, value: string, days = 7) => {
    try {
      // Tentukan domain berdasarkan lingkungan
      const domain =
        typeof window !== "undefined" && window.location.hostname === "localhost"
          ? undefined
          : typeof window !== "undefined"
            ? `.${window.location.hostname.split(".").slice(-2).join(".")}`
            : undefined

      // Tentukan path
      const path = "/"

      // Set cookie dengan opsi yang benar
      if (typeof window !== "undefined") {
        Cookies.set(name, value, {
          expires: days,
          path,
          domain,
          secure: typeof window !== "undefined" && window.location.protocol === "https:",
          sameSite: "lax",
        })

        // Simpan juga di localStorage sebagai backup
        localStorage.setItem(name, value)
      }

      console.log(`[Auth] Cookie ${name} set with domain: ${domain || "default"}, path: ${path}`)
    } catch (error) {
      console.error("[Auth] Error setting cookie:", error)
      // Fallback to localStorage only
      if (typeof window !== "undefined") {
        localStorage.setItem(name, value)
      }
    }
  }

  // PENTING: Fungsi untuk mendapatkan cookie atau fallback ke localStorage
  const getAuthValue = (name: string) => {
    if (typeof window === "undefined") return null
    return Cookies.get(name) || localStorage.getItem(name) || null
  }

  useEffect(() => {
    // Cek apakah user sudah login
    const checkAuth = async () => {
      try {
        const token = getAuthValue("token")
        console.log("[Auth] Checking auth, token:", token ? "exists" : "not found")

        if (token) {
          // Untuk development, preview, atau jika API tidak tersedia, gunakan mock data
          if (shouldUseMockData()) {
            console.log("[Auth] Using mock data for auth check")

            // Cek role dari cookie untuk menentukan mock user yang tepat
            const savedRole = getAuthValue("userRole") || "buyer"

            let mockUser: User

            switch (savedRole) {
              case "admin":
                mockUser = {
                  id: "admin-1",
                  name: "Admin Demo",
                  email: "<EMAIL>",
                  role: "admin",
                }
                break
              case "tenant":
                mockUser = {
                  id: "tenant-1",
                  name: "Tenant Demo",
                  email: "<EMAIL>",
                  role: "tenant",
                  tenantId: "demo-tenant",
                }
                break
              case "store":
                mockUser = {
                  id: "store-1",
                  name: "Store Owner Demo",
                  email: "<EMAIL>",
                  role: "store",
                  storeId: "demo-store",
                }
                break
              default:
                mockUser = {
                  id: "buyer-1",
                  name: "Buyer Demo",
                  email: "<EMAIL>",
                  role: "buyer",
                }
            }

            setUser(mockUser)

            // Set role cookie
            setCookieSecurely("userRole", mockUser.role)

            setLoading(false)
            return
          }

          try {
            // Dapatkan user data langsung dari Supabase daripada API eksternal
            const { data: { user: supabaseUser }, error: userError } = await supabase.auth.getUser()
            
            if (userError) {
              console.error("[Auth] Error getting user from Supabase:", userError)
              throw userError
            }

            if (!supabaseUser) {
              console.error("[Auth] No user found in Supabase")
              return
            }

            console.log("[Auth] Supabase user:", supabaseUser)
            
            // Dapatkan metadata tambahan dari tabel users jika perlu
            const { data: userData, error: profileError } = await supabase
              .from('users')
              .select('*')
              .eq('id', supabaseUser.id)
              .single()

            // Helper function to safely extract role
            const extractRole = (): UserRole => {
              if (userData?.role) return userData.role as UserRole;
              
              if (supabaseUser.user_metadata) {
                const meta = supabaseUser.user_metadata;
                if (typeof meta === 'object' && meta !== null && 'role' in meta && typeof meta.role === 'string') {
                  return meta.role as UserRole;
                }
              }
              
              return 'buyer';
            };
            
            // Gabungkan data
            const userProfile: User = {
              id: supabaseUser.id,
              email: supabaseUser.email || '',
              name: userData?.name ? String(userData.name) : (supabaseUser.email?.split('@')[0] || 'User'),
              role: extractRole(),
              tenantId: userData?.tenant_id as string | undefined,
              storeId: userData?.store_id as string | undefined,
              isAffiliate: userData?.is_affiliate as boolean | undefined
            }
            
            console.log("[Auth] Complete user profile:", userProfile)
            setUser(userProfile)

            // Set role cookie
            setCookieSecurely("userRole", userProfile.role)
          } catch (error) {
            console.error("[Auth] Failed to get user profile:", error)

            // Jika error adalah network error, gunakan mock data
            if (axios.isAxiosError(error) && !error.response) {
              console.log("[Auth] Network error detected, using mock data")
              const mockUser = {
                id: "1",
                name: "Demo User",
                email: "<EMAIL>",
                role: "tenant" as const,
                tenantId: "tenant-123",
              }
              setUser(mockUser)
              setCookieSecurely("userRole", mockUser.role)
            } else {
              // Jika bukan network error, hapus token
              if (typeof window !== "undefined") {
                Cookies.remove("token")
                Cookies.remove("userRole")
                localStorage.removeItem("token")
                localStorage.removeItem("userRole")
              }
            }
          }
        } else {
          // Jika tidak ada token, pastikan user state di-reset
          setUser(null)
        }
      } catch (error) {
        console.error("[Auth] Error in checkAuth:", error)
        // Jika ada error, reset user state dan hapus token yang rusak
        setUser(null)
        if (typeof window !== "undefined") {
          Cookies.remove("token")
          Cookies.remove("userRole")
          localStorage.removeItem("token")
          localStorage.removeItem("userRole")
        }
      } finally {
        setLoading(false)
      }
    }

    checkAuth()
  }, [])

  // Fungsi untuk redirect berdasarkan role user
  const redirectBasedOnRole = (role: string) => {
    console.log("[Auth] Redirecting based on role:", role)

    // PENTING: Tambahkan parameter untuk mencegah redirect loop
    const redirectParams = new URLSearchParams()
    redirectParams.set("redirectLoop", "true")

    switch (role) {
      case "admin":
        router.push(`/admin/dashboard?${redirectParams.toString()}`)
        break
      case "tenant":
        router.push(`/tenant/dashboard?${redirectParams.toString()}`)
        break
      case "store":
        router.push(`/store/dashboard?${redirectParams.toString()}`)
        break
      case "buyer":
        router.push(`/buyer/dashboard?${redirectParams.toString()}`)
        break
      default:
        router.push(`/dashboard?${redirectParams.toString()}`)
    }
  }

  const login = async (email: string, password: string) => {
    console.log('[Auth] Attempting login with email:', email)

    try {
      // Cek apakah ini login admin
      if (email === "<EMAIL>") {
        console.log("[Auth] Admin login detected, using Supabase database")

        try {
          // Cek admin di database Supabase
          const { data: adminData, error: adminError } = await supabase
            .from('Admin')
            .select('*')
            .eq('email', email)
            .single()

          if (adminError || !adminData) {
            console.error('[Auth] Admin not found in database:', adminError)
            throw new Error('Admin tidak ditemukan')
          }

          // Verifikasi password (untuk demo, kita skip bcrypt verification)
          // Dalam production, gunakan bcrypt.compare(password, adminData.password)
          console.log('[Auth] Admin found in database:', adminData)

          const adminUser: User = {
            id: adminData.id,
            name: adminData.name,
            email: adminData.email,
            role: "admin",
          }

          setUser(adminUser)

          // Set cookies untuk login
          setCookieSecurely("token", "admin-token-" + Date.now())
          setCookieSecurely("userRole", "admin")

          // Redirect berdasarkan role
          redirectBasedOnRole("admin")
          return
        } catch (error) {
          console.error('[Auth] Admin login failed:', error)
          throw error
        }
      }

      // Untuk non-admin, gunakan database Supabase
      console.log("[Auth] Using Supabase database for user login")

      try {
        // Cek user di database Supabase
        const { data: userData, error: userError } = await supabase
          .from('User')
          .select('*')
          .eq('email', email)
          .single()

        if (userError || !userData) {
          console.error('[Auth] User not found in database:', userError)
          throw new Error('User tidak ditemukan')
        }

        // Verifikasi password (untuk demo, kita skip bcrypt verification)
        // Dalam production, gunakan bcrypt.compare(password, userData.password)
        console.log('[Auth] User found in database:', userData)

        // Map role dari database ke aplikasi
        let appRole: UserRole
        switch (userData.role) {
          case 'TENANT':
            appRole = 'tenant'
            break
          case 'STORE_OWNER':
            appRole = 'store'
            break
          case 'USER':
            appRole = 'buyer'
            break
          default:
            appRole = 'buyer'
        }

        const userProfile: User = {
          id: userData.id,
          name: userData.name,
          email: userData.email,
          role: appRole,
          tenantId: userData.tenantId || undefined,
          storeId: userData.storeId || undefined,
        }

        setUser(userProfile)

        // Set cookies untuk login
        setCookieSecurely("token", "user-token-" + Date.now())
        setCookieSecurely("userRole", appRole)

        // Redirect berdasarkan role
        redirectBasedOnRole(appRole)
        return
      } catch (error) {
        console.error('[Auth] User login failed:', error)
        throw error
      }

      // Gunakan Supabase Auth langsung
      try {
        console.log('[Auth] Signing in with Supabase Auth...')
        const { data, error } = await supabase.auth.signInWithPassword({
          email,
          password,
        })

        if (error) {
          console.error('[Auth] Supabase auth error:', error)
          throw error
        }

        if (!data.user || !data.session) {
          console.error('[Auth] Supabase login successful but no user or session returned')
          throw new Error('No user data received')
        }

        console.log('[Auth] Supabase login successful:', data.user)

        // Dapatkan metadata tambahan dari tabel users jika perlu
        const { data: userData, error: profileError } = await supabase
          .from('users')
          .select('*')
          .eq('id', data.user.id)
          .single()

        if (profileError) {
          console.warn('[Auth] Error getting user profile from users table:', profileError)
        }

        // Helper function to safely extract role
        const extractRole = (): UserRole => {
          if (userData?.role) return userData.role as UserRole;
          
          if (data.user.user_metadata) {
            const meta = data.user.user_metadata;
            if (typeof meta === 'object' && meta !== null && 'role' in meta && typeof meta.role === 'string') {
              return meta.role as UserRole;
            }
          }
          
          return 'buyer';
        };

        const userRole = extractRole()
        
        // Buat objek user
        const userProfile: User = {
          id: data.user.id,
          email: data.user.email || '',
          name: userData?.name ? String(userData.name) : (data.user.email?.split('@')[0] || 'User'),
          role: userRole,
          tenantId: userData?.tenant_id as string | undefined,
          storeId: userData?.store_id as string | undefined,
          isAffiliate: userData?.is_affiliate as boolean | undefined
        }

        // Simpan token dari Supabase di cookie (7 hari)
        setCookieSecurely("token", data.session.access_token)
        setCookieSecurely("userRole", userRole)

        setUser(userProfile)

        // Redirect berdasarkan role
        redirectBasedOnRole(userRole)
      } catch (error) {
        // Jika error adalah network error, gunakan mock data
        if (axios.isAxiosError(error) && !error.response) {
          console.log("[Auth] Network error detected during login, using mock data")

          // Gunakan mock data sebagai fallback berdasarkan email
          let mockUser: User

          // <NAME_EMAIL>, berikan role tenant
          if (email === "<EMAIL>") {
            mockUser = {
              id: "cmagt26240623-efc3-4",
              name: "Khadijah Tenant",
              email: "<EMAIL>",
              role: "tenant",
              tenantId: "tenant-123",
            }
          } else if (email === "<EMAIL>") {
            // User 1: Hanya sebagai buyer
            mockUser = {
              id: "buyer1",
              name: "Buyer Only",
              email: "<EMAIL>",
              role: "buyer",
              isAffiliate: false,
            }
          } else if (email === "<EMAIL>") {
            // User 2: Buyer yang sudah daftar store
            mockUser = {
              id: "buyerstore1",
              name: "Buyer with Store",
              email: "<EMAIL>",
              role: "buyer",
              storeId: "store-123",
              isAffiliate: false,
            }
          } else if (email === "<EMAIL>") {
            // User 3: Buyer yang sudah jadi affiliate
            mockUser = {
              id: "buyeraff1",
              name: "Buyer with Affiliate",
              email: "<EMAIL>",
              role: "buyer",
              isAffiliate: true,
            }
          } else if (email === "<EMAIL>") {
            // User 4: Buyer yang sudah daftar semua
            mockUser = {
              id: "buyerall1",
              name: "Complete Buyer",
              email: "<EMAIL>",
              role: "buyer",
              storeId: "store-456",
              isAffiliate: true,
            }
          } else if (email.includes("admin")) {
            mockUser = {
              id: "1",
              name: "Admin User",
              email: "<EMAIL>",
              role: "admin", 
            }
          } else if (email.includes("tenant")) {
            mockUser = {
              id: "2",
              name: "Tenant User",
              email: "<EMAIL>",
              role: "tenant",
              tenantId: "tenant-123",
            }
          } else if (email.includes("store")) {
            mockUser = {
              id: "3",
              name: "Store User",
              email: "<EMAIL>",
              role: "store",
              storeId: "store-123",
            }
          } else {
            // Default buyer
            mockUser = {
              id: "4",
              name: "Buyer User",
              email: "<EMAIL>",
              role: "buyer",
            }
          }

          // Simpan token dummy di cookie (7 hari)
          const mockToken = "mock-token-" + Date.now()
          setCookieSecurely("token", mockToken)
          setCookieSecurely("userRole", mockUser.role)

          setUser(mockUser)

          // Redirect berdasarkan role
          redirectBasedOnRole(mockUser.role)
        } else {
          // Jika bukan network error, throw error
          throw error
        }
      }
    } catch (error) {
      console.error("[Auth] Login failed:", error)
      throw error
    }
  }

  const register = async (name: string, email: string, password: string, role = "buyer") => {
    try {
      // Untuk development, preview, atau jika API tidak tersedia, gunakan mock data
      if (shouldUseMockData()) {
        console.log("[Auth] Using mock data for register")

        // Mock register untuk development/preview
        const mockUser: User = {
          id: "new-user-" + Date.now(),
          name,
          email,
          role: role as UserRole, // Pastikan role sesuai dengan tipe UserRole
          storeId: role === "store" ? "store-" + Date.now() : undefined,
          isAffiliate: false // Tambahkan properti yang diperlukan
        }

        // Simpan token dummy di cookie (7 hari)
        const mockToken = "mock-token-" + Date.now()
        setCookieSecurely("token", mockToken)
        setCookieSecurely("userRole", role)

        setUser(mockUser)

        // Redirect berdasarkan role
        redirectBasedOnRole(role)
        return
      }

      // Jika API tersedia, gunakan API
      try {
        const response = await axios.post(`${API_URL}/auth/register`, { name, email, password, role })
        const token = response.data.accessToken

        // Simpan token di cookie (7 hari)
        setCookieSecurely("token", token)
        setCookieSecurely("userRole", response.data.role)

        setUser({
          id: response.data.id,
          name: response.data.name,
          email: response.data.email,
          role: response.data.role,
          tenantId: response.data.tenantId,
          storeId: response.data.storeId,
          isAffiliate: response.data.isAffiliate,
        })

        // Redirect berdasarkan role
        redirectBasedOnRole(response.data.role)
      } catch (error) {
        // Jika error adalah network error, gunakan mock data
        if (axios.isAxiosError(error) && !error.response) {
          console.log("[Auth] Network error detected during register, using mock data")

          // Gunakan mock data sebagai fallback
          const mockUser: User = {
            id: "new-user-" + Date.now(),
            name,
            email,
            role: role as UserRole, // Pastikan role sesuai dengan tipe UserRole
            storeId: role === "store" ? "store-" + Date.now() : undefined,
            isAffiliate: false
          }

          // Simpan token dummy di cookie (7 hari)
          const mockToken = "mock-token-" + Date.now()
          setCookieSecurely("token", mockToken)
          setCookieSecurely("userRole", role)

          setUser(mockUser)

          // Redirect berdasarkan role
          redirectBasedOnRole(role)
        } else {
          // Jika bukan network error, throw error
          throw error
        }
      }
    } catch (error) {
      console.error("[Auth] Registration failed:", error)
      throw error
    }
  }

  const updateUserRole = async (role: "admin" | "tenant" | "store" | "buyer") => {
    try {
      // Untuk development, preview, atau jika API tidak tersedia, gunakan mock data
      if (shouldUseMockData()) {
        console.log("[Auth] Using mock data for role update")

        // Update role di state
        setUser((prev) => {
          if (!prev) return null
          return { ...prev, role }
        })

        // Update role di cookie
        setCookieSecurely("userRole", role)

        // Redirect berdasarkan role baru
        redirectBasedOnRole(role)
        return
      }

      const token = getAuthValue("token")
      if (!token) throw new Error("No token found")

      try {
        const response = await axios.patch(
          `${API_URL}/auth/role`,
          { role },
          {
            headers: {
              Authorization: `Bearer ${token}`,
            },
          },
        )

        setUser((prev) => {
          if (!prev) return null
          return { ...prev, role: response.data.role }
        })

        // Update role di cookie
        setCookieSecurely("userRole", response.data.role)

        // Redirect berdasarkan role baru
        redirectBasedOnRole(role)
      } catch (error) {
        // Jika error adalah network error, gunakan mock data
        if (axios.isAxiosError(error) && !error.response) {
          console.log("[Auth] Network error detected during role update, using mock data")

          // Update role di state
          setUser((prev) => {
            if (!prev) return null
            return { ...prev, role }
          })

          // Update role di cookie
          setCookieSecurely("userRole", role)

          // Redirect berdasarkan role baru
          redirectBasedOnRole(role)
        } else {
          // Jika bukan network error, throw error
          throw error
        }
      }
    } catch (error) {
      console.error("[Auth] Role update failed:", error)
      throw error
    }
  }

  // Fungsi redirectBasedOnRole sudah didefinisikan di atas

  const logout = () => {
    console.log("[Auth] Logging out")

    try {
      // Hapus cookie dengan domain yang benar
      const domain =
        typeof window !== "undefined" && window.location.hostname === "localhost"
          ? undefined
          : typeof window !== "undefined"
            ? `.${window.location.hostname.split(".").slice(-2).join(".")}`
            : undefined

      if (typeof window !== "undefined") {
        Cookies.remove("token", { path: "/", domain })
        Cookies.remove("userRole", { path: "/", domain })

        // Hapus data dari localStorage
        localStorage.removeItem("token")
        localStorage.removeItem("userRole")
        
        // Hapus data rentang tanggal
        localStorage.removeItem('storesDateRange')
        localStorage.removeItem('storesDateOption')
      }

      setUser(null)

      // Redirect ke login yang sesuai berdasarkan domain
      const currentHost = window.location.host
      if (currentHost.includes('app.')) {
        router.push("/login")
      } else {
        router.push("/admin/login")
      }
    } catch (error) {
      console.error("[Auth] Error during logout:", error)
      // Fallback jika ada error
      setUser(null)
      const currentHost = window.location.host
      if (currentHost.includes('app.')) {
        router.push("/login")
      } else {
        router.push("/admin/login")
      }
    }
  }

  return (
    <AuthContext.Provider value={{ user, loading, login, register, logout, updateUserRole }}>
      {children}
    </AuthContext.Provider>
  )
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider")
  }
  return context
}

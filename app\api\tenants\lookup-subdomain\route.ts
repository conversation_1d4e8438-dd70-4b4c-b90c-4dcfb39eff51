import { NextRequest, NextResponse } from 'next/server'
import { getClient } from '@/lib/supabase'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const subdomain = searchParams.get('subdomain')

    if (!subdomain) {
      return NextResponse.json(
        { error: 'Subdomain parameter is required' },
        { status: 400 }
      )
    }

    console.log('🔥 API: Looking up tenant for subdomain:', subdomain)

    const supabase = getClient()

    // Look up tenant by subdomain
    const { data: tenant, error } = await supabase
      .from('tenants')
      .select('id, name, subdomain, slug, domain')
      .eq('subdomain', subdomain)
      .single()

    if (error) {
      console.log('🔥 API: No tenant found for subdomain:', subdomain, error.message)
      return NextResponse.json(
        { error: 'Tenant not found' },
        { status: 404 }
      )
    }

    console.log('🔥 API: Found tenant for subdomain:', tenant)

    return NextResponse.json({
      id: tenant.id,
      slug: tenant.slug,
      name: tenant.name,
      subdomain: tenant.subdomain,
      domain: tenant.domain
    })

  } catch (error) {
    console.error('🔥 API: Error looking up tenant by subdomain:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

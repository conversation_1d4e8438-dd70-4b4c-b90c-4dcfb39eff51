import { NextRequest, NextResponse } from 'next/server'
import { createCloudflareService } from '@/lib/services/cloudflare-dns'

export async function POST(request: NextRequest) {
  try {
    const { action, subdomain, targetIP } = await request.json()

    if (!action || !subdomain) {
      return NextResponse.json(
        { error: 'Action and subdomain are required' },
        { status: 400 }
      )
    }

    // Check if Cloudflare is configured
    if (!process.env.CLOUDFLARE_API_TOKEN || !process.env.CLOUDFLARE_ZONE_ID) {
      return NextResponse.json(
        { error: 'Cloudflare API not configured' },
        { status: 500 }
      )
    }

    console.log('🔥 DNS API: Processing request:', { action, subdomain, targetIP })

    const cloudflareService = createCloudflareService()
    let result = false

    switch (action) {
      case 'create':
        result = await cloudflareService.createSubdomainRecord(subdomain, targetIP)
        break
      
      case 'update':
        result = await cloudflareService.updateSubdomainRecord(subdomain, targetIP)
        break
      
      case 'delete':
        result = await cloudflareService.deleteSubdomainRecord(subdomain)
        break
      
      case 'verify':
        result = await cloudflareService.verifyRecord(subdomain)
        break
      
      default:
        return NextResponse.json(
          { error: 'Invalid action. Use: create, update, delete, or verify' },
          { status: 400 }
        )
    }

    return NextResponse.json({
      success: result,
      action,
      subdomain,
      message: result 
        ? `DNS record ${action} successful`
        : `DNS record ${action} failed`
    })

  } catch (error: any) {
    console.error('🔥 DNS API: Error:', error)
    return NextResponse.json(
      { error: error.message || 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const subdomain = searchParams.get('subdomain')

    if (!process.env.CLOUDFLARE_API_TOKEN || !process.env.CLOUDFLARE_ZONE_ID) {
      return NextResponse.json(
        { error: 'Cloudflare API not configured' },
        { status: 500 }
      )
    }

    const cloudflareService = createCloudflareService()

    if (subdomain) {
      // Get specific record
      const record = await cloudflareService.getRecord(subdomain)
      return NextResponse.json({
        subdomain,
        record,
        exists: !!record
      })
    } else {
      // List all subdomain records
      const records = await cloudflareService.listSubdomainRecords()
      return NextResponse.json({
        records,
        count: records.length
      })
    }

  } catch (error: any) {
    console.error('🔥 DNS API: Error:', error)
    return NextResponse.json(
      { error: error.message || 'Internal server error' },
      { status: 500 }
    )
  }
}

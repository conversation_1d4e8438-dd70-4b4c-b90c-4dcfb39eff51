# Multi-Role Database Structure - <PERSON><PERSON><PERSON>aS

## 🏗️ **<PERSON><PERSON><PERSON> dengan Struktur Lama**

### **Single Role Limitation:**
- Tabel `User` dengan single `role` field
- Tidak bisa handle user yang punya multiple capabilities
- Sulit track progression dari buyer → store owner → affiliate

### **Business Requirements:**
1. **Buyer Only** - <PERSON><PERSON> pembeli
2. **Buyer + Store** - P<PERSON><PERSON>i yang buka toko
3. **Buyer + Affiliate** - Pembeli yang jadi affiliate
4. **Buyer + Store + Affiliate** - Full capabilities

## 🔧 **Solusi: Tabel Terpisah untuk Setiap Role**

### **1. Tabel `buyers`**
```sql
CREATE TABLE buyers (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id TEXT NOT NULL REFERENCES "User"(id) ON DELETE CASCADE,
  tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
  is_buyer BOOLEAN DEFAULT true,
  is_store_owner B<PERSON><PERSON><PERSON>N DEFAULT false,
  is_affiliate BO<PERSON>EA<PERSON> DEFAULT false,
  store_id UUID REFERENCES stores(id) ON DELETE SET NULL,
  affiliate_id TEXT,
  buyer_level VARCHAR(20) DEFAULT 'bronze', -- bronze, silver, gold, platinum
  total_orders INTEGER DEFAULT 0,
  total_spent DECIMAL(15,2) DEFAULT 0,
  loyalty_points INTEGER DEFAULT 0,
  referral_code VARCHAR(20) UNIQUE,
  referred_by TEXT REFERENCES "User"(id),
  join_date TIMESTAMP DEFAULT NOW(),
  last_purchase TIMESTAMP,
  status VARCHAR(20) DEFAULT 'active',
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW(),
  UNIQUE(user_id, tenant_id)
);
```

### **2. Tabel `store_owners`**
```sql
CREATE TABLE store_owners (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id TEXT NOT NULL REFERENCES "User"(id) ON DELETE CASCADE,
  tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
  store_id UUID NOT NULL REFERENCES stores(id) ON DELETE CASCADE,
  ownership_percentage DECIMAL(5,2) DEFAULT 100.00,
  role VARCHAR(50) DEFAULT 'owner', -- owner, manager, staff
  permissions JSONB DEFAULT '{}',
  joined_store_date TIMESTAMP DEFAULT NOW(),
  status VARCHAR(20) DEFAULT 'active',
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW(),
  UNIQUE(user_id, store_id)
);
```

### **3. Tabel `affiliates`**
```sql
CREATE TABLE affiliates (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id TEXT NOT NULL REFERENCES "User"(id) ON DELETE CASCADE,
  tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
  affiliate_code VARCHAR(20) UNIQUE NOT NULL,
  commission_rate DECIMAL(5,2) DEFAULT 5.00,
  total_referrals INTEGER DEFAULT 0,
  total_commission DECIMAL(15,2) DEFAULT 0,
  pending_commission DECIMAL(15,2) DEFAULT 0,
  paid_commission DECIMAL(15,2) DEFAULT 0,
  affiliate_level VARCHAR(20) DEFAULT 'bronze', -- bronze, silver, gold, platinum
  payment_method VARCHAR(50),
  payment_details JSONB,
  joined_affiliate_date TIMESTAMP DEFAULT NOW(),
  last_payout TIMESTAMP,
  status VARCHAR(20) DEFAULT 'active',
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW(),
  UNIQUE(user_id, tenant_id)
);
```

### **4. View `user_roles` untuk Query Mudah**
```sql
CREATE OR REPLACE VIEW user_roles AS
SELECT 
  u.id as user_id,
  u.email,
  u.name,
  u.role as primary_role,
  u."tenantId" as tenant_id,
  COALESCE(b.is_buyer, false) as is_buyer,
  COALESCE(so.user_id IS NOT NULL, false) as is_store_owner,
  COALESCE(a.user_id IS NOT NULL, false) as is_affiliate,
  b.store_id as buyer_store_id,
  so.store_id as owned_store_id,
  a.affiliate_code,
  b.buyer_level,
  a.affiliate_level,
  b.total_orders,
  b.total_spent,
  a.total_commission,
  u."createdAt",
  u."updatedAt"
FROM "User" u
LEFT JOIN buyers b ON u.id = b.user_id
LEFT JOIN store_owners so ON u.id = so.user_id
LEFT JOIN affiliates a ON u.id = a.user_id
WHERE u."tenantId" IS NOT NULL;
```

## 📊 **Data Examples**

### **Tenant 1 Users:**
1. **Buyer Only** (Bronze): `<EMAIL>`
   - `buyers`: ✅ (level: bronze, 0 orders)
   - `store_owners`: ❌
   - `affiliates`: ❌

2. **Buyer + Store** (Silver): `<EMAIL>`
   - `buyers`: ✅ (level: silver, 5 orders, Rp 1.5M)
   - `store_owners`: ✅ (store: nama5)
   - `affiliates`: ❌

3. **Buyer + Affiliate** (Gold): `<EMAIL>`
   - `buyers`: ✅ (level: gold, 10 orders, Rp 3M)
   - `store_owners`: ❌
   - `affiliates`: ✅ (code: AFF1T1, 7.5%, Rp 150K)

4. **Buyer + Store + Affiliate** (Platinum): `<EMAIL>`
   - `buyers`: ✅ (level: platinum, 20 orders, Rp 5M)
   - `store_owners`: ✅ (store: tes)
   - `affiliates`: ✅ (code: AFFALL1, 10%, Rp 500K)

## 🔍 **Query Examples**

### **Get All Buyers dalam Tenant:**
```sql
SELECT * FROM user_roles 
WHERE tenant_id = '96111d56-b82d-43e7-9926-8f0530dc6063' 
AND is_buyer = true;
```

### **Get Buyer-Only Users:**
```sql
SELECT * FROM user_roles 
WHERE tenant_id = '96111d56-b82d-43e7-9926-8f0530dc6063' 
AND is_buyer = true 
AND is_store_owner = false 
AND is_affiliate = false;
```

### **Get Multi-Role Users:**
```sql
SELECT * FROM user_roles 
WHERE tenant_id = '96111d56-b82d-43e7-9926-8f0530dc6063' 
AND is_buyer = true 
AND is_store_owner = true 
AND is_affiliate = true;
```

### **Get Store Owners yang juga Buyers:**
```sql
SELECT * FROM user_roles 
WHERE tenant_id = '96111d56-b82d-43e7-9926-8f0530dc6063' 
AND is_store_owner = true 
AND is_buyer = true;
```

## 🚀 **API Endpoints**

### **GET /api/tenant/buyers**
**Parameters:**
- `tenantId` (required)
- `role` (optional): 
  - `buyer` - Semua yang punya capability buyer
  - `store` - Semua yang punya capability store owner
  - `affiliate` - Semua yang punya capability affiliate
  - `buyer-only` - Hanya buyer saja
  - `buyer-store` - Buyer + Store owner
  - `buyer-affiliate` - Buyer + Affiliate
  - `buyer-store-affiliate` - Full capabilities

**Response:**
```json
{
  "buyers": [
    {
      "id": "buyerstoreaf-t1",
      "email": "<EMAIL>",
      "name": "Buyer Store Affiliate T1",
      "primaryRole": "USER",
      "capabilities": {
        "isBuyer": true,
        "isStoreOwner": true,
        "isAffiliate": true
      },
      "buyerData": {
        "level": "platinum",
        "totalOrders": 20,
        "totalSpent": "5000000.00"
      },
      "storeData": {
        "storeId": "a70cae23-39af-4ff5-b1d8-b227a28585f8"
      },
      "affiliateData": {
        "code": "AFFALL1",
        "level": "platinum",
        "totalCommission": "500000.00"
      }
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 50,
    "total": 6,
    "totalPages": 1
  }
}
```

## ✅ **Benefits**

### **1. Flexibility**
- User bisa punya multiple roles sekaligus
- Easy progression: buyer → store → affiliate
- Granular permissions per role

### **2. Data Integrity**
- Separate concerns untuk setiap role
- Proper foreign key relationships
- Audit trail untuk setiap capability

### **3. Scalability**
- Easy to add new roles (e.g., influencer, wholesaler)
- Efficient queries dengan view
- Support untuk complex business logic

### **4. Business Logic Support**
- Buyer levels dengan benefits berbeda
- Store ownership dengan percentage
- Affiliate commission tracking
- Multi-tenant isolation tetap terjaga

## 🎯 **Use Cases**

1. **User Journey**: Buyer → buka toko → jadi affiliate
2. **Analytics**: Track performance per role
3. **Permissions**: Different access per capability
4. **Commissions**: Calculate based on affiliate + store performance
5. **Loyalty**: Buyer level affects store owner benefits
6. **Referrals**: Affiliate code works across all roles

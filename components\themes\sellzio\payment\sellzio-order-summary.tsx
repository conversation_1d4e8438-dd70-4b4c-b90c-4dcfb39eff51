"use client"

import React from 'react'

interface SellzioOrderSummaryProps {
  orderData: any
}

export const SellzioOrderSummary: React.FC<SellzioOrderSummaryProps> = ({
  orderData
}) => {
  const formatPrice = (price: number) => {
    return `Rp${price.toLocaleString('id-ID')}`
  }

  return (
    <div className="sellzio-payment-section sellzio-order-summary">
      <div className="sellzio-section-header">
        <h3 className="sellzio-section-title"><PERSON><PERSON><PERSON></h3>
        <span className="sellzio-order-id">ID: {orderData.orderId}</span>
      </div>

      <div className="sellzio-order-items">
        {orderData.items.map((item: any, index: number) => (
          <div key={index} className="sellzio-order-item">
            <div className="sellzio-item-image">
              <img 
                src={item.image || '/placeholder-product.jpg'} 
                alt={item.name}
                onError={(e) => {
                  const target = e.target as HTMLImageElement
                  target.src = '/placeholder-product.jpg'
                }}
              />
            </div>
            <div className="sellzio-item-details">
              <h4 className="sellzio-item-name">{item.name}</h4>
              <div className="sellzio-item-meta">
                <span className="sellzio-item-quantity">x{item.quantity}</span>
                <span className="sellzio-item-price">{formatPrice(item.price)}</span>
              </div>
            </div>
            <div className="sellzio-item-total">
              {formatPrice(item.price * item.quantity)}
            </div>
          </div>
        ))}
      </div>

      <div className="sellzio-order-totals">
        <div className="sellzio-total-row">
          <span>Subtotal Produk</span>
          <span>{formatPrice(orderData.subtotal)}</span>
        </div>
        <div className="sellzio-total-row">
          <span>Ongkos Kirim</span>
          <span>{formatPrice(orderData.shippingCost)}</span>
        </div>
        {orderData.voucherDiscount > 0 && (
          <div className="sellzio-total-row sellzio-discount">
            <span>Voucher</span>
            <span>-{formatPrice(orderData.voucherDiscount)}</span>
          </div>
        )}
        <div className="sellzio-total-row sellzio-final-total">
          <span>Total Pembayaran</span>
          <span>{formatPrice(orderData.total)}</span>
        </div>
      </div>

      <style jsx>{`
        .sellzio-payment-section {
          background: white;
          margin-bottom: 8px;
          padding: 16px;
          border-radius: 8px;
          box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .sellzio-section-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 16px;
          padding-bottom: 12px;
          border-bottom: 1px solid #f0f0f0;
        }

        .sellzio-section-title {
          font-size: 16px;
          font-weight: 600;
          color: #333;
          margin: 0;
        }

        .sellzio-order-id {
          font-size: 12px;
          color: #666;
          background: #f5f5f5;
          padding: 4px 8px;
          border-radius: 4px;
        }

        .sellzio-order-items {
          margin-bottom: 16px;
        }

        .sellzio-order-item {
          display: flex;
          align-items: center;
          padding: 12px 0;
          border-bottom: 1px solid #f5f5f5;
        }

        .sellzio-order-item:last-child {
          border-bottom: none;
        }

        .sellzio-item-image {
          width: 60px;
          height: 60px;
          border-radius: 8px;
          overflow: hidden;
          margin-right: 12px;
          flex-shrink: 0;
        }

        .sellzio-item-image img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }

        .sellzio-item-details {
          flex: 1;
          min-width: 0;
        }

        .sellzio-item-name {
          font-size: 14px;
          font-weight: 500;
          color: #333;
          margin: 0 0 4px 0;
          line-height: 1.4;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }

        .sellzio-item-meta {
          display: flex;
          justify-content: space-between;
          align-items: center;
          font-size: 12px;
          color: #666;
        }

        .sellzio-item-total {
          font-size: 14px;
          font-weight: 600;
          color: #ee4d2d;
          margin-left: 12px;
        }

        .sellzio-order-totals {
          border-top: 1px solid #f0f0f0;
          padding-top: 12px;
        }

        .sellzio-total-row {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 8px;
          font-size: 14px;
        }

        .sellzio-total-row:last-child {
          margin-bottom: 0;
        }

        .sellzio-discount {
          color: #27ae60;
        }

        .sellzio-final-total {
          font-weight: 600;
          font-size: 16px;
          color: #ee4d2d;
          border-top: 1px solid #f0f0f0;
          padding-top: 8px;
          margin-top: 8px;
        }

        @media (max-width: 768px) {
          .sellzio-payment-section {
            margin-bottom: 6px;
            padding: 12px;
          }

          .sellzio-item-image {
            width: 50px;
            height: 50px;
            margin-right: 10px;
          }

          .sellzio-item-name {
            font-size: 13px;
          }

          .sellzio-item-meta {
            font-size: 11px;
          }

          .sellzio-item-total {
            font-size: 13px;
            margin-left: 8px;
          }
        }
      `}</style>
    </div>
  )
}

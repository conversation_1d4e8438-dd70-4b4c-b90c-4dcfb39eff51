import { NextRequest, NextResponse } from 'next/server'

// Demo users database
const DEMO_USERS = [
  {
    id: '1',
    email: '<EMAIL>',
    password: 'tenant123',
    type: 'tenant',
    name: 'Tenant Demo',
    tenantId: 'demo-tenant'
  },
  {
    id: '2', 
    email: '<EMAIL>',
    password: 'store123',
    type: 'store',
    name: 'Store Owner Demo',
    storeId: 'demo-store'
  },
  {
    id: '3',
    email: '<EMAIL>', 
    password: 'buyer123',
    type: 'buyer',
    name: 'Buyer Demo',
    buyerId: 'demo-buyer'
  },
  {
    id: '4',
    email: '<EMAIL>',
    password: 'admin123', 
    type: 'admin',
    name: 'Admin Demo'
  },
  // Generic demo account
  {
    id: '5',
    email: '<EMAIL>',
    password: 'demo123',
    type: 'tenant', // Default to tenant
    name: 'Demo User',
    tenantId: 'demo'
  }
]

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { email, password } = body

    // Validate input
    if (!email || !password) {
      return NextResponse.json({
        success: false,
        error: 'Email and password are required'
      }, { status: 400 })
    }

    // Find user
    const user = DEMO_USERS.find(u => 
      u.email.toLowerCase() === email.toLowerCase() && 
      u.password === password
    )

    if (!user) {
      return NextResponse.json({
        success: false,
        error: 'Invalid email or password'
      }, { status: 401 })
    }

    // Determine dashboard route based on user type
    let dashboardRoute = '/tenant/dashboard'
    switch (user.type) {
      case 'tenant':
        dashboardRoute = '/tenant/dashboard'
        break
      case 'store':
        dashboardRoute = '/store/dashboard'
        break
      case 'buyer':
        dashboardRoute = '/buyer/dashboard'
        break
      case 'admin':
        dashboardRoute = '/admin/dashboard'
        break
      default:
        dashboardRoute = '/tenant/dashboard'
    }

    // Create session token (in real app, use JWT or proper session management)
    const sessionToken = `session_${user.id}_${Date.now()}`

    // Return success response
    const response = NextResponse.json({
      success: true,
      user: {
        id: user.id,
        email: user.email,
        name: user.name,
        type: user.type,
        tenantId: user.tenantId,
        storeId: user.storeId,
        buyerId: user.buyerId
      },
      dashboardRoute,
      sessionToken
    })

    // Set session cookie
    response.cookies.set('session_token', sessionToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 60 * 60 * 24 * 7 // 7 days
    })

    return response

  } catch (error) {
    console.error('Login error:', error)
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    }, { status: 500 })
  }
}

// GET endpoint to check session
export async function GET(request: NextRequest) {
  try {
    const sessionToken = request.cookies.get('session_token')?.value

    if (!sessionToken) {
      return NextResponse.json({
        success: false,
        error: 'No session found'
      }, { status: 401 })
    }

    // Extract user ID from session token (simple implementation)
    const userId = sessionToken.split('_')[1]
    const user = DEMO_USERS.find(u => u.id === userId)

    if (!user) {
      return NextResponse.json({
        success: false,
        error: 'Invalid session'
      }, { status: 401 })
    }

    return NextResponse.json({
      success: true,
      user: {
        id: user.id,
        email: user.email,
        name: user.name,
        type: user.type,
        tenantId: user.tenantId,
        storeId: user.storeId,
        buyerId: user.buyerId
      }
    })

  } catch (error) {
    console.error('Session check error:', error)
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    }, { status: 500 })
  }
}

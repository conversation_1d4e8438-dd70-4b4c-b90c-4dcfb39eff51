-- Custom Domain Requests Table Migration
-- Run this in Supabase SQL Editor

-- Create the table
CREATE TABLE IF NOT EXISTS custom_domain_requests (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
  domain VARCHAR(255) NOT NULL,
  reason TEXT,
  status VARCHAR(50) DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected', 'configuration_failed')),
  admin_notes TEXT,
  requested_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  processed_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_custom_domain_requests_tenant_id ON custom_domain_requests(tenant_id);
CREATE INDEX IF NOT EXISTS idx_custom_domain_requests_status ON custom_domain_requests(status);
CREATE INDEX IF NOT EXISTS idx_custom_domain_requests_domain ON custom_domain_requests(domain);
CREATE INDEX IF NOT EXISTS idx_custom_domain_requests_requested_at ON custom_domain_requests(requested_at DESC);

-- Create unique constraint to prevent duplicate pending requests for same domain
CREATE UNIQUE INDEX IF NOT EXISTS idx_custom_domain_requests_unique_pending 
ON custom_domain_requests(domain, tenant_id) 
WHERE status = 'pending';

-- Enable RLS (Row Level Security)
ALTER TABLE custom_domain_requests ENABLE ROW LEVEL SECURITY;

-- Create policies for RLS
-- Note: Adjust these policies based on your authentication system

-- Policy: Allow service role to do everything (for API operations)
CREATE POLICY "Service role can manage all domain requests" ON custom_domain_requests
  FOR ALL USING (auth.role() = 'service_role');

-- Policy: Authenticated users can view all requests (for admin dashboard)
CREATE POLICY "Authenticated users can view domain requests" ON custom_domain_requests
  FOR SELECT USING (auth.role() = 'authenticated');

-- Policy: Authenticated users can create requests
CREATE POLICY "Authenticated users can create domain requests" ON custom_domain_requests
  FOR INSERT WITH CHECK (auth.role() = 'authenticated');

-- Policy: Authenticated users can update requests (for admin approval)
CREATE POLICY "Authenticated users can update domain requests" ON custom_domain_requests
  FOR UPDATE USING (auth.role() = 'authenticated');

-- Create function to automatically update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger to automatically update updated_at
CREATE TRIGGER update_custom_domain_requests_updated_at 
  BEFORE UPDATE ON custom_domain_requests 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert sample data for testing (optional - remove in production)
-- INSERT INTO custom_domain_requests (tenant_id, domain, reason, status) VALUES
-- ('96111d56-b82d-43e7-9926-8f0530dc6063', 'example.com', 'Business domain for professional use', 'pending'),
-- ('96111d56-b82d-43e7-9926-8f0530dc6063', 'mybusiness.com', 'Custom domain for branding', 'approved');

-- Verify table creation
SELECT 
  table_name, 
  column_name, 
  data_type, 
  is_nullable,
  column_default
FROM information_schema.columns 
WHERE table_name = 'custom_domain_requests' 
ORDER BY ordinal_position;

import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'
import { createCloudflareService } from '@/lib/services/cloudflare-dns'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

function getClient() {
  return createClient(supabaseUrl, supabaseServiceKey)
}

// Verify domain ownership
async function verifyDomainOwnership(domain: string, tenantId: string): Promise<boolean> {
  try {
    // Check for TXT record: sellzio-verification=tenant-id
    const verificationRecord = `sellzio-verification=${tenantId}`
    
    // In production, use DNS lookup to check TXT record
    // For now, simulate verification
    console.log('🔥 DOMAIN: Verifying ownership for:', domain, 'with record:', verificationRecord)
    
    // Simulate DNS lookup delay
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // For demo, consider domain verified if it's not localhost
    return !domain.includes('localhost')
  } catch (error) {
    console.error('🔥 DOMAIN: Verification error:', error)
    return false
  }
}

// POST - Set custom domain
export async function POST(request: NextRequest) {
  try {
    const { domain, tenantId } = await request.json()

    if (!domain || !tenantId) {
      return NextResponse.json(
        { error: 'Domain and tenantId are required' },
        { status: 400 }
      )
    }

    // Validate domain format
    const domainRegex = /^[a-zA-Z0-9]([a-zA-Z0-9-]*[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9-]*[a-zA-Z0-9])?)*$/
    if (!domainRegex.test(domain)) {
      return NextResponse.json(
        { error: 'Invalid domain format' },
        { status: 400 }
      )
    }

    console.log('🔥 API: Setting custom domain:', { domain, tenantId })

    const supabase = getClient()

    // Check if domain is already taken by another tenant
    const { data: existingTenant, error: checkError } = await supabase
      .from('tenants')
      .select('id, name')
      .eq('domain', domain)
      .neq('id', tenantId)
      .single()

    if (checkError && checkError.code !== 'PGRST116') {
      console.error('🔥 API: Error checking domain availability:', checkError)
      return NextResponse.json(
        { error: 'Failed to check domain availability' },
        { status: 500 }
      )
    }

    if (existingTenant) {
      return NextResponse.json(
        { error: 'Domain is already taken by another tenant' },
        { status: 409 }
      )
    }

    // Get current tenant info
    const { data: currentTenant } = await supabase
      .from('tenants')
      .select('domain, subdomain')
      .eq('id', tenantId)
      .single()

    // Verify domain ownership
    const isVerified = await verifyDomainOwnership(domain, tenantId)
    
    if (!isVerified) {
      return NextResponse.json({
        success: false,
        error: 'Domain ownership verification failed',
        verificationRequired: true,
        verificationRecord: `sellzio-verification=${tenantId}`,
        instructions: [
          'Add a TXT record to your domain DNS:',
          `Name: sellzio-verification`,
          `Value: ${tenantId}`,
          'Wait for DNS propagation (up to 24 hours)',
          'Try again after verification record is active'
        ]
      }, { status: 400 })
    }

    // Update tenant with custom domain
    const { data, error } = await supabase
      .from('tenants')
      .update({ domain })
      .eq('id', tenantId)
      .select()
      .single()

    if (error) {
      console.error('🔥 API: Error updating custom domain:', error)
      return NextResponse.json(
        { error: 'Failed to update custom domain' },
        { status: 500 }
      )
    }

    // Auto-configure DNS via Cloudflare API
    let dnsConfigured = false
    try {
      if (process.env.CLOUDFLARE_API_TOKEN && process.env.CLOUDFLARE_ZONE_ID) {
        console.log('🔥 API: Configuring DNS for custom domain:', domain)

        const cloudflareService = createCloudflareService()
        
        // Create CNAME record pointing to main domain or subdomain
        const targetDomain = currentTenant?.subdomain 
          ? `${currentTenant.subdomain}.sellzio.my.id`
          : 'sellzio.my.id'

        const dnsCreated = await cloudflareService.createCustomDomainRecord(domain, targetDomain)

        if (dnsCreated) {
          console.log('🔥 API: DNS configured successfully for custom domain')
          dnsConfigured = true
        } else {
          console.log('🔥 API: Failed to configure DNS for custom domain')
        }
      } else {
        console.warn('🔥 API: Cloudflare credentials not configured, skipping DNS setup')
      }
    } catch (dnsError) {
      console.error('🔥 API: DNS configuration error (non-fatal):', dnsError)
    }

    // Auto-add domain to Vercel project
    let vercelConfigured = false
    try {
      if (process.env.VERCEL_TOKEN && process.env.VERCEL_PROJECT_ID) {
        console.log('🔥 API: Adding custom domain to Vercel project:', domain)

        const vercelToken = process.env.VERCEL_TOKEN
        const vercelProjectId = process.env.VERCEL_PROJECT_ID

        // Remove old custom domain from Vercel if exists
        if (currentTenant?.domain && currentTenant.domain !== domain) {
          try {
            const deleteResponse = await fetch(`https://api.vercel.com/v9/projects/${vercelProjectId}/domains/${currentTenant.domain}`, {
              method: 'DELETE',
              headers: {
                'Authorization': `Bearer ${vercelToken}`,
                'Content-Type': 'application/json',
              },
            })

            if (deleteResponse.ok || deleteResponse.status === 404) {
              console.log('🔥 API: Old custom domain removed from Vercel')
            }
          } catch (deleteError) {
            console.log('🔥 API: Error removing old custom domain from Vercel (non-fatal):', deleteError)
          }
        }

        // Add new custom domain to Vercel
        const response = await fetch(`https://api.vercel.com/v9/projects/${vercelProjectId}/domains`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${vercelToken}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            name: domain
          })
        })

        const result = await response.json()

        if (response.ok) {
          console.log('🔥 API: Custom domain added to Vercel successfully:', result)
          vercelConfigured = true
        } else {
          console.log('🔥 API: Failed to add custom domain to Vercel:', result)
          if (result.error?.code === 'domain_already_in_use' || result.error?.code === 'domain_already_exists') {
            console.log('🔥 API: Custom domain already exists in Vercel, continuing...')
            vercelConfigured = true
          }
        }
      } else {
        console.warn('🔥 API: Vercel credentials not configured, skipping domain addition')
      }
    } catch (vercelError) {
      console.error('🔥 API: Vercel domain addition error (non-fatal):', vercelError)
    }

    console.log('🔥 API: Custom domain set successfully:', data)

    return NextResponse.json({
      success: true,
      tenant: data,
      dnsConfigured,
      vercelConfigured,
      message: 'Custom domain configured successfully'
    })

  } catch (error) {
    console.error('🔥 API: Error setting custom domain:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// DELETE - Remove custom domain
export async function DELETE(request: NextRequest) {
  try {
    const { tenantId } = await request.json()

    if (!tenantId) {
      return NextResponse.json(
        { error: 'TenantId is required' },
        { status: 400 }
      )
    }

    console.log('🔥 API: Removing custom domain for tenant:', tenantId)

    const supabase = getClient()

    // Get current tenant info
    const { data: currentTenant } = await supabase
      .from('tenants')
      .select('domain')
      .eq('id', tenantId)
      .single()

    if (!currentTenant?.domain) {
      return NextResponse.json(
        { error: 'No custom domain found for this tenant' },
        { status: 404 }
      )
    }

    const customDomain = currentTenant.domain

    // Remove custom domain from tenant (set to null)
    const { data, error } = await supabase
      .from('tenants')
      .update({ domain: null })
      .eq('id', tenantId)
      .select()
      .single()

    if (error) {
      console.error('🔥 API: Error removing custom domain:', error)
      return NextResponse.json(
        { error: 'Failed to remove custom domain' },
        { status: 500 }
      )
    }

    // Auto-remove DNS record
    try {
      if (process.env.CLOUDFLARE_API_TOKEN && process.env.CLOUDFLARE_ZONE_ID) {
        console.log('🔥 API: Removing DNS record for custom domain:', customDomain)

        const cloudflareService = createCloudflareService()
        await cloudflareService.deleteCustomDomainRecord(customDomain)
        console.log('🔥 API: DNS record removed successfully')
      }
    } catch (dnsError) {
      console.error('🔥 API: DNS removal error (non-fatal):', dnsError)
    }

    // Auto-remove domain from Vercel
    try {
      if (process.env.VERCEL_TOKEN && process.env.VERCEL_PROJECT_ID) {
        console.log('🔥 API: Removing custom domain from Vercel:', customDomain)

        const vercelToken = process.env.VERCEL_TOKEN
        const vercelProjectId = process.env.VERCEL_PROJECT_ID

        const response = await fetch(`https://api.vercel.com/v9/projects/${vercelProjectId}/domains/${customDomain}`, {
          method: 'DELETE',
          headers: {
            'Authorization': `Bearer ${vercelToken}`,
            'Content-Type': 'application/json',
          },
        })

        if (response.ok || response.status === 404) {
          console.log('🔥 API: Custom domain removed from Vercel successfully')
        }
      }
    } catch (vercelError) {
      console.error('🔥 API: Vercel domain removal error (non-fatal):', vercelError)
    }

    console.log('🔥 API: Custom domain removed successfully for tenant:', tenantId)

    return NextResponse.json({
      success: true,
      tenant: data,
      message: 'Custom domain removed successfully'
    })

  } catch (error) {
    console.error('🔥 API: Error removing custom domain:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

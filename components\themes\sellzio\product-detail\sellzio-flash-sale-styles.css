/* Flash Sale Specific Styles */

/* Flash Sale Layout */
.flash-sale-layout {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee4d2d 100%);
  min-height: 100vh;
  position: relative;
  z-index: 1; /* Lower than header */
}

/* Ensure header compatibility for flash sale */
.flash-sale-layout .header {
  z-index: 1000 !important;
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100% !important;
}

/* Flash Sale Header */
.flash-sale-header {
  background: linear-gradient(135deg, #ff4757 0%, #ff3838 100%);
  padding: 16px 0;
  box-shadow: 0 4px 12px rgba(238, 77, 45, 0.3);
  margin-top: 60px; /* Space for main header */
  position: relative;
  z-index: 998; /* Below main header */
}

.flash-sale-banner {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 16px 24px;
  backdrop-filter: blur(10px);
}

.flash-sale-title {
  display: flex;
  align-items: center;
  gap: 8px;
  color: white;
  font-size: 24px;
  font-weight: 700;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.flash-sale-icon {
  font-size: 28px;
  animation: flash 1.5s infinite;
}

@keyframes flash {
  0%, 50%, 100% { opacity: 1; }
  25%, 75% { opacity: 0.5; }
}

.flash-sale-timer {
  display: flex;
  align-items: center;
  gap: 12px;
}

.timer-label {
  color: rgba(255, 255, 255, 0.9);
  font-size: 14px;
  font-weight: 500;
}

.timer-display {
  display: flex;
  align-items: center;
  gap: 8px;
}

.timer-unit {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  padding: 8px 12px;
  text-align: center;
  backdrop-filter: blur(5px);
}

.timer-number {
  display: block;
  color: white;
  font-size: 18px;
  font-weight: 700;
  line-height: 1;
}

.timer-text {
  display: block;
  color: rgba(255, 255, 255, 0.8);
  font-size: 10px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.timer-separator {
  color: white;
  font-size: 18px;
  font-weight: 700;
  animation: blink 1s infinite;
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0.3; }
}

/* Flash Sale Main Content */
.flash-sale-layout .shopee-main-content {
  background: #f5f5f5;
}

/* Flash Sale Image Badge */
.flash-sale-image-badge {
  position: absolute;
  top: 12px;
  left: 12px;
  background: linear-gradient(135deg, #ff4757 0%, #ff3838 100%);
  color: white;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 700;
  display: flex;
  align-items: center;
  gap: 4px;
  box-shadow: 0 2px 8px rgba(255, 71, 87, 0.4);
  z-index: 10;
}

.flash-sale-badge-icon {
  font-size: 14px;
  animation: flash 1.5s infinite;
}

/* Flash Sale Progress Section */
.flash-sale-progress-section {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee4d2d 100%);
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 20px;
  color: white;
}

.flash-sale-progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.flash-sale-progress-title {
  font-size: 16px;
  font-weight: 700;
  display: flex;
  align-items: center;
  gap: 4px;
}

.flash-sale-progress-text {
  font-size: 14px;
  font-weight: 500;
}

.flash-sale-progress-bar {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 20px;
  height: 8px;
  overflow: hidden;
}

.flash-sale-progress-fill {
  background: white;
  height: 100%;
  border-radius: 20px;
  transition: width 0.3s ease;
  box-shadow: 0 0 8px rgba(255, 255, 255, 0.5);
}

/* Flash Sale Product Info */
.flash-sale-product-info {
  position: relative;
}

.flash-sale-title {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee4d2d 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 600;
}

/* Flash Sale Price Section */
.flash-sale-price-section {
  background: linear-gradient(135deg, #fff5f5 0%, #ffebee 100%);
  border: 2px solid #ff4757;
  border-radius: 12px;
  padding: 20px;
  margin: 20px -20px;
  position: relative;
  overflow: hidden;
}

.flash-sale-price-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #ff4757 0%, #ff6b6b 50%, #ff4757 100%);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.flash-sale-price-container {
  position: relative;
  z-index: 1;
}

.flash-sale-price-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
}

.flash-sale-label {
  font-size: 14px;
  font-weight: 600;
  color: #ff4757;
}

.flash-sale-price-group {
  display: flex;
  align-items: center;
  gap: 12px;
}

.flash-sale-original-price {
  font-size: 16px;
  color: #999;
  text-decoration: line-through;
}

.flash-sale-current-price {
  font-size: 28px;
  font-weight: 700;
  color: #ff4757;
}

.flash-sale-discount-percent {
  background: #ff4757;
  color: white;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 700;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

.flash-sale-savings {
  color: #ff4757;
  font-size: 14px;
  font-weight: 600;
  text-align: center;
}

/* Flash Sale Action Buttons */
.flash-sale-action-buttons {
  display: flex;
  gap: 12px;
  margin: 20px 0;
}

.flash-sale-cart-btn {
  flex: 1;
  height: 48px;
  border: 2px solid #ff4757;
  background: white;
  color: #ff4757;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  transition: all 0.3s ease;
}

.flash-sale-cart-btn:hover {
  background: #ff4757;
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(255, 71, 87, 0.3);
}

.flash-sale-buy-btn {
  flex: 1.5;
  height: 48px;
  border: none;
  background: linear-gradient(135deg, #ff4757 0%, #ff3838 100%);
  color: white;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(255, 71, 87, 0.3);
}

.flash-sale-buy-btn:hover {
  background: linear-gradient(135deg, #ff3838 0%, #ff2d2d 100%);
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(255, 71, 87, 0.4);
}

/* Flash Sale Description Highlight */
.flash-sale-description-highlight {
  background: linear-gradient(135deg, #fff5f5 0%, #ffebee 100%);
  border-left: 4px solid #ff4757;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 20px;
}

.flash-sale-description-highlight h3 {
  color: #ff4757;
  font-size: 16px;
  font-weight: 700;
  margin-bottom: 8px;
}

.flash-sale-description-highlight p {
  color: #666;
  font-size: 14px;
  line-height: 1.5;
  margin: 0;
}

/* Mobile Responsive for Flash Sale */
@media (max-width: 768px) {
  .flash-sale-banner {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }
  
  .flash-sale-title {
    font-size: 20px;
  }
  
  .timer-display {
    justify-content: center;
  }
  
  .flash-sale-price-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .flash-sale-current-price {
    font-size: 24px;
  }
  
  .flash-sale-action-buttons {
    flex-direction: column;
  }

  .flash-sale-cart-btn,
  .flash-sale-buy-btn {
    width: 100%;
  }
}

/* Flash Sale Related Products */
.flash-sale-related {
  background: linear-gradient(135deg, #fff5f5 0%, #ffebee 100%);
  border: 2px solid #ff4757;
}

.flash-sale-related .shopee-section-title {
  color: #ff4757;
  background: linear-gradient(135deg, #ff4757 0%, #ff6b6b 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.flash-sale-card {
  border: 2px solid #ff4757;
  background: linear-gradient(135deg, #fff 0%, #fff5f5 100%);
}

.flash-sale-card:hover {
  box-shadow: 0 6px 20px rgba(255, 71, 87, 0.2);
  transform: translateY(-4px);
}

.flash-sale-related-badge {
  position: absolute;
  top: 8px;
  left: 8px;
  background: linear-gradient(135deg, #ff4757 0%, #ff3838 100%);
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 700;
  z-index: 10;
}

.flash-sale-price {
  color: #ff4757 !important;
}

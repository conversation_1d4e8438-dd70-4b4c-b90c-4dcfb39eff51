import { NextRequest, NextResponse } from 'next/server'
import { supabase } from '@/lib/supabase'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const tenantId = searchParams.get('tenantId')
    const roleFilter = searchParams.get('role') // 'buyer', 'store', 'affiliate', 'all'
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '50')
    const offset = (page - 1) * limit

    if (!tenantId) {
      return NextResponse.json(
        { error: 'tenantId is required' },
        { status: 400 }
      )
    }

    // Query menggunakan view user_roles untuk mendapatkan data lengkap
    let query = supabase
      .from('user_roles')
      .select('*')
      .eq('tenant_id', tenantId)

    // Filter berdasarkan role jika diminta
    if (roleFilter) {
      switch (roleFilter) {
        case 'buyer':
          query = query.eq('is_buyer', true)
          break
        case 'store':
          query = query.eq('is_store_owner', true)
          break
        case 'affiliate':
          query = query.eq('is_affiliate', true)
          break
        case 'buyer-only':
          query = query.eq('is_buyer', true).eq('is_store_owner', false).eq('is_affiliate', false)
          break
        case 'buyer-store':
          query = query.eq('is_buyer', true).eq('is_store_owner', true).eq('is_affiliate', false)
          break
        case 'buyer-affiliate':
          query = query.eq('is_buyer', true).eq('is_store_owner', false).eq('is_affiliate', true)
          break
        case 'buyer-store-affiliate':
          query = query.eq('is_buyer', true).eq('is_store_owner', true).eq('is_affiliate', true)
          break
      }
    }

    // Pagination
    query = query.range(offset, offset + limit - 1)

    // Order by creation date
    query = query.order('createdat', { ascending: false })

    const { data: buyers, error } = await query

    if (error) {
      console.error('Error fetching buyers:', error)
      return NextResponse.json(
        { error: 'Failed to fetch buyers' },
        { status: 500 }
      )
    }

    // Get total count for pagination
    const { count, error: countError } = await supabase
      .from('user_roles')
      .select('*', { count: 'exact', head: true })
      .eq('tenant_id', tenantId)

    if (countError) {
      console.error('Error counting buyers:', countError)
    }

    // Transform data untuk response
    const transformedBuyers = buyers?.map(buyer => ({
      id: buyer.user_id,
      email: buyer.email,
      name: buyer.name,
      primaryRole: buyer.primary_role,
      capabilities: {
        isBuyer: buyer.is_buyer,
        isStoreOwner: buyer.is_store_owner,
        isAffiliate: buyer.is_affiliate
      },
      buyerData: buyer.is_buyer ? {
        level: buyer.buyer_level,
        totalOrders: buyer.total_orders,
        totalSpent: buyer.total_spent,
        storeId: buyer.buyer_store_id
      } : null,
      storeData: buyer.is_store_owner ? {
        storeId: buyer.owned_store_id
      } : null,
      affiliateData: buyer.is_affiliate ? {
        code: buyer.affiliate_code,
        level: buyer.affiliate_level,
        totalCommission: buyer.total_commission
      } : null,
      createdAt: buyer.createdat,
      updatedAt: buyer.updatedat
    })) || []

    return NextResponse.json({
      buyers: transformedBuyers,
      pagination: {
        page,
        limit,
        total: count || 0,
        totalPages: Math.ceil((count || 0) / limit)
      }
    })

  } catch (error) {
    console.error('Error in buyers API:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { userId, tenantId, capabilities, buyerData, affiliateData } = body

    if (!userId || !tenantId) {
      return NextResponse.json(
        { error: 'userId and tenantId are required' },
        { status: 400 }
      )
    }

    // Insert/update buyer record
    if (capabilities.isBuyer) {
      const { error: buyerError } = await supabase
        .from('buyers')
        .upsert({
          user_id: userId,
          tenant_id: tenantId,
          is_buyer: true,
          is_store_owner: capabilities.isStoreOwner || false,
          is_affiliate: capabilities.isAffiliate || false,
          buyer_level: buyerData?.level || 'bronze',
          referral_code: buyerData?.referralCode || `BUY${Date.now()}`,
          updated_at: new Date().toISOString()
        })

      if (buyerError) {
        console.error('Error creating buyer:', buyerError)
        return NextResponse.json(
          { error: 'Failed to create buyer' },
          { status: 500 }
        )
      }
    }

    // Insert/update affiliate record
    if (capabilities.isAffiliate) {
      const { error: affiliateError } = await supabase
        .from('affiliates')
        .upsert({
          user_id: userId,
          tenant_id: tenantId,
          affiliate_code: affiliateData?.code || `AFF${Date.now()}`,
          commission_rate: affiliateData?.commissionRate || 5.00,
          affiliate_level: affiliateData?.level || 'bronze',
          updated_at: new Date().toISOString()
        })

      if (affiliateError) {
        console.error('Error creating affiliate:', affiliateError)
        return NextResponse.json(
          { error: 'Failed to create affiliate' },
          { status: 500 }
        )
      }
    }

    return NextResponse.json({
      message: 'Buyer capabilities updated successfully'
    })

  } catch (error) {
    console.error('Error in buyers POST API:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

# SaaS Multi-Tenant System - Sellzio Marketplace

## Hierarki Sistem

### 1. **Admin** (Super Admin)
- **Akses**: Semua data di seluruh platform
- **Dapat melihat**: Semua tenant, store, buyer, dan data transaksi
- **Login**: `localhost:3000/admin/login`
- **Credentials**: `<EMAIL>` / `admin123`

### 2. **Tenant** (Marketplace Owner)
- **Akses**: Data dalam tenant mereka saja
- **Dapat melihat**: 
  - Semua store dalam tenant mereka
  - Semua buyer dalam tenant mereka
  - Data transaksi dalam tenant mereka
- **Tidak dapat melihat**: Data dari tenant lain
- **Login**: `localhost:3000/app/login`
- **Credentials**: `<EMAIL>` / `user123`

### 3. **Store Owner** (Penjual)
- **Akses**: Data store mereka dalam tenant yang sama
- **Dapat melihat**:
  - Buyer dalam tenant yang sama (potential customers)
  - Data produk dan pesanan store mereka
  - Analytics store mereka
- **Tidak dapat melihat**: 
  - Data store lain (meskipun dalam tenant yang sama)
  - Data dari tenant lain
- **Login**: `localhost:3000/app/login`
- **Credentials**: `<EMAIL>` / `user123`

### 4. **Buyer** (Pembeli)
- **Akses**: Data mereka sendiri saja
- **Dapat melihat**: 
  - Profil dan pesanan mereka
  - Produk dari semua store dalam tenant
- **Login**: `localhost:3000/app/login`
- **Credentials**: `<EMAIL>` / `user123`

## Database Structure

### Relasi Tabel:
```
tenants (id, name, slug, ...)
├── stores (id, name, tenant_id, ...)
└── User (id, email, role, tenantId, storeId, ...)
```

### Roles:
- `ADMIN` → Admin platform
- `TENANT` → Tenant/marketplace owner
- `STORE_OWNER` → Store owner/seller
- `USER` → Buyer/customer

## API Endpoints

### 1. Tenant APIs
- `GET /api/tenant/stores?tenantId={id}` - Get stores dalam tenant
- `GET /api/tenant/users?tenantId={id}&role=USER` - Get buyers dalam tenant
- `GET /api/tenant/users?tenantId={id}&role=STORE_OWNER` - Get store owners dalam tenant

### 2. Store APIs
- `GET /api/store/buyers?storeId={id}&tenantId={id}` - Get buyers dalam tenant yang sama dengan store

## Dashboard Features

### Tenant Dashboard (`/tenant/dashboard`)
- **Overview**: Metrics semua store dan buyer dalam tenant
- **Stores**: Manage semua store dalam tenant
- **Buyers**: Lihat semua buyer dalam tenant
- **Products**: Manage produk dari semua store
- **Orders**: Lihat pesanan dari semua store
- **Analytics**: Analytics gabungan dari semua store

### Store Dashboard (`/store/dashboard`)
- **Overview**: Metrics store individual
- **Products**: Manage produk store
- **Orders**: Pesanan store
- **Buyers**: Lihat buyer dalam tenant yang sama (potential customers)
- **Analytics**: Analytics store individual

### Admin Dashboard (`/admin/dashboard`)
- **Overview**: Metrics seluruh platform
- **Tenants**: Manage semua tenant
- **Stores**: Lihat semua store di platform
- **Users**: Manage semua user
- **System**: Pengaturan platform

## Security & Data Isolation

### 1. **Tenant Isolation**
- Setiap query database di-filter berdasarkan `tenantId`
- Store hanya bisa akses data dalam tenant mereka
- Buyer hanya bisa lihat produk dalam tenant mereka

### 2. **Store Isolation**
- Store owner hanya bisa manage store mereka sendiri
- Tidak bisa akses data store lain meskipun dalam tenant yang sama
- Bisa lihat buyer dalam tenant sebagai potential customers

### 3. **API Security**
- Semua API endpoint memvalidasi `tenantId` dan `storeId`
- AuthContext menyimpan user role dan tenant/store association
- Middleware memblokir akses cross-tenant

## Login System

### URL Access:
1. **User Login** (tenant, store, buyer):
   - `localhost:3000/app/login`
   - `localhost:3000/login?app=true`
   - `app.localhost:3000/login` (jika hosts disetup)

2. **Admin Login**:
   - `localhost:3000/admin/login`

### Auto Redirect:
- `localhost:3000/login` → redirect ke `/app/login`
- `localhost:3000/auth/login` → redirect ke `/app/login`

## Testing Credentials

### Database Users:
- **Admin**: `<EMAIL>` / `admin123`
- **Tenant**: `<EMAIL>` / `user123`
- **Store**: `<EMAIL>` / `user123`
- **Buyer**: `<EMAIL>` / `user123`
- **Khadijah**: `<EMAIL>` / `user123`

## Implementation Benefits

### 1. **True Multi-Tenancy**
- Complete data isolation between tenants
- Scalable architecture for multiple marketplaces
- Shared infrastructure with isolated data

### 2. **Role-Based Access Control**
- Granular permissions based on user role
- Hierarchical access (Admin > Tenant > Store > Buyer)
- Secure data boundaries

### 3. **Marketplace Flexibility**
- Each tenant can have multiple stores
- Store owners can manage their business independently
- Buyers can shop across all stores in their tenant

### 4. **Business Model Support**
- SaaS model: Multiple tenants paying for platform access
- Marketplace model: Stores paying commission to tenant
- Platform model: Admin managing entire ecosystem

## Next Steps

1. **Enhanced Security**: Implement Row Level Security (RLS) in Supabase
2. **Performance**: Add database indexing for tenant-based queries
3. **Features**: Add cross-tenant analytics for admin
4. **Monitoring**: Implement tenant-specific logging and monitoring
5. **Billing**: Add tenant-based billing and subscription management

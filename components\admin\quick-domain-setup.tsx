"use client"

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { useToast } from '@/hooks/use-toast'
import { Copy, ExternalLink, CheckCircle, AlertCircle, Globe, Zap, Plus } from 'lucide-react'

interface QuickDomainSetupProps {
  onDomainAdded?: () => void
}

export function QuickDomainSetup({ onDomainAdded }: QuickDomainSetupProps) {
  const [tenantId, setTenantId] = useState('')
  const [domain, setDomain] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const { toast } = useToast()

  const handleQuickSetup = async () => {
    if (!tenantId.trim() || !domain.trim()) {
      toast({
        title: "Error",
        description: "Please enter both tenant ID and domain name",
        variant: "destructive"
      })
      return
    }

    setIsLoading(true)
    try {
      // Set domain
      const response = await fetch('/api/tenants/lookup-domain', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          domain: domain.trim(),
          tenantId: tenantId.trim()
        })
      })

      if (!response.ok) {
        throw new Error('Failed to set domain')
      }

      const data = await response.json()
      
      if (data.success) {
        setTenantId('')
        setDomain('')
        toast({
          title: "Success",
          description: `Domain ${domain} has been set for tenant ${tenantId}`,
        })
        onDomainAdded?.()
      }
    } catch (error) {
      console.error('Error setting domain:', error)
      toast({
        title: "Error",
        description: "Failed to set domain. Please try again.",
        variant: "destructive"
      })
    } finally {
      setIsLoading(false)
    }
  }

  const copyDNSInstructions = () => {
    const instructions = `DNS Configuration for ${domain}:

Type: CNAME
Name: @
Target: ${tenantId}.sellzio.com

Type: CNAME  
Name: www
Target: ${tenantId}.sellzio.com`

    navigator.clipboard.writeText(instructions)
    toast({
      title: "Copied",
      description: "DNS instructions copied to clipboard",
    })
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Zap className="h-5 w-5 text-orange-500" />
          Quick Domain Setup
        </CardTitle>
        <CardDescription>
          Quickly assign a custom domain to any tenant
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Quick Setup Form */}
        <div className="grid gap-4 md:grid-cols-2">
          <div className="space-y-2">
            <Label htmlFor="tenant-id">Tenant ID</Label>
            <Input
              id="tenant-id"
              placeholder="sellzio"
              value={tenantId}
              onChange={(e) => setTenantId(e.target.value)}
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="domain-name">Domain Name</Label>
            <Input
              id="domain-name"
              placeholder="example.com"
              value={domain}
              onChange={(e) => setDomain(e.target.value)}
            />
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex gap-2">
          <Button 
            onClick={handleQuickSetup}
            disabled={isLoading || !tenantId.trim() || !domain.trim()}
            className="flex-1"
          >
            <Plus className="h-4 w-4 mr-2" />
            {isLoading ? 'Setting Domain...' : 'Set Domain'}
          </Button>
          
          {domain && tenantId && (
            <Button 
              variant="outline"
              onClick={copyDNSInstructions}
            >
              <Copy className="h-4 w-4 mr-2" />
              Copy DNS
            </Button>
          )}
        </div>

        {/* Preview */}
        {domain && tenantId && (
          <Alert>
            <Globe className="h-4 w-4" />
            <AlertTitle>Domain Preview</AlertTitle>
            <AlertDescription className="mt-2">
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm">Custom Domain:</span>
                  <Badge variant="outline">{domain}</Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Target Tenant:</span>
                  <Badge variant="outline">{tenantId}</Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Subdomain:</span>
                  <Badge variant="outline">{tenantId}.sellzio.com</Badge>
                </div>
              </div>
            </AlertDescription>
          </Alert>
        )}

        {/* DNS Instructions */}
        {domain && tenantId && (
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>DNS Configuration Required</AlertTitle>
            <AlertDescription className="mt-2">
              <p className="mb-3">Configure these DNS records in your domain provider:</p>
              <div className="bg-gray-50 p-3 rounded text-sm font-mono border space-y-2">
                <div className="grid grid-cols-3 gap-2">
                  <div><strong>Type:</strong> CNAME</div>
                  <div><strong>Name:</strong> @</div>
                  <div><strong>Target:</strong> {tenantId}.sellzio.com</div>
                </div>
                <div className="grid grid-cols-3 gap-2">
                  <div><strong>Type:</strong> CNAME</div>
                  <div><strong>Name:</strong> www</div>
                  <div><strong>Target:</strong> {tenantId}.sellzio.com</div>
                </div>
              </div>
              <p className="mt-3 text-xs text-muted-foreground">
                Changes may take up to 24 hours to propagate globally.
              </p>
            </AlertDescription>
          </Alert>
        )}
      </CardContent>
    </Card>
  )
}

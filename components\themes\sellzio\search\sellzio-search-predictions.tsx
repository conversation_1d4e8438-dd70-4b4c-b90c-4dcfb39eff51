"use client"

import React from 'react'

interface Prediction {
  text: string
  type: string
  relevance: number
}

interface SellzioSearchPredictionsProps {
  predictions: Prediction[]
  searchValue: string
  onPredictionClick: (prediction: Prediction) => void
}

export const SellzioSearchPredictions: React.FC<SellzioSearchPredictionsProps> = ({
  predictions,
  searchValue,
  onPredictionClick
}) => {
  // Function to convert text to Title Case (huruf besar di depan setiap kata)
  const toTitleCase = (text: string) => {
    return text.toLowerCase()
  }

  // Check if prediction contains main keyword from input
  const containsMainKeyword = (input: string, predictionText: string) => {
    const inputWords = input.toLowerCase().trim().split(' ')
    const mainWord = inputWords[0]
    if (mainWord && mainWord.length >= 2) {
      return predictionText.toLowerCase().includes(mainWord)
    }
    return false
  }

  // Create highlighted text
  const highlightText = (text: string, input: string) => {
    if (!input.trim()) return text

    try {
      // Escape special regex characters
      const escapedInput = input.trim().replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
      const regex = new RegExp(`(${escapedInput})`, 'gi')
      const parts = text.split(regex)

      return parts.map((part, i) =>
        regex.test(part) ?
          <span key={i} className="highlighted">{part}</span> :
          part
      )
    } catch (e) {
      // Fallback if regex fails
      return text
    }
  }

  return (
    <div className="keyword-predictions" onClick={(e) => e.stopPropagation()}>
      {predictions.map((prediction, index) => {
        // Determine icon based on prediction type
        let iconClass = 'fa-search'
        if (prediction.type === 'history') {
          iconClass = 'fa-history'
        } else if (prediction.type === 'product') {
          iconClass = 'fa-shopping-cart'
        } else if (prediction.type === 'trending') {
          iconClass = 'fa-arrow-trend-up'
        }

        const isRelevant = containsMainKeyword(searchValue, prediction.text)

        return (
          <div
            key={index}
            className="prediction-item"
            onClick={() => onPredictionClick(prediction)}
          >
            <span className={`prediction-icon ${isRelevant ? 'matched' : ''}`}>
              <i className={`fa ${iconClass}`}></i>
            </span>
            <span className="prediction-text">
              {highlightText(toTitleCase(prediction.text), searchValue)}
            </span>
          </div>
        )
      })}
    </div>
  )
}

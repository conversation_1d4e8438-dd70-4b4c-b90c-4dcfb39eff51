{"version": 2, "buildCommand": "next build", "devCommand": "next dev", "installCommand": "bun install", "framework": "nextjs", "outputDirectory": ".next", "regions": ["iad1"], "functions": {"app/admin/dashboard/**/*": {"memory": 1024, "maxDuration": 10}}, "env": {"NEXT_PUBLIC_SUPABASE_URL": "https://uhtxhwhpobrbmcettmpt.supabase.co", "NEXT_PUBLIC_SUPABASE_ANON_KEY": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVodHhod2hwb2JyYm1jZXR0bXB0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYyNDAxODgsImV4cCI6MjA2MTgxNjE4OH0.j2sIBcA1PM4ZElH2qMn0bvx_JVl2jARsnIw6DUP154k", "SUPABASE_SERVICE_ROLE_KEY": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVodHhod2hwb2JyYm1jZXR0bXB0Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NjI0MDE4OCwiZXhwIjoyMDYxODE2MTg4fQ.yw5olRmawtEmDgfF1S65LgEXFRlVUUxW332NLVHqTaE", "NEXT_PUBLIC_MAIN_DOMAIN": "sellzio.my.id", "NEXT_PUBLIC_DASHBOARD_SUBDOMAIN": "app", "NEXT_PUBLIC_APP_URL": "https://sellzio.my.id", "CLOUDFLARE_API_TOKEN": "YOUR_CLOUDFLARE_API_TOKEN", "CLOUDFLARE_ZONE_ID": "YOUR_CLOUDFLARE_ZONE_ID", "VERCEL_IP": "***********"}, "headers": [{"source": "/(.*)", "headers": [{"key": "Cache-Control", "value": "no-store, max-age=0"}]}]}
import type React from "react"
import { use } from "react"
import { TenantThemeProvider } from "@/components/tenant/tenant-theme-provider"

interface TenantLayoutProps {
  params: Promise<{ slug: string }>
  children: React.ReactNode
}

export default function TenantLayout({ params, children }: TenantLayoutProps) {
  const resolvedParams = use(params)
  return <TenantThemeProvider tenantId={resolvedParams.slug}>{children}</TenantThemeProvider>
}

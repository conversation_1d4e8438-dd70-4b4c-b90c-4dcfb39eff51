"use client"

import type React from "react"

import { useEffect } from "react"
import { useRouter } from "next/navigation"
import { StoreHeader } from "@/components/store/store-header"
import { StoreSidebar } from "@/components/store/store-sidebar"
import { useAuth } from "@/contexts/auth-context"
import { Skeleton } from "@/components/ui/skeleton"
import { SidebarProvider } from "@/components/ui/sidebar"

export default function StoreDashboardLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const { user, loading } = useAuth()
  const router = useRouter()

  useEffect(() => {
    if (!loading && !user && typeof window !== 'undefined') {
      if (!window.location.pathname.includes('/login')) {
        router.push("/login")
      }
    }

    // Redirect jika user tidak memiliki toko
    if (!loading && user && !user.storeId) {
      router.push("/buyer/dashboard/store-application")
    }
  }, [loading, user, router])

  if (loading) {
    return (
      <div className="flex min-h-screen flex-col">
        <div className="h-14 border-b border-border/40">
          <Skeleton className="h-full w-full" />
        </div>
        <div className="flex flex-1">
          <div className="w-64 border-r border-border/40">
            <Skeleton className="h-full w-full" />
          </div>
          <main className="flex-1 p-6">
            <Skeleton className="h-8 w-64 mb-6" />
            <Skeleton className="h-64 w-full" />
          </main>
        </div>
      </div>
    )
  }

  // Jika user tidak memiliki toko, jangan tampilkan dashboard
  if (!user || !user.storeId) {
    return null
  }

  return (
    <SidebarProvider>
      <div className="flex min-h-screen flex-col bg-background">
        <StoreHeader />
        <div className="flex flex-1">
          <StoreSidebar />
          <main className="flex-1 overflow-auto p-6">{children}</main>
        </div>
      </div>
    </SidebarProvider>
  )
}

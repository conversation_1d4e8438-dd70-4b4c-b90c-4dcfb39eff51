"use client"

import React, { useState } from 'react'
import { X } from 'lucide-react'

interface PaymentMethod {
  id: string
  name: string
  type: string
  isSelected?: boolean
}

interface SellzioPaymentModalProps {
  isOpen: boolean
  onClose: () => void
  currentMethod: PaymentMethod
  onSelect: (method: PaymentMethod) => void
}

export const SellzioPaymentModal: React.FC<SellzioPaymentModalProps> = ({
  isOpen,
  onClose,
  currentMethod,
  onSelect
}) => {
  const [selectedMethod, setSelectedMethod] = useState(currentMethod.id)

  const paymentMethods: PaymentMethod[] = [
    {
      id: "bca",
      name: "Transfer Bank - Bank BCA",
      type: "bank_transfer"
    },
    {
      id: "mandiri",
      name: "Transfer Bank - Bank Mandiri",
      type: "bank_transfer"
    },
    {
      id: "bri",
      name: "Transfer Bank - Bank BRI",
      type: "bank_transfer"
    },
    {
      id: "bni",
      name: "Transfer Bank - Bank BNI",
      type: "bank_transfer"
    },
    {
      id: "gopay",
      name: "GoP<PERSON>",
      type: "e_wallet"
    },
    {
      id: "ovo",
      name: "O<PERSON>",
      type: "e_wallet"
    },
    {
      id: "dana",
      name: "DANA",
      type: "e_wallet"
    },
    {
      id: "shopeepay",
      name: "ShopeePay",
      type: "e_wallet"
    },
    {
      id: "credit_card",
      name: "Kartu Kredit/Debit",
      type: "card"
    },
    {
      id: "cod",
      name: "Bayar di Tempat (COD)",
      type: "cod"
    }
  ]

  const groupedMethods = {
    bank_transfer: paymentMethods.filter(m => m.type === 'bank_transfer'),
    e_wallet: paymentMethods.filter(m => m.type === 'e_wallet'),
    card: paymentMethods.filter(m => m.type === 'card'),
    cod: paymentMethods.filter(m => m.type === 'cod')
  }

  const handleSelect = (methodId: string) => {
    setSelectedMethod(methodId)
  }

  const handleSave = () => {
    const selected = paymentMethods.find(m => m.id === selectedMethod)
    if (selected) {
      onSelect({ ...selected, isSelected: true })
    }
    onClose()
  }

  const getMethodIcon = (type: string) => {
    switch (type) {
      case 'bank_transfer':
        return '🏦'
      case 'e_wallet':
        return '📱'
      case 'card':
        return '💳'
      case 'cod':
        return '💵'
      default:
        return '💰'
    }
  }

  if (!isOpen) return null

  return (
    <div className="sellzio-modal-overlay">
      <div className="sellzio-modal">
        <div className="sellzio-modal-header">
          <h2>Pilih Metode Pembayaran</h2>
          <button className="sellzio-modal-close" onClick={onClose}>
            <X size={20} />
          </button>
        </div>
        
        <div className="sellzio-modal-content">
          <div className="sellzio-payment-groups">
            <div className="sellzio-payment-group">
              <h3 className="sellzio-payment-group-title">Transfer Bank</h3>
              {groupedMethods.bank_transfer.map((method) => (
                <div
                  key={method.id}
                  className={`sellzio-payment-method-item ${
                    selectedMethod === method.id ? 'selected' : ''
                  }`}
                  onClick={() => handleSelect(method.id)}
                >
                  <div className="sellzio-payment-method-radio">
                    <input
                      type="radio"
                      name="payment"
                      checked={selectedMethod === method.id}
                      onChange={() => handleSelect(method.id)}
                    />
                  </div>
                  <div className="sellzio-payment-method-icon">
                    {getMethodIcon(method.type)}
                  </div>
                  <div className="sellzio-payment-method-name">
                    {method.name}
                  </div>
                </div>
              ))}
            </div>

            <div className="sellzio-payment-group">
              <h3 className="sellzio-payment-group-title">E-Wallet</h3>
              {groupedMethods.e_wallet.map((method) => (
                <div
                  key={method.id}
                  className={`sellzio-payment-method-item ${
                    selectedMethod === method.id ? 'selected' : ''
                  }`}
                  onClick={() => handleSelect(method.id)}
                >
                  <div className="sellzio-payment-method-radio">
                    <input
                      type="radio"
                      name="payment"
                      checked={selectedMethod === method.id}
                      onChange={() => handleSelect(method.id)}
                    />
                  </div>
                  <div className="sellzio-payment-method-icon">
                    {getMethodIcon(method.type)}
                  </div>
                  <div className="sellzio-payment-method-name">
                    {method.name}
                  </div>
                </div>
              ))}
            </div>

            <div className="sellzio-payment-group">
              <h3 className="sellzio-payment-group-title">Lainnya</h3>
              {[...groupedMethods.card, ...groupedMethods.cod].map((method) => (
                <div
                  key={method.id}
                  className={`sellzio-payment-method-item ${
                    selectedMethod === method.id ? 'selected' : ''
                  }`}
                  onClick={() => handleSelect(method.id)}
                >
                  <div className="sellzio-payment-method-radio">
                    <input
                      type="radio"
                      name="payment"
                      checked={selectedMethod === method.id}
                      onChange={() => handleSelect(method.id)}
                    />
                  </div>
                  <div className="sellzio-payment-method-icon">
                    {getMethodIcon(method.type)}
                  </div>
                  <div className="sellzio-payment-method-name">
                    {method.name}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
        
        <div className="sellzio-modal-footer">
          <button className="sellzio-btn-secondary" onClick={onClose}>
            Batal
          </button>
          <button className="sellzio-btn-primary" onClick={handleSave}>
            Pilih
          </button>
        </div>
      </div>
    </div>
  )
}

"use client"

import { useState, use<PERSON><PERSON>back, useEffect, useRef } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { PlusCircle, RefreshCw, MoreHorizontal, Eye, Ban, CheckCircle, Trash2, Edit, LogIn } from "lucide-react"
import Link from "next/link"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { useRouter } from "next/navigation"
import { useNotifications } from "@/components/providers/notifications-provider"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { SuspendTenantModal } from "@/components/admin/tenants/suspend-tenant-modal"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { LoginAsTenantModal } from "@/components/admin/tenants/login-as-tenant-modal"
import {
  getAllTenants,
  suspendTenant,
  reactivateTenant,
  deleteTenant,
  TENANTS_STORAGE_KEY,
} from "@/lib/services/tenant-service"
import { isWithinInterval, parseISO } from "date-fns"
import { DateRangePicker } from "@/components/ui/date-range-picker"
import type { DateRange } from "react-day-picker"
import { format } from "date-fns"
import { id } from "date-fns/locale"

export default function TenantsPage() {
  const router = useRouter()
  const { addNotification } = useNotifications()
  const [tenants, setTenants] = useState([])
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")
  const [dateRange, setDateRange] = useState<DateRange | undefined>(undefined)
  const [isLoading, setIsLoading] = useState(false)
  const [isActionInProgress, setIsActionInProgress] = useState(false)
  const [isPageLoaded, setIsPageLoaded] = useState(false)
  const actionTimeoutRef = useRef(null)

  // State untuk modal dan dialog
  const [suspendModalOpen, setSuspendModalOpen] = useState(false)
  const [selectedTenant, setSelectedTenant] = useState(null)
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [loginModalOpen, setLoginModalOpen] = useState(false)

  // Cleanup function untuk timeouts
  const clearAllTimeouts = () => {
    if (actionTimeoutRef.current) {
      clearTimeout(actionTimeoutRef.current)
      actionTimeoutRef.current = null
    }
  }

  // Membersihkan timeout saat component unmount
  useEffect(() => {
    return () => {
      clearAllTimeouts()
    }
  }, [])

  // Load data from service
  const loadTenants = useCallback(async () => {
    try {
      setIsLoading(true)
      const data = await getAllTenants()
      setTenants(data)
    } catch (error) {
      console.error("Error loading tenants:", error)
    } finally {
      setIsLoading(false)
      setIsPageLoaded(true)
    }
  }, [])

  // Load data on initial render
  useEffect(() => {
    loadTenants()
  }, [loadTenants])

  // Listen for storage events from other tabs/windows
  useEffect(() => {
    const handleStorageChange = (event) => {
      if (event.key === TENANTS_STORAGE_KEY) {
        loadTenants()
      }
    }

    window.addEventListener("storage", handleStorageChange)
    return () => {
      window.removeEventListener("storage", handleStorageChange)
    }
  }, [loadTenants])

  // Filter tenants based on all criteria
  const filterTenant = useCallback(
    (tenant) => {
      const matchesSearch =
        tenant.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        tenant.domain?.toLowerCase().includes(searchTerm.toLowerCase())

      const matchesStatus = statusFilter === "all" || tenant.status === statusFilter

      // Filter berdasarkan rentang tanggal
      let matchesDateRange = true
      if (dateRange?.from && dateRange?.to) {
        try {
          const createdDate = parseISO(tenant.createdAt)
          // Set waktu ke 00:00:00 untuk from dan 23:59:59 untuk to
          const fromDate = new Date(dateRange.from)
          fromDate.setHours(0, 0, 0, 0)

          const toDate = new Date(dateRange.to)
          toDate.setHours(23, 59, 59, 999)

          matchesDateRange = isWithinInterval(createdDate, {
            start: fromDate,
            end: toDate,
          })
        } catch (error) {
          console.error("Error parsing date:", error)
          matchesDateRange = true
        }
      }

      return matchesSearch && matchesStatus && matchesDateRange
    },
    [searchTerm, statusFilter, dateRange],
  )

  // Apply filter to get filtered tenants
  const filteredTenants = tenants.filter(filterTenant)

  // Hitung statistik berdasarkan tenant yang sudah difilter
  const activeTenants = filteredTenants.filter((t) => t.status === "active").length
  const totalRevenue = filteredTenants.reduce((sum, tenant) => {
    const revenue = Number.parseFloat(tenant.revenue.replace("$", "").replace(",", ""))
    return sum + revenue
  }, 0)

  // Handle refresh
  const handleRefresh = useCallback(() => {
    // Clear any existing timeouts
    clearAllTimeouts()

    setIsLoading(true)
    setIsActionInProgress(true)

    // Reload tenants
    loadTenants().then(() => {
      setIsLoading(false)
      setIsActionInProgress(false)

      addNotification({
        message: "Data tenant berhasil dimuat ulang",
        type: "success",
      })

      // Reset selection
      setSelectedTenant(null)
    })
  }, [addNotification, loadTenants])

  // Handle suspend tenant
  const handleOpenSuspendModal = useCallback((tenant) => {
    // Set selectedTenant first, then open modal
    setSelectedTenant(tenant)

    // Use a small timeout to ensure the state is updated before opening modal
    setTimeout(() => {
      setSuspendModalOpen(true)
    }, 50)
  }, [])

  // Ubah juga bagian handleSuspendTenant untuk menyimpan suspensionEndDate
  const handleSuspendTenant = useCallback(
    async (data) => {
      // Clear any existing timeouts
      clearAllTimeouts()

      setIsLoading(true)
      setIsActionInProgress(true)

      // Capture tenant name for notification
      const tenantName = selectedTenant?.name

      try {
        // Suspend tenant using service
        await suspendTenant(
          selectedTenant?.id,
          data.reason,
          data.durationType === "temporary" ? data.endDate : undefined,
        )

        // Reload tenants
        await loadTenants()

        // Close modal and reset state
        setSuspendModalOpen(false)
        setSelectedTenant(null)

        // Show notification
        addNotification({
          message: `Tenant ${tenantName} berhasil ditangguhkan`,
          type: "success",
        })
      } catch (error) {
        console.error("Error suspending tenant:", error)
        addNotification({
          message: `Gagal menangguhkan tenant ${tenantName}`,
          type: "error",
        })
      } finally {
        setIsLoading(false)
        setIsActionInProgress(false)
      }
    },
    [selectedTenant, addNotification, loadTenants],
  )

  // Handle activate tenant
  const handleActivateTenant = useCallback(
    async (tenantId, tenantName) => {
      // Clear any existing timeouts
      clearAllTimeouts()

      setIsLoading(true)
      setIsActionInProgress(true)

      try {
        // Activate tenant using service
        await reactivateTenant(tenantId, "Manually activated by admin")

        // Reload tenants
        await loadTenants()

        // Show notification
        addNotification({
          message: `Tenant ${tenantName} berhasil diaktifkan`,
          type: "success",
        })
      } catch (error) {
        console.error("Error activating tenant:", error)
        addNotification({
          message: `Gagal mengaktifkan tenant ${tenantName}`,
          type: "error",
        })
      } finally {
        setIsLoading(false)
        setIsActionInProgress(false)
      }
    },
    [addNotification, loadTenants],
  )

  // Handle delete tenant
  const handleOpenDeleteDialog = useCallback((tenant) => {
    // Set selectedTenant first, then open dialog
    setSelectedTenant(tenant)

    // Use a small timeout to ensure the state is updated before opening dialog
    setTimeout(() => {
      setDeleteDialogOpen(true)
    }, 50)
  }, [])

  const handleDeleteTenant = useCallback(async () => {
    // Clear any existing timeouts
    clearAllTimeouts()

    setIsLoading(true)
    setIsActionInProgress(true)

    // Capture tenant name before deletion for notification
    const tenantName = selectedTenant?.name

    try {
      // Delete tenant using service
      await deleteTenant(selectedTenant?.id)

      // Reload tenants
      await loadTenants()

      // Reset state
      setDeleteDialogOpen(false)
      setSelectedTenant(null)

      // Show notification
      addNotification({
        message: `Tenant ${tenantName} berhasil dihapus`,
        type: "success",
      })
    } catch (error) {
      console.error("Error deleting tenant:", error)
      addNotification({
        message: `Gagal menghapus tenant ${tenantName}`,
        type: "error",
      })
    } finally {
      setIsLoading(false)
      setIsActionInProgress(false)
    }
  }, [selectedTenant, addNotification, loadTenants])

  // Handle modal/dialog close
  const handleModalClose = useCallback(() => {
    // Reset states when modal is closed
    setSuspendModalOpen(false)
    setDeleteDialogOpen(false)
    setSelectedTenant(null)
    setIsLoading(false)
    setIsActionInProgress(false)
  }, [])

  // Ubah fungsi renderStatusBadge untuk menampilkan tooltip dan warna berbeda untuk penangguhan sementara

  // Ganti fungsi renderStatusBadge dengan implementasi berikut:
  const renderStatusBadge = useCallback((tenant) => {
    const status = tenant.status
    const suspensionEndDate = tenant.suspensionEndDate
    const isTemporarySuspension = status === "suspended" && suspensionEndDate

    switch (status) {
      case "active":
        return <Badge className="bg-green-100 text-green-800 border-green-200">Active</Badge>
      case "suspended":
        if (isTemporarySuspension) {
          // Format tanggal untuk tooltip
          const formattedDate = suspensionEndDate
            ? new Date(suspensionEndDate).toLocaleDateString("id-ID", {
                day: "numeric",
                month: "long",
                year: "numeric",
              })
            : ""

          return (
            <Badge
              className="bg-amber-100 text-amber-800 border-amber-200"
              title={`Tenant akan diaktifkan kembali pada ${formattedDate}`}
            >
              Suspended
            </Badge>
          )
        } else {
          return <Badge className="bg-red-100 text-red-800 border-red-200">Suspended</Badge>
        }
      case "pending":
        return <Badge className="bg-yellow-100 text-yellow-800 border-yellow-200">Pending</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }, [])

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Tenants</h2>
          <p className="text-muted-foreground">Kelola semua tenant di platform Sellzio</p>
        </div>
        <div className="flex gap-2">
          <DateRangePicker dateRange={dateRange} onDateRangeChange={setDateRange} />
          <Button asChild disabled={isActionInProgress || isLoading}>
            <Link href="/admin/dashboard/tenants/create">
              <PlusCircle className="mr-2 h-4 w-4" />
              Buat Tenant
            </Link>
          </Button>
        </div>
      </div>

      <div className="grid gap-4 md:grid-cols-3">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Tenants</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{filteredTenants.length}</div>
            <p className="text-xs text-muted-foreground">
              {dateRange?.from && dateRange?.to
                ? `Periode: ${format(dateRange.from, "dd MMM yyyy", { locale: id })} - ${format(
                    dateRange.to,
                    "dd MMM yyyy",
                    { locale: id },
                  )}`
                : "+12% dari bulan lalu"}
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Tenant Aktif</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{activeTenants}</div>
            <p className="text-xs text-muted-foreground">
              {dateRange?.from && dateRange?.to
                ? `${((activeTenants / filteredTenants.length) * 100).toFixed(0)}% dari total`
                : "+5% dari bulan lalu"}
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Pendapatan</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">${totalRevenue.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              {dateRange?.from && dateRange?.to
                ? `Rata-rata: $${(totalRevenue / (filteredTenants.length || 1)).toFixed(0)}/tenant`
                : "+18% dari bulan lalu"}
            </p>
          </CardContent>
        </Card>
      </div>

      <div className="space-y-4">
        <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
          <div className="relative w-full max-w-sm">
            <Input
              type="search"
              placeholder="Cari tenant..."
              className="w-full pl-8"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              disabled={isActionInProgress || isLoading}
            />
          </div>
          <div className="flex flex-col gap-4 sm:flex-row">
            <Select value={statusFilter} onValueChange={setStatusFilter} disabled={isActionInProgress || isLoading}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Semua Status</SelectItem>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="suspended">Suspended</SelectItem>
                <SelectItem value="pending">Pending</SelectItem>
              </SelectContent>
            </Select>
            <Button variant="outline" size="icon" onClick={handleRefresh} disabled={isActionInProgress || isLoading}>
              <RefreshCw className={`h-4 w-4 ${isLoading ? "animate-spin" : ""}`} />
            </Button>
          </div>
        </div>

        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-[200px]">Name</TableHead>
                <TableHead>Subdomain</TableHead>
                <TableHead>Domain</TableHead>
                <TableHead>Plan</TableHead>
                <TableHead>Status</TableHead>
                <TableHead className="text-center">Store Count</TableHead>
                <TableHead className="text-center">User Count</TableHead>
                <TableHead className="text-right">Revenue</TableHead>
                <TableHead>Created Date</TableHead>
                <TableHead className="w-[60px]">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredTenants.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={10} className="h-24 text-center">
                    Tidak ada tenant yang ditemukan.
                  </TableCell>
                </TableRow>
              ) : (
                filteredTenants.map((tenant) => (
                  <TableRow key={tenant.id}>
                    <TableCell className="font-medium">{tenant.name}</TableCell>
                    <TableCell className="text-sm font-medium text-gray-700">{tenant.slug}</TableCell>
                    <TableCell>{tenant.domain || '-'}</TableCell>
                    <TableCell>{tenant.plan}</TableCell>
                    <TableCell>{renderStatusBadge(tenant)}</TableCell>
                    <TableCell className="text-center">{tenant.storeCount}</TableCell>
                    <TableCell className="text-center">{tenant.userCount}</TableCell>
                    <TableCell className="text-right">{tenant.revenue}</TableCell>
                    <TableCell>{new Date(tenant.createdAt).toLocaleDateString('id-ID', { day: 'numeric', month: 'long', year: 'numeric' })}</TableCell>
                    <TableCell>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-8 w-8 p-0"
                            data-state="closed"
                            disabled={isActionInProgress || isLoading}
                          >
                            <span className="sr-only">Open menu</span>
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuLabel>Actions</DropdownMenuLabel>
                          <DropdownMenuItem onClick={() => router.push(`/admin/dashboard/tenants/${tenant.id}`)}>
                            <Eye className="mr-2 h-4 w-4" />
                            View
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => router.push(`/admin/dashboard/tenants/${tenant.id}/edit`)}>
                            <Edit className="mr-2 h-4 w-4" />
                            Edit
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem
                            onClick={() => {
                              setSelectedTenant(tenant)
                              setLoginModalOpen(true)
                            }}
                          >
                            <LogIn className="mr-2 h-4 w-4" />
                            Login as
                          </DropdownMenuItem>
                          {tenant.status === "active" ? (
                            <DropdownMenuItem onClick={() => handleOpenSuspendModal(tenant)} className="text-red-600">
                              <Ban className="mr-2 h-4 w-4" />
                              Suspend
                            </DropdownMenuItem>
                          ) : (
                            <DropdownMenuItem
                              onClick={() => handleActivateTenant(tenant.id, tenant.name)}
                              className="text-green-600"
                            >
                              <CheckCircle className="mr-2 h-4 w-4" />
                              Activate
                            </DropdownMenuItem>
                          )}
                          <DropdownMenuItem onClick={() => handleOpenDeleteDialog(tenant)} className="text-red-600">
                            <Trash2 className="mr-2 h-4 w-4" />
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>
      </div>

      {/* Modal untuk suspend tenant */}
      <SuspendTenantModal
        tenant={selectedTenant || { id: "", name: "" }}
        open={suspendModalOpen}
        onOpenChange={(open) => {
          setSuspendModalOpen(open)
          if (!open) {
            // Reset states when modal is closed
            handleModalClose()
          }
        }}
        onSuspend={handleSuspendTenant}
        isLoading={isLoading}
      />

      {/* Dialog konfirmasi untuk delete tenant */}
      <AlertDialog
        open={deleteDialogOpen}
        onOpenChange={(open) => {
          setDeleteDialogOpen(open)
          if (!open) {
            // Reset states when modal is closed
            handleModalClose()
          }
        }}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Apakah Anda yakin?</AlertDialogTitle>
            <AlertDialogDescription>
              Tindakan ini akan menghapus tenant {selectedTenant?.name} secara permanen dan tidak dapat dibatalkan.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isLoading}>Batal</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteTenant}
              className="bg-red-600 hover:bg-red-700"
              disabled={isLoading}
            >
              {isLoading ? "Menghapus..." : "Hapus"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Modal Login as Tenant */}
      <LoginAsTenantModal
        tenant={selectedTenant || { id: "", name: "" }}
        open={loginModalOpen}
        onOpenChange={(open) => {
          setLoginModalOpen(open)
          if (!open) {
            // Reset states when modal is closed
            setSelectedTenant(null)
          }
        }}
      />
    </div>
  )
}

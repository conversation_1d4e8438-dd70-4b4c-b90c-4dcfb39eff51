"use client"

import { useState, useEffect } from 'react'

// Simple cart item interface for tenant pages
interface SimpleCartItem {
  id: string
  name: string
  price: number
  image: string
  quantity: number
}

interface UseCartReturn {
  // For tenant pages compatibility
  items: SimpleCartItem[]
  addItem: (item: SimpleCartItem) => void
  removeItem: (itemId: string) => void
  updateQuantity: (itemId: string, quantity: number) => void
  clearCart: () => void
  getTotalItems: () => number
  getTotalPrice: () => number

  // For API-based cart (original)
  cartItems: SimpleCartItem[]
  cartCount: number
  isLoading: boolean
  addToCart: (item: Omit<SimpleCartItem, 'quantity'>) => Promise<void>
  removeFromCart: (itemId: string) => Promise<void>
  refreshCart: () => Promise<void>
}

export const useCart = (): UseCartReturn => {
  // Local state for simple cart (tenant pages)
  const [items, setItems] = useState<SimpleCartItem[]>([])

  // API-based cart state
  const [cartItems, setCartItems] = useState<SimpleCartItem[]>([])
  const [isLoading, setIsLoading] = useState(true)

  // Fetch cart items from API
  const fetchCartItems = async () => {
    try {
      setIsLoading(true)
      const response = await fetch('/api/cart')
      if (response.ok) {
        const items = await response.json()
        setCartItems(items)
      }
    } catch (error) {
      console.error('Error fetching cart items:', error)
    } finally {
      setIsLoading(false)
    }
  }

  // Simple cart functions for tenant pages
  const addItem = (item: SimpleCartItem) => {
    setItems(prev => {
      const existingIndex = prev.findIndex(i => i.id === item.id)
      if (existingIndex >= 0) {
        // Update quantity if item exists
        const updated = [...prev]
        updated[existingIndex].quantity += item.quantity
        return updated
      } else {
        // Add new item
        return [...prev, item]
      }
    })
  }

  const removeItem = (itemId: string) => {
    setItems(prev => prev.filter(item => item.id !== itemId))
  }

  const updateQuantity = (itemId: string, quantity: number) => {
    setItems(prev =>
      prev.map(item =>
        item.id === itemId ? { ...item, quantity: Math.max(0, quantity) } : item
      ).filter(item => item.quantity > 0)
    )
  }

  const clearCart = () => {
    setItems([])
  }

  const getTotalItems = () => {
    return items.reduce((total, item) => total + item.quantity, 0)
  }

  const getTotalPrice = () => {
    return items.reduce((total, item) => total + (item.price * item.quantity), 0)
  }

  // Add item to cart (API-based)
  const addToCart = async (item: Omit<SimpleCartItem, 'quantity'>) => {
    try {
      const response = await fetch('/api/cart', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...item,
          quantity: 1
        }),
      })

      if (response.ok) {
        await fetchCartItems() // Refresh cart after adding
      }
    } catch (error) {
      console.error('Error adding to cart:', error)
      throw error
    }
  }

  // Remove item from cart (API-based)
  const removeFromCart = async (itemId: string) => {
    try {
      const response = await fetch(`/api/cart/${itemId}`, {
        method: 'DELETE',
      })

      if (response.ok) {
        setCartItems(prev => prev.filter(item => item.id !== itemId))
      }
    } catch (error) {
      console.error('Error removing from cart:', error)
      throw error
    }
  }

  // Clear entire cart (API-based)
  const clearApiCart = async () => {
    try {
      const response = await fetch('/api/cart', {
        method: 'DELETE',
      })

      if (response.ok) {
        setCartItems([])
      }
    } catch (error) {
      console.error('Error clearing cart:', error)
      throw error
    }
  }

  // Refresh cart data
  const refreshCart = async () => {
    await fetchCartItems()
  }

  // Calculate cart count
  const cartCount = cartItems.reduce((total, item) => total + item.quantity, 0)

  // Load cart items on mount
  useEffect(() => {
    fetchCartItems()
  }, [])

  return {
    // Simple cart for tenant pages
    items,
    addItem,
    removeItem,
    updateQuantity,
    clearCart,
    getTotalItems,
    getTotalPrice,

    // API-based cart
    cartItems,
    cartCount,
    isLoading,
    addToCart,
    removeFromCart,
    refreshCart
  }
}

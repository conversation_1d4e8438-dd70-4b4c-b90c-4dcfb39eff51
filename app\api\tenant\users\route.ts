import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const tenantId = searchParams.get('tenantId')
    const role = searchParams.get('role') // 'STORE_OWNER', 'USER' (buyer)
    
    if (!tenantId) {
      return NextResponse.json({ error: 'Tenant ID is required' }, { status: 400 })
    }

    let query = supabase
      .from('User')
      .select(`
        id,
        email,
        name,
        role,
        tenantId,
        storeId,
        createdAt,
        updatedAt
      `)
      .eq('tenantId', tenantId)

    // Filter by role if specified
    if (role) {
      query = query.eq('role', role)
    }

    const { data: users, error } = await query.order('createdAt', { ascending: false })

    if (error) {
      console.error('Error fetching tenant users:', error)
      return NextResponse.json({ error: 'Failed to fetch users' }, { status: 500 })
    }

    return NextResponse.json({
      users,
      total: users?.length || 0
    })

  } catch (error) {
    console.error('Error in tenant users API:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

"use client"

import { useState, useEffect } from 'react'
import { QuickDomainSetup } from '@/components/admin/quick-domain-setup'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { useToast } from '@/hooks/use-toast'
import { Copy, ExternalLink, CheckCircle, AlertCircle, Globe, Settings, Trash2, Edit, Plus, Search } from 'lucide-react'
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

function getClient() {
  return createClient(supabaseUrl, supabaseAnonKey)
}

interface TenantData {
  id: string
  name: string
  slug: string
  domain: string | null
  subscriptionPlan: string
  subscriptionStatus: string
  createdAt: string
}

interface DomainStatus {
  domain: string
  verified: boolean
  ssl: boolean
  lastChecked: string
}

export default function AdminDomainsPage() {
  const [tenants, setTenants] = useState<TenantData[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedTenant, setSelectedTenant] = useState<TenantData | null>(null)
  const [newDomain, setNewDomain] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [domainStatus, setDomainStatus] = useState<DomainStatus | null>(null)
  const { toast } = useToast()

  useEffect(() => {
    fetchTenants()
  }, [])

  const fetchTenants = async () => {
    try {
      setLoading(true)
      const supabase = getClient()

      const { data, error } = await supabase
        .from('tenants')
        .select('id, name, subdomain as slug, domain, plan as subscriptionPlan, status as subscriptionStatus, created_at as createdAt')
        .order('created_at', { ascending: false })

      if (error) {
        throw error
      }

      setTenants(data || [])
    } catch (err) {
      console.error('Error fetching tenants:', err)
      toast({
        title: "Error",
        description: "Failed to fetch tenants data",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  const filteredTenants = tenants.filter(tenant =>
    tenant.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    tenant.slug.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (tenant.domain && tenant.domain.toLowerCase().includes(searchTerm.toLowerCase()))
  )

  const handleSetDomain = async (tenantId: string, domain: string) => {
    if (!domain.trim()) {
      toast({
        title: "Error",
        description: "Please enter a domain name",
        variant: "destructive"
      })
      return
    }

    setIsLoading(true)
    try {
      const response = await fetch('/api/tenants/lookup-domain', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          domain: domain.trim(),
          tenantId
        })
      })

      if (!response.ok) {
        throw new Error('Failed to set custom domain')
      }

      const data = await response.json()
      
      if (data.success) {
        await fetchTenants() // Refresh data
        setNewDomain('')
        setSelectedTenant(null)
        toast({
          title: "Success",
          description: `Domain ${domain} has been set for tenant`,
        })
      }
    } catch (error) {
      console.error('Error setting domain:', error)
      toast({
        title: "Error",
        description: "Failed to set domain. Please try again.",
        variant: "destructive"
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleDeleteDomain = async (tenantId: string) => {
    const confirmed = window.confirm(
      "Are you sure you want to remove the custom domain? The tenant will only be accessible via subdomain."
    )

    if (!confirmed) return

    setIsLoading(true)
    try {
      const response = await fetch('/api/tenants/lookup-domain', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ tenantId })
      })

      if (!response.ok) {
        throw new Error('Failed to delete domain')
      }

      const data = await response.json()
      
      if (data.success) {
        await fetchTenants() // Refresh data
        toast({
          title: "Success",
          description: "Custom domain has been removed",
        })
      }
    } catch (error) {
      console.error('Error deleting domain:', error)
      toast({
        title: "Error",
        description: "Failed to delete domain. Please try again.",
        variant: "destructive"
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleVerifyDomain = async (domain: string) => {
    try {
      const response = await fetch(`/api/tenants/verify-domain?domain=${domain}`)
      const data = await response.json()

      setDomainStatus({
        domain,
        verified: data.dnsVerified || false,
        ssl: data.sslActive || false,
        lastChecked: new Date().toISOString()
      })

      toast({
        title: data.dnsVerified ? "Domain Verified" : "Verification Failed",
        description: data.dnsVerified ? "Domain is properly configured" : "Please check DNS settings",
        variant: data.dnsVerified ? "default" : "destructive"
      })
    } catch (error) {
      console.error('Error verifying domain:', error)
      toast({
        title: "Verification Failed",
        description: "Failed to verify domain",
        variant: "destructive"
      })
    }
  }

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
    toast({
      title: "Copied",
      description: "Copied to clipboard",
    })
  }

  if (loading) {
    return (
      <div className="container mx-auto py-6">
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-500"></div>
          <span className="ml-2 text-gray-600">Loading tenants...</span>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Page Header */}
      <div className="space-y-2">
        <h1 className="text-3xl font-bold tracking-tight">Domain Management</h1>
        <p className="text-muted-foreground">
          Manage custom domains for all tenants in the system
        </p>
      </div>

      {/* Quick Domain Setup */}
      <QuickDomainSetup onDomainAdded={fetchTenants} />

      {/* Search */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Search className="h-5 w-5" />
            Search Tenants
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Input
            placeholder="Search by tenant name, subdomain, or domain..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="max-w-md"
          />
        </CardContent>
      </Card>

      {/* Tenants List */}
      <Card>
        <CardHeader>
          <CardTitle>Tenants & Domains</CardTitle>
          <CardDescription>
            {filteredTenants.length} tenant(s) found
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {filteredTenants.map((tenant) => (
              <div key={tenant.id} className="border rounded-lg p-4 space-y-3">
                {/* Tenant Info */}
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="font-semibold">{tenant.name}</h3>
                    <p className="text-sm text-muted-foreground">ID: {tenant.id}</p>
                  </div>
                  <Badge variant={tenant.subscriptionStatus === 'active' ? 'default' : 'secondary'}>
                    {tenant.subscriptionPlan} - {tenant.subscriptionStatus}
                  </Badge>
                </div>

                {/* Domain Info */}
                <div className="grid gap-4 md:grid-cols-2">
                  {/* Subdomain */}
                  <div className="space-y-2">
                    <Label className="text-sm font-medium">Subdomain</Label>
                    <div className="flex items-center gap-2">
                      <Input 
                        value={`${tenant.slug}.sellzio.com`} 
                        readOnly 
                        className="text-sm"
                      />
                      <Button 
                        variant="outline" 
                        size="sm"
                        onClick={() => copyToClipboard(`${tenant.slug}.sellzio.com`)}
                      >
                        <Copy className="h-3 w-3" />
                      </Button>
                      <Button 
                        variant="outline" 
                        size="sm"
                        onClick={() => window.open(`https://${tenant.slug}.sellzio.com`, '_blank')}
                      >
                        <ExternalLink className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>

                  {/* Custom Domain */}
                  <div className="space-y-2">
                    <Label className="text-sm font-medium">Custom Domain</Label>
                    <div className="flex items-center gap-2">
                      <Input 
                        value={tenant.domain || 'Not set'} 
                        readOnly 
                        className="text-sm"
                      />
                      {tenant.domain && (
                        <>
                          <Button 
                            variant="outline" 
                            size="sm"
                            onClick={() => copyToClipboard(tenant.domain!)}
                          >
                            <Copy className="h-3 w-3" />
                          </Button>
                          <Button 
                            variant="outline" 
                            size="sm"
                            onClick={() => window.open(`https://${tenant.domain}`, '_blank')}
                          >
                            <ExternalLink className="h-3 w-3" />
                          </Button>
                          <Button 
                            variant="outline" 
                            size="sm"
                            onClick={() => handleVerifyDomain(tenant.domain!)}
                          >
                            <CheckCircle className="h-3 w-3" />
                          </Button>
                          <Button 
                            variant="destructive" 
                            size="sm"
                            onClick={() => handleDeleteDomain(tenant.id)}
                            disabled={isLoading}
                          >
                            <Trash2 className="h-3 w-3" />
                          </Button>
                        </>
                      )}
                      <Button 
                        variant="outline" 
                        size="sm"
                        onClick={() => setSelectedTenant(tenant)}
                      >
                        <Edit className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Domain Setup Modal */}
      {selectedTenant && (
        <Card>
          <CardHeader>
            <CardTitle>Set Custom Domain for {selectedTenant.name}</CardTitle>
            <CardDescription>
              Configure a custom domain for this tenant
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="domain">Domain Name</Label>
              <div className="flex gap-2 mt-1">
                <Input
                  id="domain"
                  placeholder="example.com"
                  value={newDomain}
                  onChange={(e) => setNewDomain(e.target.value)}
                  className="flex-1"
                />
                <Button 
                  onClick={() => handleSetDomain(selectedTenant.id, newDomain)}
                  disabled={isLoading || !newDomain.trim()}
                >
                  {isLoading ? 'Setting...' : 'Set Domain'}
                </Button>
                <Button 
                  variant="outline"
                  onClick={() => {
                    setSelectedTenant(null)
                    setNewDomain('')
                  }}
                >
                  Cancel
                </Button>
              </div>
            </div>

            {/* DNS Instructions */}
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>DNS Configuration Required</AlertTitle>
              <AlertDescription className="mt-2">
                <p className="mb-3">Configure these DNS records in your domain provider:</p>
                <div className="bg-gray-50 p-3 rounded text-sm font-mono border">
                  <div className="grid grid-cols-3 gap-2">
                    <div><strong>Type:</strong> CNAME</div>
                    <div><strong>Name:</strong> @</div>
                    <div><strong>Target:</strong> {selectedTenant.slug}.sellzio.com</div>
                  </div>
                </div>
              </AlertDescription>
            </Alert>
          </CardContent>
        </Card>
      )}
    </div>
  )
}

import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

function getClient() {
  return createClient(supabaseUrl, supabaseServiceKey)
}

export async function POST(request: NextRequest) {
  try {
    const { subdomain, tenantId } = await request.json()

    if (!subdomain || !tenantId) {
      return NextResponse.json(
        { error: 'Subdomain and tenantId are required' },
        { status: 400 }
      )
    }

    // Validate subdomain format
    const subdomainRegex = /^[a-z0-9]([a-z0-9-]*[a-z0-9])?$/
    if (!subdomainRegex.test(subdomain)) {
      return NextResponse.json(
        { error: 'Invalid subdomain format' },
        { status: 400 }
      )
    }

    console.log('🔥 API: Updating subdomain:', { subdomain, tenantId })

    const supabase = getClient()

    // Check if subdomain is already taken by another tenant
    const { data: existingTenant, error: checkError } = await supabase
      .from('Tenant')
      .select('id')
      .eq('slug', subdomain)
      .neq('id', tenantId)
      .single()

    if (checkError && checkError.code !== 'PGRST116') {
      console.error('🔥 API: Error checking subdomain availability:', checkError)
      return NextResponse.json(
        { error: 'Failed to check subdomain availability' },
        { status: 500 }
      )
    }

    if (existingTenant) {
      return NextResponse.json(
        { error: 'Subdomain is already taken' },
        { status: 409 }
      )
    }

    // Update tenant subdomain (slug)
    const { data, error } = await supabase
      .from('Tenant')
      .update({ slug: subdomain })
      .eq('id', tenantId)
      .select()
      .single()

    if (error) {
      console.error('🔥 API: Error updating subdomain:', error)
      return NextResponse.json(
        { error: 'Failed to update subdomain' },
        { status: 500 }
      )
    }

    console.log('🔥 API: Subdomain updated successfully:', data)

    return NextResponse.json({
      success: true,
      tenant: data
    })
  } catch (error) {
    console.error('🔥 API: Error updating subdomain:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

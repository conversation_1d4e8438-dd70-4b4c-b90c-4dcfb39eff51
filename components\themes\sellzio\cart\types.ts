// Shared types for Sellzio Cart components

export interface CartItem {
  id: string
  productId: string
  name: string
  price: number
  originalPrice?: number
  image: string
  store: string
  storeId: string
  quantity: number
  variant?: string
  selected?: boolean
  isLive?: boolean
  badges?: string[]
}

export interface Shop {
  id: string
  name: string
  items: CartItem[]
  selected?: boolean
  isLive?: boolean
  badges?: string[]
}

export interface Voucher {
  id: string
  type: 'discount' | 'shipping' | 'cashback' | 'payment' | 'bank'
  title: string
  description: string
  amount: string
  minPurchase: string
  validUntil: string
  isRecommended?: boolean
  isSelected?: boolean
  isDisabled?: boolean
}

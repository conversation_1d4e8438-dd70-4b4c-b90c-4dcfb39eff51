"use client"

import React from 'react'

interface PaymentMethod {
  id: string
  name: string
  type: string
  isSelected?: boolean
}

interface SellzioPaymentSectionProps {
  method: PaymentMethod
  onEdit: () => void
}

export const SellzioPaymentSection: React.FC<SellzioPaymentSectionProps> = ({
  method,
  onEdit
}) => {
  return (
    <div className="sellzio-checkout-section sellzio-payment-section" onClick={onEdit}>
      <div className="sellzio-payment-content">
        <div className="sellzio-payment-left">
          <div className="sellzio-payment-icon">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <circle cx="12" cy="12" r="10" fill="#FFEEE5" stroke="#FF5722" strokeWidth="1.5"/>
              <path d="M12 6v6l4 2" stroke="#FF5722" strokeWidth="1.5" strokeLinecap="round"/>
            </svg>
          </div>
          <span className="sellzio-payment-label">Metode Pembayaran</span>
        </div>
        <div className="sellzio-payment-right">
          <span className="sellzio-payment-method">{method.name}</span>
          <span className="sellzio-payment-arrow">›</span>
        </div>
      </div>
    </div>
  )
}

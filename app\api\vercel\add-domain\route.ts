import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    const { domain } = await request.json()

    if (!domain) {
      return NextResponse.json(
        { error: 'Domain is required' },
        { status: 400 }
      )
    }

    const vercelToken = process.env.VERCEL_TOKEN
    const vercelProjectId = process.env.VERCEL_PROJECT_ID

    if (!vercelToken || !vercelProjectId) {
      console.error('Missing Vercel configuration:', {
        hasToken: !!vercelToken,
        hasProjectId: !!vercelProjectId
      })
      return NextResponse.json(
        { error: 'Vercel API not configured' },
        { status: 500 }
      )
    }

    console.log('🔥 VERCEL: Adding domain to project:', domain)

    // Add domain to Vercel project
    const response = await fetch(`https://api.vercel.com/v9/projects/${vercelProjectId}/domains`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${vercelToken}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        name: domain
      })
    })

    const result = await response.json()

    if (!response.ok) {
      console.error('❌ Vercel API Error:', result)
      return NextResponse.json(
        { 
          success: false, 
          error: 'Failed to add domain to Vercel',
          details: result.error || result
        },
        { status: response.status }
      )
    }

    console.log('✅ Domain added to Vercel successfully:', result)

    return NextResponse.json({
      success: true,
      message: `Domain ${domain} added to Vercel project successfully`,
      data: result
    })

  } catch (error) {
    console.error('❌ Error adding domain to Vercel:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// GET method to list domains
export async function GET(request: NextRequest) {
  try {
    const vercelToken = process.env.VERCEL_TOKEN
    const vercelProjectId = process.env.VERCEL_PROJECT_ID

    if (!vercelToken || !vercelProjectId) {
      return NextResponse.json(
        { error: 'Vercel API not configured' },
        { status: 500 }
      )
    }

    console.log('🔥 VERCEL: Listing project domains')

    const response = await fetch(`https://api.vercel.com/v9/projects/${vercelProjectId}/domains`, {
      headers: {
        'Authorization': `Bearer ${vercelToken}`,
        'Content-Type': 'application/json',
      },
    })

    const result = await response.json()

    if (!response.ok) {
      console.error('❌ Vercel API Error:', result)
      return NextResponse.json(
        { error: 'Failed to list domains' },
        { status: response.status }
      )
    }

    return NextResponse.json({
      success: true,
      domains: result.domains || []
    })

  } catch (error) {
    console.error('❌ Error listing Vercel domains:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

"use client"

import React, { useState, useEffect, use } from 'react'
import { useRouter } from 'next/navigation'
import { ArrowLeft } from 'lucide-react'
import { sampleProducts } from '@/components/data/products'
import { SellzioProductDetail } from '@/components/themes/sellzio/product-detail/sellzio-product-detail'
import { SellzioFlashSaleDetail } from '@/components/themes/sellzio/product-detail/sellzio-flash-sale-detail'
import { useCart } from '@/hooks/use-cart'
import { useTenantTheme } from '@/components/tenant/tenant-theme-provider'
import '@/components/themes/sellzio/product-detail/sellzio-product-detail.css'
import '@/components/themes/sellzio/product-detail/sellzio-flash-sale-styles.css'

interface TenantProductDetailPageProps {
  params: Promise<{
    slug: string
    id: string
  }>
}

export default function TenantProductDetailPage({ params }: TenantProductDetailPageProps) {
  const router = useRouter()
  const { theme, loading: themeLoading } = useTenantTheme()
  const { addItem } = useCart()
  const [product, setProduct] = useState<any>(null)
  const [loading, setLoading] = useState(true)

  // Unwrap params using React.use()
  const resolvedParams = use(params)
  const tenantSlug = resolvedParams.slug
  const productId = resolvedParams.id

  useEffect(() => {
    // Simulate loading product data
    const foundProduct = sampleProducts.find(p => p.id === productId)
    
    setTimeout(() => {
      setProduct(foundProduct)
      setLoading(false)
    }, 500)
  }, [productId])

  const handleBack = () => {
    router.push(`/tenant/${tenantSlug}/sellzio`)
  }

  const handleAddToCart = (product: any, quantity: number = 1) => {
    addItem({
      id: product.id,
      name: product.name,
      price: parseFloat(product.price.replace(/[^\d.-]/g, '')),
      image: product.image,
      quantity
    })
  }

  const handleBuyNow = (product: any, quantity: number = 1) => {
    handleAddToCart(product, quantity)
    router.push(`/tenant/${tenantSlug}/sellzio/checkout`)
  }

  // Theme loading
  if (themeLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading {tenantSlug} marketplace...</p>
        </div>
      </div>
    )
  }

  // Product loading
  if (loading) {
    return (
      <div className="sellzio-product-detail-page">
        <div className="sellzio-product-detail-header">
          <button onClick={handleBack} className="sellzio-back-button">
            <ArrowLeft size={20} />
          </button>
          <h1 className="sellzio-page-title">Loading...</h1>
        </div>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500 mx-auto mb-4"></div>
            <p className="text-gray-600">Loading product details...</p>
          </div>
        </div>
      </div>
    )
  }

  // Product not found
  if (!product) {
    return (
      <div className="sellzio-product-detail-page">
        <div className="sellzio-product-detail-header">
          <button onClick={handleBack} className="sellzio-back-button">
            <ArrowLeft size={20} />
          </button>
          <h1 className="sellzio-page-title">Product Not Found</h1>
        </div>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <h2 className="text-xl font-semibold text-gray-800 mb-2">Product Not Found</h2>
            <p className="text-gray-600 mb-4">The product you're looking for doesn't exist.</p>
            <button 
              onClick={handleBack}
              className="bg-orange-500 text-white px-6 py-2 rounded-lg hover:bg-orange-600 transition-colors"
            >
              Back to Store
            </button>
          </div>
        </div>
      </div>
    )
  }

  // Determine if this is a flash sale product
  const isFlashSale = product.discount && parseFloat(product.discount) > 0

  return (
    <div className="sellzio-product-detail-page">
      {/* Header */}
      <div className="sellzio-product-detail-header">
        <button onClick={handleBack} className="sellzio-back-button">
          <ArrowLeft size={20} />
        </button>
        <h1 className="sellzio-page-title">{tenantSlug} Store</h1>
      </div>

      {/* Product Detail Content */}
      {isFlashSale ? (
        <SellzioFlashSaleDetail
          product={product}
          onAddToCart={handleAddToCart}
          onBuyNow={handleBuyNow}
          onBack={handleBack}
        />
      ) : (
        <SellzioProductDetail
          product={product}
          onAddToCart={handleAddToCart}
          onBuyNow={handleBuyNow}
          onBack={handleBack}
        />
      )}
    </div>
  )
}

import { NextRequest, NextResponse } from 'next/server'
import { getClient } from '@/lib/supabase'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const domain = searchParams.get('domain')

    if (!domain) {
      return NextResponse.json(
        { error: 'Domain parameter is required' },
        { status: 400 }
      )
    }

    console.log('🔥 API: Looking up tenant for domain:', domain)

    const supabase = getClient()

    // Look up tenant by domain
    const { data: tenant, error } = await supabase
      .from('Tenant')
      .select('id, name, slug, domain')
      .eq('domain', domain)
      .single()

    if (error) {
      console.log('🔥 API: No tenant found for domain:', domain, error.message)
      return NextResponse.json(
        { error: 'Tenant not found' },
        { status: 404 }
      )
    }

    console.log('🔥 API: Found tenant:', tenant)

    return NextResponse.json({
      id: tenant.id,
      slug: tenant.slug,
      name: tenant.name,
      domain: tenant.domain
    })
  } catch (error) {
    console.error('🔥 API: Error looking up domain:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const { domain, tenantId } = await request.json()

    if (!domain || !tenantId) {
      return NextResponse.json(
        { error: 'Domain and tenantId are required' },
        { status: 400 }
      )
    }

    console.log('🔥 API: Setting custom domain:', { domain, tenantId })

    const supabase = getClient()

    // Update tenant with custom domain
    const { data, error } = await supabase
      .from('Tenant')
      .update({ domain })
      .eq('id', tenantId)
      .select()
      .single()

    if (error) {
      console.error('🔥 API: Error setting custom domain:', error)
      return NextResponse.json(
        { error: 'Failed to set custom domain' },
        { status: 500 }
      )
    }

    console.log('🔥 API: Custom domain set successfully:', data)

    return NextResponse.json({
      success: true,
      tenant: data
    })
  } catch (error) {
    console.error('🔥 API: Error setting custom domain:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { tenantId } = await request.json()

    if (!tenantId) {
      return NextResponse.json(
        { error: 'TenantId is required' },
        { status: 400 }
      )
    }

    console.log('🔥 API: Deleting custom domain for tenant:', tenantId)

    const supabase = getClient()

    // Remove custom domain from tenant (set to null)
    const { data, error } = await supabase
      .from('Tenant')
      .update({ domain: null })
      .eq('id', tenantId)
      .select()
      .single()

    if (error) {
      console.error('🔥 API: Error deleting custom domain:', error)
      return NextResponse.json(
        { error: 'Failed to delete custom domain' },
        { status: 500 }
      )
    }

    console.log('🔥 API: Custom domain deleted successfully for tenant:', tenantId)

    return NextResponse.json({
      success: true,
      tenant: data
    })
  } catch (error) {
    console.error('🔥 API: Error deleting custom domain:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

"use client"

import React from 'react'

interface Address {
  name: string
  phone: string
  address: string
}

interface SellzioAddressSectionProps {
  address: Address
  onEdit: () => void
}

export const SellzioAddressSection: React.FC<SellzioAddressSectionProps> = ({
  address,
  onEdit
}) => {
  return (
    <div className="sellzio-checkout-section">
      <div className="sellzio-section-title">
        <span><PERSON><PERSON><PERSON></span>
        <button className="sellzio-edit-btn" onClick={onEdit}>
          Ubah
        </button>
      </div>
      <div className="sellzio-address-info">
        <div className="sellzio-address-name-phone">
          <span className="sellzio-address-name">{address.name}</span>
          <span className="sellzio-address-phone">{address.phone}</span>
        </div>
        <div className="sellzio-address-text">
          {address.address}
        </div>
      </div>
    </div>
  )
}

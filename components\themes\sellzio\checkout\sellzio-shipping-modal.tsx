"use client"

import React, { useState } from 'react'
import { X } from 'lucide-react'

interface ShippingOption {
  id: string
  name: string
  price: number
  originalPrice?: number
  estimate: string
  isFree?: boolean
  isSelected?: boolean
}

interface SellzioShippingModalProps {
  isOpen: boolean
  onClose: () => void
  options: ShippingOption[]
  onSelect: (options: ShippingOption[]) => void
}

export const SellzioShippingModal: React.FC<SellzioShippingModalProps> = ({
  isOpen,
  onClose,
  options,
  onSelect
}) => {
  const [selectedOption, setSelectedOption] = useState(
    options.find(opt => opt.isSelected)?.id || options[0]?.id
  )

  const allShippingOptions: ShippingOption[] = [
    {
      id: "reguler",
      name: "Reguler",
      price: 0,
      originalPrice: 17000,
      estimate: "20 - 23 Mar",
      isFree: true
    },
    {
      id: "express",
      name: "Express",
      price: 25000,
      estimate: "18 - 19 Mar",
      isFree: false
    },
    {
      id: "same-day",
      name: "Same Day",
      price: 45000,
      estimate: "Hari ini",
      isFree: false
    },
    {
      id: "instant",
      name: "Instant (2 Jam)",
      price: 75000,
      estimate: "2 jam",
      isFree: false
    }
  ]

  const formatPrice = (price: number) => {
    return `Rp${price.toLocaleString('id-ID')}`
  }

  const handleSelect = (optionId: string) => {
    setSelectedOption(optionId)
  }

  const handleSave = () => {
    const updatedOptions = allShippingOptions.map(opt => ({
      ...opt,
      isSelected: opt.id === selectedOption
    }))
    onSelect(updatedOptions)
    onClose()
  }

  if (!isOpen) return null

  return (
    <div className="sellzio-modal-overlay">
      <div className="sellzio-modal">
        <div className="sellzio-modal-header">
          <h2>Pilih Opsi Pengiriman</h2>
          <button className="sellzio-modal-close" onClick={onClose}>
            <X size={20} />
          </button>
        </div>
        
        <div className="sellzio-modal-content">
          <div className="sellzio-shipping-options">
            {allShippingOptions.map((option) => (
              <div
                key={option.id}
                className={`sellzio-shipping-option-item ${
                  selectedOption === option.id ? 'selected' : ''
                }`}
                onClick={() => handleSelect(option.id)}
              >
                <div className="sellzio-shipping-option-radio">
                  <input
                    type="radio"
                    name="shipping"
                    checked={selectedOption === option.id}
                    onChange={() => handleSelect(option.id)}
                  />
                </div>
                
                <div className="sellzio-shipping-option-details">
                  <div className="sellzio-shipping-option-header">
                    <div className="sellzio-shipping-option-name">
                      {option.name}
                      {option.isFree && (
                        <span className="sellzio-free-badge">Gratis Ongkir</span>
                      )}
                    </div>
                    <div className="sellzio-shipping-option-price">
                      {option.originalPrice && option.originalPrice > option.price && (
                        <span className="sellzio-original-price">
                          {formatPrice(option.originalPrice)}
                        </span>
                      )}
                      <span className="sellzio-current-price">
                        {formatPrice(option.price)}
                      </span>
                    </div>
                  </div>
                  <div className="sellzio-shipping-option-estimate">
                    Estimasi tiba: {option.estimate}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
        
        <div className="sellzio-modal-footer">
          <button className="sellzio-btn-secondary" onClick={onClose}>
            Batal
          </button>
          <button className="sellzio-btn-primary" onClick={handleSave}>
            Pilih
          </button>
        </div>
      </div>
    </div>
  )
}

import { NextResponse } from 'next/server'

export async function GET() {
  try {
    const envCheck = {
      CLOUDFLARE_API_TOKEN: !!process.env.CLOUDFLARE_API_TOKEN,
      CLOUDFLARE_ZONE_ID: !!process.env.CLOUDFLARE_ZONE_ID,
      VERCEL_IP: process.env.VERCEL_IP || 'not set',
      NODE_ENV: process.env.NODE_ENV,
      VERCEL: !!process.env.VERCEL,
      NEXT_PUBLIC_MAIN_DOMAIN: process.env.NEXT_PUBLIC_MAIN_DOMAIN,
      // Show partial values for debugging (first 10 chars)
      CLOUDFLARE_API_TOKEN_PARTIAL: process.env.CLOUDFLARE_API_TOKEN ? 
        process.env.CLOUDFLARE_API_TOKEN.substring(0, 10) + '...' : 'not set',
      CLOUDFLARE_ZONE_ID_PARTIAL: process.env.CLOUDFLARE_ZONE_ID ? 
        process.env.CLOUDFLARE_ZONE_ID.substring(0, 10) + '...' : 'not set'
    }

    return NextResponse.json({
      success: true,
      environment: envCheck,
      timestamp: new Date().toISOString()
    })

  } catch (error: any) {
    return NextResponse.json({
      success: false,
      error: error.message,
      timestamp: new Date().toISOString()
    }, { status: 500 })
  }
}

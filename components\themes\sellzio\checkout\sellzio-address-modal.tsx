"use client"

import React, { useState } from 'react'
import { X } from 'lucide-react'

interface Address {
  name: string
  phone: string
  address: string
}

interface SellzioAddressModalProps {
  isOpen: boolean
  onClose: () => void
  address: Address
  onSave: (address: Address) => void
}

export const SellzioAddressModal: React.FC<SellzioAddressModalProps> = ({
  isOpen,
  onClose,
  address,
  onSave
}) => {
  const [formData, setFormData] = useState<Address>(address)
  const [errors, setErrors] = useState<Partial<Address>>({})

  const handleInputChange = (field: keyof Address, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }))
    }
  }

  const validateForm = () => {
    const newErrors: Partial<Address> = {}
    
    if (!formData.name.trim()) {
      newErrors.name = 'Nama penerima harus diisi'
    }
    
    if (!formData.phone.trim()) {
      newErrors.phone = 'Nomor telepon harus diisi'
    } else if (!/^[0-9+\-\s()]+$/.test(formData.phone)) {
      newErrors.phone = 'Format nomor telepon tidak valid'
    }
    
    if (!formData.address.trim()) {
      newErrors.address = 'Alamat lengkap harus diisi'
    } else if (formData.address.trim().length < 10) {
      newErrors.address = 'Alamat terlalu pendek (minimal 10 karakter)'
    }
    
    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSave = () => {
    if (validateForm()) {
      onSave(formData)
      onClose()
    }
  }

  const handleCancel = () => {
    setFormData(address) // Reset to original
    setErrors({})
    onClose()
  }

  if (!isOpen) return null

  return (
    <div className="sellzio-modal-overlay">
      <div className="sellzio-modal">
        <div className="sellzio-modal-header">
          <h2>Ubah Alamat Pengiriman</h2>
          <button className="sellzio-modal-close" onClick={handleCancel}>
            <X size={20} />
          </button>
        </div>
        
        <div className="sellzio-modal-content">
          <div className="sellzio-form-group">
            <label className="sellzio-form-label">Nama Penerima</label>
            <input
              type="text"
              className={`sellzio-form-input ${errors.name ? 'error' : ''}`}
              value={formData.name}
              onChange={(e) => handleInputChange('name', e.target.value)}
              placeholder="Masukkan nama penerima"
            />
            {errors.name && <span className="sellzio-form-error">{errors.name}</span>}
          </div>
          
          <div className="sellzio-form-group">
            <label className="sellzio-form-label">Nomor Telepon</label>
            <input
              type="tel"
              className={`sellzio-form-input ${errors.phone ? 'error' : ''}`}
              value={formData.phone}
              onChange={(e) => handleInputChange('phone', e.target.value)}
              placeholder="Contoh: 0812-3456-7890"
            />
            {errors.phone && <span className="sellzio-form-error">{errors.phone}</span>}
          </div>
          
          <div className="sellzio-form-group">
            <label className="sellzio-form-label">Alamat Lengkap</label>
            <textarea
              className={`sellzio-form-textarea ${errors.address ? 'error' : ''}`}
              value={formData.address}
              onChange={(e) => handleInputChange('address', e.target.value)}
              placeholder="Masukkan alamat lengkap termasuk RT/RW, kelurahan, kecamatan, kota, dan kode pos"
              rows={4}
            />
            {errors.address && <span className="sellzio-form-error">{errors.address}</span>}
          </div>
        </div>
        
        <div className="sellzio-modal-footer">
          <button className="sellzio-btn-secondary" onClick={handleCancel}>
            Batal
          </button>
          <button className="sellzio-btn-primary" onClick={handleSave}>
            Simpan
          </button>
        </div>
      </div>
    </div>
  )
}

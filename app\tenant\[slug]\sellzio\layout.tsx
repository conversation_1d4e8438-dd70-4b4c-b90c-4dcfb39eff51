import type React from "react"
import { <PERSON>ada<PERSON> } from "next"
import { SellzioTenantWrapper } from "./sellzio-tenant-wrapper"

interface SellzioTenantLayoutProps {
  params: { slug: string }
  children: React.ReactNode
}

// Metadata untuk SEO
export async function generateMetadata({ params }: { params: { slug: string } }): Promise<Metadata> {
  return {
    title: `${params.slug} - Sellzio Marketplace`,
    description: `Marketplace ${params.slug} powered by Sellzio - Jual beli online dengan mudah dan aman`,
    keywords: ["marketplace", "jual beli", "online shop", params.slug, "sellzio"],
    openGraph: {
      title: `${params.slug} - Sellzio Marketplace`,
      description: `Marketplace ${params.slug} powered by Sellzio`,
      type: "website",
      siteName: `${params.slug} Marketplace`,
    },
    twitter: {
      card: "summary_large_image",
      title: `${params.slug} - Se<PERSON><PERSON> Marketplace`,
      description: `Marketplace ${params.slug} powered by Sellzio`,
    },
  }
}

export default function SellzioTenantLayout({ params, children }: SellzioTenantLayoutProps) {
  return (
    <SellzioTenantWrapper tenantSlug={params.slug}>
      {children}
    </SellzioTenantWrapper>
  )
}

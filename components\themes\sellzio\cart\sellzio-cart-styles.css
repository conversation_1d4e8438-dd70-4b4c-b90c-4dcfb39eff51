/* Sell<PERSON>t <PERSON> Styles */

/* Modal Overlay - Fullscreen like voucher page */
.sellzio-cart-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #f5f5f5;
  z-index: 1000;
  overflow-y: auto;
}

/* Modal Container - Fullscreen */
.sellzio-cart-modal {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  max-width: 768px;
  margin: 0 auto;
  background-color: #fff;
  border-left: 1px solid #e0e0e0;
  border-right: 1px solid #e0e0e0;
}

/* Header - Fullscreen style like voucher */
.sellzio-cart-header {
  position: sticky;
  top: 0;
  width: 100%;
  background-color: #fff;
  padding: 16px 0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  z-index: 100;
}

.sellzio-cart-header-container {
  max-width: 768px;
  width: 100%;
  padding: 0 16px;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
}

.sellzio-cart-header h1 {
  font-size: 1.2rem;
  font-weight: bold;
  margin: 0;
  color: #333;
}

.sellzio-cart-back-btn {
  position: absolute;
  left: 20px;
  top: 50%;
  transform: translateY(-50%);
  border: none;
  background: none;
  font-size: 25px;
  cursor: pointer;
  color: #ee4d2d;
  line-height: 1;
}

.sellzio-cart-edit-btn {
  position: absolute;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
  color: #ee4d2d;
  background: none;
  border: none;
  font-size: 14px;
  cursor: pointer;
}

/* Cart Section - Fullscreen content */
.sellzio-cart-section {
  flex: 1;
  background-color: #f5f5f5;
  overflow-y: auto;
  padding-bottom: 65px;
}

.sellzio-cart-content {
  max-width: 768px;
  width: 100%;
  margin: 0 auto;
  padding: 15px 16px;
}

/* Shop Group */
.sellzio-shop-group {
  background-color: #fff;
  border-radius: 8px;
  margin-bottom: 12px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
  overflow: hidden;
}

.sellzio-shop-group:last-child {
  margin-bottom: 0;
}

/* Shop Header */
.sellzio-shop-header {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  background-color: #fff;
  border-bottom: 1px solid #f5f5f5;
  height: 44px;
}

.sellzio-shop-checkbox {
  margin-right: 8px;
}

.sellzio-shop-checkbox input[type="checkbox"] {
  width: 18px;
  height: 18px;
  cursor: pointer;
  accent-color: #ee4d2d;
  background-color: #fff !important;
  border: 2px solid #ddd;
  border-radius: 3px;
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  position: relative;
}

.sellzio-shop-checkbox input[type="checkbox"]:checked {
  background-color: #ee4d2d !important;
  border-color: #ee4d2d;
}

.sellzio-shop-checkbox input[type="checkbox"]:checked::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 12px;
  font-weight: bold;
}

.sellzio-shop-name {
  font-size: 14px;
  font-weight: bold;
  flex: 1;
}

/* Live Badge */
.sellzio-live-badge {
  display: inline-flex;
  align-items: center;
  background-color: #ee4d2d;
  color: white;
  font-size: 9px;
  font-weight: bold;
  padding: 2px 8px;
  border-radius: 3px;
  margin-right: 5px;
  vertical-align: middle;
  position: relative;
}

.sellzio-sound-wave {
  display: inline-flex;
  align-items: center;
  height: 7px;
  margin-right: 4px;
}

.sellzio-wave-bar {
  width: 1px;
  background-color: white;
  margin-right: 1px;
}

.sellzio-live-text {
  margin-left: 1px;
}

.sellzio-wave-bar:nth-child(1) {
  height: 7px;
  animation: sellzio-soundwave 0.8s infinite alternate;
}

.sellzio-wave-bar:nth-child(2) {
  height: 9px;
  animation: sellzio-soundwave 0.7s infinite alternate 0.1s;
}

.sellzio-wave-bar:nth-child(3) {
  height: 7px;
  animation: sellzio-soundwave 0.6s infinite alternate 0.2s;
}

@keyframes sellzio-soundwave {
  0% { height: 2px; }
  100% { height: 6px; }
}

/* Star Label */
.sellzio-star-label {
  display: inline-flex;
  align-items: center;
  border-radius: 3px;
  overflow: hidden;
  box-shadow: 0 1px 2px rgba(0,0,0,0.08);
  font-size: 9px;
  font-weight: 700;
  height: 16px;
  margin-right: 4px;
  margin-bottom: 2px;
  vertical-align: middle;
}

.sellzio-star-primary {
  background-color: #ee4d2d;
  color: white;
  padding: 0 5px;
  height: 100%;
  display: flex;
  align-items: center;
  letter-spacing: -0.1px;
}

.sellzio-star-secondary {
  background-color: #fff0e5;
  color: #ee4d2d;
  padding: 0 5px;
  height: 100%;
  display: flex;
  align-items: center;
  letter-spacing: -0.1px;
}

/* Cart Item */
.sellzio-cart-item {
  display: flex;
  padding: 12px;
  border-bottom: 1px solid #f5f5f5;
  height: 109px;
}

.sellzio-item-checkbox {
  margin-right: 10px;
  align-self: flex-start;
  margin-top: 30px;
}

.sellzio-item-checkbox input[type="checkbox"] {
  width: 18px;
  height: 18px;
  cursor: pointer;
  accent-color: #ee4d2d;
  background-color: #fff !important;
  border: 2px solid #ddd;
  border-radius: 3px;
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  position: relative;
}

.sellzio-item-checkbox input[type="checkbox"]:checked {
  background-color: #ee4d2d !important;
  border-color: #ee4d2d;
}

.sellzio-item-checkbox input[type="checkbox"]:checked::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 12px;
  font-weight: bold;
}

.sellzio-item-image {
  width: 80px;
  height: 80px;
  min-width: 80px;
  margin-right: 0px;
  background-color: #f5f5f5;
  border: 1px solid #e0e0e0;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.sellzio-item-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.sellzio-item-details {
  flex: 1;
  overflow: hidden;
  min-width: 0;
}

.sellzio-item-name {
  font-size: 14px;
  margin: 6px 0px 0px 10px;
  line-height: 1.4;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}

.sellzio-item-variant {
  font-size: 12px;
  color: #888;
  margin: 3px 0px 0px 10px;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: space-between;
  border: 1px solid #ddd;
  border-radius: 3px;
  padding: 2px 6px;
  position: relative;
  min-width: 60px;
}

.sellzio-item-variant:after {
  content: "\25BC";
  font-size: 8px;
  margin-left: 5px;
  color: #888;
}

.sellzio-item-variant:hover {
  border-color: #ee4d2d;
  color: #ee4d2d;
}

.sellzio-item-variant:hover:after {
  color: #ee4d2d;
}

.sellzio-item-price {
  margin: 0px 0px 10px 10px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.sellzio-price-section {
  display: flex;
  align-items: center;
  width: 60%;
}

.sellzio-discounted-price {
  font-weight: bold;
  font-size: 14px;
  color: #ee4d2d;
  margin-right: 5px;
  white-space: nowrap;
  flex-shrink: 0;
}

.sellzio-original-price {
  text-decoration: line-through;
  color: #888;
  font-size: 12px;
  font-weight: normal;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex-shrink: 1;
  max-width: 70px;
}

/* Quantity Controls */
.sellzio-item-quantity {
  display: flex;
  align-items: center;
  margin-left: auto;
  flex-shrink: 0;
}

.sellzio-quantity-btn {
  width: 26px;
  height: 26px;
  border: 1px solid #e0e0e0;
  background-color: #fff;
  color: #333;
  font-size: 16px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}

.sellzio-quantity-btn:hover {
  background-color: #f5f5f5;
  border-color: #ee4d2d;
}

.sellzio-quantity-btn:disabled {
  background-color: #f5f5f5;
  color: #ccc;
  cursor: not-allowed;
  border-color: #e0e0e0;
}

.sellzio-minus-btn {
  border-radius: 3px 0 0 3px;
}

.sellzio-plus-btn {
  border-radius: 0 3px 3px 0;
}

.sellzio-quantity-input {
  width: 40px;
  height: 26px;
  border: 1px solid #e0e0e0;
  border-left: none;
  border-right: none;
  text-align: center;
  font-size: 14px;
  outline: none;
  background-color: #fff;
  color: #333;
}

/* Footer - Fixed like voucher page */
.sellzio-cart-footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  width: 100%;
  background-color: #fff;
  box-shadow: 0 -2px 4px rgba(0,0,0,0.1);
  z-index: 101;
  border-left: 1px solid #e0e0e0;
  border-right: 1px solid #e0e0e0;
  padding-bottom: env(safe-area-inset-bottom);
}

.sellzio-cart-modal .sellzio-cart-footer {
  max-width: 768px;
  margin: 0 auto;
}

/* Voucher Section */
.sellzio-voucher-section {
  max-width: 768px;
  width: 100%;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 16px;
  border-bottom: 1px solid #f5f5f5;
  height: 44px;
}

.sellzio-section-title {
  display: flex;
  align-items: center;
}

.sellzio-section-title svg {
  margin-right: 8px;
  width: 22px;
  height: 22px;
  color: #ee4d2d;
}

.sellzio-voucher-badges {
  display: flex;
  flex-wrap: nowrap;
  align-items: center;
  overflow-x: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.sellzio-voucher-badges::-webkit-scrollbar {
  display: none;
}

.sellzio-voucher-badge {
  padding: 4px 10px;
  font-weight: bold;
  font-size: 12px;
  border-width: 1.5px;
  border-style: solid;
  border-radius: 3px;
  display: inline-flex;
  align-items: center;
  white-space: nowrap;
  height: 20px;
  box-sizing: border-box;
  margin-right: 6px;
  max-width: 150px;
  overflow: hidden;
  text-overflow: ellipsis;
}

.sellzio-voucher-badge.voucher-badge-discount {
  border-color: #ee4d2d;
  color: #ee4d2d;
}

.sellzio-voucher-badge.voucher-badge-shipping {
  border-color: #00bfa5;
  color: #00bfa5;
}

.sellzio-voucher-badge-close {
  margin-left: 6px;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 10px;
  border: 1px solid;
  line-height: 1;
}

.sellzio-voucher-button {
  background-color: #fff0f0;
  border: 1px solid #ee4d2d;
  color: #ee4d2d;
  border-radius: 4px;
  padding: 6px 12px;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  display: inline-block;
  text-align: center;
  white-space: nowrap;
}

.sellzio-voucher-button:hover {
  background-color: #ffecec;
}

/* Checkout Row */
.sellzio-checkout-row {
  max-width: 768px;
  width: 100%;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
}

.sellzio-select-all {
  display: flex;
  align-items: center;
}

.sellzio-select-all input[type="checkbox"] {
  width: 18px;
  height: 18px;
  margin-right: 8px;
  cursor: pointer;
  accent-color: #ee4d2d;
  background-color: #fff !important;
  border: 2px solid #ddd;
  border-radius: 3px;
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  position: relative;
}

.sellzio-select-all input[type="checkbox"]:checked {
  background-color: #ee4d2d !important;
  border-color: #ee4d2d;
}

.sellzio-select-all input[type="checkbox"]:checked::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 12px;
  font-weight: bold;
}

.sellzio-select-all label {
  font-size: 14px;
}

.sellzio-checkout-section {
  display: flex;
  align-items: center;
}

.sellzio-total-price {
  margin-right: 12px;
  text-align: right;
}

.sellzio-total-label {
  font-size: 12px;
  color: #888;
}

.sellzio-total-amount {
  font-size: 16px;
  font-weight: bold;
  color: #ee4d2d;
}

.sellzio-checkout-btn {
  background-color: #ee4d2d;
  color: #fff;
  border: none;
  padding: 10px 16px;
  border-radius: 3px;
  font-size: 14px;
  font-weight: bold;
  cursor: pointer;
  opacity: 0.7;
  pointer-events: none;
}

.sellzio-checkout-btn.active {
  opacity: 1;
  pointer-events: auto;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .sellzio-cart-modal {
    height: 100vh;
  }
  
  .sellzio-cart-section {
    height: calc(109px * 3 + 130px);
    max-height: calc(109px * 3 + 130px);
    overflow-y: auto;
  }
  
  .sellzio-item-name {
    -webkit-line-clamp: 1;
    white-space: nowrap;
    text-overflow: ellipsis;
    max-width: 100%;
    overflow: hidden;
  }
  
  .sellzio-price-section {
    max-width: 130px;
    overflow: hidden;
  }
  
  .sellzio-original-price {
    max-width: 70px;
  }
  
  .sellzio-checkout-section {
    flex-shrink: 0;
  }
  
  .sellzio-total-price {
    margin-right: 8px;
    min-width: 0;
    flex-shrink: 1;
  }
  
  .sellzio-checkout-btn {
    flex-shrink: 0;
    min-width: 90px;
    white-space: nowrap;
  }
}

/* Desktop */
@media (min-width: 769px) {
  .sellzio-cart-section {
    max-height: none;
    height: auto;
    overflow-y: visible;
  }

  .sellzio-price-section {
    max-width: none;
    overflow: visible;
  }

  .sellzio-original-price {
    max-width: none;
    overflow: visible;
    text-overflow: clip;
    flex-shrink: 0;
  }
}

/* Variant Modal */
.sellzio-variant-modal-overlay {
  position: fixed;
  z-index: 999;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
}

.sellzio-variant-modal {
  background-color: #fff;
  border-radius: 8px;
  width: 300px;
  max-width: 90%;
  text-align: left;
  overflow: hidden;
}

.sellzio-variant-modal-header {
  padding: 15px;
  border-bottom: 1px solid #f5f5f5;
}

.sellzio-variant-modal-header h3 {
  font-size: 16px;
  margin: 0;
}

.sellzio-variant-modal-body {
  padding: 15px;
  max-height: 300px;
  overflow-y: auto;
}

.sellzio-variant-group {
  margin-bottom: 15px;
}

.sellzio-variant-title {
  font-size: 14px;
  font-weight: bold;
  margin-bottom: 8px;
}

.sellzio-variant-options {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.sellzio-variant-option {
  padding: 6px 12px;
  border: 1px solid #ddd;
  border-radius: 3px;
  font-size: 12px;
  cursor: pointer;
  background-color: #fff;
  color: #333;
}

.sellzio-variant-option.selected {
  color: #ee4d2d;
  border-color: #ee4d2d;
  background-color: #fff1f0;
}

.sellzio-variant-option:hover {
  border-color: #ee4d2d;
}

.sellzio-variant-modal-footer {
  padding: 15px;
  border-top: 1px solid #f5f5f5;
  text-align: right;
}

.sellzio-variant-confirm-btn {
  background-color: #ee4d2d;
  color: #fff;
  border: none;
  padding: 8px 16px;
  border-radius: 3px;
  font-size: 14px;
  font-weight: bold;
  cursor: pointer;
}

/* Voucher Page Styles */
.sellzio-voucher-page {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #f5f5f5;
  z-index: 1000;
  overflow-y: auto;
}

.sellzio-voucher-page-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  max-width: 768px;
  margin: 0 auto;
  background-color: #fff;
  border-left: 1px solid #e0e0e0;
  border-right: 1px solid #e0e0e0;
}

.sellzio-voucher-page-header {
  position: sticky;
  top: 0;
  width: 100%;
  background-color: #fff;
  padding: 16px 0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  z-index: 100;
}

.sellzio-voucher-header-container {
  max-width: 768px;
  width: 100%;
  padding: 0 16px;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
}

.sellzio-voucher-page-header h1 {
  font-size: 1.2rem;
  font-weight: bold;
  margin: 0;
}

.sellzio-voucher-back-btn {
  position: absolute;
  left: 20px;
  top: 50%;
  transform: translateY(-50%);
  border: none;
  background: none;
  font-size: 25px;
  cursor: pointer;
  color: #ee4d2d;
  line-height: 1;
}

.sellzio-voucher-page-content {
  flex: 1;
  overflow-y: auto;
  padding-bottom: 65px;
}

.sellzio-voucher-container {
  max-width: 768px;
  width: 100%;
  margin: 0 auto;
  padding: 15px 16px;
}

/* Search Bar */
.sellzio-search-bar {
  padding: 10px 16px;
  background-color: #fff;
  position: relative;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
  margin-bottom: 8px;
}

.sellzio-search-input {
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
  border-radius: 10px;
  padding: 8px 12px;
  margin-top: 2px;
}

.sellzio-search-input input {
  flex: 1;
  border: none;
  background: none;
  outline: none;
  font-size: 14px;
}

.sellzio-search-icon {
  margin-left: 8px;
  font-size: 18px;
  color: #757575;
  cursor: pointer;
}

.sellzio-search-icon.active {
  color: #ee4d2d;
}

.sellzio-clear-icon {
  margin-right: 8px;
  font-size: 18px;
  color: #999;
  cursor: pointer;
}

/* Search Suggestions */
.sellzio-search-suggestions {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background-color: #fff;
  border-radius: 0 0 8px 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  z-index: 100;
  max-height: 300px;
  overflow-y: auto;
}

.sellzio-suggestion-item {
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
  font-size: 14px;
  cursor: pointer;
}

.sellzio-suggestion-item:hover {
  background-color: #f9f9f9;
}

.sellzio-suggestion-title {
  font-weight: bold;
  margin-bottom: 4px;
}

.sellzio-suggestion-desc {
  font-size: 12px;
  color: #757575;
}

/* Tabs */
.sellzio-tabs {
  display: flex;
  background-color: #fff;
  padding: 16px 16px 16px 16px;
  margin-bottom: 8px;
  overflow-x: auto;
  white-space: nowrap;
  scrollbar-width: none;
  -ms-overflow-style: none;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.sellzio-tabs::-webkit-scrollbar {
  display: none;
}

.sellzio-tab {
  display: inline-block;
  padding: 8px 16px;
  margin-right: 8px;
  border-radius: 16px;
  font-size: 14px;
  font-weight: bold;
  color: #757575;
  background-color: #f5f5f5;
  cursor: pointer;
  border: 1px solid #e0e0e0;
}

.sellzio-tab.active {
  color: #fff;
  background-color: #ee4d2d;
  border-color: #ee4d2d;
}

/* Content Area */
.sellzio-content-area {
  background-color: #f5f5f5;
  border-radius: 8px;
  margin-bottom: 8px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
  padding: 15px;
}

/* Voucher List */
.sellzio-voucher-list {
  padding: 0;
}

.sellzio-voucher-group {
  margin-bottom: 20px;
}

.sellzio-group-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  padding: 0 4px;
}

.sellzio-group-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

/* Voucher Item */
.sellzio-voucher-item {
  display: flex;
  margin-bottom: 12px;
  border-radius: 4px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  position: relative;
  width: 100%;
  background-color: #fff;
}

.sellzio-voucher-left {
  width: 80px;
  min-width: 80px;
  background-color: #ee4d2d;
  color: #fff;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 12px 0;
  position: relative;
}

.sellzio-voucher-left::after {
  content: '';
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  width: 8px;
  background-image: radial-gradient(circle at 0 50%, transparent 8px, #fff 8px);
  background-size: 8px 16px;
  background-repeat: repeat-y;
}

.sellzio-voucher-amount {
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 4px;
  line-height: 1;
}

.sellzio-voucher-min {
  font-size: 10px;
  opacity: 0.8;
  text-align: center;
  padding: 5px 10px;
  margin: 0px 0;
}

.sellzio-voucher-right {
  flex: 1;
  background-color: #fff;
  padding: 12px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.sellzio-voucher-title {
  font-size: 14px;
  font-weight: bold;
  margin-bottom: 4px;
  color: #212121;
}

.sellzio-voucher-desc {
  font-size: 12px;
  color: #757575;
  margin-bottom: 8px;
}

.sellzio-voucher-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.sellzio-voucher-date {
  font-size: 11px;
  color: #999;
}

/* Circular Checkbox */
.sellzio-circular-checkbox {
  width: 24px;
  height: 24px;
  position: relative;
  cursor: pointer;
}

.sellzio-circular-checkbox input[type="checkbox"] {
  width: 24px;
  height: 24px;
  cursor: pointer;
  accent-color: #ee4d2d;
  background-color: #fff !important;
  border: 2px solid #ddd;
  border-radius: 50%;
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  position: relative;
  margin: 0;
}

.sellzio-circular-checkbox input[type="checkbox"]:checked {
  background-color: #ee4d2d !important;
  border-color: #ee4d2d;
}

.sellzio-circular-checkbox input[type="checkbox"]:checked::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 14px;
  font-weight: bold;
}

.sellzio-circular-checkbox input[type="checkbox"]:disabled {
  background-color: #e0e0e0 !important;
  border-color: #cccccc;
  cursor: not-allowed;
}

/* Remove old checkmark class */
.sellzio-checkmark {
  display: none;
}

/* Recommended Badge */
.sellzio-recommended-badge {
  position: absolute;
  top: 0;
  right: 0;
  background-color: #ff9800;
  color: white;
  font-size: 10px;
  font-weight: bold;
  padding: 3px 8px;
  border-radius: 0 4px 0 4px;
  z-index: 1;
}

/* Voucher Types */
.sellzio-voucher-shipping .sellzio-voucher-left {
  background-color: #00bfa5;
}

.sellzio-voucher-cashback .sellzio-voucher-left {
  background-color: #9c27b0;
}

.sellzio-voucher-payment .sellzio-voucher-left {
  background-color: #3f51b5;
}

.sellzio-voucher-bank .sellzio-voucher-left {
  background-color: #4CAF50;
}

/* Disabled Voucher */
.sellzio-voucher-item.disabled .sellzio-voucher-left {
  background-color: #aaaaaa !important;
  opacity: 0.7;
}

.sellzio-voucher-item.disabled .sellzio-voucher-right {
  opacity: 0.7;
}

.sellzio-voucher-item.disabled .sellzio-recommended-badge {
  background-color: #888888;
}

.sellzio-voucher-item.disabled .sellzio-circular-checkbox {
  cursor: not-allowed;
}

.sellzio-voucher-item.disabled .sellzio-circular-checkbox input {
  pointer-events: none;
}

.sellzio-voucher-item.disabled .sellzio-checkmark {
  background-color: #e0e0e0;
  border-color: #cccccc;
}

/* No Results */
.sellzio-no-results-card {
  background-color: #fff8e1;
  border-radius: 8px;
  padding: 15px;
  margin-top: 10px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
  text-align: center;
  color: #ff6d00;
  font-size: 14px;
  border-left: 4px solid #ff9800;
}

/* Voucher Footer */
.sellzio-voucher-footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  width: 100%;
  background-color: #fff;
  box-shadow: 0 -2px 4px rgba(0,0,0,0.1);
  z-index: 101;
  border-left: 1px solid #e0e0e0;
  border-right: 1px solid #e0e0e0;
  padding-bottom: env(safe-area-inset-bottom);
}

.sellzio-voucher-page-wrapper .sellzio-voucher-footer {
  max-width: 768px;
  margin: 0 auto;
}

.sellzio-voucher-footer-content {
  max-width: 768px;
  width: 100%;
  margin: 0 auto;
  padding: 15px 16px;
}

.sellzio-apply-btn {
  background-color: #ee4d2d;
  color: white;
  border: none;
  padding: 12px 25px;
  border-radius: 4px;
  font-weight: bold;
  cursor: pointer;
  transition: background-color 0.3s;
  width: 100%;
}

.sellzio-apply-btn:hover {
  background-color: #d63c1e;
}

.sellzio-apply-btn:disabled {
  background-color: #aaa;
  cursor: not-allowed;
}

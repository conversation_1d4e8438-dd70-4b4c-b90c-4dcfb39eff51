"use client"

import React, { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Search, Store, Users, ShoppingBag, Star, ExternalLink } from 'lucide-react'

interface Tenant {
  id: string
  slug: string
  name: string
  domain: string
  theme: string
  status: string
  plan: string
  description?: string
  category?: string
  logo?: string
  stats?: {
    stores: number
    products: number
    rating: number
  }
  createdAt: string
}

// Mock data untuk tenant directory
const mockTenants: Tenant[] = [
  {
    id: "1",
    slug: "demo",
    name: "Demo Store",
    domain: "demo.sellzio.com",
    theme: "sellzio",
    status: "active",
    plan: "professional",
    description: "Demo marketplace untuk testing fitur Sellzio",
    category: "General",
    logo: "/sellzio-logo.png",
    stats: {
      stores: 25,
      products: 1250,
      rating: 4.8
    },
    createdAt: "2024-01-01T00:00:00.000Z"
  },
  {
    id: "2",
    slug: "fashionhub",
    name: "Fashion Hub",
    domain: "fashionhub.sellzio.com",
    theme: "sellzio",
    status: "active",
    plan: "enterprise",
    description: "Marketplace fashion terlengkap dengan brand ternama",
    category: "Fashion",
    logo: "/fashionhub-logo.png",
    stats: {
      stores: 150,
      products: 8500,
      rating: 4.9
    },
    createdAt: "2024-01-15T00:00:00.000Z"
  },
  {
    id: "3",
    slug: "techstore",
    name: "Tech Store",
    domain: "techstore.sellzio.com",
    theme: "sellzio",
    status: "active",
    plan: "professional",
    description: "Marketplace elektronik dan gadget terpercaya",
    category: "Electronics",
    logo: "/techstore-logo.png",
    stats: {
      stores: 80,
      products: 3200,
      rating: 4.7
    },
    createdAt: "2024-02-01T00:00:00.000Z"
  }
]

export function TenantDirectory() {
  const router = useRouter()
  const [tenants, setTenants] = useState<Tenant[]>([])
  const [filteredTenants, setFilteredTenants] = useState<Tenant[]>([])
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedCategory, setSelectedCategory] = useState<string>('all')
  const [loading, setLoading] = useState(true)

  // Categories for filtering
  const categories = ['all', 'General', 'Fashion', 'Electronics', 'Food', 'Beauty', 'Sports']

  useEffect(() => {
    // Simulate API call
    const fetchTenants = async () => {
      setLoading(true)
      try {
        // In real implementation, this would be an API call
        await new Promise(resolve => setTimeout(resolve, 1000))
        setTenants(mockTenants)
        setFilteredTenants(mockTenants)
      } catch (error) {
        console.error('Error fetching tenants:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchTenants()
  }, [])

  // Filter tenants based on search and category
  useEffect(() => {
    let filtered = tenants

    // Filter by search query
    if (searchQuery.trim()) {
      filtered = filtered.filter(tenant =>
        tenant.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        tenant.description?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        tenant.category?.toLowerCase().includes(searchQuery.toLowerCase())
      )
    }

    // Filter by category
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(tenant => tenant.category === selectedCategory)
    }

    setFilteredTenants(filtered)
  }, [searchQuery, selectedCategory, tenants])

  const handleVisitTenant = (tenant: Tenant) => {
    // Navigate to tenant's Sellzio store
    router.push(`/tenant/${tenant.slug}/sellzio`)
  }

  const handleViewDetails = (tenant: Tenant) => {
    // Navigate to tenant details page
    router.push(`/tenant/${tenant.slug}`)
  }

  const getPlanBadgeColor = (plan: string) => {
    switch (plan) {
      case 'enterprise': return 'bg-purple-100 text-purple-800'
      case 'professional': return 'bg-blue-100 text-blue-800'
      case 'basic': return 'bg-green-100 text-green-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading tenant directory...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">Sellzio Tenant Directory</h1>
        <p className="text-gray-600 max-w-2xl mx-auto">
          Jelajahi berbagai marketplace yang dibangun dengan platform Sellzio. 
          Temukan toko online terpercaya dengan beragam produk berkualitas.
        </p>
      </div>

      {/* Search and Filter */}
      <div className="mb-8 space-y-4">
        {/* Search */}
        <div className="relative max-w-md mx-auto">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            type="text"
            placeholder="Cari marketplace..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>

        {/* Category Filter */}
        <div className="flex flex-wrap justify-center gap-2">
          {categories.map((category) => (
            <Button
              key={category}
              variant={selectedCategory === category ? "default" : "outline"}
              size="sm"
              onClick={() => setSelectedCategory(category)}
              className="capitalize"
            >
              {category === 'all' ? 'Semua' : category}
            </Button>
          ))}
        </div>
      </div>

      {/* Results Count */}
      <div className="mb-6 text-center text-gray-600">
        Menampilkan {filteredTenants.length} dari {tenants.length} marketplace
      </div>

      {/* Tenant Grid */}
      {filteredTenants.length === 0 ? (
        <div className="text-center py-12">
          <Store className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 mb-2">Tidak ada marketplace ditemukan</h3>
          <p className="text-gray-600">Coba ubah kata kunci pencarian atau filter kategori</p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredTenants.map((tenant) => (
            <Card key={tenant.id} className="hover:shadow-lg transition-shadow duration-200">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                      <Store className="h-6 w-6 text-orange-600" />
                    </div>
                    <div>
                      <CardTitle className="text-lg">{tenant.name}</CardTitle>
                      <CardDescription className="text-sm">
                        {tenant.domain}
                      </CardDescription>
                    </div>
                  </div>
                  <Badge className={getPlanBadgeColor(tenant.plan)}>
                    {tenant.plan}
                  </Badge>
                </div>
              </CardHeader>
              
              <CardContent>
                <p className="text-gray-600 text-sm mb-4 line-clamp-2">
                  {tenant.description}
                </p>

                {/* Stats */}
                {tenant.stats && (
                  <div className="grid grid-cols-3 gap-4 mb-4 text-center">
                    <div>
                      <div className="flex items-center justify-center text-gray-500 mb-1">
                        <Store className="h-4 w-4 mr-1" />
                      </div>
                      <div className="text-sm font-semibold">{tenant.stats.stores}</div>
                      <div className="text-xs text-gray-500">Toko</div>
                    </div>
                    <div>
                      <div className="flex items-center justify-center text-gray-500 mb-1">
                        <ShoppingBag className="h-4 w-4 mr-1" />
                      </div>
                      <div className="text-sm font-semibold">{tenant.stats.products.toLocaleString()}</div>
                      <div className="text-xs text-gray-500">Produk</div>
                    </div>
                    <div>
                      <div className="flex items-center justify-center text-gray-500 mb-1">
                        <Star className="h-4 w-4 mr-1" />
                      </div>
                      <div className="text-sm font-semibold">{tenant.stats.rating}</div>
                      <div className="text-xs text-gray-500">Rating</div>
                    </div>
                  </div>
                )}

                {/* Category */}
                {tenant.category && (
                  <div className="mb-4">
                    <Badge variant="secondary" className="text-xs">
                      {tenant.category}
                    </Badge>
                  </div>
                )}

                {/* Actions */}
                <div className="flex space-x-2">
                  <Button 
                    onClick={() => handleVisitTenant(tenant)}
                    className="flex-1 bg-orange-500 hover:bg-orange-600"
                    size="sm"
                  >
                    <ExternalLink className="h-4 w-4 mr-1" />
                    Kunjungi
                  </Button>
                  <Button 
                    onClick={() => handleViewDetails(tenant)}
                    variant="outline"
                    size="sm"
                  >
                    Detail
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  )
}

"use client"

import React, { useState, useEffect, use } from 'react'
import { useRouter } from 'next/navigation'
import { SellzioHeader } from '@/components/themes/sellzio/sellzio-header'
import { SellzioFacet } from '@/components/themes/sellzio/sellzio-facet'
import SellzioCategories from '@/components/themes/sellzio/categories'
import ShopeeLiveVideo from '@/components/themes/sellzio/shopee-live-video'
import { SellzioCartModal, type CartItem } from '@/components/themes/sellzio/cart'
import { SellzioSearchSuggestions } from '@/components/themes/sellzio/search/sellzio-search-suggestions'
import { SellzioSearchPredictions } from '@/components/themes/sellzio/search/sellzio-search-predictions'
import { useCart } from '@/hooks/use-cart'
import '@/components/themes/sellzio/sellzio-styles.css'
import Head from 'next/head'
// Import data dari file terpisah
import { sampleProducts, type Product as ProductType } from '@/components/data/products'
import { keywordPredictionDB } from '@/components/data/keywords'
// Import komponen masonry dan product card Sellzio
import { SellzioMasonryLayout } from '@/components/themes/sellzio/masonry/sellzio-masonry-layout'
import { SellzioProductCard } from '@/components/themes/sellzio/product-card/sellzio-product-card'
import { useTenantTheme } from '@/components/tenant/tenant-theme-provider'
import { useTenantProducts } from '@/hooks/use-tenant-products'

// Type definitions
interface Prediction {
  text: string
  type: string
  relevance: number
}



interface TenantSellzioPageProps {
  params: Promise<{ slug: string }>
}

const TenantSellzioPage = ({ params }: TenantSellzioPageProps) => {
  const router = useRouter()
  const { theme, loading: themeLoading } = useTenantTheme()

  // Unwrap params using React.use()
  const resolvedParams = use(params)
  const tenantSlug = resolvedParams.slug

  // Fetch products from database
  const { products: dbProducts, loading: productsLoading, error: productsError } = useTenantProducts(tenantSlug)

  console.log('🔥 TENANT PAGE: Database products:', dbProducts.length, 'Loading:', productsLoading, 'Error:', productsError)

  // Use database products if available, fallback to sample products
  const products = dbProducts.length > 0 ? dbProducts : sampleProducts

  console.log('🔥 TENANT PAGE: Using products:', products.length, 'from', dbProducts.length > 0 ? 'database' : 'sample data')

  // Test Supabase connection directly
  useEffect(() => {
    const testSupabase = async () => {
      try {
        console.log('🔥 DIRECT TEST: Testing Supabase connection...')
        const { getClient } = await import('@/lib/supabase')
        const supabase = getClient()
        const { data, error } = await supabase
          .from('products')
          .select('id, name, tenant_id')
          .eq('tenant_id', tenantSlug)
          .limit(3)

        console.log('🔥 DIRECT TEST: Supabase result:', { data, error, tenantSlug })
      } catch (err) {
        console.error('🔥 DIRECT TEST: Supabase error:', err)
      }
    }

    testSupabase()
  }, [tenantSlug])

  // Convert products to ProductType format for compatibility with existing components
  const compatibleProducts: ProductType[] = products.map(product => ({
    id: product.id,
    name: product.name,
    shortName: product.shortName || product.name,
    category: product.category,
    subcategory: product.subcategory,
    price: product.price,
    originalPrice: product.originalPrice || product.price,
    discount: product.discount || '0%',
    rating: product.rating || '0',
    sold: product.sold || '0 terjual',
    shipping: product.shipping || 'Gratis Ongkir',
    image: product.image,
    isMall: product.isMall || false,
    cod: product.cod || false,
    storeName: 'Default Store', // Required by ProductType
    storeLocation: product.address?.city || 'Jakarta',
    isTerlaris: (product as any).isTerlaris || false,
    isLive: (product as any).isLive || false
  }))

  const [searchValue, setSearchValue] = useState('')
  const [isSearchExpanded, setIsSearchExpanded] = useState(false)
  const [showSuggestions, setShowSuggestions] = useState(false)
  const [showMoreSuggestions, setShowMoreSuggestions] = useState(false)
  const [searchHistory, setSearchHistory] = useState<string[]>([])
  const [showPredictions, setShowPredictions] = useState(false)
  const [predictions, setPredictions] = useState<Prediction[]>([])
  const [searchResults, setSearchResults] = useState<ProductType[]>([])
  const [originalSearchResults, setOriginalSearchResults] = useState<ProductType[]>([]) // Store original results for filtering
  const [isSearchResultShown, setIsSearchResultShown] = useState(false)
  const [hideMainContent, setHideMainContent] = useState(false)
  const [userInteractionHistory, setUserInteractionHistory] = useState<string[]>([])
  const [searchFrequency, setSearchFrequency] = useState<{ [key: string]: number }>({})
  const [showFilterTabs, setShowFilterTabs] = useState(false)
  const [activeFilterTab, setActiveFilterTab] = useState('terkait')
  const [priceSortDirection, setPriceSortDirection] = useState<'asc' | 'desc'>('asc')
  const [showFilterIcon, setShowFilterIcon] = useState(false)
  const [isMobile, setIsMobile] = useState(false)
  const [showFacetPanel, setShowFacetPanel] = useState(false)
  const [facetFilters, setFacetFilters] = useState<{[key: string]: string[]}>({})
  const [filterBadgeCount, setFilterBadgeCount] = useState(0)
  const [subcategoryContext, setSubcategoryContext] = useState<{
    category: string
    selectedSubcategory: string
    allSubcategories: any[]
  } | null>(null)

  // State untuk suggestions popup
  const [showSuggestionsPopup, setShowSuggestionsPopup] = useState(false)

  // State untuk successful search history - riwayat pencarian yang berhasil menemukan produk
  const [successfulSearchHistory, setSuccessfulSearchHistory] = useState<string[]>([])

  // State untuk cart
  const [isCartModalOpen, setIsCartModalOpen] = useState(false)
  const { cartItems, cartCount } = useCart()

  // Convert SimpleCartItem to CartItem for modal compatibility
  const convertedCartItems: CartItem[] = cartItems.map(item => ({
    id: item.id,
    productId: item.id,
    name: item.name,
    price: item.price,
    originalPrice: item.price,
    image: item.image,
    store: "Default Store",
    storeId: "store-1",
    quantity: item.quantity,
    variant: "",
    selected: false,
    isLive: false,
    badges: []
  }))

  // Function to load user interaction history from localStorage
  const loadUserInteractionHistory = () => {
    try {
      const history = localStorage.getItem('keywordPredictionHistory')
      if (history) {
        const parsedHistory = JSON.parse(history)
        setUserInteractionHistory(parsedHistory)
        console.log('Loaded prediction history:', parsedHistory)
      } else {
        console.log('No prediction history found in localStorage')
      }
    } catch (e) {
      console.log('Failed to load prediction history from localStorage', e)
    }
  }

  // Function to load search frequency from localStorage
  const loadSearchFrequency = () => {
    try {
      const savedFrequency = localStorage.getItem('searchFrequency')
      if (savedFrequency) {
        setSearchFrequency(JSON.parse(savedFrequency))
      }
    } catch (e) {
      console.log('Failed to load search frequency from localStorage', e)
    }
  }

  // Function to update search frequency
  const updateSearchFrequency = (keyword: string) => {
    setSearchFrequency(prev => {
      const updated = { ...prev }
      updated[keyword] = (updated[keyword] || 0) + 1
      localStorage.setItem('searchFrequency', JSON.stringify(updated))
      return updated
    })
  }

  // Function to save successful search to history - hanya pencarian yang menemukan produk
  const saveSuccessfulSearch = (keyword: string) => {
    if (!keyword || keyword.trim() === '') return

    const normalizedKeyword = keyword.trim().toLowerCase()

    setSuccessfulSearchHistory(prev => {
      // Hapus keyword jika sudah ada (untuk memindahkan ke posisi teratas)
      const filtered = prev.filter(item => item.toLowerCase() !== normalizedKeyword)

      // Tambahkan ke posisi teratas
      const updated = [keyword.trim(), ...filtered].slice(0, 20) // Maksimal 20 item

      // Simpan ke localStorage
      localStorage.setItem('successfulSearchHistory', JSON.stringify(updated))

      return updated
    })
  }

  // Function to load successful search history from localStorage
  const loadSuccessfulSearchHistory = () => {
    try {
      const history = localStorage.getItem('successfulSearchHistory')
      if (history) {
        const parsedHistory = JSON.parse(history)
        setSuccessfulSearchHistory(parsedHistory)
        console.log('Loaded successful search history:', parsedHistory)
      }
    } catch (e) {
      console.log('Failed to load successful search history from localStorage', e)
    }
  }

  // Function to convert text to Title Case (huruf besar di depan setiap kata)
  const toTitleCase = (text: string) => {
    return text.toLowerCase()
  }

  // Function to get trending keywords based on search frequency
  const getTrendingKeywords = () => {
    // Default trending keywords if no search frequency data
    const defaultTrending = [
      'tas sekolah', 'tas selempang', 'handphone', 'tas mata', 'tas'
    ]

    // If no search frequency data, return default
    if (Object.keys(searchFrequency).length === 0) {
      return defaultTrending
    }

    // Sort keywords by frequency and get top 5
    const sortedKeywords = Object.entries(searchFrequency)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 5)
      .map(([keyword]) => keyword)

    // If we have less than 5, fill with defaults
    while (sortedKeywords.length < 5) {
      const nextDefault = defaultTrending[sortedKeywords.length]
      if (nextDefault && !sortedKeywords.includes(nextDefault)) {
        sortedKeywords.push(nextDefault)
      } else {
        break
      }
    }

    return sortedKeywords
  }

  // Function to get popular products based on search frequency
  const getPopularProducts = () => {
    // Default popular products
    const defaultProducts = [
      { name: 'Samsung Galaxy', image: 'https://images.unsplash.com/photo-*************-5f897ff02aa9?w=150&h=150&fit=crop' },
      { name: 'Sneakers Pria', image: 'https://images.unsplash.com/photo-**********-b41d501d3772?w=150&h=150&fit=crop' },
      { name: 'Tas Selempang', image: 'https://images.unsplash.com/photo-**********-98eeb64c6a62?w=150&h=150&fit=crop' },
      { name: 'Headphone Bluetooth', image: 'https://images.unsplash.com/photo-*************-5e560c06d30e?w=150&h=150&fit=crop' },
      { name: 'Keyboard Gaming', image: 'https://images.unsplash.com/photo-*************-b024d705b90a?w=150&h=150&fit=crop' },
      { name: 'Power Bank', image: 'https://images.unsplash.com/photo-*************-b43bada2f4b8?w=150&h=150&fit=crop' },
      { name: 'Smart TV', image: 'https://images.unsplash.com/photo-*************-a4bb92f829d1?w=150&h=150&fit=crop' },
      { name: 'Tas Selempang', image: 'https://images.unsplash.com/photo-**********-98eeb64c6a62?w=150&h=150&fit=crop' }
    ]

    // If no search frequency data, return default
    if (Object.keys(searchFrequency).length === 0) {
      return defaultProducts
    }

    // Map search frequency to products
    const productMapping: { [key: string]: { name: string, image: string } } = {
      'samsung galaxy': { name: 'Samsung Galaxy', image: 'https://images.unsplash.com/photo-*************-5f897ff02aa9?w=150&h=150&fit=crop' },
      'smartphone': { name: 'Samsung Galaxy', image: 'https://images.unsplash.com/photo-*************-5f897ff02aa9?w=150&h=150&fit=crop' },
      'sneakers': { name: 'Sneakers Pria', image: 'https://images.unsplash.com/photo-**********-b41d501d3772?w=150&h=150&fit=crop' },
      'sepatu': { name: 'Sneakers Pria', image: 'https://images.unsplash.com/photo-**********-b41d501d3772?w=150&h=150&fit=crop' },
      'tas selempang': { name: 'Tas Selempang', image: 'https://images.unsplash.com/photo-**********-98eeb64c6a62?w=150&h=150&fit=crop' },
      'tas': { name: 'Tas Selempang', image: 'https://images.unsplash.com/photo-**********-98eeb64c6a62?w=150&h=150&fit=crop' },
      'headphone': { name: 'Headphone Bluetooth', image: 'https://images.unsplash.com/photo-*************-5e560c06d30e?w=150&h=150&fit=crop' },
      'keyboard': { name: 'Keyboard Gaming', image: 'https://images.unsplash.com/photo-*************-b024d705b90a?w=150&h=150&fit=crop' },
      'power bank': { name: 'Power Bank', image: 'https://images.unsplash.com/photo-*************-b43bada2f4b8?w=150&h=150&fit=crop' },
      'smart tv': { name: 'Smart TV', image: 'https://images.unsplash.com/photo-*************-a4bb92f829d1?w=150&h=150&fit=crop' }
    }

    // Get products based on search frequency
    const popularProducts: { name: string, image: string }[] = []
    const sortedKeywords = Object.entries(searchFrequency)
      .sort(([, a], [, b]) => b - a)

    for (const [keyword] of sortedKeywords) {
      const lowerKeyword = keyword.toLowerCase()
      for (const [productKey, product] of Object.entries(productMapping)) {
        if (lowerKeyword.includes(productKey) && !popularProducts.some(p => p.name === product.name)) {
          popularProducts.push(product)
          if (popularProducts.length >= 8) break
        }
      }
      if (popularProducts.length >= 8) break
    }

    // Fill with defaults if needed
    while (popularProducts.length < 8) {
      const nextDefault = defaultProducts[popularProducts.length]
      if (nextDefault && !popularProducts.some(p => p.name === nextDefault.name)) {
        popularProducts.push(nextDefault)
      } else {
        break
      }
    }

    return popularProducts
  }

  // Function to generate predictions based on input
  const generatePredictions = (input: string) => {
    if (!input || input.trim() === '') {
      setPredictions([])
      return
    }

    const query = input.toLowerCase().trim()
    const newPredictions: Prediction[] = []

    // 1. History-based predictions (from successful searches)
    successfulSearchHistory.forEach(historyItem => {
      if (historyItem.toLowerCase().includes(query)) {
        newPredictions.push({
          text: historyItem,
          type: 'history',
          relevance: 0.9
        })
      }
    })

    // 2. Product-based predictions
    compatibleProducts.forEach(product => {
      if (product.name.toLowerCase().includes(query) ||
          product.category.toLowerCase().includes(query) ||
          (product.subcategory && product.subcategory.toLowerCase().includes(query))) {
        newPredictions.push({
          text: product.name,
          type: 'product',
          relevance: 0.8
        })
      }
    })

    // 3. Keyword database predictions
    try {
      if (keywordPredictionDB && (keywordPredictionDB as any).keywords) {
        (keywordPredictionDB as any).keywords.forEach((keyword: string) => {
          if (keyword.toLowerCase().includes(query)) {
            newPredictions.push({
              text: keyword,
              type: 'keyword',
              relevance: 0.7
            })
          }
        })
      }
    } catch (e) {
      // Fallback keywords if database fails
      const fallbackKeywords = ['smartphone', 'laptop', 'tas', 'sepatu', 'baju']
      fallbackKeywords.forEach(keyword => {
        if (keyword.toLowerCase().includes(query)) {
          newPredictions.push({
            text: keyword,
            type: 'keyword',
            relevance: 0.7
          })
        }
      })
    }

    // 4. Trending keywords
    getTrendingKeywords().forEach(trending => {
      if (trending.toLowerCase().includes(query)) {
        newPredictions.push({
          text: trending,
          type: 'trending',
          relevance: 0.6
        })
      }
    })

    // Remove duplicates and sort by relevance
    const uniquePredictions = newPredictions.filter((prediction, index, self) =>
      index === self.findIndex(p => p.text.toLowerCase() === prediction.text.toLowerCase())
    )

    const sortedPredictions = uniquePredictions
      .sort((a, b) => b.relevance - a.relevance)
      .slice(0, 10) // Limit to 10 predictions

    setPredictions(sortedPredictions)
  }

  // Function to handle search input change
  const handleSearchChange = (value: string) => {
    setSearchValue(value)

    // Show predictions when typing (minimal 1 character) and search is expanded
    if (value.trim().length >= 1 && isSearchExpanded) {
      setShowPredictions(true)
      setShowSuggestions(false) // Hide suggestions when predictions are shown
      setIsSearchResultShown(false) // Reset search results
      setSearchResults([])
      generatePredictions(value)
      document.body.classList.add('show-suggestions') // Use same class for overlay effect
    } else if (value.trim() === '' && isSearchExpanded) {
      // Show suggestions when input is empty
      setShowPredictions(false)
      setShowSuggestions(true)
      setIsSearchResultShown(false) // Reset search results
      setSearchResults([])
      document.body.classList.add('show-suggestions')
    } else {
      setShowPredictions(false)
      setShowSuggestions(false)
      setIsSearchResultShown(false)
      setSearchResults([])
      document.body.classList.remove('show-suggestions')
    }
  }

  // Function to handle prediction click
  const handlePredictionClick = (prediction: Prediction) => {
    setSearchValue(prediction.text)
    setShowPredictions(false)
    setShowSuggestions(false)

    // Update search frequency for trending keywords
    updateSearchFrequency(prediction.text)

    // Execute search automatically
    executeSearch(prediction.text)
  }

  // Function to execute search
  const executeSearch = (searchText: string) => {
    if (!searchText || searchText.trim() === '') return

    const query = searchText.trim()
    const results = enhancedSearch(query)

    setOriginalSearchResults(results)
    setSearchResults(results)
    setIsSearchResultShown(true)
    setHideMainContent(true)
    setShowPredictions(false)
    setShowSuggestions(false)
    setShowFilterTabs(true)
    setShowFilterIcon(true)

    // Save to search history if results found
    if (results.length > 0) {
      saveSuccessfulSearch(query)
      setSearchHistory(prev => {
        const filtered = prev.filter(item => item.toLowerCase() !== query.toLowerCase())
        const updated = [query, ...filtered].slice(0, 20)
        localStorage.setItem('searchHistory', JSON.stringify(updated))
        return updated
      })
    }

    // Update body classes
    document.body.classList.remove('show-suggestions')
    document.body.classList.add('hide-main-content')
  }

  // Enhanced search function
  const enhancedSearch = (query: string): ProductType[] => {
    if (!query || query.trim() === '') return []

    const searchTerms = query.toLowerCase().trim().split(' ')
    const results: (ProductType & { searchScore: number, matchDetails: string[] })[] = []

    compatibleProducts.forEach(product => {
      let score = 0
      const matchDetails: string[] = []

      // Exact name match (highest score)
      if (product.name.toLowerCase() === query.toLowerCase()) {
        score += 100
        matchDetails.push('exact_name')
      }

      // Name contains query
      if (product.name.toLowerCase().includes(query.toLowerCase())) {
        score += 50
        matchDetails.push('name_contains')
      }

      // Category match
      if (product.category.toLowerCase().includes(query.toLowerCase())) {
        score += 30
        matchDetails.push('category')
      }

      // Subcategory match
      if (product.subcategory && product.subcategory.toLowerCase().includes(query.toLowerCase())) {
        score += 25
        matchDetails.push('subcategory')
      }

      // Individual word matches
      searchTerms.forEach(term => {
        if (term.length >= 2) {
          if (product.name.toLowerCase().includes(term)) {
            score += 10
            matchDetails.push(`word_${term}`)
          }
        }
      })

      // Add to results if score > 0
      if (score > 0) {
        results.push({
          ...product,
          searchScore: score,
          matchDetails
        })
      }
    })

    // Sort by score (highest first)
    return results
      .sort((a, b) => b.searchScore - a.searchScore)
      .slice(0, 50) // Limit results
  }



  // Function to handle suggestion click
  const handleSuggestionClick = (suggestion: string) => {
    setSearchValue(suggestion)
    executeSearch(suggestion)
  }

  // Function to clear search history
  const clearSearchHistory = () => {
    setSearchHistory([])
    setSuccessfulSearchHistory([])
    localStorage.removeItem('searchHistory')
    localStorage.removeItem('successfulSearchHistory')
  }

  // Load data from localStorage on component mount
  useEffect(() => {
    loadUserInteractionHistory()
    loadSearchFrequency()
    loadSuccessfulSearchHistory()

    // Load search history
    try {
      const savedHistory = localStorage.getItem('searchHistory')
      if (savedHistory) {
        setSearchHistory(JSON.parse(savedHistory))
      }
    } catch (e) {
      console.log('Failed to load search history from localStorage', e)
    }
  }, [])

  // Theme loading
  if (themeLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading {tenantSlug} marketplace...</p>
        </div>
      </div>
    )
  }

  return (
    <>
      <Head>
        <title>{tenantSlug} - Sellzio Marketplace</title>
        <meta name="description" content={`Marketplace ${tenantSlug} powered by Sellzio`} />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
      </Head>

      <div className="sellzio-page">
        {/* Header */}
        <SellzioHeader
          searchValue={searchValue}
          onSearchChange={handleSearchChange}
          onSearchFocus={() => {
            setIsSearchExpanded(true)
            if (searchValue.trim() === '') {
              setShowSuggestions(true)
              setShowPredictions(false)
              document.body.classList.add('show-suggestions')
            }
          }}
          onSearchBlur={() => {}}
          onSearchExecute={executeSearch}
          isExpanded={isSearchExpanded}
          onToggleExpanded={() => setIsSearchExpanded(!isSearchExpanded)}
          onCartClick={() => setIsCartModalOpen(true)}
          cartCount={cartCount}
          onFilterClick={() => setShowFacetPanel(!showFacetPanel)}
          showFilterIcon={showFilterIcon}
          filterBadgeCount={filterBadgeCount}
        />

        {/* Main Content */}
        <main className="main-content pt-20 container mx-auto py-4 px-2">
          {/* Loading State */}
          {productsLoading && (
            <div className="flex items-center justify-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-500"></div>
              <span className="ml-2 text-gray-600">Memuat produk...</span>
            </div>
          )}

          {/* Error State */}
          {productsError && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
              <p className="text-red-600">Error: {productsError}</p>
              <p className="text-sm text-red-500 mt-1">Menggunakan data contoh sebagai fallback.</p>
            </div>
          )}

          {/* Categories */}
          <SellzioCategories
            products={compatibleProducts}
          />

          {/* Shopee Live Video */}
          <ShopeeLiveVideo />

          {/* Products Section */}
          <section className="mb-12">
            <div className="sellzio-products-container">
              <SellzioMasonryLayout
                columnCount={{ mobile: 2, tablet: 3, desktop: 4 }}
                gap={{ mobile: 6, tablet: 10, desktop: 12 }}
              >
                {compatibleProducts.slice(0, 24).map((product, index) => {
                  const rating = product.rating ? parseFloat(product.rating) : 0
                  const sold = product.sold ? parseInt(product.sold.replace(/\D/g, '')) || 0 : 0

                  let badgeType: "mall" | "star" | "star-lite" | "termurah" | "terlaris" | "komisi-xtra" | "none" = "none"
                  
                  if (product.isMall) badgeType = "mall"
                  else if ((product as any).isTerlaris) badgeType = "terlaris"
                  else if (rating >= 4.5) badgeType = "star"
                  else if (rating >= 4.0) badgeType = "star-lite"

                  let cardType: "standard" | "video" | "flash-sale" = "standard"
                  if ((product as any).isLive) cardType = "video"
                  else if (product.discount && parseFloat(product.discount) > 0) cardType = "flash-sale"

                  // Props tambahan untuk flash sale
                  const additionalProps: any = {}
                  if (cardType === "flash-sale") {
                    const endTime = new Date()
                    endTime.setHours(endTime.getHours() + 2) // Flash sale berakhir dalam 2 jam
                    additionalProps.endTime = endTime
                    additionalProps.stockSold = Math.floor(Math.random() * 80) + 10 // 10-90 terjual
                    additionalProps.totalStock = 100
                  }

                  return (
                    <SellzioProductCard
                      key={product.id}
                      id={product.id}
                      type={cardType}
                      name={product.name}
                      price={product.price}
                      originalPrice={product.originalPrice}
                      discount={product.discount}
                      image={product.image}
                      rating={rating}
                      sold={sold}
                      hasCod={(product as any).hasCod}
                      isMall={product.isMall}
                      isTerlaris={(product as any).isTerlaris}
                      isLive={(product as any).isLive}
                      shipping={product.shipping}
                      badgeType={badgeType}
                      onClick={() => router.push(`/tenant/${tenantSlug}/sellzio/products/${product.id}`)}
                      onAddToCart={() => {}}
                      {...additionalProps}
                    />
                  )
                })}
              </SellzioMasonryLayout>
            </div>
          </section>
        </main>

        {/* Keyword Predictions Container - Muncul saat mengetik minimal 1 huruf */}
        {showPredictions && (
          <SellzioSearchPredictions
            predictions={predictions}
            searchValue={searchValue}
            onPredictionClick={handlePredictionClick}
          />
        )}

        {/* Suggestions Container - Persis seperti docs/facet.html */}
        {showSuggestions && (
          <SellzioSearchSuggestions
            searchHistory={searchHistory}
            showMoreSuggestions={showMoreSuggestions}
            onToggleMore={() => setShowMoreSuggestions(!showMoreSuggestions)}
            onSuggestionClick={handleSuggestionClick}
            onClearHistory={clearSearchHistory}
            trendingKeywords={getTrendingKeywords()}
            popularProducts={getPopularProducts()}
          />
        )}

        {/* Cart Modal */}
        <SellzioCartModal
          isOpen={isCartModalOpen}
          onClose={() => setIsCartModalOpen(false)}
          cartItems={convertedCartItems}
        />
      </div>
    </>
  )
}

export default TenantSellzioPage

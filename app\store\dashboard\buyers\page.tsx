"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  Users,
  Search,
  Mail,
  Calendar,
  ShoppingCart,
  TrendingUp,
  Store
} from "lucide-react"
import { format } from "date-fns"
import { id } from "date-fns/locale"
import { useAuth } from "@/contexts/auth-context"

interface Buyer {
  id: string
  email: string
  name: string
  role: string
  tenantId: string
  storeId: string | null
  createdAt: string
  updatedAt: string
}

export default function StoreBuyersPage() {
  const [buyers, setBuyers] = useState<Buyer[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [searchTerm, setSearchTerm] = useState("")
  const [storeId, setStoreId] = useState<string | null>(null)
  const [tenantId, setTenantId] = useState<string | null>(null)

  // Get store and tenant ID from user context
  const { user } = useAuth();

  useEffect(() => {
    if (user?.storeId && user?.tenantId) {
      setStoreId(user.storeId);
      setTenantId(user.tenantId);
    }
  }, [user]);

  // Fetch buyers data for this store
  useEffect(() => {
    async function fetchStoreBuyers() {
      if (!storeId || !tenantId) return;
      
      setIsLoading(true);
      try {
        const response = await fetch(`/api/store/buyers?storeId=${storeId}&tenantId=${tenantId}`);
        const result = await response.json();

        if (!response.ok) {
          throw new Error(result.error || 'Failed to fetch buyers');
        }

        setBuyers(result.buyers || []);
      } catch (err) {
        console.error("Error fetching store buyers:", err);
        setError("Gagal memuat data buyers. Silakan coba lagi nanti.");
      } finally {
        setIsLoading(false);
      }
    }

    fetchStoreBuyers();
  }, [storeId, tenantId]);

  // Filter buyers based on search
  const filteredBuyers = buyers.filter(buyer =>
    buyer.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    buyer.email?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const totalBuyers = filteredBuyers.length;
  const activeBuyers = filteredBuyers.filter(buyer => 
    new Date(buyer.updatedAt) > new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
  ).length;

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
            <p className="mt-2 text-muted-foreground">Memuat data buyers...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <Card>
          <CardContent className="pt-6">
            <div className="text-center text-red-600">
              <p>{error}</p>
              <Button 
                onClick={() => window.location.reload()} 
                className="mt-4"
                variant="outline"
              >
                Coba Lagi
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Buyers Store</h2>
          <p className="text-muted-foreground">
            Manage buyers yang berinteraksi dengan store Anda
          </p>
        </div>
      </div>

      {/* Info Alert */}
      <Card className="border-blue-200 bg-blue-50">
        <CardContent className="pt-6">
          <div className="flex items-center gap-2 text-blue-800">
            <Store className="h-4 w-4" />
            <p className="text-sm">
              <strong>Catatan:</strong> Sebagai store owner, Anda hanya dapat melihat buyers dalam tenant yang sama. 
              Data buyers dari tenant lain tidak akan ditampilkan untuk menjaga privasi.
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Buyers</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalBuyers}</div>
            <p className="text-xs text-muted-foreground">
              Dalam tenant yang sama
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Buyers</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{activeBuyers}</div>
            <p className="text-xs text-muted-foreground">
              Active in last 30 days
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Potential Customers</CardTitle>
            <ShoppingCart className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {Math.floor(totalBuyers * 0.3)}
            </div>
            <p className="text-xs text-muted-foreground">
              Estimated interested buyers
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Conversion Rate</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">12%</div>
            <p className="text-xs text-muted-foreground">
              Buyers to customers
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Card>
        <CardHeader>
          <div className="flex flex-col gap-4">
            <div className="flex items-center justify-between">
              <div>
                <CardTitle>Daftar Buyers</CardTitle>
                <CardDescription>
                  Buyers dalam tenant yang sama dengan store Anda
                </CardDescription>
              </div>

              {/* Search */}
              <div className="flex items-center gap-2">
                <div className="relative">
                  <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Cari buyers..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-8 w-[300px]"
                  />
                </div>
              </div>
            </div>
          </div>
        </CardHeader>

        <CardContent>
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Buyer</TableHead>
                  <TableHead>Email</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Bergabung</TableHead>
                  <TableHead>Terakhir Aktif</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredBuyers.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={5} className="text-center py-8">
                      <div className="text-muted-foreground">
                        {searchTerm ? "Tidak ada buyers yang sesuai dengan pencarian" : "Belum ada buyers dalam tenant ini"}
                      </div>
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredBuyers.map((buyer) => (
                    <TableRow key={buyer.id}>
                      <TableCell>
                        <div className="flex items-center gap-3">
                          <div className="h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center">
                            <Users className="h-4 w-4 text-primary" />
                          </div>
                          <div>
                            <div className="font-medium">{buyer.name}</div>
                            <div className="text-sm text-muted-foreground">ID: {buyer.id.slice(0, 8)}...</div>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Mail className="h-4 w-4 text-muted-foreground" />
                          {buyer.email}
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant="default" className="bg-green-100 text-green-800">
                          Active
                        </Badge>
                      </TableCell>
                      <TableCell>
                        {format(new Date(buyer.createdAt), 'dd MMM yyyy', { locale: id })}
                      </TableCell>
                      <TableCell>
                        {format(new Date(buyer.updatedAt), 'dd MMM yyyy', { locale: id })}
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

"use client"

import React, { useState } from 'react'
import { Minus, Plus } from 'lucide-react'
import { CartItem } from './types'

interface SellzioCartItemProps {
  item: CartItem
  selected: boolean
  onSelect: (selected: boolean) => void
  onQuantityChange: (quantity: number) => void
  isEditMode?: boolean
}

export const SellzioCartItem: React.FC<SellzioCartItemProps> = ({
  item,
  selected,
  onSelect,
  onQuantityChange,
  isEditMode = false
}) => {
  const [showVariantModal, setShowVariantModal] = useState(false)
  const [selectedVariant, setSelectedVariant] = useState(item.variant || '')

  const handleQuantityDecrease = () => {
    if (item.quantity > 1) {
      onQuantityChange(item.quantity - 1)
    }
  }

  const handleQuantityIncrease = () => {
    onQuantityChange(item.quantity + 1)
  }

  const handleVariantClick = () => {
    setShowVariantModal(true)
  }

  const handleVariantConfirm = () => {
    setShowVariantModal(false)
    // Update variant logic here
  }

  return (
    <>
      <div className="sellzio-cart-item">
        <div className="sellzio-item-checkbox">
          <input
            type="checkbox"
            checked={selected}
            onChange={(e) => onSelect(e.target.checked)}
          />
        </div>

        <div className="sellzio-item-image">
          <img src={item.image} alt={item.name} />
        </div>

        <div className="sellzio-item-details">
          <div className="sellzio-item-name">{item.name}</div>
          
          {item.variant && (
            <div className="sellzio-item-variant" onClick={handleVariantClick}>
              {selectedVariant}
            </div>
          )}

          <div className="sellzio-item-price">
            <div className="sellzio-price-section">
              <span className="sellzio-discounted-price">
                Rp{item.price.toLocaleString('id-ID')}
              </span>
              {item.originalPrice && (
                <span className="sellzio-original-price">
                  Rp{item.originalPrice.toLocaleString('id-ID')}
                </span>
              )}
            </div>

            <div className="sellzio-item-quantity">
              <button 
                className="sellzio-quantity-btn sellzio-minus-btn"
                onClick={handleQuantityDecrease}
                disabled={item.quantity <= 1}
              >
                <Minus size={14} />
              </button>
              <input
                type="text"
                className="sellzio-quantity-input"
                value={item.quantity}
                readOnly
              />
              <button 
                className="sellzio-quantity-btn sellzio-plus-btn"
                onClick={handleQuantityIncrease}
              >
                <Plus size={14} />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Variant Modal */}
      {showVariantModal && (
        <div className="sellzio-variant-modal-overlay" onClick={() => setShowVariantModal(false)}>
          <div className="sellzio-variant-modal" onClick={e => e.stopPropagation()}>
            <div className="sellzio-variant-modal-header">
              <h3>Pilih Varian</h3>
            </div>

            <div className="sellzio-variant-modal-body">
              <div className="sellzio-variant-group">
                <div className="sellzio-variant-title">Warna</div>
                <div className="sellzio-variant-options">
                  <div className="sellzio-variant-option selected">Putih</div>
                  <div className="sellzio-variant-option">Hitam</div>
                  <div className="sellzio-variant-option">Merah</div>
                  <div className="sellzio-variant-option">Biru</div>
                </div>
              </div>

              <div className="sellzio-variant-group">
                <div className="sellzio-variant-title">Ukuran</div>
                <div className="sellzio-variant-options">
                  <div className="sellzio-variant-option">S</div>
                  <div className="sellzio-variant-option">M</div>
                  <div className="sellzio-variant-option selected">L</div>
                  <div className="sellzio-variant-option">XL</div>
                  <div className="sellzio-variant-option">XXL</div>
                </div>
              </div>
            </div>

            <div className="sellzio-variant-modal-footer">
              <button 
                className="sellzio-variant-confirm-btn"
                onClick={handleVariantConfirm}
              >
                Konfirmasi
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  )
}

"use client"

import React from 'react'
import { useRouter } from 'next/navigation'
import { CheckCircle, ArrowLeft } from 'lucide-react'

export default function OrderSuccessPage() {
  const router = useRouter()

  const handleBackToHome = () => {
    router.push('/sellzio')
  }

  const handleViewOrders = () => {
    // Navigate to orders page when implemented
    console.log('Navigate to orders page')
  }

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="max-w-md mx-auto px-4 py-3 flex items-center">
          <button 
            onClick={handleBackToHome}
            className="p-2 -ml-2 text-gray-600 hover:text-gray-800"
          >
            <ArrowLeft size={20} />
          </button>
          <h1 className="flex-1 text-center font-semibold text-gray-800">
            <PERSON><PERSON><PERSON>
          </h1>
        </div>
      </header>

      {/* Content */}
      <div className="flex-1 flex flex-col items-center justify-center px-4 py-8">
        <div className="bg-white rounded-lg shadow-sm p-8 max-w-sm w-full text-center">
          {/* Success Icon */}
          <div className="mb-6">
            <CheckCircle size={64} className="text-green-500 mx-auto" />
          </div>

          {/* Success Message */}
          <h2 className="text-xl font-bold text-gray-800 mb-2">
            Pesanan Berhasil Dibuat!
          </h2>
          <p className="text-gray-600 mb-6">
            Terima kasih telah berbelanja. Pesanan Anda sedang diproses dan akan segera dikirim.
          </p>

          {/* Order Info */}
          <div className="bg-gray-50 rounded-lg p-4 mb-6">
            <div className="text-sm text-gray-600 mb-1">Nomor Pesanan</div>
            <div className="font-semibold text-gray-800">
              #ORD-{Date.now().toString().slice(-8)}
            </div>
          </div>

          {/* Action Buttons */}
          <div className="space-y-3">
            <button
              onClick={handleViewOrders}
              className="w-full bg-orange-500 text-white py-3 px-4 rounded-lg font-semibold hover:bg-orange-600 transition-colors"
            >
              Lihat Pesanan
            </button>
            <button
              onClick={handleBackToHome}
              className="w-full border border-gray-300 text-gray-700 py-3 px-4 rounded-lg font-semibold hover:bg-gray-50 transition-colors"
            >
              Kembali Berbelanja
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

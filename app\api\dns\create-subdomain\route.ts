import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    const { subdomain } = await request.json()

    if (!subdomain) {
      return NextResponse.json(
        { success: false, error: 'Subdomain is required' },
        { status: 400 }
      )
    }

    const apiToken = process.env.CLOUDFLARE_API_TOKEN
    const zoneId = process.env.CLOUDFLARE_ZONE_ID
    const vercelIp = process.env.VERCEL_IP

    if (!apiToken || !zoneId || !vercelIp) {
      console.error('Missing environment variables:', {
        hasApiToken: !!apiToken,
        hasZoneId: !!zoneId,
        hasVercelIp: !!vercelIp
      })
      return NextResponse.json(
        { success: false, error: 'Server configuration error' },
        { status: 500 }
      )
    }

    // Create DNS CNAME record for subdomain
    const dnsRecord = {
      type: 'CNAME',
      name: subdomain, // e.g., 'test1' for test1.sellzio.my.id
      content: 'sellzio.my.id', // Point to main domain
      ttl: 1, // Auto TTL
      proxied: true // Enable Cloudflare proxy for SSL
    }

    console.log('🚀 Creating DNS record:', dnsRecord)

    const createUrl = `https://api.cloudflare.com/client/v4/zones/${zoneId}/dns_records`
    const response = await fetch(createUrl, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiToken}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(dnsRecord)
    })

    const result = await response.json()

    if (!response.ok) {
      console.error('❌ Cloudflare API Error:', result)
      return NextResponse.json(
        { 
          success: false, 
          error: 'Failed to create DNS record',
          details: result.errors || result
        },
        { status: response.status }
      )
    }

    console.log('✅ DNS record created successfully:', result)

    return NextResponse.json({
      success: true,
      message: `Subdomain ${subdomain}.sellzio.my.id created successfully`,
      data: result.result
    })

  } catch (error) {
    console.error('❌ Error creating subdomain:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// GET method to check if subdomain exists
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const subdomain = searchParams.get('subdomain')

    if (!subdomain) {
      return NextResponse.json(
        { success: false, error: 'Subdomain parameter is required' },
        { status: 400 }
      )
    }

    const apiToken = process.env.CLOUDFLARE_API_TOKEN
    const zoneId = process.env.CLOUDFLARE_ZONE_ID

    if (!apiToken || !zoneId) {
      return NextResponse.json(
        { success: false, error: 'Server configuration error' },
        { status: 500 }
      )
    }

    // Check if DNS record exists
    const checkUrl = `https://api.cloudflare.com/client/v4/zones/${zoneId}/dns_records?name=${subdomain}.sellzio.my.id&type=CNAME`
    const response = await fetch(checkUrl, {
      headers: {
        'Authorization': `Bearer ${apiToken}`,
        'Content-Type': 'application/json',
      },
    })

    const result = await response.json()

    if (!response.ok) {
      console.error('❌ Cloudflare API Error:', result)
      return NextResponse.json(
        { success: false, error: 'Failed to check DNS record' },
        { status: response.status }
      )
    }

    const exists = result.result && result.result.length > 0

    return NextResponse.json({
      success: true,
      exists,
      records: result.result || []
    })

  } catch (error) {
    console.error('❌ Error checking subdomain:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}

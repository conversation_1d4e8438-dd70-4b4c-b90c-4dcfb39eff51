"use client"

import React, { useState } from 'react'
import type { PaymentGateway, PaymentMethod } from '@/lib/models/payment'

interface SellzioPaymentMethodsProps {
  paymentGateways: PaymentGateway[]
  selectedMethod: PaymentMethod | null
  selectedGateway: PaymentGateway | null
  onSelectMethod: (method: PaymentMethod, gateway: PaymentGateway) => void
}

export const SellzioPaymentMethods: React.FC<SellzioPaymentMethodsProps> = ({
  paymentGateways,
  selectedMethod,
  selectedGateway,
  onSelectMethod
}) => {
  const [expandedGateway, setExpandedGateway] = useState<string | null>(null)

  const handleGatewayToggle = (gatewayId: string) => {
    setExpandedGateway(expandedGateway === gatewayId ? null : gatewayId)
  }

  // Group payment methods by type
  const groupedMethods = paymentGateways.reduce((acc, gateway) => {
    gateway.methods.forEach(method => {
      if (!acc[method.type]) {
        acc[method.type] = []
      }
      acc[method.type].push({ method, gateway })
    })
    return acc
  }, {} as Record<string, Array<{ method: PaymentMethod; gateway: PaymentGateway }>>)

  // Auto-expand first payment type on load
  React.useEffect(() => {
    if (paymentGateways.length > 0 && !expandedGateway) {
      const firstType = Object.keys(groupedMethods)[0]
      if (firstType) {
        setExpandedGateway(firstType)
      }
    }
  }, [paymentGateways, expandedGateway, groupedMethods])

  const getPaymentTypeIcon = (type: string) => {
    switch (type) {
      case 'virtual_account':
        return '🏦'
      case 'e_wallet':
        return '📱'
      case 'credit_card':
        return '💳'
      case 'bank_transfer':
        return '🏧'
      case 'retail_outlet':
        return '🏪'
      default:
        return '💰'
    }
  }

  const getPaymentTypeLabel = (type: string) => {
    switch (type) {
      case 'virtual_account':
        return 'Virtual Account'
      case 'e_wallet':
        return 'E-Wallet'
      case 'credit_card':
        return 'Kartu Kredit'
      case 'bank_transfer':
        return 'Transfer Bank'
      case 'retail_outlet':
        return 'Gerai Retail'
      default:
        return 'Pembayaran'
    }
  }

  return (
    <div className="sellzio-payment-section sellzio-payment-methods">
      <div className="sellzio-section-header">
        <h3 className="sellzio-section-title">Pilih Metode Pembayaran</h3>
      </div>

      <div className="sellzio-payment-groups">
        {Object.entries(groupedMethods).map(([type, methods]) => (
          <div key={type} className="sellzio-payment-group">
            <div 
              className="sellzio-payment-group-header"
              onClick={() => handleGatewayToggle(type)}
            >
              <div className="sellzio-payment-group-info">
                <span className="sellzio-payment-group-icon">
                  {getPaymentTypeIcon(type)}
                </span>
                <span className="sellzio-payment-group-label">
                  {getPaymentTypeLabel(type)}
                </span>
              </div>
              <span className="sellzio-expand-icon">
                {expandedGateway === type ? '▼' : '▶'}
              </span>
            </div>

            {expandedGateway === type && (
              <div className="sellzio-payment-methods-list">
                {methods.map(({ method, gateway }) => (
                  <div
                    key={`${gateway.id}-${method.id}`}
                    className={`sellzio-payment-method ${
                      selectedMethod?.id === method.id && selectedGateway?.id === gateway.id
                        ? 'selected'
                        : ''
                    }`}
                    onClick={() => onSelectMethod(method, gateway)}
                  >
                    <div className="sellzio-payment-method-info">
                      <div className="sellzio-payment-method-logo">
                        {method.logo ? (
                          <img 
                            src={method.logo} 
                            alt={method.name}
                            onError={(e) => {
                              const target = e.target as HTMLImageElement
                              target.style.display = 'none'
                            }}
                          />
                        ) : (
                          <span className="sellzio-payment-method-icon">
                            {getPaymentTypeIcon(method.type)}
                          </span>
                        )}
                      </div>
                      <div className="sellzio-payment-method-details">
                        <span className="sellzio-payment-method-name">{method.name}</span>
                        <span className="sellzio-payment-method-gateway">{gateway.name}</span>
                      </div>
                    </div>
                    <div className="sellzio-payment-method-radio">
                      <div className={`sellzio-radio ${
                        selectedMethod?.id === method.id && selectedGateway?.id === gateway.id
                          ? 'checked'
                          : ''
                      }`}>
                        {selectedMethod?.id === method.id && selectedGateway?.id === gateway.id && (
                          <div className="sellzio-radio-dot"></div>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        ))}
      </div>

      <style jsx>{`
        .sellzio-payment-section {
          background: white;
          margin-bottom: 8px;
          padding: 16px;
          border-radius: 8px;
          box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .sellzio-section-header {
          margin-bottom: 16px;
          padding-bottom: 12px;
          border-bottom: 1px solid #f0f0f0;
        }

        .sellzio-section-title {
          font-size: 16px;
          font-weight: 600;
          color: #333;
          margin: 0;
        }

        .sellzio-payment-groups {
          space-y: 12px;
        }

        .sellzio-payment-group {
          border: 1px solid #e5e5e5;
          border-radius: 8px;
          margin-bottom: 12px;
          overflow: hidden;
        }

        .sellzio-payment-group-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 12px 16px;
          background: #f8f9fa;
          cursor: pointer;
          transition: background-color 0.2s ease;
        }

        .sellzio-payment-group-header:hover {
          background: #e9ecef;
        }

        .sellzio-payment-group-info {
          display: flex;
          align-items: center;
        }

        .sellzio-payment-group-icon {
          font-size: 20px;
          margin-right: 12px;
        }

        .sellzio-payment-group-label {
          font-size: 14px;
          font-weight: 500;
          color: #333;
        }

        .sellzio-expand-icon {
          font-size: 12px;
          color: #666;
          transition: transform 0.2s ease;
        }

        .sellzio-payment-methods-list {
          border-top: 1px solid #e5e5e5;
        }

        .sellzio-payment-method {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 12px 16px;
          border-bottom: 1px solid #f0f0f0;
          cursor: pointer;
          transition: background-color 0.2s ease;
        }

        .sellzio-payment-method:last-child {
          border-bottom: none;
        }

        .sellzio-payment-method:hover {
          background: #f8f9fa;
        }

        .sellzio-payment-method.selected {
          background: #fff5f5;
          border-color: #ee4d2d;
        }

        .sellzio-payment-method-info {
          display: flex;
          align-items: center;
          flex: 1;
        }

        .sellzio-payment-method-logo {
          width: 40px;
          height: 40px;
          border-radius: 8px;
          overflow: hidden;
          margin-right: 12px;
          display: flex;
          align-items: center;
          justify-content: center;
          background: #f5f5f5;
        }

        .sellzio-payment-method-logo img {
          width: 100%;
          height: 100%;
          object-fit: contain;
        }

        .sellzio-payment-method-icon {
          font-size: 20px;
        }

        .sellzio-payment-method-details {
          display: flex;
          flex-direction: column;
        }

        .sellzio-payment-method-name {
          font-size: 14px;
          font-weight: 500;
          color: #333;
          margin-bottom: 2px;
        }

        .sellzio-payment-method-gateway {
          font-size: 12px;
          color: #666;
        }

        .sellzio-payment-method-radio {
          margin-left: 12px;
        }

        .sellzio-radio {
          width: 20px;
          height: 20px;
          border: 2px solid #ddd;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          transition: border-color 0.2s ease;
        }

        .sellzio-radio.checked {
          border-color: #ee4d2d;
        }

        .sellzio-radio-dot {
          width: 10px;
          height: 10px;
          background: #ee4d2d;
          border-radius: 50%;
        }

        @media (max-width: 768px) {
          .sellzio-payment-section {
            margin-bottom: 6px;
            padding: 12px;
          }

          .sellzio-payment-group-header {
            padding: 10px 12px;
          }

          .sellzio-payment-method {
            padding: 10px 12px;
          }

          .sellzio-payment-method-logo {
            width: 36px;
            height: 36px;
            margin-right: 10px;
          }

          .sellzio-payment-method-name {
            font-size: 13px;
          }

          .sellzio-payment-method-gateway {
            font-size: 11px;
          }
        }
      `}</style>
    </div>
  )
}

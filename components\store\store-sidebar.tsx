"use client"

import { useState } from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { cn } from "@/lib/utils"
import {
  LayoutDashboard,
  Package,
  ShoppingCart,
  Users,
  BarChart,
  Settings,
  HelpCircle,
  Megaphone,
  Layers,
  FileImage,
  Video,
  PieChart,
  ChevronDown,
  ChevronRight,
} from "lucide-react"
import {
  Sidebar,
  SidebarContent,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuItem,
  SidebarTrigger,
  useSidebar,
} from "@/components/ui/sidebar"
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { useAuth } from "@/contexts/auth-context"

export function StoreSidebar() {
  const pathname = usePathname()
  const { state } = useSidebar()
  const [hoveredItem, setHoveredItem] = useState<string | null>(null)
  const { user } = useAuth()

  // State untuk menu yang di-expand
  const [expandedMenus, setExpandedMenus] = useState<Record<string, boolean>>({
    products: pathname.startsWith("/store/dashboard/products"),
    orders: pathname.startsWith("/store/dashboard/orders"),
    customers: pathname.startsWith("/store/dashboard/customers"),
    marketing: pathname.startsWith("/store/dashboard/marketing"),
    analytics: pathname.startsWith("/store/dashboard/analytics"),
    inventory: pathname.startsWith("/store/dashboard/inventory"),
    content: pathname.startsWith("/store/dashboard/content"),
    settings: pathname.startsWith("/store/dashboard/settings"),
    liveSelling: pathname.startsWith("/store/dashboard/live-selling"),
    reports: pathname.startsWith("/store/dashboard/reports"),
    help: pathname.startsWith("/store/dashboard/help"),
  })

  const toggleMenu = (menu: string) => {
    setExpandedMenus((prev) => ({
      ...prev,
      [menu]: !prev[menu],
    }))
  }

  // Menu items dengan submenu
  const menuItems = [
    {
      title: "Dashboard",
      href: "/store/dashboard",
      icon: LayoutDashboard,
      submenu: false,
    },
    {
      title: "Produk",
      key: "products",
      icon: Package,
      submenu: true,
      items: [
        { title: "Semua Produk", href: "/store/dashboard/products" },
        { title: "Tambah Produk", href: "/store/dashboard/products/create" },
        { title: "Kategori", href: "/store/dashboard/products/categories" },
        { title: "Varian", href: "/store/dashboard/products/variants" },
        { title: "Ulasan", href: "/store/dashboard/products/reviews" },
      ],
    },
    {
      title: "Pesanan",
      key: "orders",
      icon: ShoppingCart,
      submenu: true,
      items: [
        { title: "Semua Pesanan", href: "/store/dashboard/orders" },
        { title: "Menunggu Pembayaran", href: "/store/dashboard/orders/pending" },
        { title: "Perlu Dikirim", href: "/store/dashboard/orders/to-ship" },
        { title: "Dalam Pengiriman", href: "/store/dashboard/orders/shipping" },
        { title: "Selesai", href: "/store/dashboard/orders/completed" },
        { title: "Dibatalkan", href: "/store/dashboard/orders/cancelled" },
        { title: "Pengembalian", href: "/store/dashboard/orders/returns" },
      ],
    },
    {
      title: "Buyers",
      href: "/store/dashboard/buyers",
      icon: Users,
      submenu: false,
    },
    {
      title: "Pelanggan",
      key: "customers",
      icon: Users,
      submenu: true,
      items: [
        { title: "Semua Pelanggan", href: "/store/dashboard/customers" },
        { title: "Segmentasi", href: "/store/dashboard/customers/segments" },
        { title: "Grup", href: "/store/dashboard/customers/groups" },
        { title: "Feedback", href: "/store/dashboard/customers/feedback" },
      ],
    },
    {
      title: "Marketing",
      key: "marketing",
      icon: Megaphone,
      submenu: true,
      items: [
        { title: "Promosi", href: "/store/dashboard/marketing/promotions" },
        { title: "Diskon", href: "/store/dashboard/marketing/discounts" },
        { title: "Kupon", href: "/store/dashboard/marketing/coupons" },
        { title: "Bundling", href: "/store/dashboard/marketing/bundles" },
        { title: "Affiliate", href: "/store/dashboard/marketing/affiliate" },
        { title: "Email Marketing", href: "/store/dashboard/marketing/email" },
      ],
    },
    {
      title: "Analitik",
      key: "analytics",
      icon: BarChart,
      submenu: true,
      items: [
        { title: "Overview", href: "/store/dashboard/analytics" },
        { title: "Penjualan", href: "/store/dashboard/analytics/sales" },
        { title: "Produk", href: "/store/dashboard/analytics/products" },
        { title: "Pelanggan", href: "/store/dashboard/analytics/customers" },
        { title: "Marketing", href: "/store/dashboard/analytics/marketing" },
      ],
    },
    {
      title: "Inventori",
      key: "inventory",
      icon: Layers,
      submenu: true,
      items: [
        { title: "Stok", href: "/store/dashboard/inventory" },
        { title: "Penyesuaian Stok", href: "/store/dashboard/inventory/adjustments" },
        { title: "Transfer", href: "/store/dashboard/inventory/transfers" },
        { title: "Supplier", href: "/store/dashboard/inventory/suppliers" },
      ],
    },
    {
      title: "Konten",
      key: "content",
      icon: FileImage,
      submenu: true,
      items: [
        { title: "Banner", href: "/store/dashboard/content/banners" },
        { title: "Halaman", href: "/store/dashboard/content/pages" },
        { title: "Blog", href: "/store/dashboard/content/blog" },
        { title: "Media", href: "/store/dashboard/content/media" },
      ],
    },
    {
      title: "Live Selling",
      key: "liveSelling",
      icon: Video,
      badge: "Baru",
      submenu: true,
      items: [
        { title: "Jadwal Live", href: "/store/dashboard/live-selling/schedule" },
        { title: "Riwayat Live", href: "/store/dashboard/live-selling/history" },
        { title: "Produk Live", href: "/store/dashboard/live-selling/products" },
        { title: "Pengaturan Live", href: "/store/dashboard/live-selling/settings" },
      ],
    },
    {
      title: "Laporan",
      key: "reports",
      icon: PieChart,
      submenu: true,
      items: [
        { title: "Penjualan", href: "/store/dashboard/reports/sales" },
        { title: "Produk", href: "/store/dashboard/reports/products" },
        { title: "Pelanggan", href: "/store/dashboard/reports/customers" },
        { title: "Keuangan", href: "/store/dashboard/reports/finance" },
        { title: "Pajak", href: "/store/dashboard/reports/tax" },
      ],
    },
    {
      title: "Pengaturan",
      key: "settings",
      icon: Settings,
      submenu: true,
      items: [
        { title: "Profil Toko", href: "/store/dashboard/settings/profile" },
        { title: "Pengiriman", href: "/store/dashboard/settings/shipping" },
        { title: "Pembayaran", href: "/store/dashboard/settings/payment" },
        { title: "Pajak", href: "/store/dashboard/settings/tax" },
        { title: "Notifikasi", href: "/store/dashboard/settings/notifications" },
        { title: "Tim", href: "/store/dashboard/settings/team" },
        { title: "Integrasi", href: "/store/dashboard/settings/integrations" },
      ],
    },
    {
      title: "Bantuan",
      key: "help",
      icon: HelpCircle,
      submenu: true,
      items: [
        { title: "Panduan", href: "/store/dashboard/help/guides" },
        { title: "FAQ", href: "/store/dashboard/help/faq" },
        { title: "Tiket Support", href: "/store/dashboard/help/tickets" },
        { title: "Kontak", href: "/store/dashboard/help/contact" },
      ],
    },
  ]

  const renderSubmenuItems = (items, parentKey) => {
    return items.map((item) => (
      <Link
        key={item.href}
        href={item.href}
        className={cn(
          "flex w-full items-center rounded-md px-4 py-2 text-sm transition-colors",
          pathname === item.href
            ? "bg-accent/50 font-medium text-accent-foreground"
            : "text-muted-foreground hover:bg-accent/30 hover:text-foreground",
          "ml-9", // Indentation for submenu items
        )}
      >
        {item.title}
      </Link>
    ))
  }

  const renderMenuItem = (item) => {
    // For simple menu items without submenu
    if (!item.submenu) {
      return (
        <SidebarMenuItem
          key={item.href}
          onMouseEnter={() => setHoveredItem(item.href)}
          onMouseLeave={() => setHoveredItem(null)}
          className="mb-1"
        >
          {pathname === item.href ? (
            <div className="flex w-full items-center justify-between rounded-md bg-accent px-4 py-3 text-sm font-medium text-accent-foreground">
              <div className="flex items-center">
                <item.icon className="mr-3 h-5 w-5" />
                <span>{item.title}</span>
              </div>
              {item.badge && (
                <Badge variant="outline" className="ml-2 bg-primary/20 text-primary dark:bg-primary/30">
                  {item.badge}
                </Badge>
              )}
            </div>
          ) : (
            <Link
              href={item.href}
              className={cn(
                "flex w-full items-center justify-between rounded-md px-4 py-3 text-sm font-medium transition-colors hover:bg-accent/50",
                "text-muted-foreground hover:text-foreground",
                hoveredItem === item.href && "text-foreground",
              )}
            >
              <div className="flex items-center">
                <item.icon
                  className={cn(
                    "mr-3 h-5 w-5",
                    "text-muted-foreground",
                    hoveredItem === item.href && "text-foreground",
                  )}
                />
                <span>{item.title}</span>
              </div>
              {item.badge && (
                <Badge variant="outline" className="ml-2 bg-primary/10 text-primary dark:bg-primary/20">
                  {item.badge}
                </Badge>
              )}
            </Link>
          )}
        </SidebarMenuItem>
      )
    }

    // For menu items with submenu
    return (
      <div key={item.key} className="mb-1">
        <Collapsible open={expandedMenus[item.key]} onOpenChange={() => toggleMenu(item.key)} className="w-full">
          <CollapsibleTrigger asChild>
            <button
              className={cn(
                "flex w-full items-center justify-between rounded-md px-4 py-3 text-sm font-medium transition-colors",
                pathname.startsWith(`/store/dashboard/${item.key.toLowerCase()}`)
                  ? "bg-accent/50 text-accent-foreground"
                  : "text-muted-foreground hover:bg-accent/30 hover:text-foreground",
              )}
            >
              <div className="flex items-center">
                <item.icon className="mr-3 h-5 w-5" />
                <span>{item.title}</span>
              </div>
              <div className="flex items-center">
                {item.badge && (
                  <Badge variant="outline" className="mr-2 bg-primary/10 text-primary dark:bg-primary/20">
                    {item.badge}
                  </Badge>
                )}
                {expandedMenus[item.key] ? <ChevronDown className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />}
              </div>
            </button>
          </CollapsibleTrigger>
          <CollapsibleContent className="space-y-1 pt-1">{renderSubmenuItems(item.items, item.key)}</CollapsibleContent>
        </Collapsible>
      </div>
    )
  }

  return (
    <Sidebar className="border-r border-border/40 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <SidebarHeader className="flex h-16 items-center border-b border-border/40 px-6">
        <Link href="/store/dashboard" className="flex items-center gap-3">
          <div className="flex h-9 w-9 items-center justify-center rounded-full bg-primary">
            <span className="text-sm font-bold text-primary-foreground">SZ</span>
          </div>
          <span
            className={cn("text-lg font-bold transition-opacity", state === "collapsed" ? "opacity-0" : "opacity-100")}
          >
            Store Dashboard
          </span>
        </Link>
        <div className="ml-auto flex items-center gap-2">
          <SidebarTrigger />
        </div>
      </SidebarHeader>
      <SidebarContent className="px-3 py-4">
        <SidebarMenu>
          {menuItems.map(renderMenuItem)}

          <Separator className="my-4 opacity-50" />

          <SidebarMenuItem className="mb-1">
            <Link
              href="/buyer/dashboard"
              className="flex w-full items-center rounded-md px-4 py-3 text-sm font-medium text-muted-foreground transition-colors hover:bg-accent/50 hover:text-foreground"
            >
              <Users className="mr-3 h-5 w-5" />
              <span>Kembali ke Buyer</span>
            </Link>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarContent>
    </Sidebar>
  )
}

<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fitur Pencarian ala sellzio</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        html, body {
            width: 100%;
            overflow-x: hidden;
            margin: 0;
            padding: 0;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Roboto', Arial, sans-serif;
        }

        body {
            background-color: #f2f2f2;
            color: #333;
        }
        
        /* PERBAIKAN: Tambahan style untuk desktop view - memastikan header responsif */
        .desktop-view .header {
            padding: 15px;
            display: flex;
            justify-content: center;
            align-items: center;
            overflow: hidden; /* Mencegah overflow content */
        }
        
        .desktop-view .search-container {
            max-width: 800px;
            margin: 0 auto;
            width: 100%;
        }
        
        .desktop-view .search-expanded {
            display: flex;
            justify-content: center;
        }
        
        .desktop-view .search-expanded .search-container {
            width: 100%;
            max-width: 800px;
        }

        /* Header dan Navigasi */
        .header {
            background-color: #ee4d2d;
            padding: 10px 15px;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            z-index: 1000;
            transition: all 0.3s ease;
            box-sizing: border-box; /* PERBAIKAN: Memastikan padding tidak menambah lebar total */
        }

        .search-container {
            display: flex;
            align-items: center;
            position: relative;
            max-width: 800px;
            margin: 0 auto;
            width: 100%;
            box-sizing: border-box; /* PERBAIKAN: Memastikan padding tidak menambah lebar */
        }

        .search-input {
            flex-grow: 1;
            padding: 10px 40px 10px 15px;
            border: 2px solid #ee4d2d;
            border-radius: 8px;
            font-size: 14px;
            outline: none;
            color: #333;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            position: relative;
            z-index: 10; /* Memastikan input dapat diklik */
        }
    .header .search-input {
    flex-grow: 1;
    padding: 10px 60px 10px 15px;
    border: 2px solid #ee4d2d;
    border-radius: 8px; /* Tetap pertahankan border radius */
    font-size: 14px;
    outline: none;
    color: #333;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    width: calc(100% - 30px); /* Potong bagian kanan input sebesar 30px */
    margin-right: 80px; /* Geser ke kanan untuk mempertahankan posisi ikon pencarian */
    position: relative;
    z-index: 10; /* Memastikan input dapat diklik */
}
    
    /* Styling untuk ikon keranjang dan chat */
.header-icons {
    display: flex;
    align-items: center;
    position: absolute;
    right: 8px; /* Jarak dari tepi kanan search-container */
    top: 50%;
	   padding-top: 5px;
    transform: translateY(-50%);
    z-index: 1; /* Pastikan berada di atas elemen lain */
}
  
.cart-icon, .chat-icon {
    position: relative;
    color: transparent;
    font-size: 22px;
    cursor: pointer;
}

/* Styling khusus untuk ikon keranjang dengan garis yang lebih tipis */
.cart-icon {
    -webkit-text-stroke: 1.3px white; /* Menipis kan garis untuk keranjang */
}

/* Styling khusus untuk ikon chat dengan garis yang tetap tebal */
.chat-icon {
    -webkit-text-stroke: 2px white; /* Tetap mempertahankan ketebalan garis chat */
    margin-left: 25px; /* Jarak ke kanan tetap sama */
}

/* Tambahkan jarak antara ikon keranjang dan chat */
.chat-icon {
    margin-left: 20px;
}

/* Badge notifikasi dengan background putih dan angka oranye */
.cart-badge, .chat-badge {
    position: absolute;
    top: -8px;
    right: -8px;
    background-color: white;
    color: #ee4d2d; /* Warna oranye sellzio */
    font-size: 11px;
    font-weight: bold;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1;
}

/* Styling untuk titik-titik di dalam ikon chat */
.chat-icon .chat-dots {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 60%;
    height: 30%;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.chat-icon .chat-dots .dot {
    width: 3px;
    height: 3px;
    background-color: white;
    border-radius: 50%;
}


@media (max-width: 480px) {
    .cart-icon, .chat-icon {
        font-size: 20px;
    }
    
    .cart-icon {
        -webkit-text-stroke: 0.8px white; /* Garis lebih tipis untuk keranjang pada layar kecil */
    }
    
    .chat-icon {
        -webkit-text-stroke: 1.2px white; /* Garis untuk chat pada layar kecil */
        margin-left: 20px;
    }
    
    .chat-icon {
        margin-left: 15px;
    }
    
    .cart-badge, .chat-badge {
        width: 16px;
        height: 16px;
        font-size: 10px;
        -webkit-text-stroke: 0.4px #ee4d2d; /* Garis lebih tipis pada layar kecil */
    }
    
    .chat-icon .chat-dots .dot {
        width: 2px;
        height: 2px;
    }
}

/* Pastikan container tidak overflow */
.header .search-container {
    display: flex;
    align-items: center;
    position: relative;
    max-width: 800px;
    margin: 0 auto;
    width: 100%;
    box-sizing: border-box;
    overflow: hidden; /* Mencegah overflow */
}
.header .search-icon {
    position: absolute;
    right: 95px; /* Sesuaikan dengan margin-right dari input + sedikit tambahan */
    color: #ee4d2d;
    cursor: pointer;
    font-size: 16px;
    z-index: 15; 
}
        /* Placeholder warna oranye */
        .search-input::placeholder {
            color: #ee4d2d;
            opacity: 1;
        }

        /* Placeholder animasi - PERBAIKAN: Animasi berfungsi di kedua tampilan dan tidak hilang pada interaksi lain */
        .search-placeholder {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #ee4d2d;
            pointer-events: none;
            font-size: 14px;
            transition: opacity 0.3s;
            display: flex !important; /* Memastikan selalu tampil kecuali saat focus/ada nilai */
            flex-direction: column;
            width: calc(100% - 60px);
            overflow: hidden;
            text-overflow: ellipsis;
             white-space: nowrap;
            height: 20px;
            z-index: 11; /* Memastikan placeholder tetap terlihat */
        }

        .placeholder-static {
            white-space: nowrap;
            position: absolute;
            top: 0;
            left: 0;
            opacity: 1;
            animation: staticPlaceholderAnimation 12s infinite;
        }

   .placeholder-dynamic {
    position: relative;
    width: 100%;
    overflow: hidden;
    height: 20px; /* Pastikan tinggi konsisten */
    max-width: calc(100% - 60px);
    line-height: 1.2; /* Tambahkan line-height untuk konsistensi */
}

/* Pastikan setiap teks memiliki posisi absolute yang sama dan tidak bertumpuk */
.placeholder-text {
    position: absolute;
    width: 100%;
    display: flex;
    align-items: center;
    opacity: 0;
    visibility: hidden;
    top: 0;
    left: 0;
    animation: placeholderAnimation 45s infinite; /* Durasi total 45 detik untuk 15 item */
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

        @keyframes staticPlaceholderAnimation {
    0%, 8% { /* Perpanjang waktu tampil (sebelumnya 0%, 4%) */
        opacity: 1;
    }
    12%, 96% { /* Sesuaikan transisi (sebelumnya 8%, 96%) */
        opacity: 0;
    }
    100% {
        opacity: 0;
    }
}

     @keyframes placeholderAnimation {
    0% {
        opacity: 0;
        transform: translateY(20px);
        visibility: hidden;
    }
    1%, 5% { /* Durasi tampil sekitar 4% dari total durasi */
        opacity: 1;
        transform: translateY(0);
        visibility: visible;
    }
    6%, 100% { /* Pastikan benar-benar hilang sebelum item berikutnya */
        opacity: 0;
        transform: translateY(-20px);
        visibility: hidden;
    }
}

        .placeholder-text:nth-child(2) {
            animation-delay: 3s;
        }

        .placeholder-text:nth-child(3) {
            animation-delay: 6s;
        }

        .placeholder-text:nth-child(4) {
            animation-delay: 9s;
        }

        /* Sembunyikan placeholder ketika input focus atau memiliki nilai */
        .search-input:focus + .search-placeholder,
        .search-input:not(:placeholder-shown) + .search-placeholder,
        .hide-placeholder + .search-placeholder {
            opacity: 0 !important;
        }

        /* PERBAIKAN: Tambahan class untuk menyembunyikan placeholder */
        .force-hide-placeholder {
            opacity: 0 !important;
        }

        .search-icon {
    position: absolute;
    right: 15px; /* Tetap di posisi kanan */
    color: #ee4d2d;
    cursor: pointer;
    font-size: 16px;
    z-index: 3; /* Pastikan masih di atas header-icons */
}

        /* PERBAIKAN: Style untuk ikon silang sesuai gaya sellzio - DIPERBESAR */
  .clear-search-icon {
            position: absolute;
            right: 50px; /* PERBAIKAN: Posisi lebih ke kiri */
            top: 50%;
            transform: translateY(-50%);
            width: 20px; /* PERBAIKAN: Ukuran icon tetap sama */
            height: 20px; /* PERBAIKAN: Ukuran icon tetap sama */
            background-color: #ccc;
            border-radius: 50%;
            cursor: pointer;
            display: none; /* Sembunyikan awalnya */
            z-index: 30; /* Pastikan di atas elemen lain */
            align-items: center;
            justify-content: center;
            padding: 0; /* PERBAIKAN: Hapus padding yang mungkin mengganggu klik */
            border: none; /* PERBAIKAN: Hapus border yang mungkin mengganggu klik */
            -webkit-tap-highlight-color: transparent; /* PERBAIKAN: Hapus highlight saat di-tap di mobile */
            touch-action: manipulation; /* PERBAIKAN: Perbaiki perilaku touch di mobile */
        }
        
        /* NEW: Tambahkan elemen pseudo untuk memperluas area klik tanpa mengubah ukuran visual */
        .clear-search-icon::before {
            content: '';
            position: absolute;
            width: 40px; /* Area klik yang diperbesar */
            height: 40px; /* Area klik yang diperbesar */
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }
        
        
        .clear-search-icon i {
            font-size: 12px; /* PERBAIKAN: Ukuran ikon diperbesar */
            color: white;
            line-height: 1;
            pointer-events: none; /* PERBAIKAN: Mencegah ikon sendiri menangkap event */
        }
        
        /* PERBAIKAN: Tambahkan efek hover untuk memberikan indikasi interaktif */
        .clear-search-icon:hover {
            background-color: #aaa;
        }
        
        /* PERBAIKAN: Tambahkan efek tekan untuk umpan balik visual */
        .clear-search-icon:active {
            background-color: #999;
            transform: translateY(-50%) scale(0.95);
        }
        
        /* Pada expanded search, geser posisinya sesuai dengan gaya sellzio */
        .search-expanded .clear-search-icon {
            right: 68px; /* PERBAIKAN: Posisi lebih ke kiri dari ikon pencarian di expanded mode */
        }
/* Icon pencarian di luar kolom pada expanded view */
        .expanded-search-icon {
            background-color: #ee4d2d;
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-left: 10px;
            cursor: pointer;
        }

        /* Overlay area */
        .overlay {
            position: fixed;
            top: 0;
            left: 0;
            bottom: 0px;
            width: 100%;
            height: 100%;
            background-color: white;
            z-index: 999;
            display: none;
            overflow-y: auto;
            box-sizing: border-box; /* PERBAIKAN: Memastikan padding tidak menambah lebar */
        }

        /* Search expanded mode */
       .search-expanded {
			background-color: white;
			padding: 10px 15px 15px 15px; /* Menambahkan padding bawah menjadi 15px */
			box-shadow: 0 2px 5px rgba(0,0,0,0.1);
			position: fixed;
			top: 0;
			left: 0;
			width: 100%;
			z-index: 1001;
			box-sizing: border-box;
		}

        .back-btn {
            margin-right: 10px;
            color: #ee4d2d;
            cursor: pointer;
            display: flex;
            align-items: center;
        }

        /* Icon panah dibuat lebih panjang */
        .back-btn i {
            font-size: 20px;
            transform: scaleX(1.3);
        }

        /* Suggestions */
        .suggestions-container {
            background-color: white;
            width: 100%;
            max-width: 800px;
            margin: 0 auto;
            margin-top: 10px; /* PERBAIKAN: Mengoptimalkan space untuk filter tabs */
            border-radius: 3px;
            overflow: hidden;
            display: none;
            transition: all 0.3s ease;
            box-sizing: border-box;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            padding: 0; /* PERBAIKAN: Menghilangkan padding untuk mencegah overflow */
            /* FIX: Memastikan tidak ada gap antara search dan suggestions */
            position: absolute;
            top: 0px;
            left: 0;
            right: 0;
            transform: translateY(10px); /* Perbaikan: Hilangkan transform negatif */
        }

        .text-suggestions {
            padding: 10px 0px 10px 0px;
            border-bottom: 1px solid #f2f2f2;
            display: block; /* PERBAIKAN BAGIAN 1: Tampilkan text suggestions */
        }

       
				
					
        
		  /* Styling yang konsisten untuk suggestion-icon */
			.suggestion-icon {
    margin-right: 12px; /* Sama dengan prediction-icon */
    width: 20px; /* Lebar tetap sama dengan prediction-icon */
    display: inline-flex;
    justify-content: center; /* Rata tengah ikon */
    color: #999; /* Warna default */
    font-size: 14px; /* Sesuaikan dengan ukuran ikon prediksi */
    flex-shrink: 0; /* Mencegah ikon dari mengecil */
    align-self: flex-start; /* Posisi ikon di bagian atas */
    margin-top: 1px; /* Sedikit ruang atas untuk posisi ikon */
}

/* Styling untuk batas antar item sugesti */
.suggestion-item {
    padding: 12px 15px; /* Sama dengan prediction-item */
    cursor: pointer;
    display: flex;
    align-items: flex-start; /* Ubah dari center ke flex-start */
    border-bottom: 1px solid #f5f5f5; /* Tambahkan garis batas */
}

/* Container teks untuk item sugesti */
.suggestion-text {
    display: inline-block;
    line-height: 1.4;
    word-break: break-word; /* Memungkinkan break kata jika terlalu panjang */
}

			.suggestion-item:last-child {
border-bottom: 1px solid #f5f5f5;
			}

			.suggestion-item:hover {
				background-color: #f9f9f9;
			}

			/* Styling khusus untuk ikon trend */
			.additional-suggestions .suggestion-icon {
			    font-size: 18px !important;
			    margin-bottom: 6px;
				color: #ee4d2d; /* Warna oranye untuk ikon trend */
			}

        /* PERBAIKAN: Style untuk tombol Lihat Lainnya agar dapat berpindah posisi dan tidak terpotong */
        .see-more-container {
            padding: 5px 0;
            text-align: center;
            transition: all 0.2s ease;
            position: relative; /* Menambahkan posisi relatif */
            z-index: 2; /* Meningkatkan z-index untuk memastikan tetap terlihat */
            background-color: white; /* Pastikan latar belakang putih */
            margin-bottom: 15px; /* PERBAIKAN: Tambahkan margin untuk memberikan jarak */
            display: block; /* PERBAIKAN BAGIAN 1: Tampilkan tombol lihat lainnya */
        }
        
        .see-more-btn {
            background-color: transparent;
            color: #ee4d2d;
            border: none;
            padding: 10px;
            font-size: 14px;
            cursor: pointer;
            border-radius: 4px;
            font-weight: 500;
            width: 100%;
            max-width: 300px;
            margin: 0 auto;
            text-align: center;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background-color 0.2s ease;
        }
        
        .see-more-btn:hover {
    background-color: transparent;
        }        
        
        .see-more-btn i {
            margin-right: 8px;
            color: #ee4d2d;
            font-size: 14px;
        }
  /* PERBAIKAN: Style untuk additional suggestions dengan animasi slide down */
        .additional-suggestions {
            max-height: none; /* PERBAIKAN BUG: Hapus batasan tinggi maksimal */
			overflow: visible; /* PERBAIKAN BUG: Ubah dari hidden ke visible */
            transition: max-height 0.3s ease;
            border-bottom: 0px solid #f2f2f2;
            background-color: white;
            position: relative;
            z-index: 1;
            margin-bottom: 15px; /* PERBAIKAN: Tambahkan margin untuk memberikan jarak */
            display: block; /* PERBAIKAN BAGIAN 1: Tampilkan additional suggestions */
        }
        
        .additional-suggestions.open {
            max-height: none;
            border-bottom: 1px solid #f2f2f2;
            padding-bottom: 20px; /* PERBAIKAN: Tambahkan padding bawah */
        }
        
        .additional-suggestions .suggestion-item {
            padding: 10px 15px;
            cursor: pointer;
            display: flex;
            align-items: center;
        }
      
        .additional-suggestions .suggestion-item:hover {
            background-color: #f9f9f;
        }
/* Product suggestions */
        .product-suggestions {
            padding: 10px;
            width: 100%;
            box-sizing: border-box;
            margin-top: 10px; /* PERBAIKAN: Tambahkan margin untuk memberikan jarak */
        }

        .product-title {
            font-size: 14px;
            color: #666;
            margin-bottom: 15px;
            padding: 0 5px;
            display: block; /* PERBAIKAN BAGIAN 1: Tampilkan product title */
        }

        /* PERBAIKAN: Grid produk standar */
        .product-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 10px;
            width: 100%;
            box-sizing: border-box;
            border: 2px solid #eee;
            border-radius: 5px;
            margin-top: 30px;
            padding: 10px 10px 25px 10px; /* JARAK DIOPTIMALKAN: Mengurangi padding atas agar tidak terlalu longgar */
            transform: translateY(0px); 
        }

        /* PERBAIKAN: Card produk standar (full layout) */
        .product-card {
            background-color: white;
            border-radius: 3px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
            cursor: pointer;
            transform: translateY(10px); 
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }

        .product-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        }

        /* PERBAIKAN: Card produk simpel (tampilan pencarian awal) */
        .simple-product-card {
            background-color: white;
            border-radius: 3px;
            transform: translateY(10px);
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
            text-align: center;
        }

        .simple-product-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        }

        .product-img {
            width: 100%;
            height: 150px;
            object-fit: cover;
            display: block;
        }

        .product-info {
            padding: 10px;
        }

        /* PERBAIKAN TAMPILAN CARD: Style untuk icon sellzio mall */
        .sellzio-mall-icon {
            display: inline-flex;
            align-items: center;
            background-color: #ee4d2d;
            color: white;
            font-size: 9px;
            font-weight: bold;
            padding: 1px 3px;
            border-radius: 2px;
            margin-right: 4px;
            vertical-align: middle;
        }

        /* PERBAIKAN 2: Nama produk standar dengan 2 baris */
        .product-name {
            font-size: 13px;
            color: #333;
            margin-bottom: 5px;
            height: 36px;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            line-height: 1.4;
        }

        /* PERBAIKAN TAMPILAN CARD: Rating dan terjual */
        .product-rating-sold {
            display: flex;
            align-items: center;
            font-size: 12px;
            color: #666;
            margin-bottom: 5px;
        }

        .product-rating {
            color: #ee4d2d;
            margin-right: 8px;
            display: flex;
            align-items: center;
        }

        .product-rating i {
            font-size: 11px;
            margin-right: 2px;
        }

        .product-sold {
            color: #666;
            position: relative;
            padding-left: 8px;
        }

        .product-sold:before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            height: 10px;
            width: 1px;
            background-color: #ccc;
        }

        /* PERBAIKAN TAMPILAN CARD: Pengiriman */
        .product-shipping {
            display: flex;
            align-items: center;
            font-size: 11px;
            color: #666;
            margin-bottom: 5px;
        }

        .product-shipping i {
            color: #00bfa5;
            margin-right: 4px;
            font-size: 12px;
        }

        /* PERBAIKAN: Nama produk untuk tampilan simpel (3 kata) */
        .simple-product-name {
            font-size: 13px;
            color: #333;
            padding: 10px 5px;
            text-align: center;
            height: auto;
            max-height: 40px;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
        }

        .product-price {
            font-size: 14px;
            color: #ee4d2d;
            font-weight: bold;
        }

        /* PERBAIKAN 3: Perbaikan container harga agar tidak terpotong */
        .product-price-container {
            display: flex;
            flex-wrap: wrap;
            align-items: center;
            margin-bottom: 5px;
        }

        .product-price-original {
            font-size: 12px;
            color: #999;
            text-decoration: line-through;
            margin-left: 5px;
        }

        .product-discount {
            font-size: 10px;
            color: #ee4d2d;
            background-color: #ffeee8;
            padding: 1px 4px;
            border-radius: 2px;
            margin-left: 5px;
        }

        /* PERBAIKAN 5: Warna COD menjadi oranye seperti sellzio */
        .cod-icon {
            display: inline-flex;
            position: absolute;
            bottom: 10px;
            right: 10px;
            background-color: #ee4d2d;
            color: white;
            font-size: 9px;
            font-weight: bold;
            padding: 2px 4px;
            border-radius: 2px;
            align-items: center;
        }

        .cod-icon i {
            font-size: 10px;
            margin-right: 2px;
        }
  /* Content area */
        .content-area {
            padding: 70px 15px 20px;
            max-width: 1200px;
            margin: 0 auto;
        }

        /* Keyword Suggestions Animation */
        .keyword-suggestions {
            margin: 20px auto;
            max-width: 800px;
            height: 40px;
            overflow: hidden;
            position: relative;
            display: none; /* PERBAIKAN: Sembunyikan keyword suggestions pada tampilan desktop */
        }

        .keyword-suggestions-wrapper {
            position: relative;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .keyword-static {
            color: #333;
            font-size: 14px;
            margin-right: 5px;
        }

        .keyword-dynamic {
            position: relative;
            width: 200px;
            height: 20px;
            overflow: hidden;
        }

        .keyword {
            position: absolute;
            width: 100%;
            color: #ee4d2d;
            font-size: 14px;
            display: flex;
            align-items: center;
            opacity: 0;
            animation: keywordAnimation 10s infinite;
            white-space: nowrap;
        }

        /* Animasi keyword agar loop tanpa kembali */
        @keyframes keywordAnimation {
            0% {
                opacity: 0;
                transform: translateY(20px);
            }
            5%, 20% {
                opacity: 1;
                transform: translateY(0);
            }
            25% {
                opacity: 0;
                transform: translateY(-20px);
            }
            100% {
                opacity: 0;
                transform: translateY(-20px);
            }
        }

        .keyword:nth-child(2) {
            animation-delay: 2.5s;
        }

        .keyword:nth-child(3) {
            animation-delay: 5s;
        }

        .keyword:nth-child(4) {
            animation-delay: 7.5s;
        }
/* Styling untuk product header - PERBAIKAN: Membuat mobile-friendly untuk nama produk */
        .product-header {
            display: none;
            background-color: white;
            padding: 16px;
            border-bottom: 1px solid #f2f2f2;
            margin-bottom: 5px;
        }

        .product-header .product-info {
            display: flex;
            align-items: center;
        }

        .product-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            overflow: hidden;
            margin-right: 15px;
            border: 1px solid #eee;
            flex-shrink: 0; /* PERBAIKAN: Mencegah avatar mengecil */
        }

        .product-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .product-details {
            flex: 1;
            cursor: pointer;
            min-width: 0; /* PERBAIKAN: Memastikan flex item bisa menyusut di bawah min-content width */
            overflow: hidden; /* PERBAIKAN: Mencegah overflow */
        }

        .product-name {
            font-size: 16px;
            font-weight: 500;
            margin-bottom: 2px;
            white-space: nowrap; /* PERBAIKAN: Memastikan nama produk dalam satu baris */
            overflow: hidden; /* PERBAIKAN: Menyembunyikan teks yang overflow */
            text-overflow: ellipsis; /* PERBAIKAN: Menambahkan elipsis (…) */
        }

        .product-category {
            font-size: 12px;
            color: #666;
            margin-bottom: 5px;
            white-space: nowrap; /* PERBAIKAN: Mencegah wrap */
            overflow: hidden; /* PERBAIKAN: Menyembunyikan overflow */
            text-overflow: ellipsis; /* PERBAIKAN: Menambahkan elipsis */
        }

        .product-stats {
            font-size: 12px;
            color: #666;
            white-space: nowrap; /* PERBAIKAN: Mencegah wrap */
            overflow: hidden; /* PERBAIKAN: Menyembunyikan overflow */
        }

        .product-stats span {
            margin-right: 10px;
        }

        .product-action {
            color: #ee4d2d;
            font-size: 14px;
            display: flex;
            align-items: center;
            cursor: pointer;
            margin-left: 10px; /* PERBAIKAN: Tambahkan margin untuk menjaga jarak */
            flex-shrink: 0; /* PERBAIKAN: Mencegah tombol menyusut */
        }

        .product-action i {
            font-size: 16px;
            margin-left: 5px;
        }

        /* PERBAIKAN: Style untuk tab filter agar tampil dengan benar */
				.filter-tabs {
			display: none;
			background-color: white;
			width: 100%;
			border-bottom: 1px solid #f2f2f2;
			overflow-x: auto;
			white-space: nowrap;
			padding: 0;
			margin: 0;
			margin-top: 5px; /* Tambahkan margin atas untuk jarak dari search bar */
			margin-bottom: 8px; /* Tingkatkan margin bawah tab filter */
			-webkit-overflow-scrolling: touch;
			position: fixed;
			top: 60px; /* Posisi tetap di bawah search bar */
			left: 0;
			right: 0;
			z-index: 1002;
			height: 45px; /* JARAK DIOPTIMALKAN: Tetapkan tinggi untuk tab filter */
		}
        
        .filter-tabs-inner {
            display: inline-flex;
            padding: 0 10px;
            padding-left: 25px; 
        }
        
        .filter-tab {
            padding: 12px 15px;
            font-size: 14px;
            color: #666;
            cursor: pointer;
            position: relative;
            transition: color 0.2s ease;
        }
        
        .filter-tab.active {
            color: #ee4d2d;
            font-weight: 500;
        }
        
        .filter-tab.active:after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 15px;
            right: 15px;
            height: 2px;
            background-color: #ee4d2d;
        }
        
        .filter-tab-price {
            display: flex;
            align-items: center;
        }
        
        .filter-tab-price i {
            margin-left: 5px;
            font-size: 12px;
        }
        
        /*  Atur Responsive styles - PERBAIKAN UNTUK DESKTOP */
		
		@media (max-width: 480px) {
			.cart-icon, .chat-icon {
				font-size: 20px;
			}
			
			
			.chat-icon {
				-webkit-text-stroke: 0.8px white; /* Garis untuk chat pada layar kecil */
				margin-left: 15px;
			}
			
			
			.cart-badge, .chat-badge {
				width: 16px;
				height: 16px;
				font-size: 10px;
				-webkit-text-stroke: 0.4px #ee4d2d; /* Garis lebih tipis pada layar kecil */
			}
			
			.chat-icon .chat-dots .dot {
				width: 2px;
				height: 2px;
			}
		}
		
		@media (max-width: 576px) {
            .not-found-container {
                padding: 20px 15px 30px;
                margin: 10px 0;
            }
            
            .not-found-button-container {
                max-width: 100%;
            }
            
            .not-found-message {
                max-width: 95%;
            }
            
            .keyword-suggestions-popup {
                max-width: 100%;
                border-radius: 12px 12px 0 0;
            }
            
            .product-grid, .products-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 8px;
                padding-top: 5px; /* JARAK DIOPTIMALKAN: Mengurangi padding di mobile juga */
            }
            
            .product-card, .simple-product-card {
                box-shadow: 0 1px 5px rgba(0,0,0,0.1);
            }
            
            .product-img {
                height: 130px;
            }
            
            .not-found-icon {
                width: 100px;
                height: 100px;
            }
            
            .not-found-button {
                padding: 10px 12px;
                font-size: 13px;
            }
            
            /* PERBAIKAN: CSS untuk product header pada mobile */
            .product-header {
                padding: 12px;
            }
            
            .product-avatar {
                width: 40px;
                height: 40px;
                margin-right: 10px;
            }
            
            .product-action {
                font-size: 12px;
            }
            
            .product-action i {
                font-size: 14px;
            }
            
            /* PERBAIKAN: Memastikan keyword suggestions tampil di mobile */
            .keyword-suggestions {
                display: block;
            }
            
            /* FIX: Memastikan suggestions-container tetap posisi absolute dan di bawah search bar */
            .suggestions-container {
                position: absolute;
                top: 60px;
                margin-top: 0;
            }
        }
  @media (max-width: 767px) {
    .product-grid {
        width: 100vw;
        position: relative;
        left: 50%;
        right: 50%;
        margin-left: -50vw;
        margin-right: -50vw;
        border-left: none;
        border-right: none;
        border-radius: 0;
        padding-left: 10px;
        padding-right: 10px;
    }
      .header {
        padding-top: 25px; /* Tambahkan padding atas lebih besar */
    }
      .header .header-icons {
        top: 50%; /* Tetapkan ke tengah vertikal parent */
        transform: translateY(-50%); /* Geser ke atas setengah dari tingginya sendiri */
        padding-top: 3px; /* Hapus padding atas yang mungkin mempengaruhi posisi */
    }
}
		
        @media (min-width: 768px) {
		
		  .cart-icon, .chat-icon {
				font-size: 20px;
			}
			.cart-icon {
				-webkit-text-stroke: 0.8px white; /* Menipis kan garis untuk keranjang */
			}
			
			
			.chat-icon {
				-webkit-text-stroke: 0.8px white; /* Garis untuk chat pada layar kecil */
				margin-left: 15px;
			}
			
			
			.cart-badge, .chat-badge {
				width: 16px;
				height: 16px;
				font-size: 10px;
				-webkit-text-stroke: 0.4px #ee4d2d; /* Garis lebih tipis pada layar kecil */
			}
			
			.chat-icon .chat-dots .dot {
				width: 2px;
				height: 2px;
			}
		
          .filter-tabs {
            display: flex;
            justify-content: center;
            padding: 0;
            width: 100%;
          }

          .filter-tabs-inner {
            max-width: 800px;
            width: 100%;
            display: flex;
            justify-content: space-between;
            padding: 0;
            margin: 0 auto; /* Tambahkan margin auto untuk memastikan posisi tengah */
          }

          .filter-tab {
            flex: 1;
            text-align: center;
            white-space: nowrap;
            padding: 12px 20px;
          }
          
          .filter-tab.filter-tab-price {
            display: flex;
            justify-content: center;
            align-items: center;
          }

          .filter-tab-price i {
            margin-left: 5px;
            margin-right: -5px; /* Kompensasi margin agar teks lebih terpusat */
          }
          
          .product-grid {
            grid-template-columns: repeat(3, 1fr);
            width: 100%;
            box-sizing: border-box;
          }
            
          .search-container {
            max-width: 800px;
            margin: 0 auto;
            width: 100%;
            box-sizing: border-box;
          }
            
          .header, .search-expanded {
            padding: 15px;
            width: 100%;
            box-sizing: border-box;
          }
            
			.suggestions-container {
				background-color: white;
				width: 100%;
				max-width: 800px;
				margin: 0 auto;
				margin-top: 10px; /* Tambahkan margin-top */
				border-radius: 3px;
				overflow: hidden;
				display: none;
				transition: all 0.3s ease;
				box-sizing: border-box;
				box-shadow: 0 2px 8px rgba(0,0,0,0.1);
				padding: 0; /* PERBAIKAN: Menghilangkan padding untuk mencegah overflow */
				/* FIX: Memastikan tidak ada gap antara search dan suggestions */
				position: absolute;
				top: 0px;
				left: 0;
				right: 0;
				transform: translateY(10px); /* Perbaiki: Hapus transform negatif */
			}
						
          .content-area {
            max-width: 1200px;
            margin: 0 auto;
            padding: 80px 20px 30px;
            width: 100%;
            box-sizing: border-box;
          }
		.product-card, .simple-product-card {
            transition: transform 0.3s ease, box-shadow 0.3s ease;
          }
            
          .product-img {
            height: 180px;
          }
            
          .product-name, .simple-product-name {
            font-size: 14px;
          }
            
          .product-suggestions, .text-suggestions, .additional-suggestions {
            width: 100%;
            box-sizing: border-box;
          }
            
          /* PERBAIKAN: Tampilkan keyword suggestions pada tampilan mobile saja */
          .keyword-suggestions {
            display: block;
          }
		  .keyword-suggestions-popup {
                max-width: 800px;
            }
        }
		
        
		
		@media (min-width: 1024px) {
            .product-grid {
                grid-template-columns: repeat(4, 1fr);
                gap: 15px;
            }
            
            .search-container {
                max-width: 800px;
            }
            
            .suggestions-container {
                max-width: 800px;
                /* FIX: Memastikan container tetap posisi absolut di tampilan lebar */
                position: absolute;
                top: 60px;
            }
            
            .product-img {
                height: 200px;
            }
        }
        
       
        
  /* Tambahan styles untuk suggestions-container */
        /* FIX: Gaya untuk menghilangkan gap antara search dan suggestions */
        .search-expanded + .suggestions-container {
            margin-top: 0 !important;
            top: 60px !important;
        }
        
        /* JARAK DIOPTIMALKAN: Menambahkan margin lebih kecil di bawah filter tabs */
        .filter-tabs + .suggestions-container {
            margin-top: 8px; /* Jarak yang lebih minimal namun masih mencegah terpotong */
        }
        
        /* JARAK DIOPTIMALKAN: Atur ulang posisi dan margin saat filter tabs tampil */
        .search-expanded + .filter-tabs + .suggestions-container {
    margin-top: 15px !important; /* Tingkatkan margin atas */
    top: 105px !important; /* JARAK DIOPTIMALKAN: Posisi 60px (search bar) + 45px (filter tabs height) */
    padding-top: 5px; /* JARAK DIOPTIMALKAN: Mengurangi padding atas pada suggestions container */
}

        /* Tampilan Hasil Tidak Ditemukan yang diperbarui */
        .not-found-container {
            display: none;
            text-align: center;
            padding: 30px 20px 40px;
            margin: 0;
            background-color: #fff;
            width: 100%;
            position: relative;
        }

        .not-found-icon {
            margin: 0 auto 20px;
            width: 120px;
            height: 120px;
            opacity: 0.5;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .search-document-icon {
            position: relative;
            width: 100%;
            height: 100%;
        }

        .document-base {
            position: absolute;
            width: 70px;
            height: 85px;
            background-color: #ddd;
            border-radius: 5px;
            top: 20px;
            left: 25px;
        }

        .document-fold {
            position: absolute;
            width: 20px;
            height: 20px;
            background-color: #eee;
            top: 20px;
            right: 25px;
            clip-path: polygon(0 0, 100% 100%, 100% 0);
        }

        .document-lines {
            position: absolute;
            width: 40px;
            height: 4px;
            background-color: #ccc;
            top: 45px;
            left: 40px;
        }

        .document-lines:before {
            content: '';
            position: absolute;
            width: 30px;
            height: 4px;
            background-color: #ccc;
            top: 12px;
            left: 0;
        }

        .document-lines:after {
            content: '';
            position: absolute;
            width: 20px;
            height: 4px;
            background-color: #ccc;
            top: 24px;
            left: 0;
        }

        .magnifying-glass {
            position: absolute;
            width: 40px;
            height: 40px;
            border: 6px solid #ccc;
            border-radius: 50%;
            top: 10px;
            right: 10px;
        }
        
        .magnifying-glass:after {
            content: '';
            position: absolute;
            width: 6px;
            height: 20px;
            background-color: #ccc;
            transform: rotate(-45deg);
            bottom: -15px;
            right: -5px;
            border-radius: 10px;
        }

        .not-found-title {
            font-size: 18px;
            font-weight: 500;
            color: #333;
            margin-bottom: 10px;
        }

        .not-found-message {
            font-size: 14px;
            color: #888;
            margin-bottom: 25px;
        }

        .not-found-button-container {
            display: flex;
            flex-direction: column;
            gap: 12px;
            max-width: 400px;
            margin: 0 auto;
        }

        .not-found-button {
            border-radius: 4px;
            padding: 12px 16px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            border: none;
            transition: all 0.2s ease;
        }

        .not-found-button.primary {
            background-color: #ee4d2d;
            color: white;
        }

        .not-found-button.primary:hover {
            background-color: #e04224;
        }

        .not-found-button.secondary {
            background-color: white;
            color: #ee4d2d;
            border: 1px solid #ee4d2d;
        }

        .not-found-button.secondary:hover {
            background-color: rgba(238, 77, 45, 0.05);
        }
  /* PERBAIKAN: Ganti ke keyword tag sugesti lagi (bukan popup produk) */
        .keyword-suggestions-popup {
            display: none;
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background-color: white;
            padding: 20px;
            border-top-left-radius: 16px;
            border-top-right-radius: 16px;
            box-shadow: 0 -4px 10px rgba(0,0,0,0.1);
            z-index: 1000;
            max-height: 70vh;
            overflow-y: auto;
            animation: slideUp 0.3s ease;
            max-width: 800px;
            margin: 0 auto;
        }

        @keyframes slideUp {
            from {
                transform: translateY(100%);
            }
            to {
                transform: translateY(0);
            }
        }

        .keyword-suggestions-title {
            font-size: 16px;
            font-weight: 500;
            color: #333;
            margin-bottom: 15px;
            text-align: center;
        }

        /* Keyword suggestion tags */
        .keyword-suggestions-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            width: 100%;
        }

        .keyword-suggestion-tag {
            background-color: #f5f5f5;
            border-radius: 20px;
            padding: 10px 15px;
            font-size: 13px;
            color: #333;
            text-align: center;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .keyword-suggestion-tag:hover {
            background-color: #ffe8e3;
            color: #ee4d2d;
        }

        /* Products Container */
        .products-container {
            display: none;
            padding: 15px;
            background-color: #fff;
            width: 100%;
        }

        .products-title {
            font-size: 14px;
            color: #666;
            margin-bottom: 15px;
            padding: 0 5px;
        }

        .products-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 10px;
            width: 100%;
            box-sizing: border-box;
        }

        /* Overlay untuk popup */
        .suggestions-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0,0,0,0.5);
            z-index: 999;
        }

        /* Helper classes */
        .hide {
            display: none !important;
        }

        .show {
            display: block !important;
        }

        /* Show suggestions */
        .show-suggestions .keyword-suggestions-popup,
        .show-suggestions .suggestions-overlay {
            display: block;
        }

       
        /* Tambahan: Pastikan keyword suggestions tidak terlihat di tampilan pencarian expanded */
        .search-expanded ~ .keyword-suggestions {
            display: none !important;
        }
        
        /* Perbaikan bug: Paksa sembunyikan keyword suggestions di berbagai kondisi */
        .overlay[style*="display: block"] ~ .keyword-suggestions,
        #searchOverlay[style*="display: block"] ~ .keyword-suggestions,
        .search-expanded ~ .keyword-suggestions,
        .keyword-suggestions {
            display: none !important;
            visibility: hidden !important;
            opacity: 0 !important;
            pointer-events: none !important;
            max-height: 0 !important;
            overflow: hidden !important;
        }

        /* Fokus spesifik pada keyword_suggestions */
        #keywordSuggestions {
            display: none !important;
        }
        
        /* Perbaikan khusus untuk placeholder oranye di tampilan 2 */
        .search-expanded .search-placeholder,
        .search-expanded .search-input:not(:focus) + .search-placeholder {
            display: none !important;
            opacity: 0 !important;
            visibility: hidden !important;
            pointer-events: none !important;
        }

        /* SOLUSI: Style untuk menyembunyikan filter tabs secara paksa saat peringatan ditampilkan */
        .not-found-mode .filter-tabs,
        .other-products-mode .filter-tabs {
            display: none !important;
            visibility: hidden !important;
            opacity: 0 !important;
            height: 0 !important;
            overflow: hidden !important;
            position: absolute !important;
            top: -9999px !important;
            left: -9999px !important;
            pointer-events: none !important;
            z-index: -1 !important;
            max-height: 0 !important;
            max-width: 0 !important;
            margin: 0 !important;
            padding: 0 !important;
            border: none !important;
            clip: rect(0, 0, 0, 0) !important;
            clip-path: inset(100%) !important;
        }
  /* Styling untuk "Hapus riwayat pencarian" */
.clear-history {
    text-align: center;
    padding: 10px;
    margin-bottom: 5px;
    cursor: pointer;
    color: #999;
    font-size: 13px;
    border-bottom: 1px solid #f2f2f2;
}

.clear-history:hover {
    color: #ee4d2d;
}

/* Styling untuk "Sedang Trend" */
.trending-title {
   margin-top: 15px;
    color: #333;
    font-weight: 500;
    padding: 12px 15px 5px;
    font-size: 13px;
    display: flex;
    align-items: center;
}

.trending-title .trend-icon {
    margin-left: 5px;
    font-size: 13px;
    color: #ee4d2d;
}
  
  /* Tampilan Pill dengan Badge untuk Sedang Trend */
.trend-pill {
    display: inline-flex;
    align-items: center;
    background-color: #fff4f2;
    color: #ee4d2d;
    font-size: 16px;
    font-weight: 500;
    padding: 6px 15px; /* Padding diperbesar sedikit */
    border-radius: 15px;
    margin: 20px 0 20px 15px;
}

.trend-pill-badge {
    background-color: #ee4d2d;
    color: white;
    font-size: 10px;
    border-radius: 50%;
    width: 18px;
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 8px;
}

/* Styling untuk saran koreksi */
.correction-compact {
  background-color: white;
  border: 1px solid #eee;
  box-shadow: 0 2px 10px rgba(0,0,0,0.05);
  position: relative;
  border-radius: 8px;
  padding: 12px 15px;
  display: flex;
  align-items: flex-start;
  width: 100%;
  max-width: 800px;
  margin: 5px auto 50px auto;
  cursor: pointer;
  transition: all 0.2s ease;
  overflow: hidden;
  transform: translateY(30px);
}

/* Garis aksen di sebelah kiri */
.correction-compact:before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background-color: #ee4d2d;
  border-radius: 8px 0 0 8px;
  z-index: 5;
}

/* Container pesan */
.compact-message {
  display: flex;
  align-items: flex-start;
  font-size: 13px;
  color: #666;
  position: relative;
  width: 100%;
}

/* Ikon pencarian */
.compact-message i {
  color: #ee4d2d;
  margin-right: 8px;
  font-size: 14px;
  margin-top: 2px;
}

/* Container untuk teks pesan */
.message-text {
  display: flex;
  flex-wrap: wrap;
  align-items: baseline;
}

/* Label "Mungkin ini:" */
.message-label {
  margin-right: 4px;
  white-space: nowrap;
}

/* Highlight keyword */
.correction-keyword {
  color: #ee4d2d;
  font-weight: bold;
  word-break: break-word;
  line-height: 1.4;
}

/* Container content */
.correction-content {
  flex: 1;
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  position: relative;
  width: 100%;
  transition: filter 0.3s ease;
}

/* Tombol pusat container */
.center-buttons {
  display: none;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  justify-content: center;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.5);
  border-radius: 8px;
  z-index: 20;
}

.center-buttons-inner {
  display: flex;
  gap: 15px;
  position: relative;
  z-index: 25;
}

/* Tombol pusat dengan hover effect */
.btn-center {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 8px rgba(0,0,0,0.2);
  position: relative;
  z-index: 30;
}

.btn-center i {
  font-size: 18px;
}

.btn-center.yes {
  background-color: #ee4d2d;
  color: white;
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(238, 77, 45, 0.6);
  }
  70% {
    box-shadow: 0 0 0 8px rgba(238, 77, 45, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(238, 77, 45, 0);
  }
}

.btn-center.yes:hover {
  background-color: #d73f21;
  transform: scale(1.05);
}

.btn-center.no {
  background-color: #f5f5f5;
  color: #666;
  border: 1px solid #eee;
}

.btn-center.no:hover {
  background-color: #eeeeee;
  transform: scale(1.05);
}

/* Overlay untuk meningkatkan efek blur */
.blur-overlay {
  display: none;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.2);
  z-index: 15;
  pointer-events: none;
}

/* State aktif */
.correction-compact.active {
  box-shadow: 0 0 0 2px rgba(238, 77, 45, 0.5);
}

.correction-compact.active .center-buttons {
  display: flex;
}

.correction-compact.active .blur-overlay {
  display: block;
}

.correction-compact.active .correction-content {
  filter: blur(3px);
}

/* Styling untuk prediksi pencarian */
.search-predictions {
    position: absolute;
    top: 60px;
    left: 0;
    right: 0;
    background-color: white;
    border-top: 1px solid #eee;
    z-index: 1002;
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}



/* Styling untuk prediksi keyword */
				.keyword-predictions {
			position: absolute;
			top: 60px;
			left: 0;
			right: 0;
			background-color: white;
			border-radius: 0 0 8px 8px;
			box-shadow: 0 2px 5px rgba(0,0,0,0.1);
			
			display: none;
			padding: 15px 0; /* Tambahkan padding atas dan bawah */
			max-width: 800px;
			margin: 10px auto;
		    transform: translateY(10px);
		}

.keyword-predictions::-webkit-scrollbar {
    width: 6px;
}

.keyword-predictions::-webkit-scrollbar-thumb {
    background-color: #ddd;
    border-radius: 3px;
}

.keyword-predictions::-webkit-scrollbar-track {
    background-color: #f5f5f5;
}

    /* Styling untuk prediksi keyword */
.prediction-icon {
    margin-right: 12px; /* Tambahkan jarak antara ikon dan teks */
    width: 20px; /* Tetapkan lebar tetap untuk ikon */
    display: inline-flex;
    justify-content: center; /* Rata tengah ikon */
    color: #999; /* Warna default untuk ikon */
    flex-shrink: 0; /* Mencegah ikon dari mengecil */
    align-self: flex-start; /* Posisi ikon di bagian atas */
    margin-top: 2px; /* Sedikit ruang atas untuk posisi ikon */
}

/* Untuk ikon yang cocok, gunakan warna oranye */
.prediction-icon.matched {
    color: #ee4d2d;
}
    
.prediction-text {
    font-size: 14px;
    color: #333;
    line-height: 1.4;
    word-break: break-word; /* Memungkinkan break kata jika terlalu panjang */
}

.prediction-text .highlighted {
    color: #ee4d2d;
    font-weight: bold;
}

.prediction-item {
    padding: 10px 15px;
    cursor: pointer;
    border-bottom: 1px solid #f5f5f5;
    display: flex; /* Tambahkan display flex */
    align-items: flex-start; /* Align item di bagian atas */
}

.prediction-item:hover {
    background-color: #f9f9f9;
}
    
@media (min-width: 768px) {
    .keyword-predictions {
        top: 60px;
        border-radius: 8px;
        margin-top: 30px;
    }
}
  
  .extended-suggestions {
    padding: 0;
    display: none;
    transition: all 0.3s ease;
}

.extended-suggestions .suggestion-item {
    padding: 12px 15px;
    cursor: pointer;
    display: flex;
    align-items: center;
    border-bottom: 1px solid #f5f5f5;
}

.extended-suggestions .suggestion-item:last-child {
    border-bottom: 2px solid #f5f5f5;
}

.extended-suggestions .suggestion-item:hover {
    background-color: #f9f9f9;
}

.extended-suggestions.open {
    display: block;
}

/* Styling untuk item sugesti bentuk tombol */
.keyword-button-container {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    padding: 10px 15px;
}

.keyword-button {
    display: inline-flex;
    align-items: flex-start; /* Ubah dari center ke flex-start */
    background-color: white;
    border: 1px solid #e0e0e0;
    border-radius: 16px;
    padding: 8px 15px;
    cursor: pointer;
    font-size: 13px;
    color: #333;
    transition: all 0.2s ease;
    white-space: normal; /* Ubah dari nowrap ke normal untuk mengizinkan wrap */
    max-width: 100%; /* Batasi lebar maksimum */
}

.keyword-button:hover {
    border-color: #ee4d2d;
    color: #ee4d2d;
    background-color: #fff9f8;
}

.keyword-button .suggestion-icon {
    margin-right: 8px;
    color: #999;
    flex-shrink: 0; /* Mencegah ikon dari mengecil */
    margin-top: 2px; /* Sedikit ruang atas untuk posisi ikon */
}

/* Tambahan untuk wrapper konten keyword */
.keyword-button-text {
    display: inline-block;
    text-align: left;
    line-height: 1.4;
    word-break: break-word; /* Memungkinkan break kata jika terlalu panjang */
}
  
    
    /* Ukuran font untuk semua teks suggestion */
.suggestion-text,
.suggestion-item span,
.extended-suggestions .suggestion-item span,
.additional-suggestions .suggestion-item span {
    font-size: 14px;
}

/* Delay untuk 15 item dengan durasi animasi 45 detik (3 detik per item) */
.placeholder-text:nth-child(1) { animation-delay: 0s; }
.placeholder-text:nth-child(2) { animation-delay: 3s; }
.placeholder-text:nth-child(3) { animation-delay: 6s; }
.placeholder-text:nth-child(4) { animation-delay: 9s; }
.placeholder-text:nth-child(5) { animation-delay: 12s; }
.placeholder-text:nth-child(6) { animation-delay: 15s; }
.placeholder-text:nth-child(7) { animation-delay: 18s; }
.placeholder-text:nth-child(8) { animation-delay: 21s; }
.placeholder-text:nth-child(9) { animation-delay: 24s; }
.placeholder-text:nth-child(10) { animation-delay: 27s; }
.placeholder-text:nth-child(11) { animation-delay: 30s; }
.placeholder-text:nth-child(12) { animation-delay: 33s; }
.placeholder-text:nth-child(13) { animation-delay: 36s; }
.placeholder-text:nth-child(14) { animation-delay: 39s; }
.placeholder-text:nth-child(15) { animation-delay: 42s; }


/* Tambahkan CSS untuk faceted search */
.facet-container {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    margin-bottom: 15px;
    padding: 15px;
}

.facet-section {
    margin-bottom: 15px;
}

.facet-section h3 {
    font-size: 16px;
    color: #333;
    margin-bottom: 8px;
    font-weight: 500;
}

.facet-section ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.facet-section li {
    padding: 6px 0;
    display: flex;
    align-items: center;
}

.facet-section input[type="checkbox"] {
    margin-right: 8px;
}

.facet-section label {
    font-size: 14px;
    color: #666;
    cursor: pointer;
}

.facet-section label:hover {
    color: #ee4d2d;
}

.facet-section input[type="checkbox"]:checked + label {
    color: #ee4d2d;
    font-weight: 500;
}

@media (min-width: 768px) {
    .search-results-layout {
        display: flex;
        gap: 20px;
    }
    
    .facet-container {
        width: 250px;
        flex-shrink: 0;
    }
    
    .results-container {
        flex-grow: 1;
    }
}

.active-filters {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 15px;
}

.filter-tag {
    background-color: #f5f5f5;
    border-radius: 16px;
    padding: 5px 10px;
    font-size: 12px;
    color: #333;
    display: flex;
    align-items: center;
}

.filter-tag i {
    margin-left: 5px;
    color: #999;
    cursor: pointer;
}

.filter-tag i:hover {
    color: #ee4d2d;
}

/* Styling untuk icon filter */
.filter-icon {
    background-color: #ee4d2d;
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 10px;
    cursor: pointer;
    display: none; /* Sembunyikan secara default */
    position: relative;
}

.filter-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background-color: #fff;
    color: #ee4d2d;
    border: 1px solid #ee4d2d;
    font-size: 10px;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
}

/* Facet overlay untuk mobile */
.facet-overlay {
    position: fixed;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-color: rgba(0,0,0,0.5);
    z-index: 1050;
    display: none;
}

/* Panel facet untuk mobile/tablet */
.facet-panel {
    background-color: white;
    width: 100%;
    max-width: 500px;
    max-height: 80vh;
    border-radius: 10px 10px 0 0;
    overflow-y: auto;
    padding: 20px;
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    margin: 0 auto;
    animation: slideUp 0.3s ease;
}

/* Panel facet untuk desktop */
.facet-panel-desktop {
    position: fixed;
    right: 0;
    top: 0;
    height: 100%;
    width: 350px;
    background-color: white;
    box-shadow: -2px 0 10px rgba(0,0,0,0.1);
    z-index: 1050;
    padding: 20px;
    overflow-y: auto;
    display: none;
    animation: slideIn 0.3s ease;
}

@keyframes slideUp {
    from { transform: translateY(100%); }
    to { transform: translateY(0); }
}

@keyframes slideIn {
    from { transform: translateX(100%); }
    to { transform: translateX(0); }
}

.facet-header {
    position: sticky;
    top: 0;
    display: flex;
	background-color: white;
    z-index: 10;
    padding: 15px;
    align-items: center;
    justify-content: space-between;
    padding-bottom: 10px;
    border-bottom: 1px solid #f2f2f2;
    margin-bottom: 15px;
}

.facet-title {
    font-size: 16px;
    font-weight: 500;
}

.facet-close {
    color: #666;
    cursor: pointer;
    font-size: 18px;
}

.facet-buttons {
    display: flex;
	position: sticky;
    bottom: 0;
    background-color: white;
    z-index: 10;
    padding: 15px;
    gap: 10px;
    margin-top: 20px;
    padding-top: 0px;
    border-top: 1px solid #f2f2f2;
}

/* Tambahkan ruang kosong setelah konten facet terakhir */
.facet-section:last-child {
    margin-bottom: 30px; /* Tambahkan margin bawah pada section terakhir */
}

/* Mencegah scroll pada body saat facet panel aktif */
body.facet-active {
    overflow: hidden;
}

/* Untuk memastikan konten facet dapat di-scroll dengan baik */
.facet-content-wrapper {
    max-height: calc(80vh - 120px); /* Header dan footer masing-masing 60px */
    overflow-y: auto;
	 -webkit-overflow-scrolling: touch; /* Untuk scroll yang lebih smooth di iOS */
    overscroll-behavior: contain; /* Mencegah scroll chain effect */
    padding: 0 15px;
}


/* Pastikan panel facet memiliki padding yang tepat */
.facet-panel, .facet-panel-desktop {
    overscroll-behavior: contain;
    padding: 0; /* Hapus padding default */
}

.facet-button {
    flex: 1;
    padding: 12px;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 500;
    text-align: center;
    cursor: pointer;
}

.facet-button-reset {
    background-color: #f5f5f5;
    color: #666;
    border: 1px solid #e0e0e0;
}

.facet-button-apply {
    background-color: #ee4d2d;
    color: white;
    border: none;
}
    
    /* Styling untuk checkbox oranye */
.orange-checkbox {
    position: relative;
    cursor: pointer;
    appearance: none;
    -webkit-appearance: none;
    width: 18px;
    height: 18px;
    border: 1px solid #ccc;
    border-radius: 3px;
    outline: none;
    transition: all 0.2s ease;
    vertical-align: middle;
    margin-right: 8px;
}

.orange-checkbox:checked {
    background-color: #ee4d2d;
    border-color: #ee4d2d;
}

.orange-checkbox:checked::after {
    content: '';
    position: absolute;
    left: 5px;
    top: 3px;
    width: 5px;
    height: 9px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
}

/* Styling untuk label saat checkbox di-check */
.orange-checkbox:checked + label {
    color: #ee4d2d;
    font-weight: 500;
}

/* Menjadi: */
@media (min-width: 1025px) { /* 1025px untuk memastikan tablet termasuk mobile */
    .facet-panel-desktop {
        display: block;
    }
    .facet-overlay {
        display: none !important;
    }
}

    
/* Tambahkan media query untuk tablet */
@media (min-width: 768px) and (max-width: 1024px) {
    .facet-panel {
        max-width: 100%; /* Lebar maksimal untuk tablet */
        width: 800px;   /* Lebar tetap yang lebih lebar */
    }
}

    </style>
	<script src="https://cdn.jsdelivr.net/npm/fuse.js@6.6.2"></script>
</head>
<body>
<!-- Header normal dengan search bar -->
    
<header class="header" id="mainHeader">
    <div class="search-container">
        <!-- PERBAIKAN: Tidak menampilkan ikon silang di tampilan pertama -->
        <input type="text" class="search-input" id="searchInput" placeholder=" ">
        <div class="search-placeholder" id="searchPlaceholder">
            <div class="placeholder-dynamic">
                <!-- PERBAIKAN: Keyword hanya 2 kata -->
                <div class="placeholder-text">Handphone Samsung</div>
                <div class="placeholder-text">Sepatu Pria</div>
                <div class="placeholder-text">Tas Wanita</div>
                <div class="placeholder-text">Promo Elektronik</div>
            </div>
        </div>
        <div class="search-icon">
            <i class="fa fa-search"></i>
        </div>
        
        <!-- Tambahkan ikon keranjang dan chat di sini -->
        <div class="header-icons">
            <!-- Ikon keranjang dengan badge -->
            <div class="cart-icon">
                <i class="fa fa-shopping-cart"></i>
                <div class="cart-badge">5</div>
            </div>
            
            <!-- Ikon chat dengan titik-titik -->
            <div class="chat-icon">
                <i class="fa fa-comment"></i>
                <div class="chat-badge">3</div>
                <div class="chat-dots">
                    <span class="dot"></span>
                    <span class="dot"></span>
                    <span class="dot"></span>
                </div>
            </div>
        </div>
    </div>
</header>
    <!-- Keyword suggestions vertical -->
    <div class="keyword-suggestions" id="keywordSuggestions">
        <div class="keyword-suggestions-wrapper">
            
            <div class="keyword-dynamic">
              
            </div>
        </div>
    </div>

    <!-- Overlay saat search aktif -->
    <div class="overlay" id="searchOverlay">
        <!-- Search bar expanded mode -->
        <div class="search-expanded">
    <div class="search-container">
        <span class="back-btn" id="backBtn"><i class="fa fa-arrow-left"></i></span>
        <input type="text" class="search-input" id="expandedSearchInput" placeholder=" ">
        <div class="search-placeholder" id="expandedSearchPlaceholder">
            <div class="placeholder-dynamic">
                <!-- PERBAIKAN: Keyword hanya 2 kata -->
                <div class="placeholder-text">Handphone Samsung</div>
                <div class="placeholder-text">Sepatu Pria</div>
                <div class="placeholder-text">Tas Wanita</div>
                <div class="placeholder-text">Promo Elektronik</div>
            </div>
        </div>
        <!-- PERBAIKAN: Ikon silang hanya ditampilkan di tampilan kedua (expanded) -->
        <!-- PERBAIKAN: Mengubah ikon silang menjadi button untuk meningkatkan responsivitas -->
        <button class="clear-search-icon" id="expandedClearSearchIcon" aria-label="Hapus pencarian">
            <i class="fa fa-times"></i>
        </button>
        <!-- Icon pencarian dipindahkan ke luar input box -->
        <div class="expanded-search-icon" id="expandedSearchIcon">
            <i class="fa fa-search"></i>
        </div>
        <!-- Icon filter - ditambahkan di samping search icon -->
        <div class="filter-icon" id="filterIcon">
            <i class="fa fa-filter"></i>
            <div class="filter-badge" id="filterBadge" style="display: none;">0</div>
        </div>
    </div>
</div>

        <!-- Prediksi keyword -->
        <div class="keyword-predictions" id="keywordPredictions"></div>
		
		<!-- Facet Overlay untuk Mobile/Tablet -->
<div class="facet-overlay" id="facetOverlay">
    <div class="facet-panel" id="facetPanel">
        <div class="facet-header">
            <div class="facet-title">Filter</div>
            <div class="facet-close" id="facetClose"><i class="fa fa-times"></i></div>
        </div>
        <div class="facet-content-wrapper">
            <div id="facetContent">
                <!-- Facet content will be inserted here -->
            </div>
        </div>
        <div class="facet-buttons">
            <div class="facet-button facet-button-reset" id="facetResetBtn">Reset</div>
            <div class="facet-button facet-button-apply" id="facetApplyBtn">Terapkan</div>
        </div>
    </div>
</div>

<!-- Facet Panel untuk Desktop -->
<div class="facet-panel-desktop" id="facetPanelDesktop">
    <div class="facet-header">
        <div class="facet-title">Filter</div>
        <div class="facet-close" id="facetCloseDesktop"><i class="fa fa-times"></i></div>
    </div>
    <div class="facet-content-wrapper">
        <div id="facetContentDesktop">
            <!-- Facet content will be inserted here -->
        </div>
    </div>
    <div class="facet-buttons">
        <div class="facet-button facet-button-reset" id="facetResetBtnDesktop">Reset</div>
        <div class="facet-button facet-button-apply" id="facetApplyBtnDesktop">Terapkan</div>
    </div>
</div>
        
        <!-- PERBAIKAN: Tab filter di bawah kolom pencarian dengan posisi fixed -->
        <div class="filter-tabs" id="filterTabs">
            <div class="filter-tabs-inner">
                <div class="filter-tab active" data-filter="terkait">Terkait</div>
                <div class="filter-tab" data-filter="terlaris">Terlaris</div>
                <div class="filter-tab" data-filter="terbaru">Terbaru</div>
                <div class="filter-tab filter-tab-price" data-filter="harga">
                    Harga <i class="fa fa-sort" id="priceSort"></i>
                </div>
            </div>
        </div>
<!-- Suggestions container -->
        <div class="suggestions-container" id="suggestionsContainer">
           <!-- Tambahkan "Hapus riwayat pencarian" di atas text suggestions -->
<div class="clear-history" id="clearHistoryBtn">Hapus riwayat pencarian</div>

<!-- Text suggestions - Akan menampilkan 7 keyword pertama -->
<div class="text-suggestions" id="textSuggestions">
    <!-- Riwayat pencarian akan diisi dinamis di sini -->
</div>

<!-- Extended suggestions - Akan menampilkan 5 keyword tambahan -->
<div class="extended-suggestions" id="extendedSuggestions" style="display: none;">
    <!-- 5 keyword tambahan akan diisi di sini -->
</div>
            
<!-- Tombol Lihat Lainnya -->
<div class="see-more-container">
    <button class="see-more-btn" id="seeMoreBtn">
        <i class="fa fa-plus-circle"></i>
        <span>Lihat Lainnya</span>
    </button>
</div>
            
<!-- Additional suggestions untuk "Sedang Trend" -->
<div class="additional-suggestions" id="additionalSuggestions">
   <!-- Tampilan Pill dengan Badge untuk Sedang Trend -->
   <div class="trend-pill">
       Sedang Trend
       <div class="trend-pill-badge" id="trendCount">5</div>
   </div>
   
   <!-- Suggestion items akan diisi secara dinamis oleh popularKeywords.updateTrendingDisplay() -->
</div>

<!-- Product header yang akan muncul di atas card peringatan - PERBAIKAN: Mencegah overflow nama produk -->
            <div class="product-header" id="productHeader" style="display: none;">
                <div class="product-info">
                    <div class="product-avatar">
                        <img src="/api/placeholder/100/100" alt="Product image">
                    </div>
                    <div class="product-details" id="productDetails">
                        <div class="product-name">Smartphone 128GB</div>
                        <div class="product-category">Elektronik</div>
                        <div class="product-stats">
                            <span>Rp 2.999.000</span>
                            <span>★ 4.8</span>
                        </div>
                    </div>
                    <div class="product-action" id="productAction">
                        Produk Lainnya <i class="fa fa-chevron-right"></i>
                    </div>
                </div>
            </div>
            
            <div class="not-found-container" id="notFoundContainer">
                <div class="not-found-icon">
                    <div class="search-document-icon">
                        <div class="document-base"></div>
                        <div class="document-fold"></div>
                        <div class="document-lines"></div>
                        <div class="magnifying-glass"></div>
                    </div>
                </div>
                <div class="not-found-title">Hasil tidak ditemukan</div>
                <div class="not-found-message">Mohon coba kata kunci yang lain atau yang lebih umum.</div>
                <div class="not-found-button-container">
                    <button class="not-found-button primary" id="tryAnotherKeywordBtn">Coba kata kunci lain</button>
                    <button class="not-found-button secondary" id="trySuggestionBtn">Coba produk lainnya</button>
                </div>
            </div>
            <!-- Tambahkan tampilan produk lainnya -->
            <div class="products-container" id="productsContainer">
                <div class="products-title">Produk Lainnya</div>
                <div class="products-grid" id="otherProductsGrid">
                    <!-- Product cards akan diisi secara dinamis -->
                </div>
            </div>

             <!-- Product suggestions -->
            <div class="product-suggestions">
                <div class="product-title">Produk Populer</div>
                <div class="product-grid" id="productGrid">
                    <!-- Product cards will be inserted here via JavaScript -->
                </div>
            </div>
            
            <!-- PERBAIKAN: Gunakan keyword tags dengan nama produk, bukan card produk -->
<div class="keyword-suggestions-popup" id="keywordSuggestionsPopup">
    <div class="keyword-suggestions-title">Produk Populer</div>
    <div class="keyword-suggestions-grid">
        <!-- Konten akan diisi secara dinamis oleh JavaScript -->
    </div>
</div>

            <!-- Overlay untuk popup -->
            <div class="suggestions-overlay" id="suggestionsOverlay"></div>
        </div>
    </div>

    <!-- Content area -->
    <div class="content-area" id="contentArea">
        <!-- Content area kosong -->
    </div>
<script>

// Tambahkan fungsi untuk mengindeks produk
function createSearchIndex(products) {
    // Konfigurasi Fuse.js untuk pencarian teks lengkap
    const options = {
        keys: ['name', 'category', 'shortName'],
        includeScore: true,
        threshold: 0.4,
        distance: 100
    };
    
    // Buat indeks produk
    return new Fuse(products, options);
}

// Gunakan indeks untuk pencarian
function fullTextSearch(query, productIndex) {
    return productIndex.search(query).map(result => result.item);
}

// Variabel untuk menyimpan indeks
let productSearchIndex;

// Sistem Keyword Cerdas - Tambahkan di awal file JavaScript
const intelligentKeywordSystem = {
    // Versi cache untuk melacak perubahan
    CACHE_VERSION: '1.0',
    
    // Database manual dasar
    manualData: {
        synonyms: {
            'hp': ['handphone', 'smartphone', 'ponsel', 'telepon'],
            'handphone': ['hp', 'smartphone', 'ponsel', 'telepon'],
            'smartphone': ['hp', 'handphone', 'ponsel', 'telepon'],
            'laptop': ['notebook', 'komputer', 'pc portable'],
            'sepatu': ['shoes', 'sneakers', 'footwear'],
            'tas': ['bag', 'ransel', 'tote', 'backpack'],
            'murah': ['ekonomis', 'terjangkau', 'hemat', 'diskon'],
            'bagus': ['berkualitas', 'terbaik', 'premium']
        },
        typoCorrections: {
            'handpone': 'handphone',
            'smartpone': 'smartphone',
            'blutooth': 'bluetooth',
            'hadphone': 'headphone',
            'headpone': 'headphone',
            'keybord': 'keyboard',
            'keyborad': 'keyboard',
            'smarthpone': 'smartphone',
            'smarphone': 'smartphone',
            'selempangan': 'selempang',
            'wireles': 'wireless',
            'wirelless': 'wireless'
        }
    },
    
    // Data yang dihasilkan secara otomatis
    autoData: {
        synonyms: {},
        typoCorrections: {},
        relatedKeywords: {}
    },
    
    // Fungsi inisialisasi
    init: function() {
        console.log("Inisialisasi sistem keyword cerdas...");
        
        // Coba muat dari cache terlebih dahulu
        if (this.loadFromCache()) {
            console.log("Data berhasil dimuat dari cache");
        } else {
            // Inisialisasi dasar dengan data manual
            this.initBasic();
            
            // Jadwalkan pemrosesan lanjutan
            this.scheduleBackgroundProcessing();
        }
        
        // Perbarui database prediksi keyword
        this.updateKeywordDatabase();
    },
    
    // Memuat data dari cache
    loadFromCache: function() {
        try {
            const cachedData = localStorage.getItem('intelligent_keyword_cache');
            if (!cachedData) return false;
            
            const data = JSON.parse(cachedData);
            
            // Periksa versi dan kedaluwarsa
            if (data.version !== this.CACHE_VERSION) return false;
            
            // Cek kedaluwarsa (7 hari)
            const cacheTime = data.timestamp;
            const now = Date.now();
            const daysDiff = (now - cacheTime) / (1000 * 60 * 60 * 24);
            if (daysDiff > 7) return false;
            
            // Muat data
            this.autoData = data.autoData;
            return true;
        } catch (e) {
            console.error("Error memuat cache:", e);
            return false;
        }
    },
    
    // Simpan data ke cache
    saveToCache: function() {
        try {
            const cacheData = {
                version: this.CACHE_VERSION,
                timestamp: Date.now(),
                autoData: this.autoData
            };
            
            localStorage.setItem('intelligent_keyword_cache', JSON.stringify(cacheData));
            console.log("Data sistem keyword berhasil di-cache");
        } catch (e) {
            console.error("Error saat menyimpan cache:", e);
            // Coba kurangi ukuran data
            this.trimDataSize();
            try {
                // Coba simpan lagi dengan data yang dipotong
                const cacheData = {
                    version: this.CACHE_VERSION,
                    timestamp: Date.now(),
                    autoData: this.autoData
                };
                localStorage.setItem('intelligent_keyword_cache', JSON.stringify(cacheData));
            } catch (e2) {
                console.error("Gagal menyimpan cache bahkan setelah mengurangi ukuran:", e2);
            }
        }
    },
    
    // Inisialisasi dasar dengan data manual
    initBasic: function() {
        // Mulai dengan data manual
        this.autoData.synonyms = {...this.manualData.synonyms};
        this.autoData.typoCorrections = {...this.manualData.typoCorrections};
        this.autoData.relatedKeywords = {};
        
        // Tambahkan kata terkait dasar dari produk
        this.generateBasicRelatedKeywords();
    },
    
    // Jadwalkan pemrosesan background
    scheduleBackgroundProcessing: function() {
        const requestIdle = window.requestIdleCallback || 
                          (cb => setTimeout(cb, 1000));
        
        requestIdle(() => {
            console.log("Memulai pemrosesan lanjutan untuk sistem keyword...");
            this.processAdvanced();
        });
    },
    
    // Pemrosesan lanjutan
    processAdvanced: function() {
        // 1. Sinonim tambahan
        this.generateAdditionalSynonyms();
        
        // 2. Koreksi typo tambahan
        this.generateAdditionalTypoCorrections();
        
        // 3. Kata terkait lengkap
        this.generateFullRelatedKeywords();
        
        // Simpan ke cache
        this.saveToCache();
        
        // Perbarui database
        this.updateKeywordDatabase();
        
        console.log("Pemrosesan lanjutan selesai, database berisi:");
        console.log(`- ${Object.keys(this.autoData.synonyms).length} sinonim`);
        console.log(`- ${Object.keys(this.autoData.typoCorrections).length} koreksi typo`);
        console.log(`- ${Object.keys(this.autoData.relatedKeywords).length} kata terkait`);
    },
    
    // Perbarui database keyword
    updateKeywordDatabase: function() {
        // Gabungkan data manual dan otomatis
        keywordPredictionDB.synonyms = {
            ...this.manualData.synonyms,
            ...this.autoData.synonyms
        };
        
        keywordPredictionDB.typoCorrections = {
            ...this.manualData.typoCorrections,
            ...this.autoData.typoCorrections
        };
        
        keywordPredictionDB.relatedKeywords = {
            ...this.autoData.relatedKeywords
        };
    },
    
    // Generate kata terkait dasar
    generateBasicRelatedKeywords: function() {
        // Kumpulkan kata dari produk berdasarkan kategori
        const wordGroups = {};
        
        sampleProducts.forEach(product => {
            const category = product.category.toLowerCase();
            const words = product.name.toLowerCase().split(' ')
                          .filter(w => w.length >= 3);
            
            words.forEach(word => {
                if (!wordGroups[word]) {
                    wordGroups[word] = new Set();
                }
                wordGroups[word].add(category);
                
                // Tambahkan juga kategori sebagai kata kunci
                if (!wordGroups[category]) {
                    wordGroups[category] = new Set();
                }
                wordGroups[category].add(word);
            });
        });
        
        // Konversi ke related keywords
        Object.keys(wordGroups).forEach(word => {
            this.autoData.relatedKeywords[word] = Array.from(wordGroups[word]);
        });
    },
    
    // Generate sinonim tambahan
    generateAdditionalSynonyms: function() {
        // Kumpulkan semua kata dari produk
        const words = this.collectAllProductWords();
        const synonymGroups = {};
        
        // Kelompokkan berdasarkan substrings umum
        words.forEach(word1 => {
            if (word1.length < 4) return;
            
            words.forEach(word2 => {
                if (word1 === word2 || word2.length < 4) return;
                
                // Cek kesamaan
                const similarity = this.calculateSimilarity(word1, word2);
                if (similarity > 0.7 && similarity < 1.0) {
                    // Tambahkan ke grup
                    if (!synonymGroups[word1]) synonymGroups[word1] = new Set();
                    synonymGroups[word1].add(word2);
                }
            });
        });
        
        // Batasi jumlah sinonim per kata (max 3)
        Object.keys(synonymGroups).forEach(word => {
            // Konversi Set ke Array dan batasi
            const synonyms = Array.from(synonymGroups[word]).slice(0, 3);
            if (synonyms.length > 0) {
                if (!this.autoData.synonyms[word]) {
                    this.autoData.synonyms[word] = [];
                }
                
                synonyms.forEach(syn => {
                    if (!this.autoData.synonyms[word].includes(syn)) {
                        this.autoData.synonyms[word].push(syn);
                    }
                });
            }
        });
    },
    
    // Generate koreksi typo tambahan (berat)
generateAdditionalTypoCorrections: function() {
    // Kumpulkan semua kata unik dari produk
    const allWords = new Set();
    
    sampleProducts.forEach(product => {
        // Tambahkan dari nama produk
        product.name.toLowerCase().split(' ')
            .filter(w => w.length >= 3)
            .forEach(w => allWords.add(w));
            
        // Tambahkan dari kategori jika ada
        if (product.category) {
            product.category.toLowerCase().split(' ')
                .filter(w => w.length >= 3)
                .forEach(w => allWords.add(w));
        }
        
        // Tambahkan shortName jika ada
        if (product.shortName) {
            product.shortName.toLowerCase().split(' ')
                .filter(w => w.length >= 3)
                .forEach(w => allWords.add(w));
        }
    });
    
    // Konversi Set ke Array
    const words = Array.from(allWords);
    
    // Batasi jumlah kata untuk menjaga performa
    const limitedWords = words.slice(0, 100); // Ditingkatkan dari 50 ke 100
    
    // Buat variasi typo untuk kata-kata umum
    limitedWords.forEach(word => {
        // Buat variasi typo sederhana
        const typos = [
            // Hilangkan satu huruf
            ...Array.from({ length: word.length }, (_, i) => 
                word.substring(0, i) + word.substring(i + 1)),
                
            // Tukar dua huruf bersebelahan
            ...Array.from({ length: word.length - 1 }, (_, i) => 
                word.substring(0, i) + word.charAt(i + 1) + 
                word.charAt(i) + word.substring(i + 2)),
                
            // Variasi tambahan yang lebih umum
            word.replace(/a/g, 'e'),
            word.replace(/e/g, 'a'),
            word.replace(/i/g, 'y'),
            word.replace(/s/g, 'z'),
            word.replace(/k/g, 'c'),
            word.replace(/c/g, 'k'),
            word.substring(0, 1) + word.substring(1).replace(/a/g, 'e')
        ];
        
        // Tambahkan ke koreksi typo
        typos.forEach(typo => {
            if (typo !== word && typo.length >= 3 && !words.includes(typo)) {
                this.autoData.typoCorrections[typo] = word;
            }
        });
    });
},
    
    // Generate variasi typo
    generateTypoVariations: function(word) {
        const variations = [];
        
        // 1. Hilangkan satu huruf
        for (let i = 0; i < word.length; i++) {
            variations.push(word.slice(0, i) + word.slice(i + 1));
        }
        
        // 2. Tukar dua huruf bersebelahan
        for (let i = 0; i < word.length - 1; i++) {
            variations.push(
                word.slice(0, i) + 
                word[i + 1] + 
                word[i] + 
                word.slice(i + 2)
            );
        }
        
        return variations;
    },
    
    // Generate kata terkait lengkap
    generateFullRelatedKeywords: function() {
        // Buat kamus co-occurrence sederhana
        const coOccurrenceMap = {};
        
        // Batasi jumlah produk untuk efisiensi
        const maxProducts = Math.min(50, sampleProducts.length);
        
        // Proses co-occurrence dari nama dan kategori produk
        for (let i = 0; i < maxProducts; i++) {
            const product = sampleProducts[i];
            const words = [...new Set([
                ...product.name.toLowerCase().split(' '),
                ...product.category.toLowerCase().split(' ')
            ])].filter(w => w.length >= 3);
            
            // Hitung kata yang muncul bersama
            for (let j = 0; j < words.length; j++) {
                const word = words[j];
                
                if (!coOccurrenceMap[word]) {
                    coOccurrenceMap[word] = {};
                }
                
                for (let k = 0; k < words.length; k++) {
                    if (j === k) continue;
                    
                    const otherWord = words[k];
                    coOccurrenceMap[word][otherWord] = 
                        (coOccurrenceMap[word][otherWord] || 0) + 1;
                }
            }
        }
        
        // Konversi ke related keywords (max 5 per kata)
        Object.keys(coOccurrenceMap).forEach(word => {
            const related = Object.entries(coOccurrenceMap[word])
                            .sort((a, b) => b[1] - a[1])
                            .slice(0, 5)
                            .map(entry => entry[0]);
            
            if (related.length > 0) {
                this.autoData.relatedKeywords[word] = related;
            }
        });
    },
    
    // Kumpulkan semua kata dari produk
    collectAllProductWords: function() {
        const wordSet = new Set();
        
        sampleProducts.forEach(product => {
            // Kata dari nama produk
            product.name.toLowerCase().split(' ')
                .filter(w => w.length >= 3)
                .forEach(w => wordSet.add(w));
            
            // Kata dari kategori
            product.category.toLowerCase().split(' ')
                .filter(w => w.length >= 3)
                .forEach(w => wordSet.add(w));
        });
        
        return Array.from(wordSet);
    },
    
    // Hitung kesamaan string (0-1)
calculateSimilarity: function(str1, str2) {
    // Kasus khusus untuk akronim/singkatan
    if (str1.length <= 3 && str2.length <= 3) {
        return str1 === str2 ? 1 : 0;
    }
    
    // Jika sama persis
    if (str1 === str2) return 1;
    
    // Jika panjang sangat berbeda
    const lengthDiff = Math.abs(str1.length - str2.length);
    if (lengthDiff > 3) return 0; // Lebih longgar (sebelumnya 2)
    
    // Hitung jumlah karakter yang cocok secara berurutan
    let matches = 0;
    const minLength = Math.min(str1.length, str2.length);
    
    for (let i = 0; i < minLength; i++) {
        if (str1[i] === str2[i]) {
            matches++;
        }
    }
    
    // Similarity score - lebih longgar
    return matches / Math.max(str1.length, str2.length) * 1.2; // Faktor 1.2 membuat lebih longgar
},
    
    // Potong ukuran data jika terlalu besar
    trimDataSize: function() {
        // 1. Batasi jumlah sinonim
        const synonymKeys = Object.keys(this.autoData.synonyms);
        if (synonymKeys.length > 100) {
            const newSynonyms = {};
            synonymKeys.slice(0, 100).forEach(key => {
                newSynonyms[key] = this.autoData.synonyms[key].slice(0, 3);
            });
            this.autoData.synonyms = newSynonyms;
        }
        
        // 2. Batasi jumlah koreksi typo
        const typoKeys = Object.keys(this.autoData.typoCorrections);
        if (typoKeys.length > 100) {
            const newTypos = {};
            typoKeys.slice(0, 100).forEach(key => {
                newTypos[key] = this.autoData.typoCorrections[key];
            });
            this.autoData.typoCorrections = newTypos;
        }
        
        // 3. Batasi jumlah kata terkait
        const relatedKeys = Object.keys(this.autoData.relatedKeywords);
        if (relatedKeys.length > 100) {
            const newRelated = {};
            relatedKeys.slice(0, 100).forEach(key => {
                newRelated[key] = this.autoData.relatedKeywords[key].slice(0, 3);
            });
            this.autoData.relatedKeywords = newRelated;
        }
        
        console.log("Ukuran data dipotong untuk efisiensi");

    }
};



// Menginisialisasi localStorage untuk riwayat pencarian jika belum ada
function initSearchHistory() {
    if (!localStorage.getItem('searchHistory')) {
        localStorage.setItem('searchHistory', JSON.stringify([]));
    }
}

// Mendapatkan riwayat pencarian dari localStorage
function getSearchHistory() {
    try {
        return JSON.parse(localStorage.getItem('searchHistory')) || [];
    } catch (e) {
        console.error('Error parsing search history:', e);
        return [];
    }
}

// Menambahkan keyword ke riwayat pencarian
function addToSearchHistory(keyword) {
    if (!keyword || keyword.trim() === '') return;
    
    keyword = keyword.trim();
    const history = getSearchHistory();
    
    // Hapus keyword jika sudah ada (untuk mencegah duplikat)
    const index = history.indexOf(keyword);
    if (index !== -1) {
        history.splice(index, 1);
    }
    
    // Tambahkan ke awal array
    history.unshift(keyword);
    
    // Batasi jumlah maksimal riwayat pencarian ke 12
    if (history.length > 12) {
        history.pop();
    }
    
    // Simpan kembali ke localStorage
    localStorage.setItem('searchHistory', JSON.stringify(history));
    
    // Update tampilan sugesti
    updateKeywordSuggestions();
}

// Menghapus semua riwayat pencarian
function clearAllSearchHistory() {
    localStorage.setItem('searchHistory', JSON.stringify([]));
    updateKeywordSuggestions();
}


// Memperbarui tampilan sugesti keyword berdasarkan riwayat pencarian
function updateKeywordSuggestions() {
    const textSuggestions = document.getElementById('textSuggestions');
    const extendedSuggestions = document.getElementById('extendedSuggestions');
    
    if (!textSuggestions || !extendedSuggestions) return;
    
    const history = getSearchHistory();
    
    // Hapus konten lama
    textSuggestions.innerHTML = '';
    extendedSuggestions.innerHTML = '';
    
    if (history.length === 0) {
        // Tampilkan pesan jika riwayat kosong
        const emptyMessage = document.createElement('div');
        emptyMessage.className = 'suggestion-item empty-history';
        emptyMessage.innerHTML = '<span style="color: #999; font-style: italic; padding: 10px 15px;">Tidak ada riwayat pencarian</span>';
        textSuggestions.appendChild(emptyMessage);
        return;
    }
    
    // Tampilkan 7 keyword pertama di textSuggestions dalam bentuk tombol
    const initialKeywords = Math.min(7, history.length);
    for (let i = 0; i < initialKeywords; i++) {
        addSuggestionItem(history[i], textSuggestions);
    }
    
    // Update badge count di trendPill
    const trendCount = document.getElementById('trendCount');
    if (trendCount) {
        const trendItems = document.querySelectorAll('#additionalSuggestions .suggestion-item');
        trendCount.textContent = trendItems.length;
    }
}

// Fungsi helper untuk menambahkan item sugesti
function addSuggestionItem(keyword, container) {
    // Jika ini adalah text-suggestions (container utama), buat tampilan tombol
    if (container.id === 'textSuggestions') {
        // Buat container tombol jika belum ada
        let buttonContainer = container.querySelector('.keyword-button-container');
        if (!buttonContainer) {
            buttonContainer = document.createElement('div');
            buttonContainer.className = 'keyword-button-container';
            container.appendChild(buttonContainer);
        }
        
        // Buat tombol
        const button = document.createElement('div');
        button.className = 'keyword-button';
        button.setAttribute('data-text', keyword);
        
        button.innerHTML = `
            <span class="suggestion-icon"><i class="fa fa-history"></i></span>
            <span>${keyword}</span>
        `;
        
        // Tambahkan event listener untuk mengisi pencarian saat diklik
        button.addEventListener('click', function() {
            const suggestionText = this.getAttribute('data-text');
            fillSearchWithSuggestion(suggestionText);
        });
        
        buttonContainer.appendChild(button);
    } else {
        // Untuk container lain (seperti extended suggestions), gunakan tampilan list original
        const item = document.createElement('div');
        item.className = 'suggestion-item';
        item.setAttribute('data-text', keyword);
        
        item.innerHTML = `
            <span class="suggestion-icon"><i class="fa fa-history"></i></span>
            <span>${keyword}</span>
        `;
        
        // Tambahkan event listener untuk mengisi pencarian saat diklik
        item.addEventListener('click', function() {
            const suggestionText = this.getAttribute('data-text');
            fillSearchWithSuggestion(suggestionText);
        });
        
        container.appendChild(item);
    }
}

// Database untuk prediksi keyword
const keywordPredictionDB = {
    // Keyword produk
    productKeywords: [
        "smartphone android", "sepatu sneakers", "tas selempang", 
        "headphone bluetooth", "keyboard gaming", "power bank",
        "smart tv", "robot vacuum", "laptop gaming", "tablet android",
        "kamera mirrorless", "speaker bluetooth", "smartwatch", "drone mini"
    ],
    
    // Sinonim untuk kata-kata umum
    synonyms: {
        "telepon": ["hp", "handphone", "smartphone", "ponsel", "telpon"],
       "telpon": ["hp", "handphone", "smartphone", "ponsel", "telepon"],
        "hp": ["handphone", "smartphone", "ponsel", "telepon"],
        "handphone": ["hp", "smartphone", "ponsel", "telepon"],
        "smartphone": ["hp", "handphone", "ponsel", "telepon"],
        "laptop": ["notebook", "komputer", "pc portable"],
        "sepatu": ["shoes", "sneakers", "footwear"],
        "tas": ["bag", "ransel", "tote", "backpack"],
        "murah": ["ekonomis", "terjangkau", "hemat", "diskon"],
        "bagus": ["berkualitas", "terbaik", "premium"],
        "bluetooth": ["wireless", "nirkabel", "tanpa kabel"],
        "wanita": ["cewek", "perempuan", "lady"],
        "pria": ["cowok", "laki-laki", "men"]
    },
    
    // Related keywords
    relatedKeywords: {
        "tas": ["tas selempang", "tas ransel", "tas wanita", "tas pria", "tas branded", "tas kulit"],
        "sepatu": ["sepatu sneakers", "sepatu lari", "sepatu wanita", "sepatu pria", "sepatu olahraga"],
        "handphone": ["handphone android", "handphone samsung", "handphone iphone", "handphone xiaomi"],
        "laptop": ["laptop gaming", "laptop asus", "laptop lenovo", "laptop acer", "laptop hp"],
        "headphone": ["headphone bluetooth", "headphone gaming", "headphone wireless", "headphone noise cancelling"],
        "kamera": ["kamera mirrorless", "kamera dslr", "kamera pocket", "kamera action"],
        "keyboard": ["keyboard gaming", "keyboard mechanical", "keyboard wireless"],
        "speaker": ["speaker bluetooth", "speaker portable", "speaker aktif", "speaker mini"]
    },
    
    // Typo corrections
    typoCorrections: {
        "handpone": "handphone",
        "smartpone": "smartphone",
        "blutooth": "bluetooth",
        "hadphone": "headphone",
        "headpone": "headphone",
        "keybord": "keyboard",
        "keyborad": "keyboard",
        "smarthpone": "smartphone",
        "smarphone": "smartphone",
        "selempangan": "selempang",
        "wireles": "wireless",
        "wirelless": "wireless"
    },
    
    // Riwayat prediksi yang digunakan pengguna (akan diisi selama penggunaan)
    userInteractionHistory: []
};

// Objek untuk menyimpan data prediksi yang dikumpulkan
const learningPredictions = {
    data: {},
    
    // Tambahkan query ke database pembelajaran
    learn: function(query) {
        if (!query || query.length < 5) return;
        
        const words = query.toLowerCase().split(' ');
        
        // Abaikan jika kurang dari 2 kata
        if (words.length < 2) return;
        
        // Untuk setiap kata, kecuali kata terakhir
        for (let i = 0; i < words.length - 1; i++) {
            const currentWord = words[i];
            const nextWord = words[i + 1];
            
            if (currentWord.length >= 3) {
                if (!this.data[currentWord]) {
                    this.data[currentWord] = {};
                }
                
                // Hitung frekuensi kata berikutnya
                this.data[currentWord][nextWord] = (this.data[currentWord][nextWord] || 0) + 1;
            }
        }
        
        // Simpan ke localStorage untuk persistensi
        try {
            localStorage.setItem('learningPredictions', JSON.stringify(this.data));
        } catch (e) {
            console.log('Failed to save predictions to localStorage', e);
        }
    },
    
    // Dapatkan prediksi untuk kata
    getPredictions: function(word) {
        if (!word || word.length < 3) return [];
        
        word = word.toLowerCase();
        
        if (!this.data[word]) return [];
        
        // Sortir berdasarkan frekuensi dan ambil 3 teratas
        return Object.entries(this.data[word])
            .sort((a, b) => b[1] - a[1])
            .slice(0, 3)
            .map(entry => entry[0]);
    },
    
    // Muat data dari localStorage jika ada
    loadFromStorage: function() {
        try {
            const stored = localStorage.getItem('learningPredictions');
            if (stored) {
                this.data = JSON.parse(stored);
            }
        } catch (e) {
            console.log('Failed to load predictions from localStorage', e);
        }
    },
    
    // Membatasi jumlah data
    limitDataSize: function(maxEntries = 100) {
        // Dapatkan semua kata kunci dan urutkan berdasarkan frekuensi
        const allWords = Object.keys(this.data);
        
        // Jika jumlah kata lebih dari batas maksimum
        if (allWords.length > maxEntries) {
            // Hitung total penggunaan untuk setiap kata
            const wordUsage = allWords.map(word => {
                const totalUsage = Object.values(this.data[word]).reduce((sum, count) => sum + count, 0);
                return { word, totalUsage };
            });
            
            // Urutkan berdasarkan penggunaan (dari yang paling sering)
            wordUsage.sort((a, b) => b.totalUsage - a.totalUsage);
            
            // Ambil hanya sejumlah maxEntries teratas
            const wordsToKeep = wordUsage.slice(0, maxEntries).map(item => item.word);
            
            // Buat database baru dengan hanya kata-kata yang akan disimpan
            const newData = {};
            wordsToKeep.forEach(word => {
                newData[word] = this.data[word];
            });
            
            // Ganti database lama dengan yang baru
            this.data = newData;
            
            // Simpan ke localStorage
            try {
                localStorage.setItem('learningPredictions', JSON.stringify(this.data));
            } catch (e) {
                console.log('Failed to save limited predictions to localStorage', e);
            }
        }
    },
    
    // Membersihkan data lama
    cleanupOldData: function(threshold = 30) {
        // Dapatkan timestamp saat ini dalam hari
        const now = Math.floor(Date.now() / (1000 * 60 * 60 * 24));
        
        // Coba dapatkan data timestamp terakhir akses
        let lastAccess = {};
        try {
            const stored = localStorage.getItem('predictionsLastAccess');
            if (stored) {
                lastAccess = JSON.parse(stored);
            }
        } catch (e) {
            console.log('Failed to load last access data', e);
        }
        
        // Cari kata-kata yang belum diakses dalam threshold hari
        const wordsToRemove = [];
        
        Object.keys(this.data).forEach(word => {
            // Jika tidak ada catatan akses atau akses terakhir lebih dari threshold hari
            if (!lastAccess[word] || (now - lastAccess[word] > threshold)) {
                wordsToRemove.push(word);
            }
        });
        
        // Hapus kata-kata yang lama tidak diakses
        wordsToRemove.forEach(word => {
            delete this.data[word];
        });
        
        // Simpan database yang sudah dibersihkan
        if (wordsToRemove.length > 0) {
            try {
                localStorage.setItem('learningPredictions', JSON.stringify(this.data));
            } catch (e) {
                console.log('Failed to save cleaned predictions to localStorage', e);
            }
        }
    },
    
    // Catat akses untuk setiap kata
    recordAccess: function(word) {
        if (!word || word.length < 3) return;
        
        word = word.toLowerCase();
        
        // Dapatkan timestamp saat ini dalam hari
        const now = Math.floor(Date.now() / (1000 * 60 * 60 * 24));
        
        // Coba dapatkan data timestamp terakhir akses
        let lastAccess = {};
        try {
            const stored = localStorage.getItem('predictionsLastAccess');
            if (stored) {
                lastAccess = JSON.parse(stored);
            }
        } catch (e) {
            console.log('Failed to load last access data', e);
        }
        
        // Update timestamp akses untuk kata ini
        lastAccess[word] = now;
        
        // Simpan kembali data akses
        try {
            localStorage.setItem('predictionsLastAccess', JSON.stringify(lastAccess));
        } catch (e) {
            console.log('Failed to save access data to localStorage', e);
        }
    },
    
    // Kompresi dan penyimpanan data
    compressAndSave: function() {
        // Kompresi sederhana: menghilangkan kata-kata dengan frekuensi rendah
        const compressedData = {};
        
        Object.keys(this.data).forEach(word => {
            // Filter hanya nextWord dengan frekuensi > 1
            const significantNextWords = {};
            Object.entries(this.data[word]).forEach(([nextWord, count]) => {
                if (count > 1) {
                    significantNextWords[nextWord] = count;
                }
            });
            
            // Simpan hanya jika ada nextWord yang signifikan
            if (Object.keys(significantNextWords).length > 0) {
                compressedData[word] = significantNextWords;
            }
        });
        
        // Jika ukuran masih terlalu besar, ubah format data menjadi lebih ringkas
        let dataToSave;
        try {
            const jsonData = JSON.stringify(compressedData);
            if (jsonData.length > 1000000) { // Jika > 1MB
                // Format yang lebih ringkas: [kata, [[kataSel, jumlah], ...]]
                const compactFormat = Object.entries(compressedData).map(([word, nextWords]) => {
                    return [
                        word, 
                        Object.entries(nextWords)
                    ];
                });
                dataToSave = JSON.stringify(compactFormat);
            } else {
                dataToSave = jsonData;
            }
            
            localStorage.setItem('learningPredictions', dataToSave);
            localStorage.setItem('predictionsCompressed', dataToSave.length > 1000000 ? 'true' : 'false');
        } catch (e) {
            console.log('Failed to save compressed data to localStorage', e);
            // Jika masih error, coba dengan lebih sedikit data
            this.limitDataSize(50); // Paksa batasan lebih ketat
            this.compressAndSave(); // Coba simpan lagi
        }
    },
    
    // Manajemen prediksi keseluruhan
    managePredictions: function() {
        // 1. Catat ukuran data sebelum manajemen
        const initialSize = JSON.stringify(this.data).length;
        console.log(`Initial predictions size: ${initialSize} bytes`);
        
        // 2. Bersihkan data lama yang tidak digunakan (lebih dari 30 hari)
        this.cleanupOldData(30);
        
        // 3. Batasi jumlah entri (kata) ke 100
        this.limitDataSize(100);
        
        // 4. Kompresi dan simpan data
        this.compressAndSave();
        
        // 5. Catat ukuran data setelah manajemen
        try {
            const finalSize = localStorage.getItem('learningPredictions').length;
            console.log(`Final predictions size after management: ${finalSize} bytes`);
            console.log(`Reduced by: ${initialSize - finalSize} bytes (${((initialSize - finalSize) / initialSize * 100).toFixed(2)}%)`);
        } catch (e) {
            console.log('Could not calculate final size');
        }
    }
};

        // Fungsi untuk memastikan responsivitas desktop
        function adjustDesktopLayout() {
            // Hanya jalankan untuk desktop (>= 768px)
            if (window.innerWidth >= 768) {
                // Pastikan container suggestion tidak melebihi viewport
                if (document.querySelector('.suggestions-container')) {
                    const container = document.querySelector('.suggestions-container');
                    
                    // Reset ukuran untuk menghindari overflow
                    container.style.width = '100%';
                    container.style.maxWidth = '800px';
                    container.style.boxSizing = 'border-box';
                    
                    // Pastikan produk grid juga tidak overflow
                    if (document.querySelector('.product-grid')) {
                        document.querySelector('.product-grid').style.width = '100%';
                        document.querySelector('.product-grid').style.boxSizing = 'border-box';
                    }
                }
                
                // PERBAIKAN: Memastikan header tidak melebihi lebar halaman
                const header = document.getElementById('mainHeader');
                if (header) {
                    header.style.width = '100%';
                    header.style.boxSizing = 'border-box';
                }
                
                // PERBAIKAN: Sembunyikan keyword suggestions pada tampilan desktop
                if (document.getElementById('keywordSuggestions')) {
                    document.getElementById('keywordSuggestions').style.display = 'none';
                }
            } else {
                // PERBAIKAN: Tampilkan keyword suggestions pada tampilan mobile
                if (document.getElementById('keywordSuggestions')) {
                    document.getElementById('keywordSuggestions').style.display = 'block';
                }
            }
        }
        
        // Jalankan saat halaman dimuat dan saat resize window
        window.addEventListener('load', adjustDesktopLayout);
        window.addEventListener('resize', adjustDesktopLayout);
    
        // Elemen DOM
        const mainHeader = document.getElementById('mainHeader');
        const clearHistoryBtn = document.getElementById('clearHistoryBtn');
        const searchInput = document.getElementById('searchInput');
        const expandedSearchInput = document.getElementById('expandedSearchInput');
        const searchOverlay = document.getElementById('searchOverlay');
        const suggestionsContainer = document.getElementById('suggestionsContainer');
        const backBtn = document.getElementById('backBtn');
        const contentArea = document.getElementById('contentArea');
        const productGrid = document.getElementById('productGrid');
        const suggestionItems = document.querySelectorAll('.suggestion-item');
        const keywordSuggestions = document.getElementById('keywordSuggestions');
        const searchPlaceholder = document.getElementById('searchPlaceholder');
        const expandedSearchPlaceholder = document.getElementById('expandedSearchPlaceholder');
        const expandedSearchIcon = document.getElementById('expandedSearchIcon');
        const seeMoreBtn = document.getElementById('seeMoreBtn');
        const additionalSuggestions = document.getElementById('additionalSuggestions');
        const notFoundContainer = document.getElementById('notFoundContainer');
        const productsContainer = document.getElementById('productsContainer');
        const otherProductsGrid = document.getElementById('otherProductsGrid');
        const keywordSuggestionsPopup = document.getElementById('keywordSuggestionsPopup');
        const suggestionsOverlay = document.getElementById('suggestionsOverlay');
        const tryAnotherKeywordBtn = document.getElementById('tryAnotherKeywordBtn');
        const trySuggestionBtn = document.getElementById('trySuggestionBtn');
        const textSuggestions = document.querySelector('.text-suggestions');
        // Kontainer untuk prediksi keyword
        const keywordPredictions = document.getElementById('keywordPredictions');
		// PERBAIKAN: Elemen filter icon
		const filterIcon = document.getElementById('filterIcon');
		const filterBadge = document.getElementById('filterBadge');
		const facetOverlay = document.getElementById('facetOverlay');
		const facetPanel = document.getElementById('facetPanel');
		const facetClose = document.getElementById('facetClose');
		const facetPanelDesktop = document.getElementById('facetPanelDesktop');
		const facetCloseDesktop = document.getElementById('facetCloseDesktop');
		const facetContent = document.getElementById('facetContent');
		const facetContentDesktop = document.getElementById('facetContentDesktop');
		const facetResetBtn = document.getElementById('facetResetBtn');
		const facetApplyBtn = document.getElementById('facetApplyBtn');
		const facetResetBtnDesktop = document.getElementById('facetResetBtnDesktop');
		const facetApplyBtnDesktop = document.getElementById('facetApplyBtnDesktop');
        
       // Fungsi untuk menghapus riwayat pencarian
function clearSearchHistory() {
    // Hapus riwayat dan perbarui tampilan
    clearAllSearchHistory();
    console.log("Riwayat pencarian dihapus");
}

  // Event listener untuk tombol hapus riwayat pencarian
if (clearHistoryBtn) {
    clearHistoryBtn.addEventListener('click', function(e) {
        e.preventDefault();
        e.stopPropagation();
        clearSearchHistory();
    });
}
        // PERBAIKAN: Elemen filter tabs
        const filterTabs = document.getElementById('filterTabs');
        const priceSort = document.getElementById('priceSort');
        
        // PERBAIKAN: Elemen ikon silang (hanya di expanded search)
        const expandedClearSearchIcon = document.getElementById('expandedClearSearchIcon');
        let productActionVisible = true;
  // Data produk sampel
        const sampleProducts = [
            {
                id: 1,
                name: "Smartphone Android Samsung",
                price: "Rp 2.999.000",
                originalPrice: "Rp 3.499.000", // PERBAIKAN BAGIAN 2: Tambahkan harga asli
                discount: "15%", // PERBAIKAN BAGIAN 2: Tambahkan diskon
                image: "/api/placeholder/200/200",
                category: "handphone",
                shortName: "Samsung Galaxy", // PERBAIKAN: Menambahkan nama pendek untuk tampilan simple
                isMall: true, // PERBAIKAN TAMPILAN CARD: Flag untuk sellzio Mall
                rating: 4.9, // PERBAIKAN TAMPILAN CARD: Rating produk
                sold: 125, // PERBAIKAN TAMPILAN CARD: Jumlah terjual
                shipping: "Pengiriman Instan", // PERBAIKAN TAMPILAN CARD: Info pengiriman
                cod: true // PERBAIKAN BAGIAN 2: Tambahkan flag untuk COD
            },
            {
                id: 2,
                name: "Sepatu Sneakers Pria",
                price: "Rp 299.000",
                originalPrice: "Rp 399.000", // PERBAIKAN BAGIAN 2: Tambahkan harga asli
                discount: "25%", // PERBAIKAN BAGIAN 2: Tambahkan diskon
                image: "/api/placeholder/200/200",
                category: "sepatu pria",
                shortName: "Sneakers Pria", // PERBAIKAN: Menambahkan nama pendek untuk tampilan simple
                isMall: false, // PERBAIKAN TAMPILAN CARD: Flag untuk sellzio Mall
                rating: 4.8, // PERBAIKAN TAMPILAN CARD: Rating produk
                sold: 215, // PERBAIKAN TAMPILAN CARD: Jumlah terjual
                shipping: "Pengiriman Reguler", // PERBAIKAN TAMPILAN CARD: Info pengiriman
                cod: true //PERBAIKAN BAGIAN 2: Tambahkan flag untuk COD
            },
            {
                id: 3,
                name: "Tas Selempang Wanita",
                price: "Rp 179.000",
                originalPrice: "Rp 229.000", // PERBAIKAN BAGIAN 2: Tambahkan harga asli
                discount: "22%", // PERBAIKAN BAGIAN 2: Tambahkan diskon
                image: "/api/placeholder/200/200",
                category: "tas wanita",
                shortName: "Tas Selempang", // PERBAIKAN: Menambahkan nama pendek untuk tampilan simple
                isMall: true, // PERBAIKAN TAMPILAN CARD: Flag untuk sellzio Mall
                rating: 4.7, // PERBAIKAN TAMPILAN CARD: Rating produk
                sold: 98, // PERBAIKAN TAMPILAN CARD: Jumlah terjual
                shipping: "Pengiriman Instan", // PERBAIKAN TAMPILAN CARD: Info pengiriman
                cod: false // PERBAIKAN BAGIAN 2: Tambahkan flag untuk COD
            },
            {
                id: 4,
                name: "Headphone Bluetooth Wireless",
                price: "Rp 549.000",
                originalPrice: "Rp 699.000", // PERBAIKAN BAGIAN 2: Tambahkan harga asli
                discount: "21%", // PERBAIKAN BAGIAN 2: Tambahkan diskon
                image: "/api/placeholder/200/200",
                category: "elektronik",
                shortName: "Headphone Bluetooth", // PERBAIKAN: Menambahkan nama pendek untuk tampilan simple
                isMall: true, // PERBAIKAN TAMPILAN CARD: Flag untuk sellzio Mall
                rating: 4.6, // PERBAIKAN TAMPILAN CARD: Rating produk
                sold: 85, // PERBAIKAN TAMPILAN CARD: Jumlah terjual
                shipping: "Pengiriman Next Day", // PERBAIKAN TAMPILAN CARD: Info pengiriman
                cod: true // PERBAIKAN BAGIAN 2: Tambahkan flag untuk COD
            },
            {
                id: 5,
                name: "Keyboard Gaming Mechanical",
                price: "Rp 459.000",
                originalPrice: "Rp 559.000", // PERBAIKAN BAGIAN 2: Tambahkan harga asli
                discount: "18%", // PERBAIKAN BAGIAN 2: Tambahkan diskon
                image: "/api/placeholder/200/200",
                category: "elektronik",
                shortName: "Keyboard Gaming", // PERBAIKAN: Menambahkan nama pendek untuk tampilan simple
                isMall: false, // PERBAIKAN TAMPILAN CARD: Flag untuk sellzio Mall
                rating: 4.9, // PERBAIKAN TAMPILAN CARD: Rating produk
                sold: 64, // PERBAIKAN TAMPILAN CARD: Jumlah terjual
                shipping: "Pengiriman Reguler", // PERBAIKAN TAMPILAN CARD: Info pengiriman
                cod: false // PERBAIKAN BAGIAN 2: Tambahkan flag untuk COD
            },
            {
                id: 6,
                name: "Power Bank Quick Charge",
                price: "Rp 229.000",
                originalPrice: "Rp 299.000", // PERBAIKAN BAGIAN 2: Tambahkan harga asli
                discount: "23%", // PERBAIKAN BAGIAN 2: Tambahkan diskon
                image: "/api/placeholder/200/200",
                category: "handphone",
                shortName: "Power Bank", // PERBAIKAN: Menambahkan nama pendek untuk tampilan simple
                isMall: true, // PERBAIKAN TAMPILAN CARD: Flag untuk sellzio Mall
                rating: 4.7, // PERBAIKAN TAMPILAN CARD: Rating produk
                sold: 178, // PERBAIKAN TAMPILAN CARD: Jumlah terjual
                shipping: "Pengiriman Instan", // PERBAIKAN TAMPILAN CARD: Info pengiriman
                cod: true // PERBAIKAN BAGIAN 2: Tambahkan flag untuk COD
            },
            {
                id: 7,
                name: "Smart TV Android UHD",
                price: "Rp 7.499.000",
                originalPrice: "Rp 8.999.000", // PERBAIKAN BAGIAN 2: Tambahkan harga asli
                discount: "17%", // PERBAIKAN BAGIAN 2: Tambahkan diskon
                image: "/api/placeholder/200/200",
                category: "elektronik",
                shortName: "Smart TV", // PERBAIKAN: Menambahkan nama pendek untuk tampilan simple
                isMall: true, // PERBAIKAN TAMPILAN CARD: Flag untuk sellzio Mall
                rating: 4.8, // PERBAIKAN TAMPILAN CARD: Rating produk
                sold: 25, // PERBAIKAN TAMPILAN CARD: Jumlah terjual
                shipping: "Pengiriman Next Day", // PERBAIKAN TAMPILAN CARD: Info pengiriman
                cod: false // PERBAIKAN BAGIAN 2: Tambahkan flag untuk COD
            },
     {
                id: 8,
                name: "Tas Selempang mahal ",
                price: "Rp 169.000",
                originalPrice: "Rp 229.000", // PERBAIKAN BAGIAN 2: Tambahkan harga asli
                discount: "22%", // PERBAIKAN BAGIAN 2: Tambahkan diskon
                image: "/api/placeholder/200/200",
                category: "tas wanita",
                shortName: "Tas Selempang", // PERBAIKAN: Menambahkan nama pendek untuk tampilan simple
                isMall: true, // PERBAIKAN TAMPILAN CARD: Flag untuk sellzio Mall
                rating: 4.7, // PERBAIKAN TAMPILAN CARD: Rating produk
                sold: 9, // PERBAIKAN TAMPILAN CARD: Jumlah terjual
                shipping: "Pengiriman Instan", // PERBAIKAN TAMPILAN CARD: Info pengiriman
                cod: false // PERBAIKAN BAGIAN 2: Tambahkan flag untuk COD
            },
			
			 {
                id: 9,
    name: "Kacamata Polarized Anti UV",
    price: "Rp 250.000",
    originalPrice: "Rp 350.000",
    discount: "28%",
    image: "/api/placeholder/200/200",
    category: "aksesoris",
    shortName: "Kacamata Polarized",
    isMall: true,
    rating: 4.7,
    sold: 43,
    shipping: "Pengiriman Reguler",
    cod: true
            },
			
            {
                id: 10,
                name: "Robot Vacuum Cleaner Smart",
                price: "Rp 2.499.000",
                originalPrice: "Rp 2.999.000", // PERBAIKAN BAGIAN 2: Tambahkan harga asli
                discount: "17%", // PERBAIKAN BAGIAN 2: Tambahkan diskon
                image: "/api/placeholder/200/200",
                category: "elektronik",
                shortName: "Robot Vacuum", // PERBAIKAN: Menambahkan nama pendek untuk tampilan simple
                isMall: false, // PERBAIKAN TAMPILAN CARD: Flag untuk sellzio Mall
                rating: 4.5, // PERBAIKAN TAMPILAN CARD: Rating produk
                sold: 42, // PERBAIKAN TAMPILAN CARD: Jumlah terjual
                shipping: "Pengiriman Reguler", // PERBAIKAN TAMPILAN CARD: Info pengiriman
                cod: true // PERBAIKAN BAGIAN 2: Tambahkan flag untuk COD
            }
        ];

        // PERBAIKAN 4: Variabel untuk filter
        let currentFilter = 'terkait';
        let priceSortDirection = 'asc'; // 'asc' untuk termurah, 'desc' untuk termahal
        
        // PERBAIKAN: Flag untuk menandai apakah hasil pencarian sedang ditampilkan
        let isSearchResultShown = false;
        
        // PERBAIKAN: Fungsi untuk memastikan tab filter terlihat
        function forceShowFilterTabs() {
            if (!filterTabs) return;
            
            // Pastikan overlay tidak dalam mode khusus yang menyembunyikan filter tabs
            if (searchOverlay.classList.contains('not-found-mode') || 
                searchOverlay.classList.contains('other-products-mode')) {
                return; // Jangan tampilkan filter tabs dalam mode-mode khusus
            }
            
            // Pastikan tab filter terlihat
            filterTabs.style.display = 'block';
            filterTabs.style.visibility = 'visible';
            filterTabs.style.opacity = '1';
            
            console.log("Tab filter dipaksa tampil:", filterTabs.style.display);
        }

        // PERBAIKAN: Fungsi untuk memastikan tab filter tersembunyi
        function forceHideFilterTabs() {
            if (!filterTabs) return;
            
            // Pastikan tab filter tersembunyi
            filterTabs.style.display = 'none';
            filterTabs.style.visibility = 'hidden';
            filterTabs.style.opacity = '0';
            
            console.log("Tab filter dipaksa sembunyi:", filterTabs.style.display);
        }
        
        // PERBAIKAN: Fungsi untuk mengaktifkan mode hasil pencarian tidak ditemukan
function activateNotFoundMode() {
    // Tambahkan class khusus ke searchOverlay
    searchOverlay.classList.add('not-found-mode');
    
    // Pastikan tab filter tidak muncul
    forceHideFilterTabs();
    
    // PERBAIKAN: Tampilkan container peringatan tidak ditemukan
    if (notFoundContainer) {
        notFoundContainer.style.display = 'block';
    }
    
    // PERBAIKAN: Sembunyikan product grid
    if (productGrid) {
        productGrid.style.display = 'none';
    }
    
    // PERBAIKAN: Tampilkan product header jika ada
    if (document.getElementById('productHeader')) {
        document.getElementById('productHeader').style.display = 'block';
    }
    
    // PERBAIKAN: Pastikan tombol "Produk Lainnya" selalu terlihat
    const productAction = document.querySelector('.product-action');
    if (productAction) {
        productAction.style.display = 'flex';
        productActionVisible = true;
    }
    
    console.log("Mode 'hasil tidak ditemukan' diaktifkan");
}
        
        // PERBAIKAN: Fungsi untuk mengaktifkan mode produk lainnya
        function activateOtherProductsMode() {
            // Tambahkan class khusus ke searchOverlay
            searchOverlay.classList.add('other-products-mode');
            
            // Pastikan tab filter tidak muncul
            forceHideFilterTabs();
            
            console.log("Mode 'produk lainnya' diaktifkan");
        }
        
        // PERBAIKAN: Fungsi untuk menonaktifkan mode-mode khusus
function deactivateSpecialModes() {
    // Hapus semua class mode khusus
    searchOverlay.classList.remove('not-found-mode', 'other-products-mode');
    
    // PERBAIKAN: Sembunyikan pesan tidak ditemukan
    if (notFoundContainer) {
        notFoundContainer.style.display = 'none';
    }
    
    // PERBAIKAN: Sembunyikan product header
    if (document.getElementById('productHeader')) {
        document.getElementById('productHeader').style.display = 'none';
    }
    
    console.log("Mode khusus dinonaktifkan");
}
        
        // Fungsi untuk menampilkan produk lainnya
        function showOtherProducts() {
            // PERBAIKAN: Aktifkan mode produk lainnya
            activateOtherProductsMode();
            
            // Sembunyikan tampilan tidak ditemukan
            notFoundContainer.style.display = 'none';
            
            // Tampilkan container produk lainnya
            productsContainer.style.display = 'block';
            
            // Sembunyikan tombol "Produk Lainnya"
            const productAction = document.querySelector('.product-action');
            if (productAction) {
                productAction.style.display = 'none';
                productActionVisible = false;
            }
            
            // Isi grid produk lainnya
            otherProductsGrid.innerHTML = '';
            
            // PERBAIKAN: Gunakan tampilan card standar untuk produk lainnya
            sampleProducts.forEach(product => {
                const productCard = document.createElement('div');
                productCard.className = 'product-card'; // Tampilan standar
                productCard.style.position = 'relative'; // Tambahkan posisi relatif untuk icon COD
                
                // PERBAIKAN: Tambahkan icon mall, rating, jumlah terjual, dan info pengiriman dengan harga coret dan diskon
                productCard.innerHTML = `
                    <img src="${product.image}" alt="${product.name}" class="product-img">
                    <div class="product-info">
                        <div class="product-name">
                            ${product.isMall ? '<span class="sellzio-mall-icon">Mall</span>' : ''}
                            ${product.name}
                        </div>
                        <div class="product-rating-sold">
                            <div class="product-rating"><i class="fa fa-star"></i>${product.rating}</div>
                            <div class="product-sold">Terjual ${product.sold}</div>
                        </div>
                        <div class="product-shipping"><i class="fa fa-truck"></i>${product.shipping}</div>
                        <div class="product-price-container">
                            <div class="product-price">${product.price}</div>
                            <div class="product-price-original">${product.originalPrice}</div>
                            <div class="product-discount">-${product.discount}</div>
                        </div>
                    </div>
                    ${product.cod ? '<div class="cod-icon"><i class="fa fa-money-bill"></i>COD</div>' : ''}
                `;
                
                // Tambahkan event click untuk mengarahkan ke Facebook
                productCard.addEventListener('click', function() {
                    window.location.href = 'https://www.facebook.com';
                });
                otherProductsGrid.appendChild(productCard);
            });
        }
 // PERBAIKAN: Fungsi untuk render produk simpel (tampilan kedua)
function renderSimpleProducts() {
    productGrid.innerHTML = '';
    
    // PERBAIKAN: Memastikan grid produk ditampilkan
    productGrid.style.display = 'grid';
    
    // PERBAIKAN: Sembunyikan tampilan produk lainnya
    productsContainer.style.display = 'none';
    
    // PERBAIKAN: Sembunyikan tampilan peringatan jika ada
    notFoundContainer.style.display = 'none';
    
    // PERBAIKAN: Reset product header
    document.getElementById('productHeader').style.display = 'none';
    
    // PERBAIKAN: Tampilkan product title
    if (document.querySelector('.product-title')) {
        document.querySelector('.product-title').style.display = 'block';
    }
    
    // PERBAIKAN: Tampilkan product suggestions
    if (document.querySelector('.product-suggestions')) {
        document.querySelector('.product-suggestions').style.display = 'block';
    }
    
    // Gunakan tampilan card simpel
    sampleProducts.forEach(product => {
        const productCard = document.createElement('div');
        productCard.className = 'simple-product-card'; // Tampilan simpel
        productCard.innerHTML = `
            <img src="${product.image}" alt="${product.shortName}" class="product-img">
            <div class="simple-product-name">${product.shortName}</div>
        `;
        // Tambahkan event click untuk mengarahkan ke Facebook
        productCard.addEventListener('click', function() {
            window.location.href = 'https://www.facebook.com';
        });
        productGrid.appendChild(productCard);
    });
}

  // PERBAIKAN 4: Fungsi untuk mengaplikasikan filter pada produk
        function applyProductFilter(products) {
            let result = [...products];
            
            // Implementasi filter sesuai dengan tab yang aktif
            switch (currentFilter) {
                case 'terlaris':
                    result.sort((a, b) => b.sold - a.sold);
                    break;
                case 'terbaru':
                    // Dalam contoh ini, kita anggap id produk terbaru memiliki id terbesar
                    result.sort((a, b) => b.id - a.id);
                    break;
                case 'harga':
                    // Sort berdasarkan harga (hilangkan format dan konversi ke angka)
                    result.sort((a, b) => {
                        const priceA = parseInt(a.price.replace(/\D/g, ''));
                        const priceB = parseInt(b.price.replace(/\D/g, ''));
                        return priceSortDirection === 'asc' ? priceA - priceB : priceB - priceA;
                    });
                    break;
                default: // 'terkait' atau default
                    // Tidak perlu sorting khusus
                    break;
            }
            
            return result;
        }
        
        // PERBAIKAN: Render product cards standar - untuk hasil pencarian
        function renderStandardProducts(filterText = '', fromButtonClick = false) {
            // PERBAIKAN: Nonaktifkan mode khusus
            deactivateSpecialModes();
            // PERBAIKAN 1: Tandai bahwa hasil pencarian sedang ditampilkan jika dari button click
if (fromButtonClick) {
    isSearchResultShown = true;
    // Sembunyikan judul "Produk Populer" saat menampilkan hasil pencarian
    document.querySelector('.product-title').style.display = 'none';
    // Sembunyikan sugesti keyword dan elemen terkait
    textSuggestions.style.display = 'none';
    document.querySelector('.see-more-container').style.display = 'none';
    document.querySelector('.additional-suggestions').style.display = 'none';
    
    // Sembunyikan "Hapus riwayat pencarian" dan "Sedang Trend"
if (clearHistoryBtn) clearHistoryBtn.style.display = 'none';
const trendPill = document.querySelector('.trend-pill');
if (trendPill) trendPill.style.display = 'none';
    
    // PERBAIKAN: Tampilkan tab filter dengan memaksa style terlihat
    forceShowFilterTabs();
}
             else {
                // Saat bukan dari button click (misalnya saat input), tetap tampilkan sugesti
                document.querySelector('.product-title').style.display = 'block';
                textSuggestions.style.display = 'block';
                document.querySelector('.see-more-container').style.display = 'block';
                document.querySelector('.additional-suggestions').style.display = 'block';
            }
            
            productGrid.innerHTML = '';
            
            let filteredProducts = sampleProducts;
            
            // Filter produk berdasarkan teks pencarian jika ada
            if (filterText) {
                filteredProducts = sampleProducts.filter(product => 
                    product.name.toLowerCase().includes(filterText.toLowerCase()) ||
                    product.category.toLowerCase().includes(filterText.toLowerCase())
                );
            }
            
            // Cek apakah hasil pencarian kosong
            if (filteredProducts.length === 0 && filterText !== '' && fromButtonClick) {
                // PERBAIKAN: Aktifkan mode khusus untuk hasil tidak ditemukan
                activateNotFoundMode();
                
                // Tampilkan product header
                document.getElementById('productHeader').style.display = 'block';
                
                // Tampilkan pesan tidak ditemukan
                notFoundContainer.style.display = 'block';
                productGrid.style.display = 'none';
                document.querySelector('.product-title').style.display = 'none';
                
                // Sembunyikan sugesti keyword lainnya
                textSuggestions.style.display = 'none';
                document.querySelector('.see-more-container').style.display = 'none';
                document.querySelector('.additional-suggestions').style.display = 'none';
                
                // Reset tampilan produk lainnya
                productsContainer.style.display = 'none';
                
                // Pastikan tombol "Produk Lainnya" terlihat
                const productAction = document.querySelector('.product-action');
                if (productAction) {
                    productAction.style.display = 'flex';
                    productActionVisible = true;
                }
            } else {
                // Sembunyikan product header
                document.getElementById('productHeader').style.display = 'none';
                
                // Sembunyikan pesan tidak ditemukan dan tampilkan produk
                notFoundContainer.style.display = 'none';
                productsContainer.style.display = 'none';
                productGrid.style.display = 'grid';
                
                // PERBAIKAN 4: Aplikasikan filter pada produk
                if (fromButtonClick) {
                    // Filter tabs harus ditampilkan jika dari button click
                    forceShowFilterTabs();
                    
                    // Aplikasikan filter
                    filteredProducts = applyProductFilter(filteredProducts);
                    
                    // Paksa tampilkan tab dengan delay
                    setTimeout(forceShowFilterTabs, 100);
                } else {
                    forceHideFilterTabs();
                }
                
                // PERBAIKAN: Render produk dengan tampilan standar
                filteredProducts.forEach(product => {
                    const productCard = document.createElement('div');
                    productCard.className = 'product-card'; // Tampilan standar
                    productCard.style.position = 'relative'; // Tambahkan posisi relatif untuk icon COD
                    
                    // PERBAIKAN: Tambahkan icon mall, rating, jumlah terjual, dan info pengiriman dengan harga coret dan diskon
                    productCard.innerHTML = `
                        <img src="${product.image}" alt="${product.name}" class="product-img">
                        <div class="product-info">
                            <div class="product-name">
                                ${product.isMall ? '<span class="sellzio-mall-icon">Mall</span>' : ''}
                                ${product.name}
                            </div>
                            <div class="product-rating-sold">
                                <div class="product-rating"><i class="fa fa-star"></i>${product.rating}</div>
                                <div class="product-sold">Terjual ${product.sold}</div>
                            </div>
                            <div class="product-shipping"><i class="fa fa-truck"></i>${product.shipping}</div>
                            <div class="product-price-container">
                                <div class="product-price">${product.price}</div>
                                <div class="product-price-original">${product.originalPrice}</div>
                                <div class="product-discount">-${product.discount}</div>
                            </div>
                        </div>
                        ${product.cod ? '<div class="cod-icon"><i class="fa fa-money-bill"></i>COD</div>' : ''}
                    `;
                    
                    // Tambahkan event click untuk mengarahkan ke Facebook
                    productCard.addEventListener('click', function() {
                        window.location.href = 'https://www.facebook.com';
                    });
                    productGrid.appendChild(productCard);
                });
            }
        }

// PERBAIKAN: Fungsi untuk menampilkan/menyembunyikan ikon silang yang lebih responsif
function toggleClearIcon(input) {
    if (input && input.value && input.value.trim().length > 0) {
        expandedClearSearchIcon.style.display = 'flex';
    } else {
        expandedClearSearchIcon.style.display = 'none';
    }
}
        
// PERBAIKAN: Fungsi untuk membersihkan input pencarian
function clearSearchInput() {
    expandedSearchInput.value = '';
    expandedSearchInput.focus();
    expandedClearSearchIcon.style.display = 'none';
    expandedSearchPlaceholder.classList.remove('force-hide-placeholder');
	
	// PERBAIKAN: Sembunyikan overlay pencarian
if (suggestionsOverlay) {
    suggestionsOverlay.style.display = 'none';
}

// PERBAIKAN: Hapus class show-suggestions dari body
document.body.classList.remove('show-suggestions');

// PERBAIKAN: Sembunyikan popup keyword suggestions
if (keywordSuggestionsPopup) {
    keywordSuggestionsPopup.style.display = 'none';
}
    
    // PERBAIKAN: Sembunyikan prediksi keyword
    hideKeywordPredictions();
    
    // PERBAIKAN: Tampilkan kembali elemen "Hapus riwayat pencarian" dan "Sedang Trend"
    if (clearHistoryBtn) clearHistoryBtn.style.display = 'block';
    const trendPill = document.querySelector('.trend-pill');
    if (trendPill) trendPill.style.display = 'inline-flex';
    
    // PERBAIKAN: Reset isSearchResultShown saat membersihkan input
    isSearchResultShown = false;
    
    // PERBAIKAN: Nonaktifkan mode khusus
    deactivateSpecialModes();
    
    // PERBAIKAN: Sembunyikan tab filter saat membersihkan pencarian
    forceHideFilterTabs();
    
    // PERBAIKAN: Sembunyikan tampilan produk lainnya
    productsContainer.style.display = 'none';
    
    // PERBAIKAN: Sembunyikan tampilan peringatan jika ada
    notFoundContainer.style.display = 'none';
    
    // PERBAIKAN: Reset product header
    document.getElementById('productHeader').style.display = 'none';
    
    // PERBAIKAN: Reset tampilan sugesti
    textSuggestions.style.display = 'block';
    
    // Pastikan container tombol berjejer juga ditampilkan
    const buttonContainer = textSuggestions.querySelector('.keyword-button-container');
    if (buttonContainer) {
        buttonContainer.style.display = 'flex';
    }
    
    // PERBAIKAN: Reset extended suggestions
    const extendedSuggestions = document.getElementById('extendedSuggestions');
    if (extendedSuggestions) {
        extendedSuggestions.style.display = 'none';
    }
    
    // PERBAIKAN: Reset tombol Lihat Lainnya dan additional suggestions
    document.querySelector('.see-more-container').style.display = 'block';
    document.querySelector('.additional-suggestions').style.display = 'block';
    document.querySelector('.product-title').style.display = 'block';
    
    // PERBAIKAN: Kembalikan tombol Lihat Lainnya ke posisi awal
    if (document.querySelector('.see-more-container').parentNode === additionalSuggestions) {
        const extendedSuggestionsElem = document.querySelector('#extendedSuggestions');
        if (extendedSuggestionsElem && textSuggestions.parentNode) {
            textSuggestions.parentNode.insertBefore(
                document.querySelector('.see-more-container'), 
                extendedSuggestionsElem.nextSibling
            );
        }
    }
    
    // PERBAIKAN: Reset tombol Lihat Lainnya
    seeMoreBtn.innerHTML = '<i class="fa fa-plus-circle"></i><span>Lihat Lainnya</span>';
    
    // PERBAIKAN: Tampilkan tampilan simple setelah menghapus pencarian
    renderSimpleProducts();
    
    // Tampilkan kembali sugesti
    showSuggestions();
    
    // PERBAIKAN BARU: Hapus saran koreksi jika ada
	const correctionSuggestion = document.getElementById('correctionSuggestion');
	if (correctionSuggestion) {
		correctionSuggestion.remove();
	}

	// Reset toggle icon saat input dibersihkan
	toggleSearchFilterIcon(true);
	}


// Fungsi untuk menampilkan prediksi keyword
function showKeywordPredictions(inputText) {
    // Jika input kosong atau terlalu pendek, sembunyikan prediksi
    if (!inputText || inputText.length === 0) {
        hideKeywordPredictions();
        return;
    }
    
    // Kasus khusus: input diakhiri spasi diikuti beberapa huruf (misalnya "tas s")
let predictions = [];
if (inputText.includes(' ') && !inputText.endsWith(' ')) {
    const lastSpaceIndex = inputText.lastIndexOf(' ');
    const beforeSpace = inputText.substring(0, lastSpaceIndex).trim();
    const afterSpace = inputText.substring(lastSpaceIndex + 1).trim();
    
    // Jika ada kata sebelum spasi dan ada sesuatu setelah spasi
    if (beforeSpace && afterSpace) {
        console.log(`Deteksi input dengan format "${beforeSpace} ${afterSpace}"`);
        
        // Dapatkan prediksi dengan filter kata kedua berdasarkan huruf setelah spasi
        const filteredNextWordPredictions = findNextWordPredictions(beforeSpace, afterSpace);
        
        // Konversi ke format yang dibutuhkan
        const priorityPredictions = filteredNextWordPredictions.map(pred => ({
            text: pred,
            type: 'product',  // Selalu menggunakan icon keranjang
            relevance: 1000   // Prioritas sangat tinggi
        }));
        
        // Gabungkan dengan prediksi biasa yang RELEVAN dengan kata pertama
        const regularPredictions = generatePredictions(inputText)
            .filter(pred => {
                // Filter prediksi yang dimulai dengan kata pertama
                // atau mengandung kata pertama di awal kata
                const predictionWords = pred.text.toLowerCase().split(' ');
                return predictionWords[0] === beforeSpace.toLowerCase() ||
                       pred.text.toLowerCase().startsWith(beforeSpace.toLowerCase() + ' ');
            });
        
        // Gabungkan prediksi terfilter
        predictions = [...priorityPredictions, ...regularPredictions];
        
        // Jika tidak ada prediksi, gunakan generatePredictions normal tapi masih filter relevance
        if (predictions.length === 0) {
            predictions = generatePredictions(beforeSpace + ' ')
                .filter(pred => {
                    const predictionWords = pred.text.toLowerCase().split(' ');
                    return predictionWords[0] === beforeSpace.toLowerCase() ||
                           pred.text.toLowerCase().startsWith(beforeSpace.toLowerCase() + ' ');
                });
        }
    } else {
        // Jalankan prediksi normal
        predictions = generatePredictions(inputText);
    }
} else {
    // Jalankan prediksi normal
    predictions = generatePredictions(inputText);
}

// Log untuk debugging
console.log("Prediksi akhir:", predictions.map(p => p.text));
    
    // Jika tidak ada prediksi, sembunyikan kontainer
    if (predictions.length === 0) {
        hideKeywordPredictions();
        return;
    }
    
    // Kosongkan kontainer
    keywordPredictions.innerHTML = '';
    
    // Kasus khusus: jika input diakhiri dengan spasi, tambahkan prediksi dua kata
    let additionalPredictions = [];
    
    if (inputText.endsWith(' ') && inputText.trim().length > 0) {
        const searchWord = inputText.trim();
        const twoWordPredictions = findNextWordPredictions(searchWord);
        
        // Tambahkan ke daftar prediksi jika belum ada
        twoWordPredictions.forEach(predText => {
            // Pastikan prediksi ini belum ada di daftar predictions
            if (!predictions.some(p => p.text.toLowerCase() === predText.toLowerCase())) {
                additionalPredictions.push({
                    text: predText,
                    type: 'product', // Gunakan tipe produk untuk ikon keranjang
                    relevance: 100 // Prioritas tinggi
                });
            }
        });
    }
    
    // Gabungkan semua prediksi dan urutkan berdasarkan relevansi
    const allPredictions = [...predictions, ...additionalPredictions]
        .sort((a, b) => b.relevance - a.relevance);
    
    // Jika input diakhiri spasi, prioritaskan prediksi dua kata untuk ikon keranjang
    const prioritizedPredictions = inputText.endsWith(' ') ? 
        allPredictions.sort((a, b) => {
            // Jika keduanya produk, prioritaskan yang hanya memiliki 2 kata
            if (a.type === 'product' && b.type === 'product') {
                const aWordCount = a.text.split(' ').length;
                const bWordCount = b.text.split(' ').length;
                
                if (aWordCount === 2 && bWordCount !== 2) return -1;
                if (bWordCount === 2 && aWordCount !== 2) return 1;
            }
            
            // Prioritaskan produk 2 kata di atas jenis prediksi lainnya
            if (a.type === 'product' && a.text.split(' ').length === 2) return -1;
            if (b.type === 'product' && b.text.split(' ').length === 2) return 1;
            
            // Default ke relevansi
            return b.relevance - a.relevance;
        }) : 
        allPredictions;
    
    // Batasi prediksi antara 4-12 item
    const limitedPredictions = prioritizedPredictions.slice(0, Math.max(4, Math.min(12, prioritizedPredictions.length)));
    
    // PERBAIKAN: Log prediksi yang ditampilkan untuk debugging
    console.log("Prediksi yang ditampilkan:", limitedPredictions.map(p => `${p.text} (${p.type})`));
    
    // Tambahkan item prediksi ke kontainer
    limitedPredictions.forEach(prediction => {
        const predictionItem = document.createElement('div');
        predictionItem.className = 'prediction-item';
        
        // Tentukan icon berdasarkan jenis prediksi
        let iconClass = 'fa-search';
        if (prediction.type === 'history') {
            iconClass = 'fa-history';
        } else if (prediction.type === 'product') {
            iconClass = 'fa-cart-shopping';
        } else if (prediction.type === 'trending') {
            iconClass = 'fa-arrow-trend-up';
        }
        
        // Konversi teks ke Title Case untuk produk dan trending
        let displayText = prediction.text;
        if (prediction.type === 'product' || prediction.type === 'trending') {
            displayText = toTitleCase(prediction.text);
        }
        
        // Buat teks dengan highlight untuk bagian yang cocok
        const highlightedText = highlightMatchingText(displayText, inputText);
        
        // Periksa apakah prediksi mengandung kata kunci utama dari input
const containsMainKeyword = (input, prediction) => {
    // Untuk input dengan format "kata1 kata2" (spasi di tengah)
    if (input.includes(' ') && !input.endsWith(' ')) {
        const lastSpaceIndex = input.lastIndexOf(' ');
        const beforeSpace = input.substring(0, lastSpaceIndex).trim().toLowerCase();
        
        // Periksa apakah prediksi dimulai dengan kata pertama
        return prediction.toLowerCase().startsWith(beforeSpace);
    }
    
    // Untuk input dengan spasi di akhir, periksa keseluruhan frasa
    if (input.endsWith(' ')) {
        const inputPhrase = input.trim().toLowerCase();
        return prediction.toLowerCase().startsWith(inputPhrase);
    }
    
    // Untuk input normal, gunakan pendekatan dinamis
    // Dapatkan kata-kata dari input
    const inputWords = input.toLowerCase().trim().split(' ');
    
    // Kata pertama selalu dianggap sebagai kata kunci utama
    if (inputWords.length > 0) {
        const mainWord = inputWords[0];
        if (mainWord.length >= 2) {
            // Periksa apakah prediksi mengandung kata kunci utama
            return prediction.toLowerCase().includes(mainWord);
        }
    }
    
    // Fallback jika tidak ada kata yang cukup panjang
    return false;
};
        
        // Tentukan apakah ikon harus berwarna oranye
        const isRelevant = containsMainKeyword(inputText, prediction.text);
        
        predictionItem.innerHTML = `
    <span class="prediction-icon ${isRelevant ? 'matched' : ''}"><i class="fa ${iconClass}"></i></span>
    <span class="prediction-text">${highlightedText}</span>
`;

        // Tambahkan event listener
        predictionItem.addEventListener('click', function() {
            // Isi input dengan prediksi
            expandedSearchInput.value = prediction.text;
            expandedSearchPlaceholder.classList.add('force-hide-placeholder');
            expandedClearSearchIcon.style.display = 'flex';
            
            // Sembunyikan prediksi
            hideKeywordPredictions();
            
            // Tambahkan ke riwayat interaksi
            addToUserInteractionHistory(prediction.text);
            
            // Reset lastPredictionInput agar prediksi berikutnya bersih
            localStorage.setItem('lastPredictionInput', prediction.text);
            
            // Jalankan pencarian
            executeSearch();
        });
        
        keywordPredictions.appendChild(predictionItem);
    });
    
    // Tampilkan kontainer
    keywordPredictions.style.display = 'block';

    // Biarkan tinggi kontainer mengikuti jumlah item (hapus batasan tinggi)
    keywordPredictions.style.height = 'auto';
    // Hapus maxHeight agar tidak ada scroll internal
    keywordPredictions.style.maxHeight = 'none';
    
    // Sembunyikan sugesti lain
    hideSuggestions();
}
    
    
// Fungsi untuk menyembunyikan prediksi keyword
function hideKeywordPredictions() {
    if (keywordPredictions) {
        keywordPredictions.style.display = 'none';
    }
}

// Fungsi untuk menyembunyikan sugesti lain
function hideSuggestions() {
    // Sembunyikan text suggestions
    if (textSuggestions) textSuggestions.style.display = 'none';
    
    // Sembunyikan container tombol juga
    const buttonContainer = textSuggestions ? textSuggestions.querySelector('.keyword-button-container') : null;
    if (buttonContainer) {
        buttonContainer.style.display = 'none';
    }
    
    // Sembunyikan extended suggestions
    const extendedSuggestions = document.getElementById('extendedSuggestions');
    if (extendedSuggestions) {
        extendedSuggestions.style.display = 'none';
    }
    
    // Sembunyikan tombol Lihat Lainnya
    if (document.querySelector('.see-more-container')) {
        document.querySelector('.see-more-container').style.display = 'none';
    }
    
    // Sembunyikan additional suggestions
    if (document.querySelector('.additional-suggestions')) {
        document.querySelector('.additional-suggestions').style.display = 'none';
    }
    
    if (document.querySelector('.product-title')) {
        document.querySelector('.product-title').style.display = 'none';
    }
    
    // Sembunyikan "Hapus riwayat pencarian" dan "Sedang Trend"
    if (clearHistoryBtn) clearHistoryBtn.style.display = 'none';
    const trendPill = document.querySelector('.trend-pill');
    if (trendPill) trendPill.style.display = 'none';
    
    // Sembunyikan product suggestions jika mode prediksi
    if (document.querySelector('.product-suggestions')) {
        document.querySelector('.product-suggestions').style.display = 'none';
    }
}

// Fungsi untuk menampilkan kembali sugesti
function showSuggestions() {
    // Tampilkan kembali text suggestions
    if (textSuggestions) textSuggestions.style.display = 'block';
    
    // Pastikan container tombol-tombol terlihat
    const buttonContainer = textSuggestions.querySelector('.keyword-button-container');
    if (buttonContainer) {
        buttonContainer.style.display = 'flex';
    }
    
    // Reset extended suggestions ke tersembunyi
    const extendedSuggestions = document.getElementById('extendedSuggestions');
    if (extendedSuggestions) {
        extendedSuggestions.style.display = 'none';
    }
    
    // Tampilkan tombol Lihat Lainnya
    if (document.querySelector('.see-more-container')) {
        document.querySelector('.see-more-container').style.display = 'block';
    }
    
    if (document.querySelector('.product-title')) {
        document.querySelector('.product-title').style.display = 'block';
    }
    
    // Tampilkan kembali "Hapus riwayat pencarian" dan "Sedang Trend"
    if (clearHistoryBtn) clearHistoryBtn.style.display = 'block';
    const trendPill = document.querySelector('.trend-pill');
    if (trendPill) trendPill.style.display = 'inline-flex';
    
    // Tampilkan kembali additional suggestions
    if (document.querySelector('.additional-suggestions')) {
        document.querySelector('.additional-suggestions').style.display = 'block';
    }
    
    // Tampilkan kembali product suggestions
    if (document.querySelector('.product-suggestions')) {
        document.querySelector('.product-suggestions').style.display = 'block';
    }
    
    // Pastikan tombol Lihat Lainnya dalam posisi default dan di tempat yang benar
    seeMoreBtn.innerHTML = '<i class="fa fa-plus-circle"></i><span>Lihat Lainnya</span>';
    
    // Kembalikan tombol ke posisi awal jika perlu
    if (document.querySelector('.see-more-container') && 
        document.querySelector('.see-more-container').parentNode === additionalSuggestions) {
        
        const extendedSuggestionsElem = document.querySelector('#extendedSuggestions');
        if (extendedSuggestionsElem && textSuggestions && textSuggestions.parentNode) {
            textSuggestions.parentNode.insertBefore(
                document.querySelector('.see-more-container'), 
                extendedSuggestionsElem.nextSibling
            );
        }
    }
}

// Fungsi untuk highlight teks yang cocok
function highlightMatchingText(text, input) {
    if (!input || input.trim() === '') return text;
    
    // Buat tekstual HTML dari teks asli
    let result = text;
    
    // Kasus khusus untuk format "kata1 kata2" (spasi di tengah, bukan di akhir)
if (input.includes(' ') && !input.endsWith(' ')) {
    const lastSpaceIndex = input.lastIndexOf(' ');
    const beforeSpace = input.substring(0, lastSpaceIndex).trim().toLowerCase();
    const afterSpace = input.substring(lastSpaceIndex + 1).trim().toLowerCase();
    
    if (beforeSpace && afterSpace) {
        // Cari apakah teks dimulai dengan kombinasi kata
        const searchPattern = (beforeSpace + ' ' + afterSpace).toLowerCase();
        const textLower = text.toLowerCase();
        
        if (textLower.startsWith(searchPattern)) {
            // Highlight seluruh pola pencarian
            const patternLength = searchPattern.length;
            return `<span class="highlighted">${text.substring(0, patternLength)}</span>${text.substring(patternLength)}`;
        } 
        else if (textLower.startsWith(beforeSpace)) {
            // Highlight kata pertama + huruf awal dari kata kedua
            const partToHighlight = beforeSpace + ' ' + afterSpace;
            const startOfSecond = textLower.indexOf(beforeSpace + ' ') + beforeSpace.length + 1;
            const endHighlight = startOfSecond + afterSpace.length;
            
            if (startOfSecond > beforeSpace.length) {
                return `<span class="highlighted">${text.substring(0, endHighlight)}</span>${text.substring(endHighlight)}`;
            }
        }
    }
}
    
    // Persiapkan input untuk highlight
    const inputLower = input.toLowerCase().trim();
    const words = inputLower.split(' ').filter(word => word.length > 0);
    
    // Jika input diakhiri dengan spasi, highlight semua kata sebelumnya
    if (input.endsWith(' ')) {
        // Kasus khusus untuk input berakhir dengan spasi - highlight semua kata input
        if (words.length > 0) {
            // Ambil semua kata input tanpa spasi di akhir
            const highlightPhrase = words.join(' ');
            
            // Periksa apakah teks dimulai dengan frasa input
            const textLower = text.toLowerCase();
            
            if (textLower.startsWith(highlightPhrase)) {
                // Highlight frasa input di awal teks
                const phraseLength = highlightPhrase.length;
                result = `<span class="highlighted">${text.substring(0, phraseLength)}</span>${text.substring(phraseLength)}`;
            } else {
                // Coba highlight frasa di mana saja dalam teks
                const startIndex = textLower.indexOf(highlightPhrase);
                if (startIndex >= 0) {
                    const endIndex = startIndex + highlightPhrase.length;
                    result = `${text.substring(0, startIndex)}<span class="highlighted">${text.substring(startIndex, endIndex)}</span>${text.substring(endIndex)}`;
                }
            }
            
            return result;
        }
    }
    
    // Jika tidak diakhiri dengan spasi, gunakan logika asli
    // Kasus 1: Input tanpa spasi akhir (mengetik kata tunggal atau lanjutan kata terakhir)
    if (!input.endsWith(' ')) {
        // Ambil kata terakhir yang sedang diketik
        const lastWord = words[words.length - 1];
        
        // Pecah teks menjadi kata-kata
        const textWords = text.toLowerCase().split(' ');
        
        // Buat array untuk menyimpan kata-kata dengan highlight
        const highlightedWords = [];
        
        // Periksa setiap kata dalam teks
        textWords.forEach((textWord, index) => {
            // Jika kata ini dimulai dengan kata terakhir yang diketik pengguna
            if (textWord.startsWith(lastWord)) {
                // Highlight awal kata sesuai dengan apa yang diketik pengguna
                const highlighted = `<span class="highlighted">${text.split(' ')[index].substring(0, lastWord.length)}</span>${text.split(' ')[index].substring(lastWord.length)}`;
                highlightedWords.push(highlighted);
            } 
            // Jika kata sebelumnya dari input cocok dengan kata di teks
            else if (words.length > 1 && words.slice(0, -1).some(w => textWord === w)) {
                highlightedWords.push(`<span class="highlighted">${text.split(' ')[index]}</span>`);
            }
            else {
                highlightedWords.push(text.split(' ')[index]);
            }
        });
        
        // Gabungkan kembali kata-kata
        result = highlightedWords.join(' ');
    }
    // Case 2: Input dengan spasi akhir (selesai mengetik kata)
    else {
        // Highlight semua kata yang cocok dengan kata input lengkap
        words.forEach(word => {
            if (word.length < 1) return;
            
            // Match kata lengkap
            const textParts = text.split(' ');
            const textPartsLower = text.toLowerCase().split(' ');
            
            for (let i = 0; i < textPartsLower.length; i++) {
                if (textPartsLower[i] === word) {
                    textParts[i] = `<span class="highlighted">${textParts[i]}</span>`;
                }
            }
            
            result = textParts.join(' ');
        });
    }
    
    return result;
}

// Fungsi untuk menghasilkan prediksi
function generatePredictions(input) {
    const inputLower = input.toLowerCase().trim();
    const words = inputLower.split(' ');
    const results = [];
    
    // PERBAIKAN: Analisis input untuk menentukan konteks
    // Cek apakah input adalah penambahan huruf pada kata terakhir dari pencarian sebelumnya
    const lastPrediction = localStorage.getItem('lastPredictionInput') || '';
    let isAppendToLastWord = false;
    
    if (lastPrediction && inputLower.startsWith(lastPrediction) && inputLower.length > lastPrediction.length) {
        isAppendToLastWord = true;
        console.log("Mendeteksi penambahan huruf ke pencarian sebelumnya:", lastPrediction, "->", inputLower);
    }
    
    // Simpan input saat ini untuk perbandingan berikutnya
    localStorage.setItem('lastPredictionInput', inputLower);
    
    // Menyimpan hasil berdasarkan kategori untuk memungkinkan duplikasi berdasarkan sumber
let historyResults = [];
let productResults = [];
let relatedResults = [];
let synonymResults = [];
let correctionResults = [];
let suggestionResults = [];

// 1. Tambahkan dari riwayat interaksi pengguna (maksimal 4)
let historyCount = 0;
for (let i = 0; i < keywordPredictionDB.userInteractionHistory.length && historyCount < 4; i++) {
    const item = keywordPredictionDB.userInteractionHistory[i];
    if (item.toLowerCase().includes(inputLower)) {
        historyResults.push({
            text: item,
            type: 'history',
            relevance: calculateRelevance(item, inputLower, 'history')
        });
        historyCount++;
    }
}
    
    // 2. Tambahkan dari keyword produk
keywordPredictionDB.productKeywords.forEach(keyword => {
    if (keyword.toLowerCase().includes(inputLower)) {
        productResults.push({
            text: keyword,
            type: 'product',
            relevance: calculateRelevance(keyword, inputLower, 'product')
        });
    }
});
    
    // Tambahkan trending keywords ke hasil prediksi
const trendingKeywords = popularKeywords.getTopKeywords(5);
trendingKeywords.forEach(keyword => {
    if (keyword.toLowerCase().includes(inputLower)) {
        // Tambahkan ke hasil dengan tipe 'trending'
        productResults.push({
            text: keyword,
            type: 'trending',
            relevance: calculateRelevance(keyword, inputLower, 'trending') + 10 // Tambahkan bobot untuk trending
        });
    }
});
    
    // Tambahkan prediksi produk bertahap
const productStepPredictions = generateProductPredictions(input);
productStepPredictions.forEach(prediction => {
    productResults.push(prediction);
});
    
    syncProductKeywords();
    // PERBAIKAN: Jika ini penambahan huruf pada kata terakhir, prioritaskan kata yang mengandung
    // kata dasar sebelumnya
    if (isAppendToLastWord) {
        // Dapatkan kata dasar (pencarian sebelumnya)
        const baseWords = lastPrediction.split(' ');
        if (baseWords.length > 0) {
            const baseWord = baseWords[0]; // Ambil kata pertama sebagai kata dasar
            if (baseWord.length >= 2) { // Pastikan kata cukup panjang untuk relevan
                // Tambahkan bobot ekstra untuk hasil yang mengandung kata dasar
                results.forEach(result => {
                    if (result.text.toLowerCase().includes(baseWord)) {
                        result.relevance += 50; // Tambah bobot tinggi untuk hasil yang mengandung kata dasar
                    }
                });
            }
        }
    }
    
    
    // 3. Tambahkan dari related keywords
words.forEach(word => {
    if (keywordPredictionDB.relatedKeywords[word]) {
        keywordPredictionDB.relatedKeywords[word].forEach(related => {
            // Hapus pengecekan duplikat di sini
            relatedResults.push({
                text: related,
                type: 'related',
                relevance: calculateRelevance(related, inputLower, 'related')
            });
        });
    }
});
    
  // 4. Tambahkan dari sinonim
words.forEach(word => {
    if (keywordPredictionDB.synonyms[word]) {
        keywordPredictionDB.synonyms[word].forEach(synonym => {
            // Ganti kata dengan sinonimnya dalam input
            const newQuery = inputLower.replace(word, synonym);
            if (newQuery !== inputLower) {
                synonymResults.push({
                    text: newQuery,
                    type: 'synonym',
                    relevance: calculateRelevance(newQuery, inputLower, 'synonym')
                });
            }
        });
    }
});
    
  // 5. Periksa kemungkinan typo
words.forEach(word => {
    if (keywordPredictionDB.typoCorrections[word]) {
        const corrected = inputLower.replace(word, keywordPredictionDB.typoCorrections[word]);
        correctionResults.push({
            text: corrected,
            type: 'correction',
            relevance: calculateRelevance(corrected, inputLower, 'correction')
        });
    }
});
    
    // 6. Tambahkan ide dari awal kata dengan relevansi terhadap kata sebelumnya
if (words.length > 1) {
    // Hanya lakukan jika ada kata sebelumnya untuk konteks
    const lastWord = words[words.length - 1]; // Kata terakhir yang sedang diketik
    const previousWords = words.slice(0, words.length - 1); // Kata-kata sebelumnya
    
    if (lastWord.length >= 1) {
        // Cari keyword yang dimulai dengan kata terakhir dan relevan dengan kata sebelumnya
        keywordPredictionDB.productKeywords.forEach(keyword => {
            const keywordWords = keyword.toLowerCase().split(' ');
            
            // Periksa apakah keyword mengandung setidaknya satu kata sebelumnya
            const isRelevant = previousWords.some(prevWord => 
                keywordWords.some(kw => kw.includes(prevWord) || prevWord.includes(kw))
            );
            
            // Hanya tampilkan jika relevan dengan kata sebelumnya
            if (isRelevant) {
                keywordWords.forEach(kw => {
                    if (kw.startsWith(lastWord)) {
                        suggestionResults.push({
                            text: keyword,
                            type: 'suggestion',
                            relevance: calculateRelevance(keyword, words.join(' '), 'suggestion') + 50 // Tambah bobot untuk kesesuaian
                        });
                    }
                });
            }
        });
    }
    
} else {
    // Jika hanya ada satu kata, tampilkan semua prediksi
    const word = words[0];
    if (word.length >= 1) {
        keywordPredictionDB.productKeywords.forEach(keyword => {
            const keywordWords = keyword.toLowerCase().split(' ');
            keywordWords.forEach(kw => {
                if (kw.startsWith(word)) {
                    suggestionResults.push({
                        text: keyword,
                        type: 'suggestion',
                        relevance: calculateRelevance(keyword, word, 'suggestion')
                    });
                }
            });
        });
    }
}
    
    // Urutkan berdasarkan relevansi
    results.sort((a, b) => b.relevance - a.relevance);
    
    // Gabungkan semua hasil
results.push(...historyResults);
results.push(...productResults);
results.push(...relatedResults);
results.push(...synonymResults);
results.push(...correctionResults);
results.push(...suggestionResults);

// Urutkan berdasarkan relevansi
results.sort((a, b) => b.relevance - a.relevance);

// Hapus duplikat namun perhatikan 'type'
// Izinkan item yang sama muncul jika tipenya berbeda
const uniqueResults = [];
const seenPairs = new Set(); // Catat kombinasi text+type

results.forEach(item => {
    const pair = `${item.text}|${item.type}`;
    if (!seenPairs.has(pair)) {
        seenPairs.add(pair);
        uniqueResults.push(item);
    }
});
    
    // Fungsi untuk sync keywords dari produk

      // Fungsi untuk sync keywords dari produk
function syncProductKeywords() {
    // Reset productKeywords ke array kosong
    let newKeywords = [];
    
    // Iterasi melalui sampleProducts untuk mengekstrak keywords
    sampleProducts.forEach(product => {
        // 1. Tambahkan nama produk lengkap
        newKeywords.push(product.name);
        
        // 2. Tambahkan nama pendek produk jika berbeda dan tidak terkandung dalam nama lengkap
        if (product.shortName && 
            product.shortName !== product.name && 
            !product.name.toLowerCase().includes(product.shortName.toLowerCase())) {
            newKeywords.push(product.shortName);
        }
        
        // 3. Tambahkan kategori produk
        newKeywords.push(product.category);
        
        // 4. Tambahkan kombinasi kategori + nama pendek hanya jika keduanya tidak terlalu mirip
        if (product.category && product.shortName && 
            !product.category.toLowerCase().includes(product.shortName.toLowerCase()) &&
            !product.shortName.toLowerCase().includes(product.category.toLowerCase())) {
            newKeywords.push(`${product.category} ${product.shortName}`);
        }
    });
    
    // Hapus duplikat dengan membandingkan versi lowercase
    const uniqueKeywords = [];
    const seen = new Set();
    
    newKeywords.forEach(keyword => {
        const lowerKeyword = keyword.toLowerCase();
        if (!seen.has(lowerKeyword)) {
            seen.add(lowerKeyword);
            uniqueKeywords.push(keyword);
        }
    });
    
    // Tambahkan ke database keyword, hindari duplikasi
    keywordPredictionDB.productKeywords = []; // Reset database
    
    uniqueKeywords.forEach(keyword => {
        // Periksa jika keyword mengandung kata yang berulang
        const words = keyword.toLowerCase().split(' ');
        const uniqueWords = [...new Set(words)];
        
        // Hanya tambahkan jika tidak ada kata yang berulang
        if (uniqueWords.length === words.length) {
            keywordPredictionDB.productKeywords.push(keyword);
        }
    });
    
    console.log("Product keywords disinkronkan:", keywordPredictionDB.productKeywords.length, "keywords");
}
    
    return uniqueResults;
}

// Fungsi untuk menghitung relevansi prediksi
function calculateRelevance(prediction, input, type) {
    let score = 0;
    
    // Bobot berdasarkan tipe
    const typeWeights = {
    'history': 100,
    'product': 80,
    'correction': 75,
    'related': 60,
    'synonym': 50,
    'suggestion': 40,
    'trending': 85 // Tambahkan ini
};
    
    // Tambahkan bobot tipe
    score += typeWeights[type] || 0;
    
    // PERBAIKAN: Cari kata-kata dalam input
    const inputWords = input.toLowerCase().split(' ');
    const predictionWords = prediction.toLowerCase().split(' ');
    const predictionLower = prediction.toLowerCase();
    
    // Jika prediksi dimulai dengan input, tambahkan skor lebih tinggi
    if (predictionLower.startsWith(input.toLowerCase())) {
        score += 30;
    }
    
    // Jika prediksi berisi input persis, tambahkan skor
    if (predictionLower.includes(input.toLowerCase())) {
        score += 20;
    }
    
    // PERBAIKAN: Bobot lebih tinggi untuk setiap kata dalam input yang ada dalam prediksi
    let matchingWords = 0;
    inputWords.forEach(word => {
        if (word.length > 0) {
            // Cek jika ada kata dalam prediksi yang mengandung kata input ini
            const hasMatch = predictionWords.some(pw => pw.includes(word));
            if (hasMatch) {
                matchingWords++;
                // PERBAIKAN: Bobot lebih tinggi untuk kata yang lebih panjang
                score += word.length * 2; // Kata panjang lebih relevan
            }
        }
    });
    
    // Tambahkan skor berdasarkan persentase kata yang cocok
    score += (matchingWords / inputWords.length) * 25;
    
    // PERBAIKAN: Jika prediksi memiliki kata yang mengandung kata input paling panjang, beri bobot tinggi
    if (inputWords.length > 0) {
        // Urutkan kata input berdasarkan panjang (dari terpanjang)
        const sortedInputWords = [...inputWords].sort((a, b) => b.length - a.length);
        const longestWord = sortedInputWords[0];
        
        if (longestWord.length >= 2 && predictionLower.includes(longestWord)) {
            score += 40; // Bobot tinggi jika mengandung kata terpanjang
        }
    }
    
    // Skor lebih rendah untuk prediksi yang terlalu panjang
    if (prediction.length > input.length + 20) {
        score -= 10;
    }
    
    // Tambahkan bobot trending jika tipenya trending
if (type === 'trending') {
    // Berikan bobot tambahan berdasarkan posisi dalam daftar trending
    const trendingKeywords = popularKeywords.getTopKeywords(5);
    const index = trendingKeywords.findIndex(k => 
        k.toLowerCase() === prediction.toLowerCase()
    );
    
    if (index !== -1) {
        score += (5 - index) * 5; // 25, 20, 15, 10, 5 poin tambahan berdasarkan posisi
    }
}
    
    return score;
}

// Fungsi untuk menambahkan ke riwayat interaksi pengguna
function addToUserInteractionHistory(text) {
    // Hindari duplikat dengan menghapus item yang sama
    const index = keywordPredictionDB.userInteractionHistory.indexOf(text);
    if (index !== -1) {
        keywordPredictionDB.userInteractionHistory.splice(index, 1);
    }
    
    // Tambahkan ke awal array
    keywordPredictionDB.userInteractionHistory.unshift(text);
    
    // Batasi jumlah item riwayat
    if (keywordPredictionDB.userInteractionHistory.length > 20) {
        keywordPredictionDB.userInteractionHistory.pop();
    }
    
    // Simpan ke localStorage jika tersedia
    try {
        localStorage.setItem('keywordPredictionHistory', JSON.stringify(keywordPredictionDB.userInteractionHistory));
    } catch (e) {
        console.log('Failed to save prediction history to localStorage', e);
    }
}

// Fungsi untuk memuat riwayat interaksi dari localStorage
function loadUserInteractionHistory() {
    try {
        const history = localStorage.getItem('keywordPredictionHistory');
        if (history) {
            keywordPredictionDB.userInteractionHistory = JSON.parse(history);
        }
    } catch (e) {
        console.log('Failed to load prediction history from localStorage', e);
    }
}
        
        // PERBAIKAN: Event listener untuk ikon silang yang lebih responsif
        // Menggunakan click dan mousedown untuk memastikan respons cepat
        expandedClearSearchIcon.addEventListener('click', function(e) {
            e.preventDefault(); // Mencegah default browser behavior
            e.stopPropagation(); // Mencegah event menyebar ke elemen lain
            clearSearchInput();
        });
        
        // PERBAIKAN: Tambahan event listener untuk responsivitas lebih baik pada mobile
        expandedClearSearchIcon.addEventListener('touchstart', function(e) {
            e.preventDefault(); // Mencegah default browser behavior
            e.stopPropagation(); // Mencegah event menyebar ke elemen lain
            clearSearchInput();
        }, { passive: false });
        
        // PERBAIKAN: Mencegah focus loss saat tombol diklik
        expandedClearSearchIcon.addEventListener('mousedown', function(e) {
            e.preventDefault(); // Mencegah default browser behavior yang mungkin menghilangkan fokus dari input
        });
        
       // Tampilkan mode pencarian
function showSearchMode() {
    console.log("Showing search mode");
    searchOverlay.style.display = 'block';
    mainHeader.style.display = 'none';
    contentArea.style.display = 'none';
    
    // PERBAIKAN: Reset tinggi dan style search-expanded
    const searchExpanded = document.querySelector('.search-expanded');
    if (searchExpanded) {
        searchExpanded.style.height = 'auto';
        searchExpanded.style.minHeight = '';
        searchExpanded.style.maxHeight = '';
        searchExpanded.style.padding = '10px 15px';
    }
    
    // PERBAIKAN: Tampilkan kembali elemen "Hapus riwayat pencarian" dan "Sedang Trend"
    if (clearHistoryBtn) clearHistoryBtn.style.display = 'block';
    const trendPill = document.querySelector('.trend-pill');
    if (trendPill) trendPill.style.display = 'inline-flex';
    
    // PERBAIKAN: Nonaktifkan mode khusus
    deactivateSpecialModes();
    
    // PERBAIKAN PENTING: Pastikan keyword suggestions benar-benar tersembunyi
    keywordSuggestions.style.display = 'none';
    keywordSuggestions.style.visibility = 'hidden';
    keywordSuggestions.style.opacity = '0';
    keywordSuggestions.style.height = '0';
    keywordSuggestions.style.overflow = 'hidden';
    keywordSuggestions.style.pointerEvents = 'none';
    
    // Reset additional suggestions ke tertutup
    additionalSuggestions.classList.remove('open');
    
    // Kembalikan tombol Lihat Lainnya ke posisi awal
    if (document.querySelector('.see-more-container').parentNode === additionalSuggestions) {
        textSuggestions.parentNode.insertBefore(document.querySelector('.see-more-container'), additionalSuggestions);
        seeMoreBtn.innerHTML = '<i class="fa fa-plus-circle"></i><span>Lihat Lainnya</span>';
    }
    
    // PERBAIKAN: Tampilkan placeholder jika input kosong
    expandedSearchPlaceholder.style.display = 'flex';
    
    // PERBAIKAN: Reset kolom pencarian expanded
    expandedSearchInput.value = '';
    expandedSearchPlaceholder.classList.remove('force-hide-placeholder');
    expandedClearSearchIcon.style.display = 'none';
    
    // PERBAIKAN: Sembunyikan tab filter saat tampilan awal
    forceHideFilterTabs();
    
    // PERBAIKAN: Reset isSearchResultShown
    isSearchResultShown = false;
    
    // PERBAIKAN: Tampilkan kembali semua elemen sugesti
    textSuggestions.style.display = 'block';
    document.querySelector('.see-more-container').style.display = 'block';
    document.querySelector('.product-title').style.display = 'block';
    
    // Pastikan tampilan responsive
suggestionsContainer.style.width = '100%';
suggestionsContainer.style.maxWidth = '800px';
suggestionsContainer.style.margin = '10px auto 0'; // Tambahkan margin atas 10px
suggestionsContainer.style.boxSizing = 'border-box';
suggestionsContainer.style.position = 'absolute';
suggestionsContainer.style.top = '60px'; // FIX: Pastikan menempel pada search bar
suggestionsContainer.style.left = '0';
suggestionsContainer.style.right = '0';
    
    expandedSearchInput.focus();
    
    // Tampilkan sugesti
    suggestionsContainer.style.display = 'block';
    
    // PERBAIKAN: Pastikan tampilan produk lainnya dan peringatan disembunyikan
    productsContainer.style.display = 'none';
    notFoundContainer.style.display = 'none';
    document.getElementById('productHeader').style.display = 'none';
    
    // PERBAIKAN: Tampilkan produk dengan layout simpel (hanya gambar dan judul 3 kata)
    renderSimpleProducts();
    
    // Sembunyikan prediksi keyword saat awal
    hideKeywordPredictions();
    
    // PERBAIKAN PENTING: Tambahan timer untuk memastikan keyword suggestions tersembunyi
    setTimeout(function() {
        keywordSuggestions.style.display = 'none';
        keywordSuggestions.style.visibility = 'hidden';
        keywordSuggestions.style.opacity = '0';
    }, 50);
    
    setTimeout(function() {
        keywordSuggestions.style.display = 'none';
        keywordSuggestions.style.visibility = 'hidden';
        keywordSuggestions.style.opacity = '0';
    }, 300);
    
    setTimeout(function() {
        keywordSuggestions.style.display = 'none';
        keywordSuggestions.style.visibility = 'hidden';
        keywordSuggestions.style.opacity = '0';
    }, 1000);
	
	 // Pastikan panel facet tersembunyi saat awal
    hideFacetPanel();
	
	// Reset toggle icon
toggleSearchFilterIcon(true);
}


// Sembunyikan mode pencarian
function hideSearchMode() {
    searchOverlay.style.display = 'none';
    mainHeader.style.display = 'block';
    contentArea.style.display = 'block';
    
    // PERBAIKAN: Nonaktifkan mode khusus
    deactivateSpecialModes();
    
    // PERBAIKAN: Sembunyikan overlay suggestions
    if (suggestionsOverlay) {
        suggestionsOverlay.style.display = 'none';
    }
    
    // PERBAIKAN: Hapus class show-suggestions dari body
    document.body.classList.remove('show-suggestions');
    
    // PERBAIKAN: Sembunyikan popup keyword suggestions
    if (keywordSuggestionsPopup) {
        keywordSuggestionsPopup.style.display = 'none';
    }
    
    // Sembunyikan prediksi keyword
    hideKeywordPredictions();
    
    // Tampilkan keyword suggestions berdasarkan ukuran layar
    if (window.innerWidth < 768) {
        keywordSuggestions.style.display = 'block';
    } else {
        keywordSuggestions.style.display = 'none';
    }
    
    suggestionsContainer.style.display = 'none';
    
    // PERBAIKAN: Teks pencarian tidak dibawa ke tampilan awal
    searchInput.value = '';
    
    // PERBAIKAN BARU: Pastikan input expanded juga dibersihkan
    expandedSearchInput.value = '';
    // Tampilkan placeholder
    expandedSearchPlaceholder.classList.remove('force-hide-placeholder');
    // Sembunyikan ikon silang
    expandedClearSearchIcon.style.display = 'none';
    
    // PERBAIKAN BUG BARU: Reset keadaan additional suggestions
    if (additionalSuggestions) {
        additionalSuggestions.classList.remove('open');
    }
    
    // PERBAIKAN BUG BARU: Kembalikan tombol "Lihat Lainnya" ke posisi awal jika perlu
    const seeMoreContainer = document.querySelector('.see-more-container');
    if (seeMoreContainer && additionalSuggestions && textSuggestions && textSuggestions.parentNode) {
        if (seeMoreContainer.parentNode === additionalSuggestions) {
            textSuggestions.parentNode.insertBefore(seeMoreContainer, additionalSuggestions);
            
            // Reset juga teks tombol
            if (seeMoreBtn) {
                seeMoreBtn.innerHTML = '<i class="fa fa-plus-circle"></i><span>Lihat Lainnya</span>';
            }
        }
    }
    
    // PERBAIKAN: Tandai bahwa tampilan expanded pernah dibuka
    searchOverlay.dataset.wasOpened = 'true';
}

      // Fungsi untuk mengisi kolom pencarian dengan teks sugesti
function fillSearchWithSuggestion(text) {
    console.log("Mengisi sugesti:", text);
    
    if (!expandedSearchInput || !text) {
        console.error("Input atau teks sugesti tidak valid");
        return;
    }
    
    // Isi nilai input
    expandedSearchInput.value = text;
    
    // Sembunyikan placeholder
    if (expandedSearchPlaceholder) {
        expandedSearchPlaceholder.classList.add('force-hide-placeholder');
    }
    
    // Tampilkan ikon silang
    if (expandedClearSearchIcon) {
        expandedClearSearchIcon.style.display = 'flex';
    }
    
    // Sembunyikan prediksi keyword
    hideKeywordPredictions();
    
    // PERBAIKAN BUG 2: Sembunyikan semua sugesti
    // Sembunyikan text-suggestions
    if (textSuggestions) {
        textSuggestions.style.display = 'none';
    }
    
    // Sembunyikan container tombol juga
    const buttonContainer = textSuggestions ? textSuggestions.querySelector('.keyword-button-container') : null;
    if (buttonContainer) {
        buttonContainer.style.display = 'none';
    }
    
    // Sembunyikan extended-suggestions
    const extendedSuggestions = document.getElementById('extendedSuggestions');
    if (extendedSuggestions) {
        extendedSuggestions.style.display = 'none';
    }
    
    // Sembunyikan tombol "Lihat Lainnya" atau "Sembunyikan"
    const seeMoreContainer = document.querySelector('.see-more-container');
    if (seeMoreContainer) {
        seeMoreContainer.style.display = 'none';
    }
    
    // Sembunyikan bagian "Sedang Trend"
    if (additionalSuggestions) {
        additionalSuggestions.style.display = 'none';
    }
    
    // Sembunyikan "Hapus riwayat pencarian"
    if (clearHistoryBtn) {
        clearHistoryBtn.style.display = 'none';
    }
    
    // Tandai bahwa ini adalah hasil pencarian
    isSearchResultShown = true;
	
	// Tambahkan ke riwayat pencarian
    addToSearchHistory(text);
    
    // Delay pendek sebelum menjalankan pencarian (memastikan UI diupdate dulu)
    setTimeout(function() {
        // Jalankan pencarian
        executeSearch();
    }, 50);
}

  // Variabel untuk menghitung jumlah pencarian
let searchCounter = 0;

// Jalankan pencarian - Mengubah ke card produk standar saat ikon search diklik
function executeSearch() {
    const searchText = expandedSearchInput.value.trim();
    console.log("Melakukan pencarian untuk: " + searchText);
	
	// PERBAIKAN BUG: Reset filter setiap kali pencarian baru dilakukan
    resetAllFilters();
	
    
    // Sembunyikan prediksi keyword
    hideKeywordPredictions();
    
    if (searchText) {
        // Catat akses untuk kata-kata dalam pencarian
        searchText.toLowerCase().split(' ').forEach(word => {
            if (word.length >= 3) {
                learningPredictions.recordAccess(word);
            }
        });
        
        // Tambahkan pencarian ke pembelajaran
        learningPredictions.learn(searchText);
        
        // Setelah menambahkan ke riwayat pencarian
    addToSearchHistory(searchText);
    
    // Update placeholder berdasarkan histori pencarian terbaru
    setTimeout(updateDynamicPlaceholders, 100);
        
        // Tambahkan ke riwayat interaksi
        addToUserInteractionHistory(searchText);
        
        // Tambahkan ke riwayat pencarian untuk sugesti keyword
        addToSearchHistory(searchText);
        
        // Tambahkan ke popularKeywords untuk melacak keyword populer
        popularKeywords.addSearch(searchText);
        
        // Manajemen database secara periodik
        searchCounter++;
        if (searchCounter >= 50) {
            searchCounter = 0;
            // Jalankan di background dengan setTimeout
            setTimeout(function() {
                learningPredictions.managePredictions();
            }, 100);
        }
        
        // PERBAIKAN: Reset filter ke "terkait" saat pencarian baru
        currentFilter = 'terkait';
        updateFilterTabsUI();
        
        // Menampilkan hasil pencarian
        try {
            // Gunakan fungsi pencarian yang ditingkatkan
            const searchResults = enhancedSearch(searchText);
            console.log("Hasil pencarian:", searchResults.length, "produk ditemukan");
            
            // Periksa jika query yang dikoreksi berbeda dari input asli
            const correctedQuery = correctTypo(searchText);
            if (correctedQuery !== searchText.toLowerCase() && correctedQuery !== searchText) {
                // Tambahkan variabel untuk melacak koreksi yang sudah diklik
                try {
                    const clickedCorrections = JSON.parse(localStorage.getItem('clickedCorrections') || '[]');
                    
                    // Hanya tampilkan saran jika belum pernah diklik sebelumnya
                    if (!clickedCorrections.includes(correctedQuery)) {
                        showCorrectionSuggestion(correctedQuery);
                    }
                } catch (e) {
                    console.error("Error saat memproses koreksi:", e);
                }
            } else {
                // Hapus saran koreksi jika ada
                const correctionSuggestion = document.getElementById('correctionSuggestion');
                if (correctionSuggestion) {
                    correctionSuggestion.remove();
                }
            }
            
            // Tampilkan hasil
            displaySearchResults(searchResults, searchText);
			
			// Sembunyikan text-suggestions
            if (textSuggestions) {
                textSuggestions.style.display = 'none';
            }
            
            // Sembunyikan extended-suggestions
            const extendedSuggestionsElem = document.getElementById('extendedSuggestions');
            if (extendedSuggestionsElem) {
                extendedSuggestionsElem.style.display = 'none';
            }
            
            // Sembunyikan tombol "Lihat Lainnya" atau "Sembunyikan"
            const seeMoreContainer = document.querySelector('.see-more-container');
            if (seeMoreContainer) {
                seeMoreContainer.style.display = 'none';
            }
            
            // Sembunyikan bagian "Sedang Trend"
            if (additionalSuggestions) {
                additionalSuggestions.style.display = 'none';
            }
            
            // Sembunyikan "Hapus riwayat pencarian"
            if (clearHistoryBtn) {
                clearHistoryBtn.style.display = 'none';
            }
            
            // Tampilkan product suggestions container jika tersembunyi
            const productSuggestions = document.querySelector('.product-suggestions');
            if (productSuggestions) {
                productSuggestions.style.display = 'block';
            }
			
			 // PERBAIKAN: Tampilkan ikon filter setelah pencarian selesai
            setTimeout(function() {
                toggleSearchFilterIcon(false);
            }, 100);
            
        } catch (error) {
            console.error("Error saat pencarian:", error);
            // Fallback: tampilkan semua produk
            displaySearchResults(sampleProducts, searchText);
        }
    } else {
        // Jika pencarian kosong, tampilkan produk dengan layout simpel
        renderSimpleProducts();
        
        // Tampilkan kembali sugesti
        showSuggestions();
        
        // Reset flag pencarian
        isSearchResultShown = false;
        
        // PERBAIKAN: Nonaktifkan mode khusus
        deactivateSpecialModes();
        
        // PERBAIKAN: Sembunyikan tab filter
        forceHideFilterTabs();
        
        // PERBAIKAN: Tambahkan penundaan tambahan untuk memastikan tab filter tersembunyi
        setTimeout(forceHideFilterTabs, 50);
        setTimeout(forceHideFilterTabs, 200);
        
        // PERBAIKAN BUG: Sembunyikan teks oranye bergerak (keyword suggestions)
        if (keywordSuggestions) {
            keywordSuggestions.style.display = 'none';
        }
        
        // PERBAIKAN BUG: Pastikan placeholder tetap terlihat dengan benar
        if (expandedSearchPlaceholder) {
            expandedSearchPlaceholder.classList.remove('force-hide-placeholder');
        }
        
        // Sembunyikan prediksi jika ada
        const predictionContainer = document.getElementById('searchPredictions');
        if (predictionContainer) {
            predictionContainer.style.maxHeight = '0';
        }
    }

    // Perbarui tampilan icon pencarian/filter
    toggleSearchFilterIcon(searchText === '');
    
    // Perbarui badge filter jika mode pencarian
    if (searchText !== '') {
        updateFilterBadge();
    }
}
  
  // Fungsi untuk menampilkan hasil pencarian
function displaySearchResults(results, searchText) {
// Toggle icon pencarian/filter
    toggleSearchFilterIcon(false);
    // PERBAIKAN: Reset tinggi search-expanded saat menampilkan hasil pencarian
    const searchExpanded = document.querySelector('.search-expanded');
    if (searchExpanded) {
        // Jika ada filter tabs yang akan ditampilkan, atur tinggi yang cukup
        if (results.length > 0) {
            searchExpanded.style.height = 'auto';
            searchExpanded.style.minHeight = '';
            searchExpanded.style.maxHeight = '';
        } else {
            // Jika tidak ada hasil, kembalikan ke ukuran default
            searchExpanded.style.height = 'auto';
            searchExpanded.style.minHeight = '';
            searchExpanded.style.maxHeight = '';
        }
    }
    
    // Sembunyikan prediksi keyword
    hideKeywordPredictions();
    
    // Sembunyikan judul "Produk Populer" saat menampilkan hasil pencarian
    if (document.querySelector('.product-title')) {
        document.querySelector('.product-title').style.display = 'none';
    }
    
    // Sembunyikan sugesti keyword dan elemen terkait
    if (textSuggestions) textSuggestions.style.display = 'none';
    if (document.querySelector('.see-more-container')) document.querySelector('.see-more-container').style.display = 'none';
    if (document.querySelector('.additional-suggestions')) document.querySelector('.additional-suggestions').style.display = 'none';
    
    // Sembunyikan "Hapus riwayat pencarian" dan "Sedang Trend"
    if (clearHistoryBtn) clearHistoryBtn.style.display = 'none';
    const trendPill = document.querySelector('.trend-pill');
    if (trendPill) trendPill.style.display = 'none';
    
    // PERBAIKAN: Tampilkan tab filter dengan memaksa style terlihat
    forceShowFilterTabs();
    
    // Tambahkan delay untuk memastikan tab filter benar-benar ditampilkan
    setTimeout(forceShowFilterTabs, 100);
    
    // Tampilkan product suggestions
    if (document.querySelector('.product-suggestions')) {
        document.querySelector('.product-suggestions').style.display = 'block';
    }
    
    // Reset dan siapkan product grid
    if (productGrid) {
        productGrid.innerHTML = '';
        productGrid.style.display = 'grid';
    }
    
    // Cek apakah hasil pencarian kosong
    if (results.length === 0) {
        // PERBAIKAN: Aktifkan mode khusus untuk hasil tidak ditemukan
        activateNotFoundMode();
        
        // Tampilkan product header
        if (document.getElementById('productHeader')) {
            document.getElementById('productHeader').style.display = 'block';
        }
        
        // Tampilkan pesan tidak ditemukan
        if (notFoundContainer) {
            notFoundContainer.style.display = 'block';
        }
        if (productGrid) {
            productGrid.style.display = 'none';
        }
    } else {
        // Sembunyikan product header
        if (document.getElementById('productHeader')) {
            document.getElementById('productHeader').style.display = 'none';
        }
        
        // Sembunyikan pesan tidak ditemukan dan tampilkan produk
        if (notFoundContainer) {
            notFoundContainer.style.display = 'none';
        }
        if (productsContainer) {
            productsContainer.style.display = 'none';
        }
        
        // Tampilkan product grid
        if (productGrid) {
            productGrid.style.display = 'grid';
        }
        
        console.log("Menampilkan " + results.length + " produk");
        
        // Aplikasikan filter
        results = applyProductFilter(results);
        
        // Render produk dengan tampilan standar
        results.forEach(product => {
            if (!productGrid) return;
            
            const productCard = document.createElement('div');
            productCard.className = 'product-card';
            productCard.style.position = 'relative';
            
            productCard.innerHTML = `
                <img src="${product.image}" alt="${product.name}" class="product-img">
                <div class="product-info">
                    <div class="product-name">
                        ${product.isMall ? '<span class="sellzio-mall-icon">Mall</span>' : ''}
                        ${product.name}
                    </div>
                    <div class="product-rating-sold">
                        <div class="product-rating"><i class="fa fa-star"></i>${product.rating}</div>
                        <div class="product-sold">Terjual ${product.sold}</div>
                    </div>
                    <div class="product-shipping"><i class="fa fa-truck"></i>${product.shipping}</div>
                    <div class="product-price-container">
                        <div class="product-price">${product.price}</div>
                        <div class="product-price-original">${product.originalPrice}</div>
                        <div class="product-discount">-${product.discount}</div>
                    </div>
                </div>
                ${product.cod ? '<div class="cod-icon"><i class="fa fa-money-bill"></i>COD</div>' : ''}
            `;
            
            productCard.addEventListener('click', function() {
                window.location.href = 'https://www.facebook.com';
            });
            productGrid.appendChild(productCard);
        });
    }
    
    // Set flag bahwa hasil pencarian sedang ditampilkan
    isSearchResultShown = true;
}
        
        // PERBAIKAN 4: Update UI untuk tab filter
        function updateFilterTabsUI() {
            // Jangan lakukan apa-apa jika tidak ada pencarian aktif
            if (!isSearchResultShown) {
                forceHideFilterTabs();
                return;
            }
            
            // Jangan tampilkan filter jika dalam mode khusus
            if (searchOverlay.classList.contains('not-found-mode') || 
                searchOverlay.classList.contains('other-products-mode')) {
                forceHideFilterTabs();
                return;
            }
            
            // Pastikan filter tabs terlihat hanya saat pencarian aktif
            forceShowFilterTabs();
            
            // Hapus kelas active dari semua tab
            document.querySelectorAll('.filter-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // Tambahkan kelas active ke tab yang dipilih
            const activeTab = document.querySelector(`.filter-tab[data-filter="${currentFilter}"]`);
            if (activeTab) {
                activeTab.classList.add('active');
            }
            
            // Update ikon sort untuk tab harga jika dipilih
            if (currentFilter === 'harga') {
                priceSort.className = priceSortDirection === 'asc' ? 'fa fa-sort-up' : 'fa fa-sort-down';
            } else {
                priceSort.className = 'fa fa-sort';
            }
        }

        // PERBAIKAN: Implementasi yang diperbaiki untuk tombol "Lihat Lainnya"
function toggleAdditionalSuggestions() {
    // Log untuk debugging
    console.log("Tombol 'Lihat Lainnya' diklik");
    
    // Sembunyikan placeholder
    expandedSearchPlaceholder.classList.add('force-hide-placeholder');
    
    // Dapatkan riwayat pencarian
    const history = getSearchHistory();
    
    // Dapatkan referensi elemen yang dibutuhkan
    const textSuggestions = document.getElementById('textSuggestions');
    const extendedSuggestions = document.getElementById('extendedSuggestions');
    
    if (extendedSuggestions) {
        const isOpen = extendedSuggestions.style.display === 'block';
        
        if (!isOpen) {
            // PERBAIKAN: Tampilkan semua keyword dalam format list
            
            // Kosongkan extendedSuggestions
            extendedSuggestions.innerHTML = '';
            
            // Dapatkan semua riwayat pencarian
            // Tampilkan semua keyword (hingga 12)
            const maxKeywords = Math.min(12, history.length);
            for (let i = 0; i < maxKeywords; i++) {
                const item = document.createElement('div');
                item.className = 'suggestion-item';
                item.setAttribute('data-text', history[i]);
                
                item.innerHTML = `
                    <span class="suggestion-icon"><i class="fa fa-history"></i></span>
                    <span>${history[i]}</span>
                `;
                
                // Tambahkan event listener untuk mengisi pencarian saat diklik
                item.addEventListener('click', function() {
                    const suggestionText = this.getAttribute('data-text');
                    fillSearchWithSuggestion(suggestionText);
                });
                
                extendedSuggestions.appendChild(item);
            }
            
            // Tampilkan extended suggestions
            extendedSuggestions.style.display = 'block';
            
            // Sembunyikan text-suggestions (tombol)
            if (textSuggestions) {
                textSuggestions.style.display = 'none';
            }
            
            // Ubah teks menjadi "Sembunyikan"
            seeMoreBtn.innerHTML = '<i class="fa fa-minus-circle"></i><span>Sembunyikan</span>';
            
            // Pastikan additionalSuggestions tetap terlihat
            additionalSuggestions.style.display = 'block';
            
            // PERBAIKAN BUG: Pindahkan tombol "Sembunyikan" ke bagian bawah setelah semua konten additionalSuggestions
            additionalSuggestions.appendChild(document.querySelector('.see-more-container'));
        } else {
            // Sembunyikan extended suggestions
            extendedSuggestions.style.display = 'none';
            
            // Tampilkan kembali text-suggestions (tombol)
            if (textSuggestions) {
                textSuggestions.style.display = 'block';
            }
            
            // Ubah teks kembali menjadi "Lihat Lainnya"
            seeMoreBtn.innerHTML = '<i class="fa fa-plus-circle"></i><span>Lihat Lainnya</span>';
            
            // Pindahkan tombol kembali ke posisi awal
            const extendedSuggestionsElem = document.querySelector('#extendedSuggestions');
            
            if (textSuggestions && textSuggestions.parentNode && extendedSuggestionsElem) {
                textSuggestions.parentNode.insertBefore(
                    document.querySelector('.see-more-container'), 
                    extendedSuggestionsElem.nextSibling
                );
            }
        }
        
        // Log untuk debugging
        console.log("Extended suggestions tampilan:", extendedSuggestions.style.display);
    }
}

// Fungsi baru untuk memulihkan tampilan expanded dengan semua komponennya
function restoreExpandedView() {
    // Tampilkan overlay dan komponen pencarian expanded
    searchOverlay.style.display = 'block';
    mainHeader.style.display = 'none';
    contentArea.style.display = 'none';
    
    // Reset state sugesti dan prediksi
    isSearchResultShown = false;
    
    // Nonaktifkan mode khusus
    deactivateSpecialModes();
    
    // Sembunyikan tab filter
    forceHideFilterTabs();
    
    // Pastikan semua komponen sugesti ditampilkan
    if (textSuggestions) textSuggestions.style.display = 'block';
    
    // Sembunyikan extended suggestions
    const extendedSuggestions = document.getElementById('extendedSuggestions');
    if (extendedSuggestions) {
        extendedSuggestions.style.display = 'none';
    }
    
    // Pastikan additional suggestions dengan "Sedang Trend" ditampilkan
    if (additionalSuggestions) {
        additionalSuggestions.style.display = 'block';
    }
    
    // Reset tombol "Lihat Lainnya"
    const seeMoreContainer = document.querySelector('.see-more-container');
    if (seeMoreContainer) {
        // Pastikan tombol tampil
        seeMoreContainer.style.display = 'block';
        
        // Jika tombol berada di dalam additionalSuggestions, pindahkan ke posisi awalnya
        if (seeMoreContainer.parentNode === additionalSuggestions && textSuggestions && textSuggestions.parentNode) {
            const extendedSuggestionsElem = document.querySelector('#extendedSuggestions');
            if (extendedSuggestionsElem) {
                textSuggestions.parentNode.insertBefore(seeMoreContainer, extendedSuggestionsElem.nextSibling);
            }
        }
        
        // Reset teks tombol "Lihat Lainnya"
        if (seeMoreBtn) {
            seeMoreBtn.innerHTML = '<i class="fa fa-plus-circle"></i><span>Lihat Lainnya</span>';
        }
    }
    
    // Pastikan judul produk tampil
    if (document.querySelector('.product-title')) {
        document.querySelector('.product-title').style.display = 'block';
    }
    
    // Tampilkan kembali elemen "Hapus riwayat pencarian" dan "Sedang Trend"
    if (clearHistoryBtn) clearHistoryBtn.style.display = 'block';
    const trendPill = document.querySelector('.trend-pill');
    if (trendPill) trendPill.style.display = 'inline-flex';
    
    // Pastikan keyword predictions disembunyikan
    hideKeywordPredictions();
    
    // Tampilkan sugesti
    if (suggestionsContainer) suggestionsContainer.style.display = 'block';
    
    // Tampilkan produk dengan layout simpel
    renderSimpleProducts();
    
    // Pastikan input benar-benar kosong
    if (expandedSearchInput) {
        expandedSearchInput.value = '';
    }
    
    // Tampilkan placeholder
    if (expandedSearchPlaceholder) {
        expandedSearchPlaceholder.classList.remove('force-hide-placeholder');
    }
    
    // Sembunyikan ikon silang
    if (expandedClearSearchIcon) {
        expandedClearSearchIcon.style.display = 'none';
    }
    
    // Focus pada input
    if (expandedSearchInput) {
        expandedSearchInput.focus();
    }
}
  
  // Event listeners
       searchInput.addEventListener('click', function(e) {
    console.log("Search input clicked");
    // Gunakan restoreExpandedView untuk memastikan semua komponen ditampilkan
    // jika tampilan expanded pernah dibuka sebelumnya
    if (searchOverlay.dataset.wasOpened === 'true') {
        restoreExpandedView();
    } else {
        showSearchMode();
        // Tandai bahwa tampilan expanded pernah dibuka
        searchOverlay.dataset.wasOpened = 'true';
    }
});
        
       // Event ketika tekan Enter di expanded search input
expandedSearchInput.addEventListener('keypress', function(e) {
    if (e.key === 'Enter') {
        e.preventDefault(); // Mencegah submit form default
        
        // Kita simulasikan klik pada icon pencarian
        if (expandedSearchIcon) {
            // Sembunyikan keyboard
            this.blur();
            
            // Sama persis dengan logika klik icon
            hideKeywordPredictions();
            
            // Eksekusi pencarian
            executeSearch();
            
            // Tampilkan tab filter
            if (isSearchResultShown) {
                forceShowFilterTabs();
                
                // PERBAIKAN TAMBAHAN: Tambahkan penundaan untuk memastikan tab filter terlihat
                setTimeout(forceShowFilterTabs, 100);
                setTimeout(forceShowFilterTabs, 300);
            }
        }
    }
});
        
        // Event listener untuk focus pada expanded search input
expandedSearchInput.addEventListener('focus', function() {
    // Sembunyikan card peringatan jika sedang ditampilkan
    if (notFoundContainer && notFoundContainer.style.display === 'block') {
        notFoundContainer.style.display = 'none';
        
        // Nonaktifkan mode khusus
        deactivateSpecialModes();
        
        // Reset product header jika ada
        if (document.getElementById('productHeader')) {
            document.getElementById('productHeader').style.display = 'none';
        }
    }
	
	// PERBAIKAN BUG: Sembunyikan facet panel ketika input mendapat fokus
    hideFacetPanel();
    
    
    // PERBAIKAN: Reset tinggi search-expanded
    const searchExpanded = document.querySelector('.search-expanded');
    if (searchExpanded) {
        searchExpanded.style.height = 'auto';
        searchExpanded.style.minHeight = '';
        searchExpanded.style.maxHeight = '';
        
        // Reset padding juga untuk memastikan ukuran konsisten
        searchExpanded.style.padding = '10px 15px';
    }
    
    // PERBAIKAN: Sembunyikan filter tabs jika visible
    if (filterTabs && filterTabs.style.display === 'block') {
        forceHideFilterTabs();
    }
	
	// PERBAIKAN BUG: Sembunyikan facet panel ketika input mendapat fokus
    hideFacetPanel();

	
	// PERBAIKAN BUG: Jika dalam mode hasil pencarian, kembalikan ke ikon pencarian
    if (isSearchResultShown) {
        toggleSearchFilterIcon(true);
    }
    
    // Jika sedang dalam mode hasil pencarian dan input tidak kosong
    if (isSearchResultShown && this.value.trim() !== '') {
        // Tampilkan prediksi keyword
        showKeywordPredictions(this.value);
	 }	
		
	// Tambahkan baris ini untuk mengembalikan ikon filter ke ikon pencarian
    if (isSearchResultShown) {
        toggleSearchFilterIcon(true);	
    }
});
        
 // Klik tombol kembali
backBtn.addEventListener('click', function() {
    // Sembunyikan mode pencarian
    hideSearchMode();
	
	// TAMBAHAN: Sembunyikan facet panel
    hideFacetPanel();
	
	// TAMBAHAN: Reset toggle icon - pastikan kembali ke icon pencarian
    toggleSearchFilterIcon(true);
	
	
    // TAMBAHAN: Hapus koreksi keyword jika ada - tambahan redundan untuk memastikan terhapus
    const correctionSuggestion = document.getElementById('correctionSuggestion');
    if (correctionSuggestion) {
        correctionSuggestion.remove();
    }
    
	// PERBAIKAN: Pastikan juga overlay suggestions disembunyikan
if (suggestionsOverlay) {
    suggestionsOverlay.style.display = 'none';
}
document.body.classList.remove('show-suggestions');

// PERBAIKAN: Sembunyikan popup keyword suggestions
if (keywordSuggestionsPopup) {
    keywordSuggestionsPopup.style.display = 'none';
}
    
    // Tambahkan event listener satu kali untuk click pada searchInput
    searchInput.addEventListener('click', function onSearchClick() {
        // Tampilkan mode pencarian dengan fungsi restore
        restoreExpandedView();
        
        // Pastikan nilai input bersih
        expandedSearchInput.value = '';
        expandedSearchPlaceholder.classList.remove('force-hide-placeholder');
        expandedClearSearchIcon.style.display = 'none';
		
		// TAMBAHAN: Pastikan facet panel tersembunyi
        hideFacetPanel();
		
		// Pastikan sugesti keyword ditampilkan dengan benar
        setTimeout(function() {
            // Perbarui sugesti keyword
            updateKeywordSuggestions();
            
            // Pastikan container tombol terlihat
            const textSuggestionsElem = document.getElementById('textSuggestions');
            if (textSuggestionsElem) {
                textSuggestionsElem.style.display = 'block';
                
                // Pastikan container tombol ditampilkan
                const buttonContainer = textSuggestionsElem.querySelector('.keyword-button-container');
                if (buttonContainer) {
                    buttonContainer.style.display = 'flex';
                }
            }
            
            // Tampilkan semua elemen sugesti
            if (document.querySelector('.see-more-container')) {
                document.querySelector('.see-more-container').style.display = 'block';
            }
            
            if (document.querySelector('.additional-suggestions')) {
                document.querySelector('.additional-suggestions').style.display = 'block';
            }
            
            if (document.querySelector('.product-title')) {
                document.querySelector('.product-title').style.display = 'block';
            }
            
            // Pastikan tombol "Lihat Lainnya" dalam posisi default
            if (seeMoreBtn) {
                seeMoreBtn.innerHTML = '<i class="fa fa-plus-circle"></i><span>Lihat Lainnya</span>';
            }
            
            // Pastikan semua extended suggestions tersembunyi
            const extendedSuggestions = document.getElementById('extendedSuggestions');
            if (extendedSuggestions) {
                extendedSuggestions.style.display = 'none';
            }
            
            // Tambahan: Tampilkan kembali riwayat pencarian & trend jika ada
            if (clearHistoryBtn) clearHistoryBtn.style.display = 'block';
            const trendPill = document.querySelector('.trend-pill');
            if (trendPill) trendPill.style.display = 'inline-flex';
            
            // Tambahan: Pastikan sugesti container ditampilkan
            if (suggestionsContainer) {
                suggestionsContainer.style.display = 'block';
            }
            
            // Tambahan: Inisialisasi ulang tombol sugesti
            initializeButtonSuggestions();
        }, 50);
        
        
        // Hapus event listener ini agar tidak dijalankan lagi
        searchInput.removeEventListener('click', onSearchClick);
    }, { once: true });
});
  
        
        // PERBAIKAN: Klik tombol search - Memaksa tab filter muncul
        expandedSearchIcon.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            console.log("Tombol search diklik");
            
            // Eksekusi pencarian
            executeSearch();
        });
        
        // PERBAIKAN: Tambahkan event listener baru untuk tombol "Lihat Lainnya"
        seeMoreBtn.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            toggleAdditionalSuggestions();
        });
        
        // PERBAIKAN 4: Event listener untuk tab filter
        document.querySelectorAll('.filter-tab').forEach(tab => {
    tab.addEventListener('click', function(e) {
        e.preventDefault();
        e.stopPropagation();
        
        console.log("Filter tab diklik:", this.getAttribute('data-filter'));
        
        const filterType = this.getAttribute('data-filter');
        
        if (filterType === 'harga' && currentFilter === 'harga') {
            // Toggle arah pengurutan untuk harga
            priceSortDirection = priceSortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            currentFilter = filterType;
        }
        
        // Update UI
        updateFilterTabsUI();
        
        // RE-RENDER DENGAN PENDEKATAN BARU: Gunakan hasil pencarian yang sudah ada
        if (isSearchResultShown) {
            const searchText = expandedSearchInput.value.trim();
            // Gunakan enhancedSearch untuk mendapatkan hasil yang sama
            const searchResults = enhancedSearch(searchText);
            
            // Tampilkan hasil yang difilter tanpa mengubah hasil pencarian
            displaySearchResults(searchResults, searchText);
        }
    });
});
        
        // Event listener untuk perubahan input
expandedSearchInput.addEventListener('input', function() {
    // Pastikan suggestions container menempel pada search bar
    if (suggestionsContainer) {
        suggestionsContainer.style.position = 'absolute';
        suggestionsContainer.style.top = '60px';
        suggestionsContainer.style.left = '0';
        suggestionsContainer.style.right = '0';
        suggestionsContainer.style.marginTop = '0';
    }
    
    // Toggle ikon silang berdasarkan isi input
    toggleClearIcon(this);
	toggleSearchFilterIcon(true);
	
	
    // PERBAIKAN: Jika input kosong, lakukan tindakan seperti mengklik ikon silang
    if (this.value.trim().length === 0) {

        // Sembunyikan prediksi keyword
        hideKeywordPredictions();
        
        // Reset isSearchResultShown
        isSearchResultShown = false;
        
        // Nonaktifkan mode khusus
        deactivateSpecialModes();
        
        // Sembunyikan tab filter
        forceHideFilterTabs();
        
		 // TAMBAHAN BARU: Sembunyikan overlay dan popup
        if (suggestionsOverlay) {
            suggestionsOverlay.style.display = 'none';
        }
        document.body.classList.remove('show-suggestions');
        if (keywordSuggestionsPopup) {
            keywordSuggestionsPopup.style.display = 'none';
        }
		
        // Tampilkan sugesti
        showSuggestions();
        
        // Reset extended suggestions
        const extendedSuggestions = document.getElementById('extendedSuggestions');
        if (extendedSuggestions) {
            extendedSuggestions.style.display = 'none';
        }
        
        // Reset tombol Lihat Lainnya
        seeMoreBtn.innerHTML = '<i class="fa fa-plus-circle"></i><span>Lihat Lainnya</span>';
        
        // Kembalikan tombol ke posisi awal jika perlu
        if (document.querySelector('.see-more-container') && 
            document.querySelector('.see-more-container').parentNode === additionalSuggestions) {
            
            const extendedSuggestionsElem = document.querySelector('#extendedSuggestions');
            if (extendedSuggestionsElem && textSuggestions.parentNode) {
                textSuggestions.parentNode.insertBefore(
                    document.querySelector('.see-more-container'), 
                    extendedSuggestionsElem.nextSibling
                );
            }
        }
        
        // Tampilkan produk dengan layout simpel
        // PERBAIKAN: Hapus sugesti koreksi ketika kolom pencarian dikosongkan
const correctionSuggestion = document.getElementById('correctionSuggestion');
if (correctionSuggestion) {
    correctionSuggestion.remove();
}
        renderSimpleProducts();
    } else {
        // Jika input tidak kosong, tampilkan prediksi keyword
        showKeywordPredictions(this.value);
    }
    
    // Hapus prediksi yang lama jika ada
    const predictionsContainer = document.getElementById('searchPredictions');
    if (predictionsContainer) {
        predictionsContainer.style.maxHeight = '0';
    }
});
        
        // Event listener untuk item sugesti
        suggestionItems.forEach(item => {
            item.addEventListener('click', function() {
                const suggestionText = this.getAttribute('data-text');
                fillSearchWithSuggestion(suggestionText);
            });
        });
        // Event listener khusus untuk overlay - TAMBAHKAN INI SEBELUM EVENT LISTENER DOCUMENT
searchOverlay.addEventListener('click', function(event) {
    // Periksa apakah klik langsung pada overlay itu sendiri, bukan pada elemen di dalamnya
    if (event.target === searchOverlay) {
        // Hentikan propagasi event agar tidak mencapai document event listener
        event.stopPropagation();
        // Mencegah default behavior
        event.preventDefault();
    }
});

// Event listener untuk klik di luar area pencarian - INI KODE YANG SUDAH ADA, DENGAN SEDIKIT MODIFIKASI
document.addEventListener('click', function(event) {
    // Tambahkan pengecekan untuk mengabaikan klik pada overlay itu sendiri
    if (event.target === searchOverlay) {
        return; // Keluar dari fungsi tanpa melakukan apa-apa
    }
    
    // Tambahkan pengecekan untuk saran koreksi
    if (event.target.closest('.correction-suggestion') || event.target.closest('#correctionSuggestion')) {
        return; // Abaikan klik pada saran koreksi
    }
    
    // Tambahkan pengecekan untuk keywordPredictions
    if (event.target.closest('#keywordPredictions') || event.target.closest('.prediction-item')) {
        return; // Abaikan klik pada prediksi keyword
    }
    
    const isClickInsideSearch = 
        expandedSearchInput.contains(event.target) || 
        backBtn.contains(event.target) ||
        expandedSearchIcon.contains(event.target) ||
        expandedClearSearchIcon.contains(event.target) || 
        filterTabs.contains(event.target) ||
        event.target.closest('.filter-tab') ||
        event.target === searchInput;
    
    const isClickInsideSuggestions = suggestionsContainer ? suggestionsContainer.contains(event.target) : false;
    const isClickInsidePredictions = keywordPredictions ? keywordPredictions.contains(event.target) : false;
    
    if (searchOverlay.style.display === 'block' && !isClickInsideSearch && !isClickInsideSuggestions && !isClickInsidePredictions) {
        // Pastikan tidak klik pada elemen-elemen yang terkait dengan pencarian
        if (!event.target.closest('.product-card') && 
            !event.target.closest('.simple-product-card') &&
            !event.target.closest('.suggestion-item') &&
            !event.target.closest('.see-more-btn') &&
            !event.target.closest('.filter-tab') &&
            !event.target.closest('.correction-suggestion') &&
            !event.target.closest('.prediction-item')) {
            // hideSearchMode();  // Komentar atau hapus baris ini
            console.log("Klik di luar area pencarian, tapi tidak menutup expanded search");
            
            // Jika klik di luar, sembunyikan prediksi keyword
            hideKeywordPredictions();
        }
    }
});
       
// Event listener untuk additional suggestions
        const additionalSuggestionItems = additionalSuggestions.querySelectorAll('.suggestion-item');
        additionalSuggestionItems.forEach(item => {
            item.addEventListener('click', function() {
                const suggestionText = this.getAttribute('data-text');
                fillSearchWithSuggestion(suggestionText);
            });
        });
        
        // Event listener untuk tombol "Produk Lainnya"
        document.addEventListener('click', function(event) {
            const target = event.target;
            const isProductAction = target.classList.contains('product-action') || 
                                    target.closest('.product-action');
            
            if (isProductAction) {
                showOtherProducts();
            }
        });

        // Event listener untuk tombol "Coba kata kunci lain"
if (tryAnotherKeywordBtn) {
    tryAnotherKeywordBtn.addEventListener('click', function() {
        // PERBAIKAN: Nonaktifkan mode khusus sebelum membersihkan
        deactivateSpecialModes();
        
        // Bersihkan input
        clearSearchInput();
        
        // PERBAIKAN: Fokus ke input
        if (expandedSearchInput) {
            expandedSearchInput.focus();
        }
    });
}

        // Perbarui event listener untuk tombol "Coba produk lainnya"
if (trySuggestionBtn) {
    trySuggestionBtn.addEventListener('click', function() {
        // Update keyword suggestions sebelum menampilkan popup
        updateKeywordSuggestionsPopup();
        
        // Tampilkan popup
        document.body.classList.add('show-suggestions');
        keywordSuggestionsPopup.style.display = 'block';
        suggestionsOverlay.style.display = 'block';
    });
}
        
        // Event listener untuk overlay sugesti
        if (suggestionsOverlay) {
            suggestionsOverlay.addEventListener('click', function() {
                document.body.classList.remove('show-suggestions');
                keywordSuggestionsPopup.style.display = 'none';
                suggestionsOverlay.style.display = 'none';
            });
        }
        
       
        
        // Event listener untuk product details
        document.addEventListener('DOMContentLoaded', function() {
            const productDetails = document.getElementById('productDetails');
            if (productDetails) {
                productDetails.addEventListener('click', function() {
                    window.location.href = 'https://www.facebook.com';
                });
            }
        });

        
        // PERBAIKAN: Inisialisasi tampilan awal
        renderSimpleProducts();
		
		// Fungsi untuk menambahkan data dummy ke riwayat pencarian (hanya untuk pengujian)
function addDummySearchHistory() {
    // Cek jika riwayat pencarian kosong
    const history = getSearchHistory();
    if (history.length === 0) {
        // Tambahkan beberapa keyword dummy
        const dummyKeywords = [
            "Handphone Samsung", "Sepatu Pria", "Tas Wanita", "Headphone Bluetooth",
            "Keyboard Gaming", "Power Bank", "Smart TV", "Robot Vacuum",
            "Laptop Gaming", "Kamera Mirrorless", "Speaker Bluetooth", "Smartwatch"
        ];
        
        // Simpan ke localStorage
        localStorage.setItem('searchHistory', JSON.stringify(dummyKeywords));
    }
}
        
        // PERBAIKAN BUG: Pastikan tab filter tersembunyi saat halaman dimuat
        document.addEventListener('DOMContentLoaded', function() {
		
            // Inisialisasi popularKeywords
    popularKeywords.init();
	
	// Inisialisasi indeks pencarian
productSearchIndex = createSearchIndex(sampleProducts);
	
	// Tambahkan ini: Inisialisasi sistem keyword cerdas
    intelligentKeywordSystem.init();
            
// Update placeholder berdasarkan histori pencarian valid
    updateDynamicPlaceholders();
    
    // Update tampilan trending
    popularKeywords.updateTrendingDisplay();
            
		 // Inisialisasi dan perbarui riwayat pencarian
    initSearchHistory();
    updateKeywordSuggestions();
	initExtendedSuggestionListeners();
          
		  // Inisialisasi tombol sugesti setelah DOM dimuat
    initializeButtonSuggestions();
		  
          // Load data prediksi dari localStorage
    learningPredictions.loadFromStorage();
    
    // Load riwayat interaksi dari localStorage
    loadUserInteractionHistory();
    
    // Manajemen data dengan delay
    setTimeout(function() {
        learningPredictions.managePredictions();
    }, 3000);
    
    // Update placeholder dinamis
    updateDynamicPlaceholders();
    
    // Update jumlah trend
    if (typeof updateTrendCount === 'function') {
        updateTrendCount();
    }
    
	// Inisialisasi tombol sugesti setelah DOM dimuat
    initializeButtonSuggestions();
	
	// Inisialisasi event listener untuk suggestion items dalam extended suggestions
function initExtendedSuggestionListeners() {
    // Tambahkan event listener untuk extended suggestions
    const extendedSuggestionItems = document.querySelectorAll('#extendedSuggestions .suggestion-item');
    extendedSuggestionItems.forEach(item => {
        item.addEventListener('click', function(e) {
            // Mencegah event bubbling yang dapat menyebabkan masalah
            e.preventDefault();
            e.stopPropagation();
            
            const suggestionText = this.getAttribute('data-text');
            
            // PERBAIKAN BUG 2: Pastikan keyword sugesti tidak muncul di atas container produk
            // dengan memanggil fillSearchWithSuggestion dengan flag tambahan
            fillSearchWithSuggestion(suggestionText);
            
            // Set flag pencarian
            isSearchResultShown = true;
            
            // Paksa sembunyikan sugesti
            if (textSuggestions) textSuggestions.style.display = 'none';
            const extendedSuggestionsElem = document.getElementById('extendedSuggestions');
            if (extendedSuggestionsElem) extendedSuggestionsElem.style.display = 'none';
            if (additionalSuggestions) additionalSuggestions.style.display = 'none';
        });
    });
}
	
    // Inisialisasi kontainer prediksi
    const keywordPredictionsElement = document.getElementById('keywordPredictions');
    if (keywordPredictionsElement) {
        // Atur lebar maksimum sama dengan suggestionsContainer
        keywordPredictionsElement.style.maxWidth = '800px';
        keywordPredictionsElement.style.margin = '0 auto';
        keywordPredictionsElement.style.boxSizing = 'border-box';
        keywordPredictionsElement.style.position = 'absolute';
        keywordPredictionsElement.style.top = '60px';
        keywordPredictionsElement.style.left = '0';
        keywordPredictionsElement.style.right = '0';
    }
    
    // Pastikan tab filter tersembunyi saat awal
    forceHideFilterTabs();
    
    // PERBAIKAN: Pastikan tidak ada mode khusus aktif
    deactivateSpecialModes();
            
            // Tambahkan pengecekan penundaan untuk memastikan
            setTimeout(forceHideFilterTabs, 100);
            setTimeout(forceHideFilterTabs, 500);
            
            // FIX: Pastikan suggestions container akan selalu menempel pada search bar expanded
            // dengan menerapkan posisi absolute dan menghilangkan jarak
            if (suggestionsContainer) {
                suggestionsContainer.style.position = 'absolute';
                suggestionsContainer.style.top = '60px';
                suggestionsContainer.style.left = '0';
                suggestionsContainer.style.right = '0';
                suggestionsContainer.style.marginTop = '0';
                
                // FIX: Tambahkan event listener untuk tetap menjaga posisi
                // suggestions container saat layar diubah ukurannya
                window.addEventListener('resize', function() {
                    suggestionsContainer.style.position = 'absolute';
                    suggestionsContainer.style.top = '60px';
                    suggestionsContainer.style.left = '0';
                    suggestionsContainer.style.right = '0';
                    suggestionsContainer.style.marginTop = '0';
                });
            }
			// TAMBAHKAN KODE BARU DI SINI, TEPAT SEBELUM KURUNG KURAWAL PENUTUP
    updateKeywordSuggestionsPopup();
        });
        
        // PERBAIKAN PENTING: Tambahkan mutationObserver untuk memantau perubahan tampilan
        const searchObserver = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.attributeName === 'style' && 
                    searchOverlay.style.display === 'block') {
                    // Ketika tampilan 2 aktif, paksa sembunyikan keyword suggestions
                    if (keywordSuggestions) {
                        keywordSuggestions.style.display = 'none';
                        keywordSuggestions.style.visibility = 'hidden';
                        keywordSuggestions.style.opacity = '0';
                        keywordSuggestions.style.height = '0';
                        keywordSuggestions.style.overflow = 'hidden';
                        keywordSuggestions.style.pointerEvents = 'none';
                    }
                    
                    // FIX: Pastikan suggestions container selalu menempel pada expanded search bar
                    if (suggestionsContainer) {
                        suggestionsContainer.style.position = 'absolute';
                        suggestionsContainer.style.top = '60px';
                        suggestionsContainer.style.left = '0';
                        suggestionsContainer.style.right = '0';
                        suggestionsContainer.style.marginTop = '0';
                    }
                    
                    // Periksa jika mode khusus aktif
                    if (searchOverlay.classList.contains('not-found-mode') || 
                        searchOverlay.classList.contains('other-products-mode')) {
                        forceHideFilterTabs();
                    }
                }
            });
        });

        // Mulai mengamati perubahan pada searchOverlay
        if (searchOverlay) {
            searchObserver.observe(searchOverlay, { attributes: true });
        }

        // Tambahkan event click khusus untuk ikon pencarian yang diperluas
        if (expandedSearchIcon) {
            expandedSearchIcon.addEventListener('click', function() {
                // Paksa sembunyikan keyword suggestions saat ikon diklik
                if (keywordSuggestions) {
                    keywordSuggestions.style.display = 'none';
                    keywordSuggestions.style.visibility = 'hidden';
                    keywordSuggestions.style.opacity = '0';
                    keywordSuggestions.style.height = '0';
                    keywordSuggestions.style.overflow = 'hidden';
                    keywordSuggestions.style.pointerEvents = 'none';
                }
                
                // FIX: Pastikan suggestions container selalu menempel pada search bar
                if (suggestionsContainer) {
                    suggestionsContainer.style.position = 'absolute';
                    suggestionsContainer.style.top = '60px';
                    suggestionsContainer.style.left = '0';
                    suggestionsContainer.style.right = '0';
                    suggestionsContainer.style.marginTop = '0';
                }
            }, true);
        }

        // FIX: Tambahkan event listener saat input expanded mendapat focus
        expandedSearchInput.addEventListener('focus', function() {
            // Pastikan suggestions container menempel pada search bar
            if (suggestionsContainer) {
                suggestionsContainer.style.position = 'absolute';
                suggestionsContainer.style.top = '60px';
                suggestionsContainer.style.left = '0';
                suggestionsContainer.style.right = '0';
                suggestionsContainer.style.marginTop = '0';
            }
        });
		
		// Event listener untuk icon filter
if (filterIcon) {
    filterIcon.addEventListener('click', function(e) {
        e.preventDefault();
        e.stopPropagation();
        showFacetPanel();
    });
}

// Event listener untuk tombol tutup facet
if (facetClose) {
    facetClose.addEventListener('click', function() {
        hideFacetPanel();
    });
}

if (facetCloseDesktop) {
    facetCloseDesktop.addEventListener('click', function() {
        hideFacetPanel();
    });
}

// Event listener untuk tombol Reset
if (facetResetBtn) {
    facetResetBtn.addEventListener('click', function() {
        resetAllFilters();
        renderFacetContent(facetContent);
    });
}

if (facetResetBtnDesktop) {
    facetResetBtnDesktop.addEventListener('click', function() {
        resetAllFilters();
        renderFacetContent(facetContentDesktop);
    });
}

// Event listener untuk tombol Terapkan
if (facetApplyBtn) {
    facetApplyBtn.addEventListener('click', function() {
        applyFilters();
    });
}

if (facetApplyBtnDesktop) {
    facetApplyBtnDesktop.addEventListener('click', function() {
        applyFilters();
    });
}

// Event listener untuk facet overlay (klik di luar panel)
if (facetOverlay) {
    facetOverlay.addEventListener('click', function(e) {
        if (e.target === facetOverlay) {
            hideFacetPanel();
        }
    });
}
  
  // PERBAIKAN: Tambahkan MutationObserver untuk memantau perubahan class pada searchOverlay
        const classObserver = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.attributeName === 'class') {
                    // Jika mode khusus aktif, pastikan tab filter tersembunyi
                    if (searchOverlay.classList.contains('not-found-mode') || 
                        searchOverlay.classList.contains('other-products-mode')) {
                        forceHideFilterTabs();
                    }
                }
            });
        });
        
        // Mulai memantau perubahan class pada searchOverlay
        if (searchOverlay) {
            classObserver.observe(searchOverlay, { attributes: true, attributeFilter: ['class'] });
        }
        
        // PERBAIKAN: Tambahkan MutationObserver untuk notFoundContainer
        const notFoundObserver = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.attributeName === 'style') {
                    // Jika notFoundContainer ditampilkan, aktifkan mode khusus
                    if (notFoundContainer.style.display === 'block') {
                        activateNotFoundMode();
                    }
                }
            });
        });
        
        // Mulai memantau perubahan style pada notFoundContainer
        if (notFoundContainer) {
            notFoundObserver.observe(notFoundContainer, { attributes: true, attributeFilter: ['style'] });
        }
        
        // PERBAIKAN: Tambahkan MutationObserver untuk productsContainer
        const productsObserver = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.attributeName === 'style') {
                    // Jika productsContainer ditampilkan, aktifkan mode khusus
                    if (productsContainer.style.display === 'block') {
                        activateOtherProductsMode();
                    }
                }
            });
        });
        
        // Mulai memantau perubahan style pada productsContainer
        if (productsContainer) {
            productsObserver.observe(productsContainer, { attributes: true, attributeFilter: ['style'] });
        }
  
// Fungsi untuk menghitung jumlah keyword trend dan update badge
function updateTrendCount() {
    // Hitung jumlah item dalam additional suggestions
    const trendItems = document.querySelectorAll('.additional-suggestions .suggestion-item');
    const trendCount = trendItems.length;
    
    // Update badge dengan jumlah
    const trendBadge = document.getElementById('trendCount');
    if (trendBadge) {
        trendBadge.textContent = trendCount;
    }
}

// Fungsi untuk membangun database prediksi dari produk
function buildPredictionDatabase(products) {
    const predictionDB = {};
    
    products.forEach(product => {
        // Pecah nama produk menjadi kata-kata
        const words = product.name.toLowerCase().split(' ');
        
        // Untuk setiap kata, kecuali kata terakhir
        for (let i = 0; i < words.length - 1; i++) {
            const currentWord = words[i];
            const nextWord = words[i + 1];
            
            // Jika kata memiliki panjang yang cukup
            if (currentWord.length >= 3) {
                if (!predictionDB[currentWord]) {
                    predictionDB[currentWord] = [];
                }
                
                // Tambahkan kata berikutnya sebagai prediksi
                if (!predictionDB[currentWord].includes(nextWord)) {
                    predictionDB[currentWord].push(nextWord);
                }
            }
        }
        
        // Tambahkan prediksi dari kategori
        const firstWord = words[0];
        if (firstWord.length >= 3) {
            if (!predictionDB[firstWord]) {
                predictionDB[firstWord] = [];
            }
            
            const category = product.category.toLowerCase();
            if (!predictionDB[firstWord].includes(category)) {
                predictionDB[firstWord].push(category);
            }
        }
    });
    
    return predictionDB;
}

// Bangun database prediksi otomatis
const autoPredictions = buildPredictionDatabase(sampleProducts);


// Fungsi koreksi typo dengan Levenshtein distance
function correctTypo(input) {
    // Periksa apakah koreksi untuk input ini pernah ditolak sebelumnya
    try {
        const rejectedCorrections = JSON.parse(localStorage.getItem('rejectedCorrections') || '[]');
        const matchedRejections = rejectedCorrections.filter(item => 
            item.original.toLowerCase() === input.toLowerCase()
        );
        
        if (matchedRejections.length > 0) {
            // Kumpulkan semua koreksi yang pernah ditolak untuk input ini
            const rejectedSuggestions = matchedRejections.map(item => item.rejected.toLowerCase());
            console.log("Koreksi yang pernah ditolak:", rejectedSuggestions);
            
            // Gunakan database keyword cerdas untuk mencari semua kandidat koreksi
            const words = input.toLowerCase().split(' ');
            let corrected = [];
            
            // Periksa setiap kata
            words.forEach(word => {
                if (word.length < 3) {
                    corrected.push(word); // Abaikan kata pendek
                    return;
                }
                
                // Cek database typo corrections
                if (keywordPredictionDB.typoCorrections[word]) {
                    const correction = keywordPredictionDB.typoCorrections[word];
                    // Jangan gunakan koreksi yang pernah ditolak
                    if (!rejectedSuggestions.includes(correction)) {
                        corrected.push(correction);
                    } else {
                        corrected.push(word); // Gunakan kata asli
                    }
                } else {
                    // Jika tidak ada koreksi, gunakan kata asli
                    corrected.push(word);
                }
            });
            
            return corrected.join(' ');
        }
    } catch (e) {
        console.error("Error saat memeriksa koreksi yang ditolak:", e);
    }
    
    // Jika tidak ada riwayat penolakan, gunakan logika koreksi normal
    const words = input.toLowerCase().split(' ');
    let corrected = [];
    
    // Periksa setiap kata
    words.forEach(word => {
        if (word.length < 3) {
            corrected.push(word); // Abaikan kata yang terlalu pendek
            return;
        }
        
        // Cek database typo corrections
        if (keywordPredictionDB.typoCorrections[word]) {
            corrected.push(keywordPredictionDB.typoCorrections[word]);
        } else {
            // Jika tidak, gunakan kata asli
            corrected.push(word);
        }
    });
    
    return corrected.join(' ');
}

// Fungsi untuk memperluas pencarian dengan sinonim
function expandWithSynonyms(input) {
    const words = input.toLowerCase().split(' ');
    let expandedTerms = [...words]; // Mulai dengan kata-kata asli
    
    words.forEach(word => {
        if (keywordPredictionDB.synonyms[word]) {
            // Tambahkan sinonim ke daftar
            expandedTerms = expandedTerms.concat(keywordPredictionDB.synonyms[word]);
        }
    });
    
    return [...new Set(expandedTerms)]; // Hapus duplikat
}

// Fungsi untuk mencari frasa khusus
function detectSpecialPhrases(input) {
    const lowerInput = input.toLowerCase();
    let specialFilters = [];
    
    Object.keys(searchDatabase.specialPhrases).forEach(phrase => {
        if (lowerInput.includes(phrase)) {
            specialFilters.push(searchDatabase.specialPhrases[phrase]);
        }
    });
    
    return specialFilters;
}

// Fungsi untuk memperkirakan kata selanjutnya
function predictNextWord(input) {
    const lastWord = input.toLowerCase().trim().split(' ').pop();
    let predictions = [];
    
    // 1. Cari di prediksi statis
    if (searchDatabase.predictions && searchDatabase.predictions[lastWord]) {
        predictions = predictions.concat(
            searchDatabase.predictions[lastWord].map(next => lastWord + next)
        );
    }
    
    // 2. Cari di prediksi otomatis dari data produk
    if (autoPredictions[lastWord]) {
        predictions = predictions.concat(
            autoPredictions[lastWord].map(next => lastWord + ' ' + next)
        );
    }
    
    // 3. Cari di prediksi yang dipelajari dari interaksi
    const learnedPredictions = learningPredictions.getPredictions(lastWord);
    if (learnedPredictions.length > 0) {
        predictions = predictions.concat(
            learnedPredictions.map(next => lastWord + ' ' + next)
        );
    }
    
    // 4. Cari berdasarkan prefiks (untuk semua sumber)
    const allWordKeys = [
        ...(searchDatabase.predictions ? Object.keys(searchDatabase.predictions) : []),
        ...Object.keys(autoPredictions),
        ...Object.keys(learningPredictions.data)
    ];
    
    const uniqueKeys = [...new Set(allWordKeys)];
    
    uniqueKeys.forEach(key => {
        if (key.startsWith(lastWord) && key !== lastWord && key.length > lastWord.length) {
            // Dari statis
            if (searchDatabase.predictions && searchDatabase.predictions[key]) {
                predictions.push(key + searchDatabase.predictions[key][0]);
            }
            
            // Dari otomatis
            if (autoPredictions[key] && autoPredictions[key].length > 0) {
                predictions.push(key + ' ' + autoPredictions[key][0]);
            }
            
            // Dari pembelajaran
            const learned = learningPredictions.getPredictions(key);
            if (learned.length > 0) {
                predictions.push(key + ' ' + learned[0]);
            }
        }
    });
    
    // Hapus duplikat dan batasi jumlah
    return [...new Set(predictions)].slice(0, 5);
}

// Fungsi untuk memelihara riwayat pencarian pengguna
function updateUserHistory(query) {
    // Tambahkan query ke riwayat, batasi hingga 10 item
    if (query && query.trim().length > 0) {
        // Hapus jika sudah ada untuk menghindari duplikat
        const index = searchDatabase.userHistory.indexOf(query);
        if (index > -1) {
            searchDatabase.userHistory.splice(index, 1);
        }
        
        // Tambahkan di awal
        searchDatabase.userHistory.unshift(query);
        
        // Batasi sampai 10 item
        if (searchDatabase.userHistory.length > 10) {
            searchDatabase.userHistory.pop();
        }
    }
}

// Fungsi untuk mendapatkan sugesti berdasarkan riwayat dan tren
function getSmartSuggestions(input = "") {
    let suggestions = [];
    
    // Tambahkan prediksi berdasarkan ketikan
    if (input.trim().length > 0) {
        const predictions = predictNextWord(input);
        if (predictions) {
            suggestions = suggestions.concat(predictions.slice(0, 3));
        }
    }
    
    // Tambahkan dari riwayat pengguna
    suggestions = suggestions.concat(searchDatabase.userHistory.slice(0, 3));
    
    // Tambahkan dari kata kunci musiman
    suggestions = suggestions.concat(searchDatabase.seasonalKeywords().slice(0, 2));
    
    // Hapus duplikat dan batasi jumlahnya
    return [...new Set(suggestions)].slice(0, 5);
}

// Fungsi pencarian utama yang ditingkatkan
// Fungsi pencarian utama yang ditingkatkan
function enhancedSearch(query) {
    console.log("Melakukan enhanced search untuk query:", query);
    
    // Jika query kosong, kembalikan semua produk
    if (!query || query.trim() === "") {
        console.log("Query kosong, mengembalikan semua produk:", sampleProducts.length);
        return sampleProducts;
    }
    
    try {
        // 1. Koreksi typo
        const correctedQuery = correctTypo(query);
        console.log("Query dikoreksi:", correctedQuery);
        
        // 2. Pencarian teks lengkap menggunakan Fuse.js
        let filteredProducts = fullTextSearch(correctedQuery, productSearchIndex);
        console.log("Produk yang cocok dari pencarian teks lengkap:", filteredProducts.length);
        
        // 3. Jika tidak ada hasil, coba dengan query asli
        if (filteredProducts.length === 0 && correctedQuery !== query) {
            filteredProducts = fullTextSearch(query, productSearchIndex);
            console.log("Mencoba dengan query asli, hasil:", filteredProducts.length);
        }
        
        // 4. Jika masih tidak ada hasil, coba dengan filter yang lebih longgar
        if (filteredProducts.length === 0) {
            console.log("Mencoba dengan filter yang lebih longgar...");
            
            filteredProducts = sampleProducts.filter(product => {
                const productText = (product.name + " " + product.category + " " + (product.shortName || "")).toLowerCase();
                
                // Filter longgar: setidaknya satu kata dalam query harus ada dalam produk
                const queryWords = correctedQuery.toLowerCase().split(' ');
                for (const word of queryWords) {
                    if (word.length >= 3 && productText.includes(word)) {
                        return true;
                    }
                }
                
                return false;
            });
            
            console.log("Produk yang cocok dengan filter longgar:", filteredProducts.length);
        }
        
        // 5. Urutkan produk berdasarkan relevansi
        filteredProducts = calculateProductRelevance(filteredProducts, correctedQuery);
        
        return filteredProducts;
    } catch (error) {
        console.error("Error dalam enhanced search:", error);
        // Kembalikan array kosong sebagai fallback jika terjadi error
        return [];
    }
}

// Fungsi untuk menghitung dan mengurutkan berdasarkan relevansi
function calculateProductRelevance(products, query) {
    const queryWords = query.toLowerCase().split(' ').filter(w => w.length > 2);
    
    return products.map(product => {
        const textToSearch = (product.name + " " + product.category + " " + (product.shortName || "")).toLowerCase();
        let relevanceScore = 0;
        
        // Hitung skor relevansi berdasarkan kecocokan kata
        queryWords.forEach(word => {
            // Bobot untuk kecocokan di nama produk
            if (product.name.toLowerCase().includes(word)) {
                relevanceScore += 10;
                // Bonus untuk kecocokan di awal nama
                if (product.name.toLowerCase().startsWith(word)) {
                    relevanceScore += 5;
                }
            }
            
            // Bobot untuk kecocokan di kategori
            if (product.category.toLowerCase().includes(word)) {
                relevanceScore += 5;
            }
            
            // Bobot untuk kecocokan di shortName
            if (product.shortName && product.shortName.toLowerCase().includes(word)) {
                relevanceScore += 3;
            }
        });
        
        // Bobot tambahan berdasarkan popularitas
        relevanceScore += (product.sold || 0) * 0.01; // Produk terlaris
        relevanceScore += (product.rating || 0) * 2;  // Produk dengan rating tinggi
        
        // Bonus untuk produk dengan diskon
        if (product.discount) {
            const discountValue = parseInt(product.discount);
            if (!isNaN(discountValue) && discountValue > 0) {
                relevanceScore += Math.min(discountValue * 0.2, 5); // Maksimal bonus 5 poin
            }
        }
        
        // Bonus untuk produk COD
        if (product.cod) {
            relevanceScore += 2;
        }
        
        // Tambahkan skor ke objek produk
        return {...product, relevanceScore};
    }).sort((a, b) => b.relevanceScore - a.relevanceScore); // Urutkan dari yang paling relevan
}

// Fungsi untuk menghitung facet dari hasil pencarian
function extractFacets(searchResults) {
    const facets = {
        categories: {},
        priceRanges: {
            "Di bawah Rp 100.000": 0,
            "Rp 100.000 - Rp 500.000": 0,
            "Rp 500.000 - Rp 1.000.000": 0,
            "Di atas Rp 1.000.000": 0
        },
        ratings: {
            "5 Bintang": 0,
            "4+ Bintang": 0,
            "3+ Bintang": 0
        },
        shipping: {},
        features: {
            "COD": 0,
            "SellZio Mall": 0
        }
    };
    
    searchResults.forEach(product => {
        // Hitung kategori
        facets.categories[product.category] = (facets.categories[product.category] || 0) + 1;
        
        // Hitung rentang harga
        const price = parseInt(product.price.replace(/\D/g, ''));
        if (price < 100000) 
            facets.priceRanges["Di bawah Rp 100.000"]++;
        else if (price < 500000) 
            facets.priceRanges["Rp 100.000 - Rp 500.000"]++;
        else if (price < 1000000) 
            facets.priceRanges["Rp 500.000 - Rp 1.000.000"]++;
        else 
            facets.priceRanges["Di atas Rp 1.000.000"]++;
            
        // Hitung rating
        if (product.rating >= 5)
            facets.ratings["5 Bintang"]++;
        else if (product.rating >= 4)
            facets.ratings["4+ Bintang"]++;
        else if (product.rating >= 3)
            facets.ratings["3+ Bintang"]++;
            
        // Hitung pengiriman
        facets.shipping[product.shipping] = (facets.shipping[product.shipping] || 0) + 1;
        
        // Hitung fitur
        if (product.cod) {
            facets.features["COD"]++;
        }
        
        if (product.isMall) {
            facets.features["SellZio Mall"]++;
        }
    });
    
    // Hapus facet yang kosong
    Object.keys(facets).forEach(facetType => {
        if (typeof facets[facetType] === 'object') {
            Object.keys(facets[facetType]).forEach(value => {
                if (facets[facetType][value] === 0) {
                    delete facets[facetType][value];
                }
            });
            
            if (Object.keys(facets[facetType]).length === 0) {
                delete facets[facetType];
            }
        }
    });
    
    return facets;
}

// Tambahkan fungsi untuk menampilkan facet dalam UI
function renderFacets(facets) {
    const facetContainer = document.createElement('div');
    facetContainer.className = 'facet-container';
    facetContainer.id = 'facetContainer';
    
    // Render kategori
    if (facets.categories && Object.keys(facets.categories).length > 0) {
        const categoryFacet = createFacetSection('Kategori', facets.categories);
        facetContainer.appendChild(categoryFacet);
    }
    
    // Render rentang harga
    if (facets.priceRanges && Object.keys(facets.priceRanges).length > 0) {
        const priceFacet = createFacetSection('Rentang Harga', facets.priceRanges);
        facetContainer.appendChild(priceFacet);
    }
    
    // Render rating
    if (facets.ratings && Object.keys(facets.ratings).length > 0) {
        const ratingFacet = createFacetSection('Rating', facets.ratings);
        facetContainer.appendChild(ratingFacet);
    }
    
    // Render pengiriman
    if (facets.shipping && Object.keys(facets.shipping).length > 0) {
        const shippingFacet = createFacetSection('Pengiriman', facets.shipping);
        facetContainer.appendChild(shippingFacet);
    }
    
    // Render fitur
    if (facets.features && Object.keys(facets.features).length > 0) {
        const featuresFacet = createFacetSection('Fitur', facets.features);
        facetContainer.appendChild(featuresFacet);
    }
    
    // Tambahkan container untuk filter aktif
    const activeFiltersContainer = document.createElement('div');
    activeFiltersContainer.className = 'active-filters';
    activeFiltersContainer.id = 'activeFilters';
    activeFiltersContainer.style.display = 'none'; // Sembunyikan sampai ada filter aktif
    
    facetContainer.insertBefore(activeFiltersContainer, facetContainer.firstChild);
    
    return facetContainer;
}

// Helper untuk membuat bagian facet

function createFacetSection(title, items) {
    const section = document.createElement('div');
    section.className = 'facet-section';
    
    const header = document.createElement('h3');
    header.textContent = title;
    section.appendChild(header);
    
    const list = document.createElement('ul');
    Object.entries(items).forEach(([name, count]) => {
        const item = document.createElement('li');
        const checkbox = document.createElement('input');
        checkbox.type = 'checkbox';
        checkbox.id = `facet-${title.toLowerCase()}-${name.replace(/\s+/g, '-').replace(/[^a-z0-9-]/gi, '')}`;
        
        // Tambahkan dataset untuk tipe facet dan nilai
        checkbox.dataset.facetType = title.toLowerCase();
        checkbox.dataset.facetValue = name;
        
        // PERBAIKAN: Gunakan tempActiveFilters untuk pengecekan status
        if (tempActiveFilters[title.toLowerCase()] && 
            tempActiveFilters[title.toLowerCase()].includes(name)) {
            checkbox.checked = true;
        } else if (activeFilters[title.toLowerCase()] && 
                  activeFilters[title.toLowerCase()].includes(name)) {
            checkbox.checked = true;
        }
        
        // Modifikasi event listener
        checkbox.addEventListener('change', function() {
            const type = this.dataset.facetType;
            const value = this.dataset.facetValue;
            
            // Gunakan fungsi untuk filter sementara
            if (this.checked) {
                addTempFilter(type, value);
            } else {
                removeTempFilter(type, value);
            }
        });
        
        const label = document.createElement('label');
        label.htmlFor = checkbox.id;
        label.textContent = `${name} (${count})`;
        
        // PERBAIKAN: Ubah warna ceklis menjadi oranye dengan CSS khusus
        checkbox.className = 'orange-checkbox';
        
        item.appendChild(checkbox);
        item.appendChild(label);
        list.appendChild(item);
    });
    
    section.appendChild(list);
    return section;
}
// Variabel untuk menyimpan filter aktif
let activeFilters = {};
    
    // Tambahkan variabel baru untuk menyimpan filter sementara
let tempActiveFilters = {};

// Fungsi untuk menambahkan filter
function addFilter(type, value) {
    if (!activeFilters[type]) {
        activeFilters[type] = [];
    }
    
    if (!activeFilters[type].includes(value)) {
        activeFilters[type].push(value);
    }
    
    // Update UI filter aktif
    updateActiveFiltersUI();
	
	
}

// Fungsi untuk menghapus filter
function removeFilter(type, value) {
    if (!activeFilters[type]) return;
    
    activeFilters[type] = activeFilters[type].filter(v => v !== value);
    
    if (activeFilters[type].length === 0) {
        delete activeFilters[type];
    }
    
    // Update UI
    const checkbox = document.getElementById(`facet-${type}-${value.replace(/\s+/g, '-').replace(/[^a-z0-9-]/gi, '')}`);
    if (checkbox) {
        checkbox.checked = false;
    }
    
    // Update UI filter aktif
    updateActiveFiltersUI();
	
	
    
}

    // Fungsi baru untuk mengelola filter sementara
function addTempFilter(type, value) {
    if (!tempActiveFilters[type]) {
        tempActiveFilters[type] = [];
    }
    
    if (!tempActiveFilters[type].includes(value)) {
        tempActiveFilters[type].push(value);
    }
}

function removeTempFilter(type, value) {
    if (!tempActiveFilters[type]) return;
    
    tempActiveFilters[type] = tempActiveFilters[type].filter(v => v !== value);
    
    if (tempActiveFilters[type].length === 0) {
        delete tempActiveFilters[type];
    }
}
    
// Fungsi untuk memperbarui UI filter aktif
function updateActiveFiltersUI() {
    const activeFiltersContainer = document.getElementById('activeFilters');
    if (!activeFiltersContainer) return;
    
    // Reset container
    activeFiltersContainer.innerHTML = '';
    
    // Hitung jumlah total filter aktif
    let totalFilters = 0;
    Object.values(activeFilters).forEach(filters => {
        totalFilters += filters.length;
    });
    
    // Tampilkan atau sembunyikan container berdasarkan jumlah filter
    if (totalFilters > 0) {
        activeFiltersContainer.style.display = 'flex';
        
        // Tambahkan tag untuk setiap filter aktif
        Object.entries(activeFilters).forEach(([type, values]) => {
            values.forEach(value => {
                const filterTag = document.createElement('div');
                filterTag.className = 'filter-tag';
                filterTag.innerHTML = `${value} <i class="fa fa-times"></i>`;
                
                // Tambahkan event listener untuk menghapus filter
                filterTag.querySelector('i').addEventListener('click', function() {
                    removeFilter(type, value);
                    executeSearch();
                });
                
                activeFiltersContainer.appendChild(filterTag);
            });
        });
        
        // Tambahkan tombol reset semua filter
        if (totalFilters > 1) {
            const resetBtn = document.createElement('div');
            resetBtn.className = 'filter-tag';
            resetBtn.innerHTML = `Reset Semua <i class="fa fa-times"></i>`;
            resetBtn.style.backgroundColor = '#ffebe8';
            resetBtn.style.color = '#ee4d2d';
            
            resetBtn.addEventListener('click', function() {
                resetAllFilters();
                executeSearch();
            });
            
            activeFiltersContainer.appendChild(resetBtn);
        }
    } else {
        activeFiltersContainer.style.display = 'none';
    }
}

// Fungsi untuk mereset semua filter
function resetAllFilters() {
    // Reset filter aktif
    activeFilters = {};
    
    // Reset juga filter sementara
    tempActiveFilters = {};
    
    // Reset semua checkbox
    document.querySelectorAll('.facet-section input[type="checkbox"]').forEach(checkbox => {
        checkbox.checked = false;
    });
    
    // Update UI
    updateActiveFiltersUI();
    
    // Update badge setelah reset
    updateFilterBadge();
}

// Fungsi untuk menerapkan filter pada hasil pencarian
function applyFiltersToResults(products) {
    if (Object.keys(activeFilters).length === 0) {
        return products;
    }
    
    return products.filter(product => {
        // Filter berdasarkan kategori
        if (activeFilters.kategori && 
            activeFilters.kategori.length > 0 &&
            !activeFilters.kategori.includes(product.category)) {
            return false;
        }
        
        // Filter berdasarkan rentang harga
        if (activeFilters['rentang harga'] && activeFilters['rentang harga'].length > 0) {
            const price = parseInt(product.price.replace(/\D/g, ''));
            const inRange = activeFilters['rentang harga'].some(range => {
                if (range === "Di bawah Rp 100.000") 
                    return price < 100000;
                else if (range === "Rp 100.000 - Rp 500.000") 
                    return price >= 100000 && price < 500000;
                else if (range === "Rp 500.000 - Rp 1.000.000") 
                    return price >= 500000 && price < 1000000;
                else if (range === "Di atas Rp 1.000.000") 
                    return price >= 1000000;
                return false;
            });
            
            if (!inRange) return false;
        }
        
        // Filter berdasarkan rating
        if (activeFilters.rating && activeFilters.rating.length > 0) {
            const meetsRating = activeFilters.rating.some(rating => {
                if (rating === "5 Bintang") 
                    return product.rating >= 5;
                else if (rating === "4+ Bintang") 
                    return product.rating >= 4;
                else if (rating === "3+ Bintang") 
                    return product.rating >= 3;
                return false;
            });
            
            if (!meetsRating) return false;
        }
        
        // Filter berdasarkan pengiriman
        if (activeFilters.pengiriman && 
            activeFilters.pengiriman.length > 0 &&
            !activeFilters.pengiriman.includes(product.shipping)) {
            return false;
        }
        
        // Filter berdasarkan fitur
        if (activeFilters.fitur && activeFilters.fitur.length > 0) {
            let hasFeaturesRequired = true;
            
            activeFilters.fitur.forEach(feature => {
                if (feature === "COD" && !product.cod) {
                    hasFeaturesRequired = false;
                }
                if (feature === "SellZio Mall" && !product.isMall) {
                    hasFeaturesRequired = false;
                }
            });
            
            if (!hasFeaturesRequired) return false;
        }
        
        return true;
    });
}

// Struktur data untuk melacak interaksi pengguna
const userLearningSystem = {
    clicks: {}, // Pencarian -> klik produk
    abandonments: {}, // Pencarian -> tidak ada klik
    weights: {}, // Bobot per-kata
    
    init: function() {
        this.loadFromStorage();
        console.log("Learning system initialized with:");
        console.log(`- ${Object.keys(this.clicks).length} search clicks`);
        console.log(`- ${Object.keys(this.abandonments).length} search abandonments`);
        console.log(`- ${Object.keys(this.weights).length} keyword weights`);
    },
    
    recordClick: function(query, productId) {
        const normalizedQuery = query.toLowerCase().trim();
        if (!normalizedQuery) return;
        
        if (!this.clicks[normalizedQuery]) {
            this.clicks[normalizedQuery] = {};
        }
        
        this.clicks[normalizedQuery][productId] = (this.clicks[normalizedQuery][productId] || 0) + 1;
        
        // Update bobot kata berdasarkan klik
        this.updateKeywordWeights(normalizedQuery, productId, true);
        
        // Simpan ke localStorage
        this.saveToStorage();
    },
    
    recordAbandonment: function(query) {
        const normalizedQuery = query.toLowerCase().trim();
        if (!normalizedQuery) return;
        
        this.abandonments[normalizedQuery] = (this.abandonments[normalizedQuery] || 0) + 1;
        
        // Update bobot kata berdasarkan abandonment
        this.updateKeywordWeights(normalizedQuery, null, false);
        
        this.saveToStorage();
    },
    
    updateKeywordWeights: function(query, productId, isPositive) {
        const words = query.split(' ').filter(w => w.length > 2);
        if (words.length === 0) return;
        
        const product = productId ? sampleProducts.find(p => p.id == productId) : null;
        const weightChange = isPositive ? 0.1 : -0.05; // Bobot lebih besar untuk positif
        
        words.forEach(word => {
            if (!this.weights[word]) {
                this.weights[word] = {
                    general: 1.0, // Bobot umum
                    categories: {}, // Bobot per kategori
                    products: {}   // Bobot per produk
                };
            }
            
            // Update bobot umum
            this.weights[word].general = Math.max(0.1, Math.min(2.0, this.weights[word].general + weightChange));
            
            // Update bobot kategori jika ada produk
            if (product && product.category) {
                const category = product.category.toLowerCase();
                this.weights[word].categories[category] = (this.weights[word].categories[category] || 1.0) + weightChange;
                this.weights[word].categories[category] = Math.max(0.1, Math.min(2.0, this.weights[word].categories[category]));
            }
            
            // Update bobot produk
            if (product) {
                this.weights[word].products[productId] = (this.weights[word].products[productId] || 1.0) + (weightChange * 2);
                this.weights[word].products[productId] = Math.max(0.1, Math.min(3.0, this.weights[word].products[productId]));
            }
        });
    },
    
    applyLearningToResults: function(query, results) {
        if (Object.keys(this.weights).length === 0) {
            return results; // No learning data available
        }
        
        const words = query.toLowerCase().trim().split(' ').filter(w => w.length > 2);
        if (words.length === 0) return results;
        
        // Apply learning weights to results
        return results.map(product => {
            let learningBoost = 0;
            
            words.forEach(word => {
                if (this.weights[word]) {
                    // Apply general weight
                    learningBoost += (this.weights[word].general - 1.0) * 10;
                    
                    // Apply category weight if applicable
                    if (this.weights[word].categories[product.category.toLowerCase()]) {
                        learningBoost += (this.weights[word].categories[product.category.toLowerCase()] - 1.0) * 15;
                    }
                    
                    // Apply product weight if applicable
                    if (this.weights[word].products[product.id]) {
                        learningBoost += (this.weights[word].products[product.id] - 1.0) * 25;
                    }
                }
            });
            
            // Combine with existing relevance score
            const finalScore = (product.relevanceScore || 0) + learningBoost;
            
            return {...product, relevanceScore: finalScore, learningBoost};
        }).sort((a, b) => b.relevanceScore - a.relevanceScore);
    },
    
    saveToStorage: function() {
        try {
            const data = {
                clicks: this.clicks,
                abandonments: this.abandonments,
                weights: this.weights,
                timestamp: Date.now()
            };
            
            localStorage.setItem('userLearningData', JSON.stringify(data));
        } catch (e) {
            console.error('Error saving learning data:', e);
            // Truncate data if too large
            this.truncateData();
            this.saveToStorage();
        }
    },
    
    loadFromStorage: function() {
        try {
            const data = localStorage.getItem('userLearningData');
            if (data) {
                const parsed = JSON.parse(data);
                this.clicks = parsed.clicks || {};
                this.abandonments = parsed.abandonments || {};
                this.weights = parsed.weights || {};
            }
        } catch (e) {
            console.error('Error loading learning data:', e);
            this.clicks = {};
            this.abandonments = {};
            this.weights = {};
        }
    },
    
    truncateData: function() {
        // Keep only the most recent click data
        const maxQueries = 50;
        const queries = Object.keys(this.clicks);
        if (queries.length > maxQueries) {
            const sortedQueries = queries.sort((a, b) => {
                const aCount = Object.values(this.clicks[a]).reduce((sum, val) => sum + val, 0);
                const bCount = Object.values(this.clicks[b]).reduce((sum, val) => sum + val, 0);
                return bCount - aCount; // Sort by most clicked
            });
            
            const newClicks = {};
            sortedQueries.slice(0, maxQueries).forEach(query => {
                newClicks[query] = this.clicks[query];
            });
            
            this.clicks = newClicks;
        }
        
        // Truncate weights
        const maxWeights = 100;
        const weightKeys = Object.keys(this.weights);
        if (weightKeys.length > maxWeights) {
            const sortedWeights = weightKeys.sort((a, b) => 
                this.weights[b].general - this.weights[a].general
            );
            
            const newWeights = {};
            sortedWeights.slice(0, maxWeights).forEach(key => {
                newWeights[key] = this.weights[key];
            });
            
            this.weights = newWeights;
        }
    }
};

// Fungsi untuk menampilkan saran koreksi

function showCorrectionSuggestion(correctedQuery) {
    // Cek apakah container saran koreksi sudah ada
    let correctionContainer = document.getElementById('correctionSuggestion');
    
    // Hapus container yang sudah ada (jika ada)
    if (correctionContainer) {
        correctionContainer.remove();
    }
    
    // Buat container baru
    correctionContainer = document.createElement('div');
    correctionContainer.id = 'correctionSuggestion';
    correctionContainer.className = 'correction-compact';
    
    // PERBAIKAN: Sisipkan hanya di bawah tab filter
    // Temukan lokasi penyisipan yang tepat - tepat di bawah filter tabs dan di atas product grid
    const filterTabs = document.getElementById('filterTabs');
    const productGrid = document.getElementById('productGrid');
    
    // Tambahkan container koreksi hanya jika tab filter dan product grid ditemukan, 
    // dan hanya jika mode peringatan tidak aktif
    if (filterTabs && productGrid && !searchOverlay.classList.contains('not-found-mode')) {
        // Pastikan filterTabs ditampilkan
        if (filterTabs.style.display !== 'none') {
            // Sisipkan setelah filterTabs dan sebelum productGrid
            if (productGrid.parentNode) {
                productGrid.parentNode.insertBefore(correctionContainer, productGrid);
                
                // Get current search query
                const currentQuery = expandedSearchInput.value.trim();
                
                // Isi konten saran koreksi dengan struktur baru
                correctionContainer.innerHTML = `
                    <div class="blur-overlay"></div>
                    <div class="center-buttons">
                      <div class="center-buttons-inner">
                        <button class="btn-center yes" title="Ya, benar">
                          <i class="fa fa-check"></i>
                        </button>
                        <button class="btn-center no" title="Bukan">
                          <i class="fa fa-times"></i>
                        </button>
                      </div>
                    </div>
                    <div class="correction-content">
                      <div class="compact-message">
                        <i class="fa fa-search"></i>
                        <div class="message-text">
                          <span class="message-label">Mungkin ini:</span>
                          <span class="correction-keyword">${correctedQuery}</span>
                        </div>
                      </div>
                    </div>
                `;
                
                // Tampilkan container
                correctionContainer.style.display = 'flex';
                
                // Tambahkan event listener untuk toggle container
                correctionContainer.addEventListener('click', function(e) {
                    // Hanya toggle jika click bukan pada tombol
                    if (!e.target.closest('.btn-center')) {
                        if (this.classList.contains('active')) {
                            this.classList.remove('active');
                        } else {
                            this.classList.add('active');
                        }
                    }
                });
                
                // Tambahkan event listener untuk tombol Yes (Ceklis)
                const yesButton = correctionContainer.querySelector('.btn-center.yes');
                if (yesButton) {
                    yesButton.addEventListener('click', function(e) {
                        e.preventDefault();
                        e.stopPropagation();
                        
                        // Isi nilai input dengan koreksi
                        expandedSearchInput.value = correctedQuery;
                        
                        // Tampilkan ikon silang
                        if (expandedClearSearchIcon) {
                            expandedClearSearchIcon.style.display = 'flex';
                        }
                        
                        // Sembunyikan placeholder
                        expandedSearchPlaceholder.classList.add('force-hide-placeholder');
                        
                        // Simpan koreksi yang sudah diklik ke localStorage
                        const clickedCorrections = JSON.parse(localStorage.getItem('clickedCorrections') || '[]');
                        if (!clickedCorrections.includes(correctedQuery)) {
                            clickedCorrections.push(correctedQuery);
                            localStorage.setItem('clickedCorrections', JSON.stringify(clickedCorrections));
                        }
                        
                        // Jalankan pencarian
                        executeSearch();
                        
                        // Hapus saran koreksi secara permanen
                        correctionContainer.remove();
                    });
                }
                
                // Tambahkan event listener untuk tombol No (Silang)
                const noButton = correctionContainer.querySelector('.btn-center.no');
                if (noButton) {
                    noButton.addEventListener('click', function(e) {
                        e.preventDefault();
                        e.stopPropagation();
                        
                        // Simpan ke localStorage sebagai koreksi yang ditolak
                        const rejectedCorrections = JSON.parse(localStorage.getItem('rejectedCorrections') || '[]');
                        if (!rejectedCorrections.some(item => item.original === currentQuery)) {
                            // Simpan pasangan original query dan koreksi yang ditolak
                            rejectedCorrections.push({
                                original: currentQuery,
                                rejected: correctedQuery,
                                timestamp: Date.now()
                            });
                            localStorage.setItem('rejectedCorrections', JSON.stringify(rejectedCorrections));
                        }
                        
                        // Hapus saran koreksi secara permanen
                        correctionContainer.remove();
                    });
                }
            }
        }
    }
}
// Fungsi untuk mengupdate placeholder dinamis
function updateDynamicPlaceholders() {
    // Dapatkan elemen placeholder
    const placeholderDynamic = document.querySelector('.placeholder-dynamic');
    
    if (!placeholderDynamic) return;
    
    // PENTING: Kosongkan container terlebih dahulu!
    placeholderDynamic.innerHTML = '';
    
    // Dapatkan saran berdasarkan histori pencarian valid (20 keyword)
    const suggestions = getPlaceholderSuggestions();
    
    // Dalam fungsi updateDynamicPlaceholders()
const displaySuggestions = suggestions.slice(0, 15); // Tetap 15 placeholder
const totalDuration = 45; // Total durasi animasi dalam detik
const itemDuration = totalDuration / displaySuggestions.length; // Sekitar 3 detik per item

placeholderDynamic.innerHTML = displaySuggestions.map((suggestion, index) => 
    `<div class="placeholder-text" style="animation-delay: ${index * itemDuration}s">${suggestion}</div>`
).join('');
    
    // Update placeholder untuk expanded search juga
    const expandedPlaceholderDynamic = document.querySelector('.search-expanded .placeholder-dynamic');
    if (expandedPlaceholderDynamic) {
        expandedPlaceholderDynamic.innerHTML = placeholderDynamic.innerHTML;
    }
}
    
// Inisialisasi tombol sugesti setelah DOM dimuat
function initializeButtonSuggestions() {
    const textSuggestions = document.getElementById('textSuggestions');
    if (!textSuggestions) return;
    
    // Periksa apakah sudah memiliki container untuk tombol
    let buttonContainer = textSuggestions.querySelector('.keyword-button-container');
    if (!buttonContainer) {
        // Buat container baru
        buttonContainer = document.createElement('div');
        buttonContainer.className = 'keyword-button-container';
        textSuggestions.appendChild(buttonContainer);
        
        // Tambahkan beberapa tombol dari riwayat pencarian
        const history = getSearchHistory();
        const initialKeywords = Math.min(7, history.length);
        
        for (let i = 0; i < initialKeywords; i++) {
            addSuggestionItem(history[i], textSuggestions);
        }
    }
}
    
    // Fungsi untuk melacak dan mengelola keyword populer
const popularKeywords = {
    // Data default untuk keyword populer
    defaultKeywords: [
        "Headphone Bluetooth",
        "Keyboard Gaming",
        "Power Bank",
        "Smart TV",
        "Robot Vacuum"
    ],
    
    // Data untuk menyimpan jumlah pencarian
    searchCounts: {},
    
    // Inisialisasi keyword populer
    init: function() {
        // Coba dapatkan data dari localStorage
        try {
            const savedCounts = localStorage.getItem('popularKeywordCounts');
            if (savedCounts) {
                this.searchCounts = JSON.parse(savedCounts);
                console.log("Berhasil memuat data keyword populer:", Object.keys(this.searchCounts).length, "keyword");
            }
        } catch (e) {
            console.error("Error saat memuat data keyword populer:", e);
            this.searchCounts = {};
        }
    },
    
    // Fungsi untuk menambah jumlah pencarian
    addSearch: function(query) {
        if (!query || query.trim() === '') return;
        
        // Bersihkan dan normalkan query
        const normalizedQuery = this.normalizeKeyword(query);
        
        // Hanya catat jika keyword produk (1-3 kata, relevan dengan produk)
        if (this.isProductKeyword(normalizedQuery)) {
            // Tambah jumlah pencarian atau atur ke 1 jika belum ada
            this.searchCounts[normalizedQuery] = (this.searchCounts[normalizedQuery] || 0) + 1;
            
            // Simpan ke localStorage
            try {
                localStorage.setItem('popularKeywordCounts', JSON.stringify(this.searchCounts));
            } catch (e) {
                console.error("Error saat menyimpan data keyword populer:", e);
            }
            
            // Update tampilan trending jika diperlukan
            this.updateTrendingDisplay();
        }
    },
    
    // Fungsi untuk menormalisasi keyword
    normalizeKeyword: function(query) {
        // Hilangkan spasi berlebih, ubah ke lowercase
        let normalized = query.trim().toLowerCase();
        
        // Filter kata-kata stop words
        const stopWords = ['dan', 'atau', 'dengan', 'untuk', 'yang', 'di', 'ke', 'dari', 'pada', 'ini', 'itu'];
        let words = normalized.split(' ').filter(word => !stopWords.includes(word));
        
        // Ambil maksimal 3 kata pertama untuk keyword produk
        words = words.slice(0, 3);
        
        // Gabungkan kembali kata-kata
        return words.join(' ');
    },
    
    // Fungsi untuk memeriksa apakah keyword adalah keyword produk
    isProductKeyword: function(keyword) {
        if (!keyword || keyword.trim() === '') return false;
        
        // Periksa panjang keyword (1-3 kata)
        const wordCount = keyword.split(' ').length;
        if (wordCount > 3) return false;
        
        // Periksa panjang minimal
        if (keyword.length < 3) return false;
        
        // Periksa apakah mengandung kata produk umum
        const productTerms = ['hp', 'phone', 'handphone', 'smartphone', 'sepatu', 'tas', 'keyboard', 'mouse', 'laptop', 'komputer', 'tv', 'headphone', 'earphone', 'power', 'bank', 'gaming', 'kamera', 'robot', 'vacuum', 'smart', 'watch', 'jam', 'tangan', 'speaker', 'bluetooth'];
        
        const words = keyword.split(' ');
        
        // Jika salah satu kata dalam keyword adalah kata produk, anggap sebagai keyword produk
        for (const word of words) {
            if (productTerms.includes(word)) {
                return true;
            }
        }
        
        // Periksa jika keyword adalah kategori produk
        const productCategories = ['elektronik', 'fashion', 'kecantikan', 'kesehatan', 'perabotan', 'dapur', 'otomotif', 'olahraga', 'mainan', 'buku', 'aksesoris'];
        
        if (productCategories.includes(keyword)) {
            return true;
        }
        
        // Jika sampai di sini, kemungkinan bukan keyword produk
        return false;
    },
    
    // Fungsi untuk mendapatkan 5 keyword populer
    getTopKeywords: function(count = 5) {
    // Jika tidak ada data pencarian, gunakan default
    if (Object.keys(this.searchCounts).length === 0) {
        return this.defaultKeywords.slice(0, count);
    }
    
    // Urutkan keyword berdasarkan jumlah pencarian (dari tertinggi)
    const sortedKeywords = Object.entries(this.searchCounts)
        .sort((a, b) => b[1] - a[1])
        .map(entry => entry[0]);
    
    // Jika jumlah keyword kurang dari yang diminta, tambahkan dari default
    if (sortedKeywords.length < count) {
        // Filter default keyword yang belum ada di sortedKeywords
        const remainingDefaults = this.defaultKeywords.filter(keyword => 
            !sortedKeywords.includes(keyword)
        );
        
        // Tambahkan default keyword yang belum ada
        return [...sortedKeywords, ...remainingDefaults].slice(0, count);
    }
    
    // Kembalikan top keywords sejumlah yang diminta
    return sortedKeywords.slice(0, count);
},
    
    // Fungsi untuk memperbarui tampilan trending
    updateTrendingDisplay: function() {
        // Dapatkan container untuk trending items
        const trendingContainer = document.getElementById('additionalSuggestions');
        if (!trendingContainer) return;
        
        // Dapatkan semua suggestion-item yang ada di container
        const existingItems = trendingContainer.querySelectorAll('.suggestion-item');
        
        // Hapus semua item yang ada
        existingItems.forEach(item => {
            // Hindari menghapus elemen lain seperti trend-pill
            if (item.classList.contains('suggestion-item')) {
                item.remove();
            }
        });
        
        // Dapatkan 5 keyword populer
        const topKeywords = this.getTopKeywords(5);
        
        // Update badge count
        const trendCount = document.getElementById('trendCount');
        if (trendCount) {
            trendCount.textContent = topKeywords.length;
        }
        
        // Buat item untuk setiap keyword populer
        topKeywords.forEach(keyword => {
            const item = document.createElement('div');
            item.className = 'suggestion-item';
            item.setAttribute('data-text', keyword);
            
            item.innerHTML = `
    <span class="suggestion-icon"><i class="fa fa-arrow-trend-up"></i></span>
    <span class="suggestion-text">${toTitleCase(keyword)}</span>
`;
            
            // Tambahkan event listener untuk mengisi pencarian saat diklik
            item.addEventListener('click', function() {
                const suggestionText = this.getAttribute('data-text');
                fillSearchWithSuggestion(suggestionText);
            });
            
            trendingContainer.appendChild(item);
        });
    }
};
    // Fungsi untuk mendapatkan placeholder berdasarkan histori pencarian valid

function getPlaceholderSuggestions() {
    // Dapatkan histori pencarian
    let searchHistory = getSearchHistory();
    
    // Jika histori kosong, ambil dari localStorage terakhir
    if (searchHistory.length === 0) {
        try {
            // Coba dapatkan dari localStorage keyword terakhir
            const lastKeywords = localStorage.getItem('lastPlaceholderKeywords');
            if (lastKeywords) {
                searchHistory = JSON.parse(lastKeywords);
            }
        } catch (e) {
            console.error("Error saat mengambil keyword terakhir:", e);
        }
    }
    
    // Filter hanya pencarian valid (yang bukan typo dan mengandung keyword produk)
    const validSearches = searchHistory.filter(keyword => {
        // Dapatkan riwayat koreksi dari localStorage (yang pernah dikoreksi = typo)
        const correctedKeywords = JSON.parse(localStorage.getItem('clickedCorrections') || '[]');
        // Cek apakah mengandung keyword produk
        const containsProductKeyword = isProductKeyword(keyword);
        // Jangan masukkan keyword yang pernah dikoreksi dan yang bukan produk
        return !correctedKeywords.includes(keyword) && containsProductKeyword;
    });
    
    // Simpan ke localStorage untuk penggunaan masa depan
    try {
        if (validSearches.length > 0) {
            localStorage.setItem('lastPlaceholderKeywords', JSON.stringify(validSearches));
        }
    } catch (e) {
        console.error("Error saat menyimpan keyword placeholder:", e);
    }
    
    // PRIORITAS 1: Batasi hingga 10 keyword terakhir dari riwayat
    const recentValidSearches = validSearches.slice(0, 10);
    
    // Ekstrak keyword utama dari tiap pencarian
    const mainKeywords = recentValidSearches.map(extractMainKeyword);
    
  // Tambahkan array variasi prefix dengan bobot
const prefixVariations = [
    { text: "Promo", weight: 10 },           // Bobot 15% 
    { text: "Diskon", weight: 15 },          // Bobot 15%
    { text: "Disini Ada", weight: 5 },            // Bobot 10%
    { text: "Cari", weight: 15 },           // Bobot 10%
    { text: "Terbaru", weight: 15 },         // Bobot 15% (ditingkatkan karena Branded dipindahkan)
    { text: "Big Promo", weight: 10 },        // Bobot 15% (ditingkatkan karena Branded dipindahkan)
    { text: "Supplier", weight: 10 },   // Bobot 10%
    { text: "Flash Sale", weight: 15 },       // Bobot 5%
    { text: "Distributor", weight: 5 }          // Bobot 5%
    // Total bobot 100%
];

// Tambahkan array variasi suffix dengan bobot
const suffixVariations = [
    { text: "Termurah", weight: 20 },          // Bobot 20% (dikurangi)
    { text: "Gratis Ongkir", weight: 20 },    // Bobot 20% (dikurangi)
    { text: "Trending", weight: 20 },        // Bobot 20%
    { text: "Original", weight: 15 },     // Bobot 15%
    { text: "Grosir", weight: 10 },         // Bobot 10% (dikurangi)
    { text: "Branded", weight: 15 }         // Bobot 15% (ditambahkan)
    // Total bobot 100%
];

// Fungsi untuk memilih variasi berdasarkan bobot
function selectVariationByWeight(variations) {
    // Hitung total bobot
    const totalWeight = variations.reduce((sum, variation) => sum + variation.weight, 0);
    
    // Pilih angka acak antara 0 dan total bobot
    const random = Math.random() * totalWeight;
    
    // Temukan variasi yang sesuai
    let cumulativeWeight = 0;
    for (const variation of variations) {
        cumulativeWeight += variation.weight;
        if (random < cumulativeWeight) {
            return variation.text;
        }
    }
    
    // Fallback jika ada masalah
    return variations[0].text;
}

// Distribusi untuk kata tambahan di riwayat pencarian: 
// 40% prefix, 30% suffix, 30% tanpa tambahan
const processedRecentKeywords = mainKeywords.map(keyword => {
    const random = Math.random() * 100;
    
    if (random < 40) {
        // Tambahkan prefix dengan cek duplikasi
        const prefix = selectVariationByWeight(prefixVariations);
        return addPrefix(keyword, prefix);
    } else if (random < 70) {
        // Tambahkan suffix
        const suffix = selectVariationByWeight(suffixVariations);
        return keyword + " " + suffix;
    }
    
    // 30% tanpa tambahan
    return keyword;
});
    
    // PRIORITAS 2: Keyword promo spesial (selalu disertakan)
const specialPromoKeywords = getSpecialPromoKeywords();
    
    // PRIORITAS 3: Keyword dari trend untuk melengkapi
    // Ambil dari trending suggestions atau gunakan default
    let trendKeywords = [];
    try {
        // Coba ambil keyword dari trending di HTML
        const trendItems = document.querySelectorAll('#additionalSuggestions .suggestion-item');
        if (trendItems && trendItems.length > 0) {
            trendKeywords = Array.from(trendItems).map(item => item.getAttribute('data-text')).filter(Boolean);
        }
    } catch (e) {
        console.error("Error saat mengambil trend keywords:", e);
    }
    
    // Jika tidak ada trend keywords, gunakan default
    if (trendKeywords.length === 0) {
        trendKeywords = [
            "Handphone Samsung",
            "Sepatu Pria",
            "Tas Wanita",
            "Laptop Gaming",
            "Headphone Bluetooth",
            "Kamera Mirrorless",
            "Smart TV Android",
            "Jam Tangan Digital",
            "Mainan Edukasi",
            "Peralatan Dapur"
        ];
    }
    
  // Modifikasi bagian dari keyword trend
// Distribusi untuk keyword trend:
// 30% prefix, 30% suffix, 40% tanpa tambahan
const processedTrendKeywords = trendKeywords.map(keyword => {
    const random = Math.random() * 100;
    
    if (random < 30) {
        // Tambahkan prefix dengan cek duplikasi
        const prefix = selectVariationByWeight(prefixVariations);
        return addPrefix(keyword, prefix);
    } else if (random < 60) {
        // Tambahkan suffix
        const suffix = selectVariationByWeight(suffixVariations);
        return keyword + " " + suffix;
    }
    
    // 40% tanpa tambahan
    return keyword;
});
    
    // Gabungkan semua keyword sesuai prioritas
    let allKeywords = [...processedRecentKeywords, ...specialPromoKeywords];
    
    // Jika masih kurang dari 20, tambahkan dari trend keywords
    if (allKeywords.length < 20) {
        const neededCount = 20 - allKeywords.length;
        allKeywords = allKeywords.concat(processedTrendKeywords.slice(0, neededCount));
    }
    
    // Jika masih kurang dari 20 (situasi sangat jarang terjadi), duplikasi beberapa
    while (allKeywords.length < 20) {
        // Duplikasi dengan variasi berbeda (tambah/hapus "Promo")
        const baseKeywords = [...processedRecentKeywords, ...processedTrendKeywords];
        if (baseKeywords.length === 0) break; // Safeguard
        
        const randomIndex = Math.floor(Math.random() * baseKeywords.length);
        let duplicateKeyword = baseKeywords[randomIndex];
        
        // Jika sudah ada "Promo", hapus; jika belum ada, tambahkan
        if (duplicateKeyword.startsWith("Promo ")) {
            duplicateKeyword = duplicateKeyword.substring(6);
        } else {
            duplicateKeyword = "Promo " + duplicateKeyword;
        }
        
        allKeywords.push(duplicateKeyword);
    }
    
    // Batasi ke 20 dan acak urutan
    const finalKeywords = shuffleArray(allKeywords).slice(0, 20);
    
    // Pastikan tidak melebihi lebar dengan potong keyword jika terlalu panjang
// dan konversi ke Title Case (huruf kapital di awal kata)
return finalKeywords.map(keyword => toTitleCase(truncateKeywordIfNeeded(keyword)));
}
  
  // Fungsi untuk mengubah teks menjadi Title Case (huruf kapital di awal kata)
function toTitleCase(text) {
    return text.split(' ')
        .map(word => {
            if (word.length === 0) return word;
            return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase();
        })
        .join(' ');
}
    
    // Fungsi untuk mengekstrak keyword utama
function extractMainKeyword(keyword) {
    // Hilangkan kata "Promo" jika ada
    let cleanKeyword = keyword;
    if (cleanKeyword.toLowerCase().startsWith("promo ")) {
        cleanKeyword = cleanKeyword.substring(6);
    }
    
    // Potong keyword menjadi kata-kata
    const words = cleanKeyword.split(' ');
    
    // Jika keyword pendek (1-2 kata), gunakan apa adanya
    if (words.length <= 2) {
        return cleanKeyword;
    }
    
    // Coba temukan keyword produk
    const productTerms = [
        'hp', 'phone', 'handphone', 'smartphone', 'telepon', 'gadget',
        'sepatu', 'shoes', 'sandal', 'sneakers', 
        'tas', 'bag', 'dompet', 'wallet', 
        'laptop', 'notebook', 'komputer', 'pc', 'monitor',
        'keyboard', 'mouse', 'headset', 'earphone', 'headphone',
        'kamera', 'camera', 'lensa', 'tripod',
        'tv', 'televisi', 'smart', 
        'jam', 'watch', 'smartwatch',
        'speaker', 'audio', 'bluetooth',
        'mesin', 'kulkas', 'freezer', 'ac', 'kipas',
        'baju', 'kaos', 'kemeja', 'dress', 'jaket', 'celana', 'jeans',
        'mainan', 'toys', 'game', 'gaming',
        'buku', 'novel', 'komik',
        'makeup', 'skincare', 'kecantikan', 'parfum'
    ];
    
    // Cek apakah ada kata produk, ambil kata tersebut dan satu kata setelahnya
    for (let i = 0; i < words.length - 1; i++) {
        if (productTerms.includes(words[i].toLowerCase())) {
            return words[i] + (words[i+1] ? " " + words[i+1] : "");
        }
    }
    
    // Jika tidak ditemukan kata produk, ambil 2 kata pertama
    return words.slice(0, 2).join(' ');
}
    
// Fungsi untuk mengecek apakah keyword mengandung kata produk
function isProductKeyword(keyword) {
    if (!keyword || keyword.trim() === '') return false;
    
    // Daftar kata produk
    const productTerms = [
        'hp', 'phone', 'handphone', 'smartphone', 'telepon', 'gadget',
        'sepatu', 'shoes', 'sandal', 'sneakers', 
        'tas', 'bag', 'dompet', 'wallet', 
        'laptop', 'notebook', 'komputer', 'pc', 'monitor',
        'keyboard', 'mouse', 'headset', 'earphone', 'headphone',
        'kamera', 'camera', 'lensa', 'tripod',
        'tv', 'televisi', 'smart tv', 
        'jam', 'watch', 'smartwatch',
        'speaker', 'audio', 'bluetooth',
        'mesin', 'kulkas', 'freezer', 'ac', 'kipas',
        'baju', 'kaos', 'kemeja', 'dress', 'jaket', 'celana', 'jeans',
        'mainan', 'toys', 'game', 'gaming',
        'buku', 'novel', 'komik',
        'makeup', 'skincare', 'kecantikan', 'parfum'
    ];
    
    // Kategori produk
    const categories = [
        'elektronik', 'fashion', 'kecantikan', 'kesehatan', 'perabotan', 
        'dapur', 'otomotif', 'olahraga', 'buku', 'aksesoris', 'rumah tangga'
    ];
    
    // Cek apakah keyword mengandung kata produk
    const words = keyword.toLowerCase().split(' ');
    for (const word of words) {
        if (productTerms.includes(word) || categories.includes(word)) {
            return true;
        }
    }
    
    // Cek apakah ada beberapa kata yang berurutan yang mungkin jadi nama produk
    for (let i = 0; i < words.length - 1; i++) {
        const twoWords = words[i] + ' ' + words[i+1];
        if (productTerms.includes(twoWords) || categories.includes(twoWords)) {
            return true;
        }
    }
    
    return false;
}

// Fungsi untuk mengacak array (tetap sama)
function shuffleArray(array) {
    const newArray = [...array];
    for (let i = newArray.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [newArray[i], newArray[j]] = [newArray[j], newArray[i]];
    }
    return newArray;
}
    
    // Fungsi untuk menambahkan prefix dengan cek duplikasi
function addPrefix(keyword, prefix) {
    // Jika keyword sudah diawali dengan prefix yang sama, jangan tambahkan lagi
    if (keyword.toLowerCase().startsWith(prefix.toLowerCase())) {
        return keyword;
    }
    return prefix + " " + keyword;
}

// Fungsi untuk memotong keyword jika terlalu panjang (tetap sama)
function truncateKeywordIfNeeded(keyword) {
    const MAX_LENGTH = 20; // Sesuaikan dengan lebar yang tersedia
    
    if (keyword.length <= MAX_LENGTH) {
        return keyword;
    }
    
    // Potong keyword menjadi kata-kata
    const words = keyword.split(' ');
    
    // Jika hanya 1 kata dan terlalu panjang
    if (words.length === 1) {
        return keyword.substring(0, MAX_LENGTH) + '...';
    }
    
    // Coba ambil 1-2 kata pertama saja (keyword utama)
    const mainKeywords = words.slice(0, Math.min(2, words.length)).join(' ');
    
    // Jika masih terlalu panjang, potong
    if (mainKeywords.length > MAX_LENGTH) {
        return mainKeywords.substring(0, MAX_LENGTH) + '...';
    }
    
    return mainKeywords;
}
    
    // Fungsi untuk mendapatkan keyword promo spesial berdasarkan tanggal
function getSpecialPromoKeywords() {
    const now = new Date();
    const currentDate = now.getDate();
    const currentMonth = now.getMonth(); // 0-11 (Jan-Dec)
    const currentYear = now.getFullYear();
    const currentDay = now.getDay(); // 0-6 (Sun-Sat)
    
    // Array untuk menyimpan keyword promo spesial
    const specialPromoKeywords = [];
    
    // Flag untuk menandai apakah ada promo tanggal kembar yang aktif
    let hasDoubleDatePromo = false;
    
    // Aturan 1: Promo tanggal kembar (9 hari sebelum, dan 1 hari setelah)
    // Cek apakah tanggal saat ini dalam rentang promo tanggal kembar
    for (let i = 1; i <= 12; i++) { // Cek untuk setiap bulan
        const doubleDate = i; // Tanggal kembar (1,2,3,...,12)
        const doubleMonth = i - 1; // Bulan untuk tanggal kembar (0-11 untuk Jan-Des)
        
        // Tanggal kembar yang tepat
        const exactDoubleDate = new Date(currentYear, doubleMonth, doubleDate);
        
        // Tanggal awal promo (9 hari sebelum tanggal kembar)
        const promoStartDate = new Date(currentYear, doubleMonth, doubleDate - 9);
        
        // Tanggal akhir promo (1 hari setelah tanggal kembar)
        const promoEndDate = new Date(currentYear, doubleMonth, doubleDate + 1);
        
        // Cek apakah hari ini berada dalam rentang promo
        if (now >= promoStartDate && now <= promoEndDate) {
            // Selalu tampilkan format "Promo X.X" untuk rentang tanggal ini
            specialPromoKeywords.push(`Promo ${i}.${i}`);
            hasDoubleDatePromo = true;
            break; // Hentikan loop setelah menemukan rentang yang cocok
        }
    }
    
    // Jika tidak ada dalam rentang tanggal kembar, gunakan promo berdasarkan hari
    if (!hasDoubleDatePromo) {
        const days = ["Minggu", "Senin", "Selasa", "Rabu", "Kamis", "Jumat", "Sabtu"];
        const dayNames = ["Ceria", "Berkah", "Spesial", "Hemat", "Untung", "Bahagia", "Santai"];
        specialPromoKeywords.push(`Promo ${days[currentDay]} ${dayNames[currentDay]}`);
    }
            
    // Aturan 2: Promo gajian (5 hari sebelum dan 1 hari setelah tanggal 25)
    const paydayStart = new Date(currentYear, currentMonth, 25 - 5);
    const paydayEnd = new Date(currentYear, currentMonth, 25 + 1);
    if (now >= paydayStart && now <= paydayEnd) {
        specialPromoKeywords.push("Promo Gajian");
    }
    
    // Aturan 4: Promo Tahun Baru (10 hari sebelum dan 3 hari setelah tanggal 1 Januari)
    const newYearStart = new Date(currentYear, 0, 1 - 10); // 0 = Januari
    const newYearEnd = new Date(currentYear, 0, 1 + 3);
    if (now >= newYearStart && now <= newYearEnd) {
        specialPromoKeywords.push("Promo Tahun Baru");
    }
    
    // Aturan 3 dan 5: Tetap sama
    specialPromoKeywords.push("Flash Sale Mingguan");
    specialPromoKeywords.push("Cashback 90%");
    
    // Jika tidak ada promo spesial yang aktif, tambahkan default
    if (specialPromoKeywords.length < 5) {
        const defaultPromos = [
            "Diskon Spesial Akhir Tahun",
            "Gratis Ongkir Spesial",
            "Promo Bundle Hemat",
            "Diskon Pengguna Baru",
            "Voucher Spesial"
        ];
        
        // Tambahkan promo default yang diperlukan
        for (let i = 0; i < defaultPromos.length && specialPromoKeywords.length < 5; i++) {
            specialPromoKeywords.push(defaultPromos[i]);
        }
    }
    
    return specialPromoKeywords;
}

// Buat fungsi untuk mengisi keyword suggestions secara dinamis
function updateKeywordSuggestionsPopup() {
    // Ambil container
    const keywordGrid = document.querySelector('.keyword-suggestions-grid');
    if (!keywordGrid) return;
    
    // Kosongkan isi container
    keywordGrid.innerHTML = '';
    
    // Kumpulkan berbagai sumber keyword
    let keywordSources = [];
    
    // Sumber 1: Dari popularKeywords jika ada
    const trendingKeywords = popularKeywords.getTopKeywords(4);
    keywordSources = keywordSources.concat(trendingKeywords);
    
    // Sumber 2: Dari kategori produk di sampleProducts
    const productCategories = [...new Set(sampleProducts.map(p => p.category))];
    keywordSources = keywordSources.concat(productCategories.slice(0, 3));
    
    // Sumber 3: Dari riwayat pencarian - FILTER KUALITAS
    const searchHistory = getSearchHistory().slice(0, 6); // Ambil lebih banyak untuk filter
    const filteredHistory = searchHistory.filter(keyword => {
        // Filter keyword yang terlalu pendek (kemungkinan typo)
        if (keyword.length < 3) return false;
        
        // Filter keyword yang terlalu panjang (kemungkinan bukan keyword produk)
        if (keyword.length > 30) return false;
        
        // Filter keyword yang tidak mengandung kata produk
        const productTerms = [
            'hp', 'phone', 'handphone', 'smartphone', 'sepatu', 'tas',
            'laptop', 'keyboard', 'mouse', 'headphone', 'tv', 'smart',
            'kamera', 'robot', 'vacuum', 'jam', 'speaker', 'bluetooth',
            'baju', 'celana', 'jaket', 'mainan', 'game', 'buku',
            'makeup', 'parfum', 'kulkas', 'mesin', 'komputer'
        ];
        
        // Cek apakah mengandung kata produk
        const hasProductTerm = productTerms.some(term => 
            keyword.toLowerCase().includes(term)
        );
        
        // Hanya terima jika mengandung kata produk
        return hasProductTerm;
    });
    keywordSources = keywordSources.concat(filteredHistory.slice(0, 3));
    
    // Sumber 4: Dari nama pendek produk
    const shortNames = [...new Set(sampleProducts.map(p => p.shortName))];
    keywordSources = keywordSources.concat(shortNames.slice(0, 4));
    
    // Hapus duplikat dan batasi jumlah
    const uniqueKeywords = [...new Set(keywordSources)];
    
    // FILTER AKHIR: Memastikan semua keyword valid
    const validKeywords = uniqueKeywords.filter(keyword => {
        // Pastikan bukan string kosong
        if (!keyword || keyword.trim() === '') return false;
        
        // Filter keyword yang terlalu pendek
        if (keyword.length < 3) return false;
        
        // Filter keyword yang terlihat seperti typo (hanya karakter berulang)
        const repeatedCharsPattern = /^(.)\1+$/;
        if (repeatedCharsPattern.test(keyword)) return false;
        
        // Filter keyword yang tidak memiliki vokal (kemungkinan typo)
        const hasVowel = /[aeiouy]/i.test(keyword);
        if (!hasVowel) return false;
        
        // Filter keyword dengan terlalu banyak karakter aneh
        const specialCharsCount = (keyword.match(/[^a-zA-Z0-9\s]/g) || []).length;
        if (specialCharsCount > 2) return false;
        
        return true;
    });
    
    // Acak urutan keyword untuk variasi
    const shuffledKeywords = shuffleArray(validKeywords);
    const displayKeywords = shuffledKeywords.slice(0, 8); // Tampilkan maksimal 8 keyword
    
    // Jika tidak ada keyword valid yang tersisa, tambahkan beberapa default
    if (displayKeywords.length === 0) {
        const defaultKeywords = [
            "Smartphone Android", 
            "Sepatu Sneakers", 
            "Tas Selempang", 
            "Headphone Bluetooth", 
            "Keyboard Gaming", 
            "Power Bank", 
            "Smart TV", 
            "Robot Vacuum"
        ];
        displayKeywords.push(...defaultKeywords);
    }
    
    // Tambahkan ke container
    displayKeywords.forEach(keyword => {
        const tag = document.createElement('div');
        tag.className = 'keyword-suggestion-tag';
        tag.setAttribute('data-product', keyword);
        tag.textContent = toTitleCase(keyword);
        
        // Tambahkan event listener
        tag.addEventListener('click', function() {
            const productName = this.getAttribute('data-product') || this.textContent.trim();
            
            // Sembunyikan popup
            document.body.classList.remove('show-suggestions');
            keywordSuggestionsPopup.style.display = 'none';
            suggestionsOverlay.style.display = 'none';
            
            // Nonaktifkan mode khusus
            deactivateSpecialModes();
            
            // Isi input pencarian dan tampilkan ikon silang
            expandedSearchInput.value = productName;
            expandedSearchPlaceholder.classList.add('force-hide-placeholder');
            expandedClearSearchIcon.style.display = 'flex';
            
            // Jalankan pencarian langsung dengan sedikit delay
            setTimeout(function() {
                executeSearch();
                
                // Pastikan tab filter ditampilkan
                if (isSearchResultShown) {
                    forceShowFilterTabs();
                }
            }, 50);
        });
        
        keywordGrid.appendChild(tag);
    });
}
  
    // Fungsi untuk mendapatkan kata-kata unik dari semua produk
function getUniqueProductWords() {
    const allWords = new Set();
    
    // Proses nama produk
    sampleProducts.forEach(product => {
        // Pecah nama produk menjadi kata-kata
        const productWords = product.name.toLowerCase().split(' ');
        
        // Tambahkan kata-kata ke Set
        productWords.forEach(word => {
            if (word.length >= 2) { // Hanya kata dengan panjang minimal 2
                allWords.add(word);
            }
        });
        
        // Tambahkan juga kategori produk
        if (product.category) {
            const categoryWords = product.category.toLowerCase().split(' ');
            categoryWords.forEach(word => {
                if (word.length >= 2) {
                    allWords.add(word);
                }
            });
        }
    });
    
    // Konversi Set ke Array dan urutkan
    return Array.from(allWords).sort();
}

    
// Fungsi untuk mendapatkan frasa dari produk berdasarkan kata awal
function getProductPhrases(startWord, targetWordCount) {
    const phrases = [];
    const startWordLower = startWord.toLowerCase().trim();
    
    console.log(`Mencari frasa dengan kata awal: "${startWordLower}"`);
    
    // Cari di nama produk dan kategori
    sampleProducts.forEach(product => {
        console.log(`Memeriksa produk: ${product.name}`);
        
        // Proses nama produk
        processProductText(product.name, startWordLower, targetWordCount, phrases);
        
        // Proses kategori produk
        if (product.category) {
            console.log(`Memeriksa kategori: ${product.category}`);
            processProductText(product.category, startWordLower, targetWordCount, phrases);
        }
    });
    
    console.log(`Hasil frasa untuk "${startWordLower}":`, phrases);
    return phrases;
}
    
    
// Fungsi helper untuk memproses teks produk
function processProductText(text, startWord, targetWordCount, resultPhrases) {
    // Pastikan text adalah string
    if (!text || typeof text !== 'string') return;
    
    // Konversi ke lowercase untuk pencarian case-insensitive
    const textLower = text.toLowerCase();
    const words = text.split(' ');
    
    // Cari startWord dalam text
    const startWordLower = startWord.toLowerCase();
    const searchIndex = textLower.indexOf(startWordLower);
    
    if (searchIndex >= 0) {
        // Temukan kata berikutnya setelah startWord
        // Pertama identifikasi di mana startWord berada dalam array kata
        let startWordIndex = -1;
        const wordsLower = words.map(w => w.toLowerCase());
        
        for (let i = 0; i < wordsLower.length; i++) {
            if (wordsLower[i] === startWordLower) {
                startWordIndex = i;
                break;
            }
        }
        
        // Jika ditemukan dan ada kata berikutnya
        if (startWordIndex >= 0 && startWordIndex + 1 < words.length) {
            // Buat prediksi dengan 2 kata: startWord + kata berikutnya
            const prediction = startWord + " " + words[startWordIndex + 1];
            
            // Tambahkan ke hasil jika belum ada
            if (!resultPhrases.includes(prediction)) {
                resultPhrases.push(prediction);
                console.log(`Menambahkan prediksi: ${prediction} dari teks: ${text}`);
            }
        }
    }
}
    
    // Fungsi untuk mendapatkan semua varian kata berdasarkan sinonim, typo, dan kata terkait
function getWordVariants(word) {
    const variants = new Set([word]); // Mulai dengan kata asli
    
    // 1. Tambahkan sinonim
    if (keywordPredictionDB.synonyms[word]) {
        keywordPredictionDB.synonyms[word].forEach(synonym => {
            variants.add(synonym);
        });
    }
    
    // 2. Periksa koreksi typo (kata yang typo -> kata yang benar)
    Object.entries(keywordPredictionDB.typoCorrections).forEach(([typo, correction]) => {
        // Jika kata adalah koreksi dari suatu typo, tambahkan typo nya ke varian
        if (correction === word) {
            variants.add(typo);
        }
        // Jika kata adalah typo, tambahkan koreksinya
        if (typo === word) {
            variants.add(correction);
        }
    });
    
    // 3. Tambahkan kata terkait (related keywords)
    if (keywordPredictionDB.relatedKeywords[word]) {
        // Karena related biasanya adalah frasa, kita hanya perlu kata pertama
        keywordPredictionDB.relatedKeywords[word].forEach(related => {
            const firstWord = related.split(' ')[0];
            if (firstWord && firstWord !== word) {
                variants.add(firstWord);
            }
        });
    }
    
    // 4. Cek kata-kata umum dengan persamaan tinggi
    const commonReplacements = {
        'telepon': ['telpon', 'telephone', 'hp', 'handphone', 'smartphone', 'ponsel'],
        'handphone': ['hp', 'telephone', 'telpon', 'smartphone', 'ponsel', 'telepon'],
        'smartphone': ['hp', 'telephone', 'telpon', 'handphone', 'ponsel', 'telepon'],
        'sepatu': ['shoes', 'sneakers', 'sendal', 'sandal'],
        'tas': ['bag', 'dompet', 'ransel', 'backpack', 'handbag'],
        'laptop': ['notebook', 'komputer', 'pc', 'netbook'],
        'tv': ['televisi', 'television', 'monitor']
    };
    
    // Tambahkan dari common replacements
    Object.entries(commonReplacements).forEach(([key, replacements]) => {
        if (key === word) {
            replacements.forEach(r => variants.add(r));
        } else if (replacements.includes(word)) {
            variants.add(key);
            // Tambahkan juga replacements lain
            replacements.forEach(r => {
                if (r !== word) variants.add(r);
            });
        }
    });
    
    // 5. Tambahkan varian fuzzy matching untuk kesalahan ejaan umum
    if (word.length > 3) {
        // Contoh kesalahan umum dalam ejaan bahasa Indonesia
        const fuzzyVariants = [];
        
        // Penukaran huruf vokal (misal: handphane -> handphone)
        const vowels = ['a', 'e', 'i', 'o', 'u'];
        for (let i = 0; i < word.length; i++) {
            if (vowels.includes(word[i])) {
                for (const vowel of vowels) {
                    if (vowel !== word[i]) {
                        const variant = word.substring(0, i) + vowel + word.substring(i + 1);
                        fuzzyVariants.push(variant);
                    }
                }
            }
        }
        
        // Penghilangan huruf (misal: hndphone -> handphone)
        for (let i = 0; i < word.length; i++) {
            const variant = word.substring(0, i) + word.substring(i + 1);
            if (variant.length > 2) { // Abaikan hasil yang terlalu pendek
                fuzzyVariants.push(variant);
            }
        }
        
        // Tambahkan penambahan huruf umum (misal: handpone -> handphone)
        const commonLetterPairs = {
            'p': 'ph', 'f': 'ph', 'c': 'ch',
            'o': 'ou', 'i': 'ie', 'n': 'ng', 
            's': 'sh', 'a': 'ah'
        };
        
        for (let i = 0; i < word.length; i++) {
            if (commonLetterPairs[word[i]]) {
                const variant = word.substring(0, i) + commonLetterPairs[word[i]] + word.substring(i + 1);
                fuzzyVariants.push(variant);
            }
        }
        
        // Tambahkan varian fuzzy jika cukup mirip dengan kata asli
        fuzzyVariants.forEach(variant => {
            // Hitung kesamaan dengan Levenshtein distance sederhana
            const similarity = calculateSimilarity(word, variant);
            if (similarity > 0.7) { // Minimal 70% mirip
                variants.add(variant);
            }
        });
    }
    
    return Array.from(variants);
}

// Fungsi sederhana untuk menghitung kesamaan string (0-1)
function calculateSimilarity(str1, str2) {
    // Jika sama persis
    if (str1 === str2) return 1;
    
    // Jika panjang sangat berbeda
    const lengthDiff = Math.abs(str1.length - str2.length);
    if (lengthDiff > 2) return 0;
    
    // Hitung jumlah karakter yang cocok secara berurutan
    let matches = 0;
    const minLength = Math.min(str1.length, str2.length);
    
    for (let i = 0; i < minLength; i++) {
        if (str1[i] === str2[i]) {
            matches++;
        }
    }
    
    // Nilai kesamaan berdasarkan jumlah kecocokan dibanding panjang maksimum
    return matches / Math.max(str1.length, str2.length);
}

    // Fungsi pencarian sederhana untuk menemukan kata berikutnya dari nama produk
function findNextWordPredictions(searchWord, nextStartsWith = "") {
    searchWord = searchWord.toLowerCase().trim();
    const predictions = [];
    
    // PENDEKATAN BARU: Langsung cari kata kedua dari produk 
    const secondWords = new Map(); // Gunakan Map untuk menghindari duplikat dan menyimpan jumlah kemunculan
    
    // Periksa semua produk
    sampleProducts.forEach(product => {
        const words = product.name.toLowerCase().split(' ');
        
        // Cari kata pertama yang cocok
        for (let i = 0; i < words.length - 1; i++) {
            if (words[i] === searchWord) {
                // Ambil kata kedua
                const nextWord = words[i+1];
                if (nextWord) {
                    // Filter berdasarkan nextStartsWith jika diberikan
                    if (nextStartsWith && !nextWord.startsWith(nextStartsWith.toLowerCase())) {
                        continue; // Lewati jika tidak dimulai dengan huruf yang diinginkan
                    }
                    
                    // Tambahkan ke map dengan jumlah kemunculan
                    if (secondWords.has(nextWord)) {
                        secondWords.set(nextWord, {
                            word: nextWord,
                            count: secondWords.get(nextWord).count + 1
                        });
                    } else {
                        secondWords.set(nextWord, {
                            word: nextWord,
                            count: 1
                        });
                    }
                }
            }
        }
        
        // Periksa juga kategori
        if (product.category) {
            const catWords = product.category.toLowerCase().split(' ');
            for (let i = 0; i < catWords.length - 1; i++) {
                if (catWords[i] === searchWord) {
                    const nextWord = catWords[i+1];
                    if (nextWord) {
                        // Filter berdasarkan nextStartsWith jika diberikan
                        if (nextStartsWith && !nextWord.startsWith(nextStartsWith.toLowerCase())) {
                            continue;
                        }
                        
                        if (secondWords.has(nextWord)) {
                            secondWords.set(nextWord, {
                                word: nextWord,
                                count: secondWords.get(nextWord).count + 1
                            });
                        } else {
                            secondWords.set(nextWord, {
                                word: nextWord,
                                count: 1
                            });
                        }
                    }
                }
            }
        }
    });
    
    // Konversi Map ke array, urutkan berdasarkan frekuensi, dan buat prediksi
    const sortedSecondWords = Array.from(secondWords.values())
        .sort((a, b) => b.count - a.count);
    
    // Buat prediksi dari kata kedua yang ditemukan
    sortedSecondWords.forEach(item => {
        predictions.push(searchWord + ' ' + item.word);
    });
    
    // Log untuk debugging
    console.log(`PREDIKSI KATA KEDUA untuk "${searchWord}" (nextStartsWith: "${nextStartsWith}"):`, predictions);
    
    // Jika tidak ada prediksi, coba alternatif dari keyword database
    if (predictions.length === 0) {
        // Cek dari keywordPredictionDB
        keywordPredictionDB.productKeywords.forEach(keyword => {
            const words = keyword.toLowerCase().split(' ');
            if (words[0] === searchWord && words.length > 1) {
                // Filter berdasarkan nextStartsWith jika diberikan
                if (nextStartsWith && !words[1].startsWith(nextStartsWith.toLowerCase())) {
                    return;
                }
                predictions.push(searchWord + ' ' + words[1]);
            }
        });
    }
    
    return predictions;
}
    
    
// Fungsi utama untuk prediksi bertahap berdasarkan kata
function generateProductPredictions(input) {
    const inputLower = input.toLowerCase().trim();
    const words = inputLower.split(' ').filter(word => word.length > 0);
    const results = [];
    
    // TAMBAHAN INI: Deteksi input dengan spasi di akhir
    if (input.endsWith(' ') && words.length === 1) {
        // Gunakan fungsi khusus untuk mendapatkan prediksi dua kata
        const twoWordPredictions = findNextWordPredictions(words[0]);
        
        // Konversi ke format yang diharapkan
        twoWordPredictions.forEach(prediction => {
            results.push({
                text: prediction,
                type: 'product', // Gunakan tipe produk untuk ikon keranjang
                relevance: 100 // Prioritas tinggi
            });
        });
        
        // Return langsung hasil prediksi dua kata
        return results;
    }
    
    // Case 1: User baru mengetik satu huruf atau sedang mengetik kata pertama
    if (words.length === 1 && words[0].length >= 1 && !inputLower.endsWith(' ')) {
        // Dapatkan daftar kata unik dari semua produk
        const uniqueProductWords = getUniqueProductWords();
        
        // Filter kata yang dimulai dengan input
        const matchingWords = uniqueProductWords.filter(word => 
            word.toLowerCase().startsWith(words[0])
        );
        
        // Tambahkan kata yang cocok sebagai prediksi
        matchingWords.forEach(word => {
            results.push({
                text: toTitleCase(word),
                type: 'product',
                relevance: 80 + (30 - Math.min(30, word.length)) // Kata pendek dapat skor lebih tinggi
            });
        });
    }
    
    // Case 2: User sudah mengetik 1 kata lengkap (ada spasi di akhir)
    else if (words.length === 1 && inputLower.endsWith(' ')) {
        // Ambil kata yang sedang dicari (tanpa spasi)
        const searchWord = words[0];
        console.log(`Mencari prediksi untuk kata: "${searchWord}"`);
        
        // Gunakan fungsi pencarian langsung yang sudah dimodifikasi
        const nextWordPredictions = findNextWordPredictions(searchWord);
        console.log(`Prediksi yang ditemukan:`, nextWordPredictions);
        
        // Tambahkan semua prediksi ke hasil
        nextWordPredictions.forEach(prediction => {
            results.push({
                text: toTitleCase(prediction),
                type: 'product',
                relevance: 90 // Prioritas sangat tinggi
            });
        });
        
        // Pastikan ada hasil - tambahkan debug untuk melihat semua produk jika tidak ada prediksi
        if (nextWordPredictions.length === 0) {
            console.log("Tidak ada prediksi yang ditemukan. Daftar semua produk:");
            sampleProducts.forEach(product => {
                console.log(`- Produk: ${product.name}, Kategori: ${product.category}`);
            });
        }
    }
    
    // Kasus lainnya tetap sama
    // Case 3: User sudah mengetik 2 kata atau lebih
    else if (words.length >= 2) {
        // Cari frasa produk yang cocok dengan kata-kata input
        const matchingPhrases = getProductPhrases(words.join(' '), words.length + 1);
        
        // Tambahkan frasa yang cocok
        matchingPhrases.forEach(phrase => {
            results.push({
                text: toTitleCase(phrase),
                type: 'product',
                relevance: 70
            });
        });
    }
    
    // Case 4: User mengetik kata yang ada di tengah nama produk (tidak di awal)
    if (words.length >= 1) {
        // Cari produk yang mengandung kata input di tengah
        sampleProducts.forEach(product => {
            const productNameLower = product.name.toLowerCase();
            const categoryLower = product.category.toLowerCase();
            
            // Periksa apakah semua kata input ada dalam nama produk atau kategori
            const allWordsExist = words.every(word => 
                productNameLower.includes(word) || categoryLower.includes(word)
            );
            
            if (allWordsExist) {
                // Jika input adalah bagian dari nama produk tapi bukan awalnya
                if (!productNameLower.startsWith(inputLower)) {
                    results.push({
                        text: product.name,
                        type: 'product',
                        relevance: 65
                    });
                }
                
                // Jika input adalah bagian dari kategori
                if (categoryLower.includes(inputLower) && !results.some(r => r.text.toLowerCase() === product.category.toLowerCase())) {
                    results.push({
                        text: product.category,
                        type: 'product',
                        relevance: 60
                    });
                }
            }
        });
    }
    
    return results;
}

// Fungsi untuk memverifikasi integrasi produk dalam system
function verifyProductIntegration(productName) {
    const words = productName.toLowerCase().split(' ')
                 .filter(w => w.length >= 3);
    
    console.log("=== VERIFIKASI INTEGRASI PRODUK ===");
    console.log("Produk:", productName);
    console.log("Kata-kata penting:", words);
    
    words.forEach(word => {
        console.log(`\nKata: "${word}"`);
        
        // Cek sinonim
        if (keywordPredictionDB.synonyms[word]) {
            console.log("✅ Sinonim ditemukan:", keywordPredictionDB.synonyms[word]);
        } else {
            console.log("❌ Tidak ada sinonim");
        }
        
        // Cek kata terkait
        if (keywordPredictionDB.relatedKeywords[word]) {
            console.log("✅ Kata terkait ditemukan:", keywordPredictionDB.relatedKeywords[word]);
        } else {
            console.log("❌ Tidak ada kata terkait");
        }
        
        // Cek typo yang mengarah ke kata ini
        const typos = Object.entries(keywordPredictionDB.typoCorrections)
            .filter(([typo, correction]) => correction === word)
            .map(([typo]) => typo);
        
        if (typos.length > 0) {
            console.log("✅ Typo corrections ditemukan:", typos);
        } else {
            console.log("❌ Tidak ada typo corrections");
        }
    });
}

// Fungsi untuk menampilkan facet overlay berdasarkan device
function showFacetPanel() {
    // Reset tempActiveFilters dengan menyalin activeFilters yang sekarang
    tempActiveFilters = JSON.parse(JSON.stringify(activeFilters || {}));
    
    if (window.innerWidth < 1025) { // 1025px bukan 768px
        // Mobile & Tablet
        facetOverlay.style.display = 'flex';
        // Render facet content
        renderFacetContent(facetContent);
    } else {
        // Desktop
        facetPanelDesktop.style.display = 'block';
        // Render facet content
        renderFacetContent(facetContentDesktop);
    }
}

// Perbaiki fungsi hideFacetPanel
function hideFacetPanel() {
    // Sembunyikan panel facet
    if (facetOverlay) facetOverlay.style.display = 'none';
    if (facetPanelDesktop) facetPanelDesktop.style.display = 'none';
    
    // Pastikan tampilan desktop juga tersembunyi
    const desktopFacetPanel = document.querySelector('.facet-panel-desktop');
    if (desktopFacetPanel) desktopFacetPanel.style.display = 'none';
}

// Fungsi untuk render konten facet
// Perbaiki fungsi renderFacetContent
function renderFacetContent(container) {
    // Kosongkan container
    container.innerHTML = '';
    
    // Dapatkan hasil pencarian saat ini
    const searchText = expandedSearchInput.value.trim();
    const searchResults = enhancedSearch(searchText);
    
    // Ekstrak facet dari hasil pencarian
    const facets = extractFacets(searchResults);
    
    // Render setiap jenis facet
    if (facets.categories && Object.keys(facets.categories).length > 0) {
        const categorySection = createFacetSection('Kategori', facets.categories);
        container.appendChild(categorySection);
    }
    
    if (facets.priceRanges && Object.keys(facets.priceRanges).length > 0) {
        const priceSection = createFacetSection('Rentang Harga', facets.priceRanges);
        container.appendChild(priceSection);
    }
    
    if (facets.ratings && Object.keys(facets.ratings).length > 0) {
        const ratingSection = createFacetSection('Rating', facets.ratings);
        container.appendChild(ratingSection);
    }
    
    if (facets.shipping && Object.keys(facets.shipping).length > 0) {
        const shippingSection = createFacetSection('Pengiriman', facets.shipping);
        container.appendChild(shippingSection);
    }
    
    if (facets.features && Object.keys(facets.features).length > 0) {
        const featuresSection = createFacetSection('Fitur', facets.features);
        container.appendChild(featuresSection);
    }
    
    // Sesuaikan status checkbox berdasarkan filter aktif
    updateCheckboxesFromActiveFilters(container);
    
    // PERBAIKAN BUG: Tambahkan event listener untuk semua checkbox
    const checkboxes = container.querySelectorAll('input[type="checkbox"]');
    checkboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            const type = this.dataset.facetType;
            const value = this.dataset.facetValue;
            
            if (this.checked) {
                addFilter(type, value);
            } else {
                removeFilter(type, value);
            }
        });
    });
}

// Fungsi update checkbox berdasarkan filter aktif
function updateCheckboxesFromActiveFilters(container) {
    // Loop melalui setiap filter aktif
    Object.entries(activeFilters).forEach(([type, values]) => {
        values.forEach(value => {
            // Temukan checkbox yang sesuai
            const checkboxId = `facet-${type}-${value.replace(/\s+/g, '-').replace(/[^a-z0-9-]/gi, '')}`;
            const checkbox = container.querySelector(`#${checkboxId}`);
            if (checkbox) {
                checkbox.checked = true;
            }
        });
    });
}

// Fungsi untuk menghitung total filter aktif
function countActiveFilters() {
    let total = 0;
    Object.values(activeFilters).forEach(values => {
        total += values.length;
    });
    return total;
}

// Fungsi untuk memperbarui tampilan badge filter
function updateFilterBadge() {
    const count = countActiveFilters();
    if (count > 0) {
        filterBadge.textContent = count;
        filterBadge.style.display = 'flex';
    } else {
        filterBadge.style.display = 'none';
    }
}

/// Perbaiki fungsi applyFilters()
function applyFilters() {
    // Terapkan filter sementara ke filter aktif
    activeFilters = JSON.parse(JSON.stringify(tempActiveFilters || {}));
    
    // Sembunyikan panel facet
    hideFacetPanel();
    
    // Update badge
    updateFilterBadge();
    
    // PERBAIKAN BUG: Pastikan filter benar-benar diterapkan
    const searchText = expandedSearchInput.value.trim();
    if (searchText) {
        // Dapatkan hasil pencarian original
        const searchResults = enhancedSearch(searchText);
        
        // Terapkan filter pada hasil pencarian
        const filteredResults = applyFiltersToResults(searchResults);
        
        // Tampilkan hasil yang difilter
        displaySearchResults(filteredResults, searchText);
        
        // Pastikan tab filter ditampilkan
        forceShowFilterTabs();
    }
}

// Perbaiki fungsi toggleSearchFilterIcon
function toggleSearchFilterIcon(isSearchMode) {
    // isSearchMode true = tampilkan ikon pencarian, false = tampilkan ikon filter
    
    // Pastikan filter tidak muncul saat pertama kali
    if (isSearchMode) {
        // Mode pencarian - tampilkan ikon pencarian
        if (expandedSearchIcon) expandedSearchIcon.style.display = 'flex';
        if (filterIcon) filterIcon.style.display = 'none';
    } else {
        // Mode hasil - tampilkan ikon filter
        // Hanya tampilkan filter jika pencarian sudah dilakukan dan ada hasil
        if (isSearchResultShown && expandedSearchInput.value.trim() !== '') {
            if (expandedSearchIcon) expandedSearchIcon.style.display = 'none';
            if (filterIcon) filterIcon.style.display = 'flex';
        } else {
            // Tetap tampilkan ikon pencarian
            if (expandedSearchIcon) expandedSearchIcon.style.display = 'flex';
            if (filterIcon) filterIcon.style.display = 'none';
        }
    }
}

// Fungsi untuk menganalisis produk spesifik
function analyzeSpecificProduct(productId) {
    const product = sampleProducts.find(p => p.id === productId);
    if (!product) {
        console.error("Produk tidak ditemukan:", productId);
        return;
    }
    
    console.log("Menganalisis produk:", product.name);
    
    // Ekstrak kata-kata penting
    const words = product.name.toLowerCase().split(' ')
                  .filter(w => w.length >= 3);
    
    console.log("Kata-kata penting:", words);
    
    // Tambahkan ke database sinonim, typo, dan related
    words.forEach(word => {
        // Tambahkan ke related keywords
        if (!keywordPredictionDB.relatedKeywords[word]) {
            keywordPredictionDB.relatedKeywords[word] = [];
        }
        
        // Tambahkan kategori sebagai related keyword
        if (product.category && 
            !keywordPredictionDB.relatedKeywords[word].includes(product.category)) {
            keywordPredictionDB.relatedKeywords[word].push(product.category);
        }
        
        // Buat variasi typo sederhana
        const typos = [
            word.substring(1), // Hapus huruf pertama
            word.substring(0, word.length-1), // Hapus huruf terakhir
            word.replace(/a/g, 'e'), // Ganti a dengan e
            word.replace(/i/g, 'y') // Ganti i dengan y
        ];
        
        typos.forEach(typo => {
            if (typo.length >= 3 && typo !== word) {
                keywordPredictionDB.typoCorrections[typo] = word;
            }
        });
    });
    
    console.log("Analisis selesai, database diperbarui");
}

// Reset cache dan paksa pemrosesan ulang
localStorage.removeItem('intelligent_keyword_cache');

// Paksa analisis ulang keyword
intelligentKeywordSystem.forceProcess();

// Cari ID produk Kacamata (sesuaikan dengan ID sebenarnya)
const kacamataId = sampleProducts.findIndex(p => p.name.includes("Kacamata"));
if (kacamataId !== -1) {
    // Analisis produk kacamata secara khusus
    analyzeSpecificProduct(kacamataId);
    
    // Verifikasi integrasi
    verifyProductIntegration("Kacamata Polarized Anti UV");
} else {
    console.error("Produk Kacamata tidak ditemukan!");
}
    
    </script>
</body>
</html>
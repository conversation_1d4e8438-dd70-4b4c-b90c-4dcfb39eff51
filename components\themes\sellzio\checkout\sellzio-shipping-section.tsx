"use client"

import React from 'react'

interface ShippingOption {
  id: string
  name: string
  price: number
  originalPrice?: number
  estimate: string
  isFree?: boolean
  isSelected?: boolean
}

interface SellzioShippingSectionProps {
  options: ShippingOption[]
  onEdit: () => void
}

export const SellzioShippingSection: React.FC<SellzioShippingSectionProps> = ({
  options,
  onEdit
}) => {
  const formatPrice = (price: number) => {
    return `Rp${price.toLocaleString('id-ID')}`
  }

  const selectedOption = options.find(opt => opt.isSelected) || options[0]

  return (
    <div className="sellzio-checkout-section">
      <div className="sellzio-section-title">
        <span>Opsi Pengiriman</span>
        <button className="sellzio-edit-btn" onClick={onEdit}>
          Lihat Semua <span style={{ fontSize: '14px' }}>›</span>
        </button>
      </div>
      
      <div className="sellzio-shipping-option-selected">
        {selectedOption.isFree && (
          <div className="sellzio-free-shipping-badge"><PERSON><PERSON><PERSON></div>
        )}
        
        <div className="sellzio-shipping-header">
          <div className="sellzio-shipping-name">{selectedOption.name}</div>
          <div className="sellzio-shipping-price-container">
            {selectedOption.originalPrice && selectedOption.originalPrice > selectedOption.price && (
              <span className="sellzio-shipping-original-price">
                {formatPrice(selectedOption.originalPrice)}
              </span>
            )}
            <span className="sellzio-shipping-price">
              {formatPrice(selectedOption.price)}
            </span>
            <span className="sellzio-shipping-check">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <circle cx="12" cy="12" r="10" stroke="#00BFA5" strokeWidth="2" fill="none"/>
                <path d="M8 12L10.5 14.5L16 9" stroke="#00BFA5" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            </span>
          </div>
        </div>
        
        <div className="sellzio-shipping-estimate">
          <span className="sellzio-truck-icon">
            <svg width="24" height="24" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
              <g transform="translate(0, 5) scale(0.9)">
                <path d="M14 30C15.6569 30 17 28.6569 17 27C17 25.3431 15.6569 24 14 24C12.3431 24 11 25.3431 11 27C11 28.6569 12.3431 30 14 30Z" fill="#00BFA5"/>
                <path d="M28 30C29.6569 30 31 28.6569 31 27C31 25.3431 29.6569 24 28 24C26.3431 24 25 25.3431 25 27C25 28.6569 26.3431 30 28 30Z" fill="#00BFA5"/>
                <path d="M33 13H27L25 8H10L7 13H3V27H7M33 27H36V17L33 13" stroke="#00BFA5" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                <path d="M14 27H28" stroke="#00BFA5" strokeWidth="2" strokeLinecap="round"/>
                <path d="M1 17.5H3.5" stroke="#00BFA5" strokeWidth="2.5" strokeLinecap="round"/>
                <path d="M4.5 17.5H7.5" stroke="#00BFA5" strokeWidth="2" strokeLinecap="round"/>
                <path d="M8.5 17.5H11" stroke="#00BFA5" strokeWidth="1.5" strokeLinecap="round"/>
                <path d="M1 20.5H3.5" stroke="#00BFA5" strokeWidth="2.5" strokeLinecap="round"/>
                <path d="M4.5 20.5H7.5" stroke="#00BFA5" strokeWidth="2" strokeLinecap="round"/>
                <path d="M8.5 20.5H11" stroke="#00BFA5" strokeWidth="1.5" strokeLinecap="round"/>
              </g>
            </svg>
          </span>
          <span className="sellzio-estimate-text">
            Garansi tiba: {selectedOption.estimate}
          </span>
        </div>
        
        <div className="sellzio-shipping-guarantee">
          Voucher s/d Rp10.000 jika pesanan belum tiba {selectedOption.estimate.split(' - ')[1]} 2025.
        </div>
      </div>
    </div>
  )
}

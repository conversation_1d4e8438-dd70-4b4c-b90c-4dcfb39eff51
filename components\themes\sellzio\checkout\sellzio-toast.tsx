"use client"

import React, { useEffect, useState } from 'react'

interface SellzioToastProps {
  message: string
  isVisible: boolean
  onClose: () => void
  type?: 'success' | 'error' | 'info'
}

export const SellzioToast: React.FC<SellzioToastProps> = ({
  message,
  isVisible,
  onClose,
  type = 'info'
}) => {
  useEffect(() => {
    if (isVisible) {
      const timer = setTimeout(() => {
        onClose()
      }, 3000)
      
      return () => clearTimeout(timer)
    }
  }, [isVisible, onClose])

  if (!isVisible) return null

  return (
    <div className={`sellzio-toast sellzio-toast-${type}`}>
      {message}
    </div>
  )
}

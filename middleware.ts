import { NextResponse } from "next/server"
import type { NextRequest } from "next/server"

// Configuration constants
const MAIN_DOMAIN = process.env.NEXT_PUBLIC_MAIN_DOMAIN || 'localhost:3000'
const ADMIN_SUBDOMAIN = 'admin'
const APP_SUBDOMAIN = 'app'

// Cache for platform config to avoid repeated API calls
let platformConfigCache: any = null
let configCacheTime = 0
const CACHE_DURATION = 5 * 60 * 1000 // 5 minutes

// Get platform configuration
async function getPlatformConfig() {
  // For Vercel deployment, skip API calls and use environment variables or defaults
  if (process.env.VERCEL || process.env.NODE_ENV === 'production') {
    return {
      platform_domain: process.env.NEXT_PUBLIC_MAIN_DOMAIN || MAIN_DOMAIN,
      dashboard_subdomain: process.env.NEXT_PUBLIC_DASHBOARD_SUBDOMAIN || APP_SUBDOMAIN,
      dashboard_enabled: 'true'
    }
  }

  const now = Date.now()

  // Return cached config if still valid
  if (platformConfigCache && (now - configCacheTime) < CACHE_DURATION) {
    return platformConfigCache
  }

  try {
    const apiUrl = `${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/api/admin/platform-settings`
    const response = await fetch(apiUrl)

    // Check if response is JSON
    const contentType = response.headers.get('content-type')
    if (!contentType || !contentType.includes('application/json')) {
      console.log('🔥 MIDDLEWARE: Platform config API returned non-JSON response, using defaults')
      throw new Error('Non-JSON response')
    }

    const data = await response.json()

    if (data.success) {
      const config: any = {}
      data.settings.forEach((setting: any) => {
        config[setting.key] = setting.value
      })

      // Cache the config
      platformConfigCache = config
      configCacheTime = now

      return config
    }
  } catch (error) {
    console.error('🔥 MIDDLEWARE: Error fetching platform config:', error)
  }

  // Return default config if API fails
  return {
    platform_domain: MAIN_DOMAIN,
    dashboard_subdomain: APP_SUBDOMAIN,
    dashboard_enabled: 'true'
  }
}

export async function middleware(request: NextRequest) {
  const { pathname, hostname } = request.nextUrl

  // Tambahkan header untuk mencegah caching
  const response = NextResponse.next()
  response.headers.set("Cache-Control", "no-store, max-age=0")

  console.log('🔥 MIDDLEWARE: Processing request', { hostname, pathname })

  // Get platform configuration
  const platformConfig = await getPlatformConfig()
  const dashboardSubdomain = platformConfig.dashboard_subdomain || APP_SUBDOMAIN
  const platformDomain = platformConfig.platform_domain || MAIN_DOMAIN

  // Handle localhost development (path-based routing)
  if (hostname.includes('localhost') || hostname.includes('127.0.0.1')) {
    return handleLocalhost(request, response, pathname)
  }

  // Handle admin subdomain (admin.sellzio.my.id)
  if (hostname.startsWith(`${ADMIN_SUBDOMAIN}.`)) {
    return handleAdminSubdomain(request, response, pathname)
  }

  // Handle dashboard subdomain (app.sellzio.my.id or custom)
  if (hostname.startsWith(`${dashboardSubdomain}.`) ||
      hostname === `${dashboardSubdomain}.sellzio.my.id` ||
      hostname === `app.sellzio.my.id`) {
    return handleDashboardSubdomain(request, response, pathname, platformConfig)
  }

  // Handle tenant subdomains (tenant-name.sellzio.com)
  if (hostname.endsWith(`.${platformDomain}`) &&
      !hostname.startsWith(`${ADMIN_SUBDOMAIN}.`) &&
      !hostname.startsWith(`${dashboardSubdomain}.`)) {
    return await handleTenantSubdomain(request, response, hostname, pathname, platformDomain)
  }

  // Handle custom domains (tenant's own domain)
  if (hostname !== platformDomain && !hostname.endsWith(`.${platformDomain}`)) {
    return await handleCustomDomain(request, response, hostname, pathname)
  }

  // Default main domain handling
  return response
}

// Handle localhost development
function handleLocalhost(request: NextRequest, response: NextResponse, pathname: string) {
  const { hostname } = request.nextUrl

  // Check for subdomain in localhost (app.localhost:3000)
  // Juga support jika user mengakses dengan IP atau hostname yang mengandung 'app'
  if (hostname.includes('app.localhost') ||
      (hostname.includes('localhost') && request.headers.get('host')?.includes('app.localhost'))) {
    console.log('🔥 MIDDLEWARE: App subdomain detected in localhost')
    response.headers.set('x-panel-type', 'dashboard')
    response.headers.set('x-dashboard-subdomain', 'app')

    // Block admin routes on app subdomain
    if (pathname.startsWith('/admin')) {
      return new NextResponse('Admin access not allowed on this domain. Use localhost:3000/admin/login', { status: 403 })
    }

    // Handle dashboard routes
    if (pathname === '/' || pathname === '') {
      return NextResponse.redirect(new URL('/login', request.url))
    }

    // Allow login and dashboard routes (termasuk /auth untuk logout)
    if (pathname.startsWith('/login') ||
        pathname.startsWith('/auth') ||
        pathname.startsWith('/register') ||
        pathname.startsWith('/tenant/dashboard') ||
        pathname.startsWith('/store/dashboard') ||
        pathname.startsWith('/buyer/dashboard')) {
      return response
    }

    // Redirect other routes to login
    return NextResponse.redirect(new URL('/login', request.url))
  }

  // Check for admin subdomain in localhost (admin.localhost:3000)
  if (hostname.includes('admin.localhost')) {
    console.log('🔥 MIDDLEWARE: Admin subdomain detected in localhost')
    response.headers.set('x-panel-type', 'admin')

    if (!pathname.startsWith('/admin')) {
      return NextResponse.rewrite(
        new URL(`/admin${pathname === '/' ? '' : pathname}`, request.url)
      )
    }
    return response
  }

  // Path-based routing untuk development
  if (pathname.startsWith('/tenant/')) {
    const pathParts = pathname.split('/')
    if (pathParts.length >= 3) {
      const tenantSlug = pathParts[2]
      response.headers.set('x-tenant-slug', tenantSlug)
      response.headers.set('x-tenant-type', 'path')

      if (pathParts.length >= 4 && pathParts[3] === 'sellzio') {
        response.headers.set('x-tenant-theme', 'sellzio')
      }
    }
  }

  if (pathname.startsWith('/sellzio')) {
    response.headers.set('x-tenant-slug', 'demo')
    response.headers.set('x-tenant-type', 'theme')
    response.headers.set('x-tenant-theme', 'sellzio')
  }

  if (pathname.startsWith('/admin/')) {
    response.headers.set('x-panel-type', 'admin')
  }

  // Handle /app path-based routing untuk user login
  if (pathname.startsWith('/app/')) {
    response.headers.set('x-panel-type', 'app')

    // Allow /app/login langsung tanpa rewrite
    if (pathname === '/app/login') {
      return response
    }

    // Allow other app routes
    return response
  }

  // Check for app parameter in login URL
  const url = request.nextUrl
  const isAppLogin = url.searchParams.get('app') === 'true'

  // Hanya untuk main domain localhost (bukan subdomain) - pastikan bukan app.localhost atau admin.localhost
  if ((hostname === 'localhost' || hostname.startsWith('localhost:')) &&
      !hostname.includes('app.localhost') &&
      !hostname.includes('admin.localhost')) {

    // Allow /login dengan parameter app=true
    if (pathname === '/login' && isAppLogin) {
      // Rewrite ke /app/login tapi tetap tampilkan URL /login?app=true
      return NextResponse.rewrite(new URL('/app/login', request.url))
    }

    // Redirect /login tanpa parameter ke /app/login
    if (pathname === '/login' && !isAppLogin) {
      return NextResponse.redirect(new URL('/app/login', request.url))
    }

    if (pathname.startsWith('/auth/login')) {
      return NextResponse.redirect(new URL('/app/login', request.url))
    }

    // Allow main domain root without redirect
    if (pathname === '/' || pathname === '') {
      return response
    }
  }

  return response
}

// Handle admin subdomain
function handleAdminSubdomain(request: NextRequest, response: NextResponse, pathname: string) {
  if (!pathname.startsWith('/admin')) {
    return NextResponse.rewrite(
      new URL(`/admin${pathname === '/' ? '' : pathname}`, request.url)
    )
  }
  response.headers.set('x-panel-type', 'admin')
  return response
}

// Handle dashboard subdomain (configurable)
function handleDashboardSubdomain(request: NextRequest, response: NextResponse, pathname: string, platformConfig: any) {
  console.log('🔥 MIDDLEWARE: Dashboard subdomain detected:', platformConfig.dashboard_subdomain)

  // Check if dashboard is enabled
  if (platformConfig.dashboard_enabled !== 'true') {
    return new NextResponse('Dashboard subdomain is disabled', { status: 404 })
  }

  response.headers.set('x-panel-type', 'dashboard')
  response.headers.set('x-dashboard-subdomain', platformConfig.dashboard_subdomain)

  // Handle dashboard routes
  if (pathname === '/' || pathname === '') {
    // Redirect to login page
    return NextResponse.redirect(new URL('/login', request.url))
  }

  // Handle login and auth routes
  if (pathname.startsWith('/login') || pathname.startsWith('/auth') || pathname.startsWith('/register')) {
    return response
  }

  // Handle tenant dashboard routes
  if (pathname.startsWith('/tenant/dashboard')) {
    return response
  }

  // Handle store dashboard routes
  if (pathname.startsWith('/store/dashboard')) {
    return response
  }

  // Handle buyer dashboard routes
  if (pathname.startsWith('/buyer/dashboard')) {
    return response
  }

  // For other routes, redirect to login
  return NextResponse.redirect(new URL('/login', request.url))
}

// Handle app subdomain (legacy)
function handleAppSubdomain(request: NextRequest, response: NextResponse, pathname: string) {
  if (!pathname.startsWith('/app')) {
    return NextResponse.rewrite(
      new URL(`/app${pathname === '/' ? '' : pathname}`, request.url)
    )
  }
  response.headers.set('x-panel-type', 'app')
  return response
}

// Handle tenant subdomain
async function handleTenantSubdomain(request: NextRequest, response: NextResponse, hostname: string, pathname: string, platformDomain: string = MAIN_DOMAIN) {
  const subdomain = hostname.replace(`.${platformDomain}`, '')

  console.log('🔥 MIDDLEWARE: Tenant subdomain detected:', subdomain)

  // Set tenant headers
  response.headers.set('x-tenant-slug', subdomain)
  response.headers.set('x-tenant-type', 'subdomain')
  response.headers.set('x-tenant-theme', 'sellzio')

  // Handle tenant admin routes
  if (pathname.startsWith('/admin')) {
    return NextResponse.rewrite(
      new URL(`/tenant/dashboard${pathname === '/admin' ? '' : pathname.substring(6)}`, request.url)
    )
  }

  // Handle tenant storefront - redirect to Sellzio theme
  return NextResponse.rewrite(
    new URL(`/tenant/${subdomain}/sellzio${pathname}`, request.url)
  )
}

// Handle custom domain
async function handleCustomDomain(request: NextRequest, response: NextResponse, hostname: string, pathname: string) {
  console.log('🔥 MIDDLEWARE: Custom domain detected:', hostname)

  try {
    // Look up tenant by custom domain
    const tenant = await lookupTenantByDomain(hostname)

    if (!tenant) {
      console.log('🔥 MIDDLEWARE: No tenant found for domain:', hostname)
      return new NextResponse('Domain not found', { status: 404 })
    }

    console.log('🔥 MIDDLEWARE: Found tenant for custom domain:', tenant.slug)

    // Set tenant headers
    response.headers.set('x-tenant-slug', tenant.slug)
    response.headers.set('x-tenant-type', 'custom-domain')
    response.headers.set('x-tenant-theme', 'sellzio')
    response.headers.set('x-custom-domain', hostname)

    // Handle tenant admin routes
    if (pathname.startsWith('/admin')) {
      return NextResponse.rewrite(
        new URL(`/tenant/dashboard${pathname === '/admin' ? '' : pathname.substring(6)}`, request.url)
      )
    }

    // Handle tenant storefront - redirect to Sellzio theme
    return NextResponse.rewrite(
      new URL(`/tenant/${tenant.slug}/sellzio${pathname}`, request.url)
    )
  } catch (error) {
    console.error('🔥 MIDDLEWARE: Error handling custom domain:', error)
    return new NextResponse('Internal Server Error', { status: 500 })
  }
}

// Lookup tenant by custom domain
async function lookupTenantByDomain(domain: string) {
  try {
    // For development, use simple mapping to avoid API calls in middleware
    if (domain.includes('localhost') || domain.includes('127.0.0.1')) {
      const domainMappings: { [key: string]: { slug: string, id: string } } = {
        'sellzio.localhost': { slug: 'sellzio', id: 'sellzio' },
        'demo.localhost': { slug: 'demo', id: 'demo' },
        'store1.localhost': { slug: 'store1', id: 'store1' }
      }
      return domainMappings[domain] || null
    }

    // For Vercel deployment, skip API calls and return default tenant
    if (process.env.VERCEL || process.env.NODE_ENV === 'production') {
      // Return a default tenant for custom domains in production
      return {
        slug: 'demo',
        id: 'demo'
      }
    }

    // For production, make API call to lookup domain
    const apiUrl = `${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/api/tenants/lookup-domain?domain=${encodeURIComponent(domain)}`

    const response = await fetch(apiUrl, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    })

    if (!response.ok) {
      console.log('🔥 MIDDLEWARE: Domain lookup failed:', response.status)
      return null
    }

    const data = await response.json()
    return {
      slug: data.slug,
      id: data.id
    }
  } catch (error) {
    console.error('🔥 MIDDLEWARE: Error looking up tenant by domain:', error)
    return null
  }
}

// Konfigurasi untuk menentukan path mana yang akan diproses oleh middleware
export const config = {
  matcher: [
    // Proses semua path kecuali yang disebutkan
    "/((?!api|_next/static|_next/image|favicon.ico).*)",
  ],
}

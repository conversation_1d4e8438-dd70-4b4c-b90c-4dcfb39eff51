import { SellzioProductDetail, FlashSaleProductDetail } from "@/components/themes/sellzio/product-detail"
import { sampleProducts } from "@/components/data/products"
import { isFlashSaleProduct } from "@/utils/product-helpers"

export default function ProductDetailPage({ params }: { params: { id: string } }) {
  // Check if product is flash sale - berdasarkan logic yang sama dengan halaman utama
  const product = sampleProducts.find(p => p.id === parseInt(params.id))

  if (!product) {
    return <SellzioProductDetail productId={params.id} />
  }

  // Gunakan helper function untuk konsistensi
  const isFlashSale = isFlashSaleProduct(product)

  // Render appropriate component based on product type
  if (isFlashSale) {
    return <FlashSaleProductDetail productId={params.id} />
  }

  return <SellzioProductDetail productId={params.id} />
}

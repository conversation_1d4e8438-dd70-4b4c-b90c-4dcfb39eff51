import { NextResponse } from "next/server"
import type { NextRequest } from "next/server"

// Configuration constants
const MAIN_DOMAIN = process.env.NEXT_PUBLIC_MAIN_DOMAIN || 'localhost:3000'
const ADMIN_SUBDOMAIN = 'admin'
const APP_SUBDOMAIN = 'app'

export async function middleware(request: NextRequest) {
  const { pathname, hostname } = request.nextUrl

  // Tambahkan header untuk mencegah caching
  const response = NextResponse.next()
  response.headers.set("Cache-Control", "no-store, max-age=0")

  console.log('🔥 MIDDLEWARE: Processing request', { hostname, pathname })

  // Handle localhost development (path-based routing)
  if (hostname.includes('localhost') || hostname.includes('127.0.0.1')) {
    return handleLocalhost(request, response, pathname)
  }

  // Handle admin subdomain (admin.sellzio.com)
  if (hostname.startsWith(`${ADMIN_SUBDOMAIN}.`)) {
    return handleAdminSubdomain(request, response, pathname)
  }

  // Handle app subdomain (app.sellzio.com)
  if (hostname.startsWith(`${APP_SUBDOMAIN}.`)) {
    return handleAppSubdomain(request, response, pathname)
  }

  // Handle tenant subdomains (tenant-name.sellzio.com)
  if (hostname.endsWith(`.${MAIN_DOMAIN}`) &&
      !hostname.startsWith(`${ADMIN_SUBDOMAIN}.`) &&
      !hostname.startsWith(`${APP_SUBDOMAIN}.`)) {
    return await handleTenantSubdomain(request, response, hostname, pathname)
  }

  // Handle custom domains (tenant's own domain)
  if (hostname !== MAIN_DOMAIN && !hostname.endsWith(`.${MAIN_DOMAIN}`)) {
    return await handleCustomDomain(request, response, hostname, pathname)
  }

  // Default main domain handling
  return response
}

// Handle localhost development
function handleLocalhost(request: NextRequest, response: NextResponse, pathname: string) {
  // Path-based routing untuk development
  if (pathname.startsWith('/tenant/')) {
    const pathParts = pathname.split('/')
    if (pathParts.length >= 3) {
      const tenantSlug = pathParts[2]
      response.headers.set('x-tenant-slug', tenantSlug)
      response.headers.set('x-tenant-type', 'path')

      if (pathParts.length >= 4 && pathParts[3] === 'sellzio') {
        response.headers.set('x-tenant-theme', 'sellzio')
      }
    }
  }

  if (pathname.startsWith('/sellzio')) {
    response.headers.set('x-tenant-slug', 'demo')
    response.headers.set('x-tenant-type', 'theme')
    response.headers.set('x-tenant-theme', 'sellzio')
  }

  if (pathname.startsWith('/admin/')) {
    response.headers.set('x-panel-type', 'admin')
  }

  if (pathname.startsWith('/app/')) {
    response.headers.set('x-panel-type', 'app')
  }

  return response
}

// Handle admin subdomain
function handleAdminSubdomain(request: NextRequest, response: NextResponse, pathname: string) {
  if (!pathname.startsWith('/admin')) {
    return NextResponse.rewrite(
      new URL(`/admin${pathname === '/' ? '' : pathname}`, request.url)
    )
  }
  response.headers.set('x-panel-type', 'admin')
  return response
}

// Handle app subdomain
function handleAppSubdomain(request: NextRequest, response: NextResponse, pathname: string) {
  if (!pathname.startsWith('/app')) {
    return NextResponse.rewrite(
      new URL(`/app${pathname === '/' ? '' : pathname}`, request.url)
    )
  }
  response.headers.set('x-panel-type', 'app')
  return response
}

// Handle tenant subdomain
async function handleTenantSubdomain(request: NextRequest, response: NextResponse, hostname: string, pathname: string) {
  const subdomain = hostname.replace(`.${MAIN_DOMAIN}`, '')

  console.log('🔥 MIDDLEWARE: Tenant subdomain detected:', subdomain)

  // Set tenant headers
  response.headers.set('x-tenant-slug', subdomain)
  response.headers.set('x-tenant-type', 'subdomain')
  response.headers.set('x-tenant-theme', 'sellzio')

  // Handle tenant admin routes
  if (pathname.startsWith('/admin')) {
    return NextResponse.rewrite(
      new URL(`/tenant/dashboard${pathname === '/admin' ? '' : pathname.substring(6)}`, request.url)
    )
  }

  // Handle tenant storefront - redirect to Sellzio theme
  return NextResponse.rewrite(
    new URL(`/tenant/${subdomain}/sellzio${pathname}`, request.url)
  )
}

// Handle custom domain
async function handleCustomDomain(request: NextRequest, response: NextResponse, hostname: string, pathname: string) {
  console.log('🔥 MIDDLEWARE: Custom domain detected:', hostname)

  try {
    // Look up tenant by custom domain
    const tenant = await lookupTenantByDomain(hostname)

    if (!tenant) {
      console.log('🔥 MIDDLEWARE: No tenant found for domain:', hostname)
      return new NextResponse('Domain not found', { status: 404 })
    }

    console.log('🔥 MIDDLEWARE: Found tenant for custom domain:', tenant.slug)

    // Set tenant headers
    response.headers.set('x-tenant-slug', tenant.slug)
    response.headers.set('x-tenant-type', 'custom-domain')
    response.headers.set('x-tenant-theme', 'sellzio')
    response.headers.set('x-custom-domain', hostname)

    // Handle tenant admin routes
    if (pathname.startsWith('/admin')) {
      return NextResponse.rewrite(
        new URL(`/tenant/dashboard${pathname === '/admin' ? '' : pathname.substring(6)}`, request.url)
      )
    }

    // Handle tenant storefront - redirect to Sellzio theme
    return NextResponse.rewrite(
      new URL(`/tenant/${tenant.slug}/sellzio${pathname}`, request.url)
    )
  } catch (error) {
    console.error('🔥 MIDDLEWARE: Error handling custom domain:', error)
    return new NextResponse('Internal Server Error', { status: 500 })
  }
}

// Lookup tenant by custom domain
async function lookupTenantByDomain(domain: string) {
  try {
    // For development, use simple mapping to avoid API calls in middleware
    if (domain.includes('localhost') || domain.includes('127.0.0.1')) {
      const domainMappings: { [key: string]: { slug: string, id: string } } = {
        'sellzio.localhost': { slug: 'sellzio', id: 'sellzio' },
        'demo.localhost': { slug: 'demo', id: 'demo' },
        'store1.localhost': { slug: 'store1', id: 'store1' }
      }
      return domainMappings[domain] || null
    }

    // For production, make API call to lookup domain
    const apiUrl = `${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/api/tenants/lookup-domain?domain=${encodeURIComponent(domain)}`

    const response = await fetch(apiUrl, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    })

    if (!response.ok) {
      console.log('🔥 MIDDLEWARE: Domain lookup failed:', response.status)
      return null
    }

    const data = await response.json()
    return {
      slug: data.slug,
      id: data.id
    }
  } catch (error) {
    console.error('🔥 MIDDLEWARE: Error looking up tenant by domain:', error)
    return null
  }
}

// Konfigurasi untuk menentukan path mana yang akan diproses oleh middleware
export const config = {
  matcher: [
    // Proses semua path kecuali yang disebutkan
    "/((?!api|_next/static|_next/image|favicon.ico).*)",
  ],
}

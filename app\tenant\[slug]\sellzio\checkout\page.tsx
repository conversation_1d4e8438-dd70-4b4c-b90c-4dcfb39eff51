"use client"

import React, { use } from 'react'
import { useRouter } from 'next/navigation'
import dynamic from 'next/dynamic'
import { useCart } from '@/hooks/use-cart'
import { useTenantTheme } from '@/components/tenant/tenant-theme-provider'

// Dynamic import untuk men<PERSON><PERSON><PERSON>Error
const SellzioCheckoutPage = dynamic(
  () => import('@/components/themes/sellzio/checkout/sellzio-checkout-page').then(mod => ({ default: mod.SellzioCheckoutPage })),
  {
    ssr: false,
    loading: () => (
      <div style={{
        minHeight: '100vh',
        backgroundColor: '#f5f5f5',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      }}>
        <div style={{
          textAlign: 'center',
          backgroundColor: 'white',
          padding: '2rem',
          borderRadius: '8px',
          boxShadow: '0 1px 3px rgba(0,0,0,0.1)'
        }}>
          <div style={{
            width: '48px',
            height: '48px',
            border: '3px solid #f3f3f3',
            borderTop: '3px solid #ee4d2d',
            borderRadius: '50%',
            animation: 'spin 1s linear infinite',
            margin: '0 auto 16px'
          }}></div>
          <p style={{ color: '#666', margin: 0 }}>Loading checkout...</p>
        </div>
      </div>
    )
  }
)

interface TenantCheckoutPageProps {
  params: Promise<{ slug: string }>
}

export default function TenantCheckoutPage({ params }: TenantCheckoutPageProps) {
  const router = useRouter()
  const { theme, loading: themeLoading } = useTenantTheme()
  const { items, clearCart } = useCart()

  // Unwrap params using React.use()
  const resolvedParams = use(params)
  const tenantSlug = resolvedParams.slug

  // Theme loading
  if (themeLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading {tenantSlug} marketplace...</p>
        </div>
      </div>
    )
  }

  // Convert cart items to checkout format
  const checkoutItems = items.map(item => ({
    id: item.id,
    name: item.name,
    price: item.price,
    quantity: item.quantity,
    image: item.image,
    seller: `${tenantSlug} Store`,
    shipping: 'Reguler',
    variant: 'Default'
  }))

  const handleBack = () => {
    router.push(`/tenant/${tenantSlug}/sellzio`)
  }

  const handleOrderComplete = () => {
    // Clear cart after successful order
    clearCart()
    
    // Redirect to success page or back to store
    router.push(`/tenant/${tenantSlug}/sellzio?order=success`)
  }

  // If no items in cart, redirect back
  if (items.length === 0) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-50">
        <div className="text-center bg-white p-8 rounded-lg shadow-sm">
          <h2 className="text-xl font-semibold text-gray-800 mb-4">Cart is Empty</h2>
          <p className="text-gray-600 mb-6">Add some products to your cart before checkout.</p>
          <button 
            onClick={handleBack}
            className="bg-orange-500 text-white px-6 py-3 rounded-lg hover:bg-orange-600 transition-colors"
          >
            Continue Shopping
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="tenant-checkout-wrapper">
      {/* Add tenant-specific styling */}
      <style jsx global>{`
        .tenant-checkout-wrapper {
          min-height: 100vh;
          background: #f5f5f5;
        }
        
        .sellzio-checkout-page {
          --tenant-primary: ${theme?.colors?.primary || '#ee4d2d'};
          --tenant-secondary: ${theme?.colors?.secondary || '#ff6b35'};
        }
        
        .sellzio-checkout-header h1::after {
          content: " - ${tenantSlug}";
          font-weight: normal;
          opacity: 0.8;
        }
        
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `}</style>

      <SellzioCheckoutPage
        items={checkoutItems}
        onBack={handleBack}
        onOrderComplete={handleOrderComplete}
        tenantInfo={{
          name: tenantSlug,
          slug: tenantSlug,
          theme: theme?.name || 'Sellzio'
        }}
      />
    </div>
  )
}

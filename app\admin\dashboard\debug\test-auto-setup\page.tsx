"use client"

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { useToast } from '@/hooks/use-toast'
import { 
  Zap, 
  CheckCircle, 
  AlertCircle,
  Loader2,
  Globe,
  Settings
} from 'lucide-react'

export default function TestAutoSetupPage() {
  const [testSubdomain, setTestSubdomain] = useState('auto-test')
  const [testTenantId, setTestTenantId] = useState('96111d56-b82d-43e7-9926-8f0530dc6063') // test1 tenant
  const [loading, setLoading] = useState(false)
  const [result, setResult] = useState<any>(null)
  const { toast } = useToast()

  const testAutoSetup = async () => {
    if (!testSubdomain || !testTenantId) {
      toast({
        title: "Error",
        description: "Please enter subdomain and tenant ID",
        variant: "destructive"
      })
      return
    }

    setLoading(true)
    setResult(null)

    try {
      console.log('🔥 TEST: Starting auto setup test for:', testSubdomain)

      const response = await fetch('/api/tenants/update-subdomain', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          subdomain: testSubdomain.trim(),
          tenantId: testTenantId.trim()
        })
      })

      const data = await response.json()
      setResult({
        status: response.status,
        success: response.ok,
        data
      })

      if (response.ok && data.success) {
        toast({
          title: "Auto Setup Test Successful",
          description: `Subdomain ${testSubdomain} configured automatically!`,
        })
      } else {
        toast({
          title: "Auto Setup Test Failed",
          description: data.error || "Failed to configure subdomain",
          variant: "destructive"
        })
      }

    } catch (error: any) {
      console.error('🔥 TEST: Error:', error)
      setResult({
        status: 500,
        success: false,
        data: { error: 'Network error', details: error.message }
      })
      toast({
        title: "Error",
        description: error.message || "Failed to test auto setup",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  const testSubdomainAccess = async () => {
    if (!testSubdomain) return

    const fullDomain = `${testSubdomain}.sellzio.my.id`
    
    toast({
      title: "Testing Subdomain Access",
      description: `Opening ${fullDomain} in new tab...`,
    })

    // Open in new tab
    window.open(`https://${fullDomain}`, '_blank')
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Page Header */}
      <div className="space-y-2">
        <h1 className="text-3xl font-bold tracking-tight flex items-center gap-2">
          <Zap className="h-8 w-8" />
          Test Auto Setup
        </h1>
        <p className="text-muted-foreground">
          Test sistem otomatis Cloudflare DNS + Vercel Domain untuk subdomain tenant
        </p>
      </div>

      {/* Test Configuration */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Test Configuration
          </CardTitle>
          <CardDescription>
            Konfigurasi test untuk sistem auto setup
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <div className="space-y-2">
              <Label htmlFor="testSubdomain">Test Subdomain:</Label>
              <Input
                id="testSubdomain"
                value={testSubdomain}
                onChange={(e) => setTestSubdomain(e.target.value.toLowerCase())}
                placeholder="auto-test"
              />
              <p className="text-sm text-muted-foreground">
                Will create: {testSubdomain}.sellzio.my.id
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="testTenantId">Test Tenant ID:</Label>
              <Input
                id="testTenantId"
                value={testTenantId}
                onChange={(e) => setTestTenantId(e.target.value)}
                placeholder="tenant-uuid"
              />
              <p className="text-sm text-muted-foreground">
                Default: test1 tenant
              </p>
            </div>
          </div>

          <div className="flex gap-2 flex-wrap">
            <Button onClick={testAutoSetup} disabled={loading || !testSubdomain || !testTenantId}>
              {loading ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Testing Auto Setup...
                </>
              ) : (
                <>
                  <Zap className="h-4 w-4 mr-2" />
                  Test Auto Setup
                </>
              )}
            </Button>

            <Button 
              variant="outline" 
              onClick={testSubdomainAccess} 
              disabled={!testSubdomain}
            >
              <Globe className="h-4 w-4 mr-2" />
              Test Access
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Test Results */}
      {result && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              {result.success ? (
                <CheckCircle className="h-5 w-5 text-green-500" />
              ) : (
                <AlertCircle className="h-5 w-5 text-red-500" />
              )}
              Test Results
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid gap-4 md:grid-cols-3">
              <div className="space-y-2">
                <h4 className="font-medium">HTTP Status</h4>
                <Badge variant={result.success ? "default" : "destructive"}>
                  {result.status}
                </Badge>
              </div>

              <div className="space-y-2">
                <h4 className="font-medium">DNS Configured</h4>
                <Badge variant={result.data?.dnsConfigured ? "default" : "secondary"}>
                  {result.data?.dnsConfigured ? (
                    <CheckCircle className="h-3 w-3 mr-1" />
                  ) : (
                    <AlertCircle className="h-3 w-3 mr-1" />
                  )}
                  {result.data?.dnsConfigured ? 'Yes' : 'No'}
                </Badge>
              </div>

              <div className="space-y-2">
                <h4 className="font-medium">Vercel Configured</h4>
                <Badge variant={result.data?.vercelConfigured ? "default" : "secondary"}>
                  {result.data?.vercelConfigured ? (
                    <CheckCircle className="h-3 w-3 mr-1" />
                  ) : (
                    <AlertCircle className="h-3 w-3 mr-1" />
                  )}
                  {result.data?.vercelConfigured ? 'Yes' : 'No'}
                </Badge>
              </div>
            </div>

            <div className="space-y-2">
              <h4 className="font-medium">Full Response:</h4>
              <div className="p-4 bg-gray-50 rounded-lg">
                <pre className="text-sm overflow-auto">
                  {JSON.stringify(result.data, null, 2)}
                </pre>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Instructions */}
      <Alert>
        <Zap className="h-4 w-4" />
        <AlertDescription>
          <strong>Cara Test:</strong>
          <ol className="list-decimal list-inside mt-2 space-y-1">
            <li>Masukkan subdomain unik untuk test</li>
            <li>Click "Test Auto Setup" untuk trigger sistem otomatis</li>
            <li>Tunggu 2-3 menit untuk propagasi DNS</li>
            <li>Click "Test Access" untuk cek apakah subdomain bisa diakses</li>
            <li>Verifikasi tidak ada lagi error DEPLOYMENT_NOT_FOUND</li>
          </ol>
        </AlertDescription>
      </Alert>
    </div>
  )
}

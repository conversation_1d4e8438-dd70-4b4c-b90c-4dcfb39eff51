import { sampleProducts, type Product } from "@/components/data/products"

/**
 * Helper function untuk menentukan apakah produk adalah flash sale
 * berdasarkan logic yang sama dengan halaman utama (posisi index)
 */
export function isFlashSaleProduct(product: Product): boolean {
  const productIndex = sampleProducts.findIndex(p => p.id === product.id)
  return productIndex === 2 || productIndex === 7 || productIndex === 12 || productIndex === 17
}

/**
 * Helper function untuk mendapatkan semua produk flash sale
 */
export function getFlashSaleProducts(): Product[] {
  return sampleProducts.filter((product, index) => {
    return index === 2 || index === 7 || index === 12 || index === 17
  })
}

/**
 * Helper function untuk menentukan card type berdasarkan index
 * (sama seperti logic di halaman utama)
 */
export function getCardType(index: number): "standard" | "video" | "image-slider" | "flash-sale" {
  const isVideoCard = index === 4 || index === 14 // Produk ke-5 dan ke-15
  const isImageSliderCard = index === 9 || index === 19 // Produk ke-10 dan ke-20
  const isFlashSaleCard = index === 2 || index === 7 || index === 12 || index === 17 // Produk ke-3, 8, 13, 18

  if (isVideoCard) return "video"
  else if (isImageSliderCard) return "image-slider"
  else if (isFlashSaleCard) return "flash-sale"
  else return "standard"
}

/**
 * Helper function untuk mendapatkan related products berdasarkan subcategory
 */
export function getRelatedProducts(currentProduct: Product, limit: number = 4): Product[] {
  return sampleProducts
    .filter(p => p.id !== currentProduct.id && p.subcategory === currentProduct.subcategory)
    .slice(0, limit)
}

/**
 * Helper function untuk mendapatkan related flash sale products
 */
export function getRelatedFlashSaleProducts(currentProduct: Product, limit: number = 4): Product[] {
  return sampleProducts
    .filter((p, index) => {
      const isFlashSaleCard = index === 2 || index === 7 || index === 12 || index === 17
      return p.id !== currentProduct.id && isFlashSaleCard
    })
    .slice(0, limit)
}

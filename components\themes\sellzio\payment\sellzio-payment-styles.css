/* Sellzio Payment Styles */

/* Global Payment Styles */
.sellzio-payment-page {
  min-height: 100vh;
  background: #f5f5f5;
  color: #333;
  line-height: 1.6;
  font-family: '<PERSON>o', Arial, sans-serif;
  width: 100%;
  overflow-x: hidden;
  position: relative;
  margin: 0;
  padding: 0;
}

.sellzio-payment-container {
  padding: 0 16px 80px 16px;
  max-width: 100%;
  margin: 0 auto;
}

.sellzio-payment-section {
  background: white;
  margin-bottom: 8px;
  padding: 16px;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.sellzio-section-header {
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #f0f0f0;
}

.sellzio-section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

/* Payment Header Styles */
.sellzio-payment-header {
  position: sticky;
  top: 0;
  z-index: 100;
  background: white;
  border-bottom: 1px solid #e5e5e5;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.sellzio-payment-header-inner {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  max-width: 100%;
  margin: 0 auto;
}

.sellzio-back-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: none;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  transition: background-color 0.2s ease;
  color: #333;
}

.sellzio-back-btn:hover {
  background-color: #f5f5f5;
}

.sellzio-payment-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin: 0;
  text-align: center;
  flex: 1;
}

/* Payment Footer Styles */
.sellzio-payment-footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  border-top: 1px solid #e5e5e5;
  box-shadow: 0 -2px 8px rgba(0,0,0,0.1);
  z-index: 100;
}

.sellzio-payment-footer-inner {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  max-width: 100%;
  margin: 0 auto;
}

.sellzio-payment-total {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.sellzio-total-label {
  font-size: 12px;
  color: #666;
  margin-bottom: 2px;
}

.sellzio-total-amount {
  font-size: 18px;
  font-weight: 700;
  color: #ee4d2d;
}

.sellzio-payment-btn {
  background: #ee4d2d;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 12px 24px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 140px;
  height: 48px;
}

.sellzio-payment-btn:hover:not(:disabled) {
  background: #d73527;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(238, 77, 45, 0.3);
}

.sellzio-payment-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Order Summary Styles */
.sellzio-order-summary .sellzio-order-items {
  margin-bottom: 16px;
}

.sellzio-order-item {
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f5f5f5;
}

.sellzio-order-item:last-child {
  border-bottom: none;
}

.sellzio-item-image {
  width: 60px;
  height: 60px;
  border-radius: 8px;
  overflow: hidden;
  margin-right: 12px;
  flex-shrink: 0;
}

.sellzio-item-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.sellzio-item-details {
  flex: 1;
  min-width: 0;
}

.sellzio-item-name {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin: 0 0 4px 0;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.sellzio-item-total {
  font-size: 14px;
  font-weight: 600;
  color: #ee4d2d;
  margin-left: 12px;
}

/* Payment Methods Styles */
.sellzio-payment-groups {
  space-y: 12px;
}

.sellzio-payment-group {
  border: 1px solid #e5e5e5;
  border-radius: 8px;
  margin-bottom: 12px;
  overflow: hidden;
}

.sellzio-payment-group-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #f8f9fa;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.sellzio-payment-group-header:hover {
  background: #e9ecef;
}

.sellzio-payment-method {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.sellzio-payment-method:hover {
  background: #f8f9fa;
}

.sellzio-payment-method.selected {
  background: #fff5f5;
  border-color: #ee4d2d;
}

.sellzio-payment-method-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.sellzio-payment-method-logo {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  overflow: hidden;
  margin-right: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f5f5;
}

.sellzio-radio {
  width: 20px;
  height: 20px;
  border: 2px solid #ddd;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: border-color 0.2s ease;
}

.sellzio-radio.checked {
  border-color: #ee4d2d;
}

.sellzio-radio-dot {
  width: 10px;
  height: 10px;
  background: #ee4d2d;
  border-radius: 50%;
}

/* Responsive Styles */
@media (max-width: 768px) {
  .sellzio-payment-container {
    padding: 0 12px 80px 12px;
  }

  .sellzio-payment-section {
    margin-bottom: 6px;
    padding: 12px;
  }

  .sellzio-payment-header-inner {
    padding: 10px 12px;
  }

  .sellzio-payment-title {
    font-size: 16px;
  }

  .sellzio-payment-footer-inner {
    padding: 12px;
  }

  .sellzio-total-amount {
    font-size: 16px;
  }

  .sellzio-payment-btn {
    padding: 10px 20px;
    font-size: 14px;
    min-width: 120px;
    height: 44px;
  }
}

@media (max-width: 480px) {
  .sellzio-payment-footer-inner {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .sellzio-payment-total {
    align-items: center;
    text-align: center;
  }

  .sellzio-payment-btn {
    width: 100%;
  }
}

/* Animation Styles */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.sellzio-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 8px;
}

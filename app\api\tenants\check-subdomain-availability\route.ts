import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const subdomain = searchParams.get('name') || searchParams.get('subdomain')
    const currentTenant = searchParams.get('currentTenant')

    if (!subdomain) {
      return NextResponse.json(
        { available: false, error: 'Subdomain parameter is required' },
        { status: 400 }
      )
    }

    // Validate subdomain format
    const subdomainRegex = /^[a-z0-9]([a-z0-9-]*[a-z0-9])?$/
    if (!subdomainRegex.test(subdomain)) {
      return NextResponse.json({
        available: false,
        error: 'Invalid subdomain format'
      })
    }

    // Check reserved subdomains
    const reservedNames = [
      'admin', 'api', 'www', 'cdn', 'mail', 'ftp', 'blog', 'shop', 'app',
      'dashboard', 'panel', 'control', 'manage', 'system', 'root', 'test'
    ]

    if (reservedNames.includes(subdomain.toLowerCase())) {
      return NextResponse.json({
        available: false,
        error: 'Subdomain is reserved'
      })
    }

    console.log('🔥 API: Checking subdomain availability:', { subdomain, currentTenant })

    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    // Check if subdomain is already taken by another tenant
    let query = supabase
      .from('tenants')
      .select('id, name, subdomain, slug')
      .eq('subdomain', subdomain)

    // Exclude current tenant if provided (use slug instead of id for comparison)
    if (currentTenant) {
      query = query.neq('slug', currentTenant)
    }

    const { data: existingTenants, error } = await query

    if (error) {
      console.error('🔥 API: Database error:', error)
      return NextResponse.json({
        available: false,
        error: 'Database error'
      }, { status: 500 })
    }

    const available = !existingTenants || existingTenants.length === 0

    console.log('🔥 API: Subdomain availability result:', {
      subdomain,
      available,
      existingTenant: existingTenants && existingTenants.length > 0 ? existingTenants[0] : null
    })

    return NextResponse.json({
      available,
      subdomain,
      message: available
        ? 'Subdomain tersedia'
        : `Subdomain sudah digunakan oleh tenant: ${existingTenants?.[0]?.name || 'Unknown'}`
    })

  } catch (error) {
    console.error('🔥 API: Error checking subdomain availability:', error)
    return NextResponse.json({
      available: false,
      error: 'Internal server error'
    }, { status: 500 })
  }
}

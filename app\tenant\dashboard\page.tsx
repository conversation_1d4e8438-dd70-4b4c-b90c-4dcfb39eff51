"use client"

import { But<PERSON> } from "@/components/ui/button"
import { ArrowLeft, RefreshCw, Download, Settings } from "lucide-react"
import Link from "next/link"
import { useNotifications } from "@/components/providers/notifications-provider"
import { useSession } from "@/hooks/use-session"
import { useEffect, useRef } from "react"
import { use } from "react"

// Import komponen baru
import { KPICards } from "@/components/tenant/kpi-cards"
import { RevenueChart, StorePerformanceChart } from "@/components/tenant/dashboard-charts"
import { AlertDashboard } from "@/components/tenant/alert-dashboard"
import { ActivityFeed } from "@/components/tenant/activity-feed"

export default function TenantDashboardPage({ searchParams }: { searchParams: Promise<{ tenant?: string }> }) {
  // Unwrap searchParams dengan React.use()
  const resolvedSearchParams = use(searchParams)
  const tenantId = resolvedSearchParams.tenant || "unknown"

  const { showNotification } = useNotifications()
  const { user, loading, error } = useSession()
  const notificationShownRef = useRef(false)

  useEffect(() => {
    if (!notificationShownRef.current) {
      showNotification("Selamat datang di dashboard tenant", "success", "Dashboard Tenant")
      notificationShownRef.current = true
    }
  }, [showNotification])

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="icon" asChild>
            <Link href="/admin/dashboard/tenants">
              <ArrowLeft className="h-4 w-4" />
              <span className="sr-only">Back</span>
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Dashboard Overview</h1>
            <p className="text-muted-foreground">
              Tenant ID: <span className="font-medium">{tenantId}</span> •
              User: <span className="font-medium">{user?.name || 'Loading...'}</span> •
              Marketplace Demo • Paket Business
            </p>
            {error && (
              <p className="text-red-500 text-sm mt-1">Session Error: {error}</p>
            )}
          </div>
        </div>
        
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Export Data
          </Button>
          <Button variant="outline" size="sm">
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button variant="outline" size="sm" asChild>
            <Link href="/tenant/dashboard/settings">
              <Settings className="h-4 w-4 mr-2" />
              Settings
            </Link>
          </Button>
        </div>
      </div>

      {/* KPI Cards */}
      <section>
        <h2 className="text-lg font-semibold mb-4">Key Performance Indicators</h2>
        <KPICards />
      </section>

      {/* Charts Section */}
      <section>
        <h2 className="text-lg font-semibold mb-4">Analytics Overview</h2>
        <div className="grid gap-4 md:grid-cols-7">
          <RevenueChart />
          <StorePerformanceChart />
        </div>
      </section>

      {/* Alert & Activity Section */}
      <section>
        <h2 className="text-lg font-semibold mb-4">Monitoring & Activity</h2>
        <div className="grid gap-6 lg:grid-cols-2">
          <AlertDashboard />
          <ActivityFeed />
        </div>
      </section>

      {/* Quick Actions - Simplified Version */}
      <section>
        <h2 className="text-lg font-semibold mb-4">Quick Actions</h2>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Link 
            href="/tenant/dashboard/stores" 
            className="block p-4 border rounded-lg hover:bg-accent transition-colors"
          >
            <h3 className="font-medium">Kelola Stores</h3>
            <p className="text-sm text-muted-foreground mt-1">
              Manage stores, aplikasi, dan pengaturan
            </p>
          </Link>
          
          <Link 
            href="/tenant/dashboard/products" 
            className="block p-4 border rounded-lg hover:bg-accent transition-colors"
          >
            <h3 className="font-medium">Kelola Produk</h3>
            <p className="text-sm text-muted-foreground mt-1">
              Manage produk, kategori, dan moderasi
            </p>
          </Link>
          
          <Link 
            href="/tenant/dashboard/orders" 
            className="block p-4 border rounded-lg hover:bg-accent transition-colors"
          >
            <h3 className="font-medium">Kelola Pesanan</h3>
            <p className="text-sm text-muted-foreground mt-1">
              Fulfill pesanan dan kelola returns
            </p>
          </Link>
          
          <Link 
            href="/tenant/dashboard/marketing" 
            className="block p-4 border rounded-lg hover:bg-accent transition-colors"
          >
            <h3 className="font-medium">Marketing</h3>
            <p className="text-sm text-muted-foreground mt-1">
              Campaigns, promotions, dan email marketing
            </p>
          </Link>
        </div>
      </section>

      {/* Tenant Information - Simplified */}
      <section>
        <h2 className="text-lg font-semibold mb-4">Tenant Information</h2>
        <div className="grid gap-4 md:grid-cols-3">
          <div className="p-4 border rounded-lg">
            <h3 className="font-medium">Business Info</h3>
            <div className="mt-2 space-y-1 text-sm text-muted-foreground">
              <p>Nama: Marketplace Demo</p>
              <p>Status: Aktif</p>
              <p>Paket: Business Plan</p>
            </div>
          </div>
          
          <div className="p-4 border rounded-lg">
            <h3 className="font-medium">Performance</h3>
            <div className="mt-2 space-y-1 text-sm text-muted-foreground">
              <p>Uptime: 99.9%</p>
              <p>Response Time: ~200ms</p>
              <p>Last Backup: 2 jam lalu</p>
            </div>
          </div>
          
          <div className="p-4 border rounded-lg">
            <h3 className="font-medium">Support</h3>
            <div className="mt-2 space-y-1 text-sm text-muted-foreground">
              <p>Plan: 24/7 Priority Support</p>
              <p>Contact: <EMAIL></p>
              <p>Documentation: Available</p>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}

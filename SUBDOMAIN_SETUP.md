# Setup Subdomain untuk Development

## 1. Edit Hosts File

Untuk testing subdomain di localhost, Anda perlu menambahkan entry di hosts file:

### Windows:
1. Buka Command Prompt sebagai Administrator
2. Edit file: `C:\Windows\System32\drivers\etc\hosts`
3. Tambahkan baris berikut:

```
127.0.0.1 app.localhost
127.0.0.1 admin.localhost
127.0.0.1 demo.localhost
```

### macOS/Linux:
1. Buka Terminal
2. Edit file: `sudo nano /etc/hosts`
3. Tambahkan baris berikut:

```
127.0.0.1 app.localhost
127.0.0.1 admin.localhost
127.0.0.1 demo.localhost
```

## 2. Testing URLs

Setelah setup hosts file, Anda bisa mengakses:

### Dashboard Subdomain:
- **Login**: http://app.localhost:3000/login
- **Tenant Dashboard**: http://app.localhost:3000/tenant/dashboard
- **Store Dashboard**: http://app.localhost:3000/store/dashboard
- **Buyer Dashboard**: http://app.localhost:3000/buyer/dashboard

### Admin Subdomain:
- **Admin Dashboard**: http://admin.localhost:3000/dashboard
- **Platform Settings**: http://admin.localhost:3000/dashboard/platform-settings

### Main Domain:
- **Main Site**: http://localhost:3000
- **Admin (path-based)**: http://localhost:3000/admin/dashboard

## 3. Demo Credentials

### Tenant Login:
- Email: `<EMAIL>`
- Password: `tenant123`
- Redirect: `/tenant/dashboard`

### Store Login:
- Email: `<EMAIL>`
- Password: `store123`
- Redirect: `/store/dashboard`

### Buyer Login:
- Email: `<EMAIL>`
- Password: `buyer123`
- Redirect: `/buyer/dashboard`

### Demo Account:
- Email: `<EMAIL>`
- Password: `demo123`
- Redirect: `/tenant/dashboard` (default)

## 4. Flow Testing

1. **Akses Dashboard Subdomain**: http://app.localhost:3000
2. **Auto Redirect ke Login**: http://app.localhost:3000/login
3. **Login dengan Credentials**: Pilih salah satu demo account
4. **Auto Redirect ke Dashboard**: Sesuai dengan user type

## 5. Platform Settings

Untuk mengkonfigurasi dashboard subdomain:

1. Akses: http://localhost:3000/admin/dashboard/platform-settings
2. Konfigurasi "Dashboard Configuration" section:
   - Dashboard Subdomain: `app`
   - Dashboard Enabled: `true`
   - Dashboard SSL: `false` (untuk development)
3. Save settings

## 6. Troubleshooting

### Subdomain tidak berfungsi:
1. Pastikan hosts file sudah di-edit dengan benar
2. Restart browser setelah edit hosts file
3. Clear browser cache
4. Pastikan tidak ada typo di hosts file

### Login tidak berfungsi:
1. Cek console browser untuk error
2. Pastikan API `/api/auth/login` berjalan
3. Cek network tab untuk request/response

### Redirect tidak berfungsi:
1. Cek middleware logs di terminal
2. Pastikan platform settings sudah dikonfigurasi
3. Cek headers `x-panel-type` di browser dev tools
